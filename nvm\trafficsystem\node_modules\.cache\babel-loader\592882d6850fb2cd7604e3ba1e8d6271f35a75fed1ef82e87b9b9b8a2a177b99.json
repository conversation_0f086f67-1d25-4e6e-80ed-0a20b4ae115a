{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createBlock as _createBlock, normalizeClass as _normalizeClass, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"four-way-preview-container\"\n};\nconst _hoisted_2 = {\n  class: \"preview-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-left\"\n};\nconst _hoisted_4 = {\n  class: \"preview-title\"\n};\nconst _hoisted_5 = {\n  class: \"header-controls\"\n};\nconst _hoisted_6 = {\n  class: \"sync-indicator\"\n};\nconst _hoisted_7 = {\n  class: \"sync-text\"\n};\nconst _hoisted_8 = {\n  class: \"preview-item north\"\n};\nconst _hoisted_9 = {\n  class: \"direction-label\"\n};\nconst _hoisted_10 = {\n  class: \"detection-stats\"\n};\nconst _hoisted_11 = {\n  class: \"preview-item west\"\n};\nconst _hoisted_12 = {\n  class: \"direction-label\"\n};\nconst _hoisted_13 = {\n  class: \"detection-stats\"\n};\nconst _hoisted_14 = {\n  class: \"intersection-center\"\n};\nconst _hoisted_15 = {\n  class: \"intersection-status\"\n};\nconst _hoisted_16 = {\n  class: \"status-info\"\n};\nconst _hoisted_17 = {\n  class: \"total-vehicles\"\n};\nconst _hoisted_18 = {\n  class: \"processing-status\"\n};\nconst _hoisted_19 = {\n  key: 0,\n  class: \"progress-ring\"\n};\nconst _hoisted_20 = {\n  class: \"preview-item east\"\n};\nconst _hoisted_21 = {\n  class: \"direction-label\"\n};\nconst _hoisted_22 = {\n  class: \"detection-stats\"\n};\nconst _hoisted_23 = {\n  class: \"preview-item south\"\n};\nconst _hoisted_24 = {\n  class: \"direction-label\"\n};\nconst _hoisted_25 = {\n  class: \"detection-stats\"\n};\nconst _hoisted_26 = {\n  key: 0,\n  class: \"sync-controls\"\n};\nconst _hoisted_27 = {\n  class: \"sync-options\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Monitor = _resolveComponent(\"Monitor\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_VideoPause = _resolveComponent(\"VideoPause\");\n  const _component_VideoPlay = _resolveComponent(\"VideoPlay\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_RefreshLeft = _resolveComponent(\"RefreshLeft\");\n  const _component_FullScreen = _resolveComponent(\"FullScreen\");\n  const _component_el_button_group = _resolveComponent(\"el-button-group\");\n  const _component_Connection = _resolveComponent(\"Connection\");\n  const _component_Top = _resolveComponent(\"Top\");\n  const _component_RealTimeFrameViewer = _resolveComponent(\"RealTimeFrameViewer\");\n  const _component_Back = _resolveComponent(\"Back\");\n  const _component_Grid = _resolveComponent(\"Grid\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_Right = _resolveComponent(\"Right\");\n  const _component_Bottom = _resolveComponent(\"Bottom\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_slider = _resolveComponent(\"el-slider\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 标题和控制栏 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h3\", _hoisted_4, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Monitor)]),\n    _: 1 /* STABLE */\n  }), _cache[2] || (_cache[2] = _createTextVNode(\" 四方向实时检测预览 \"))]), $props.taskId ? (_openBlock(), _createBlock(_component_el_tag, {\n    key: 0,\n    type: \"info\",\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(\"任务ID: \" + _toDisplayString($props.taskId), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_button_group, null, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: $setup.isPlaying ? 'danger' : 'primary',\n      size: \"small\",\n      onClick: $setup.togglePlayback,\n      disabled: !$setup.hasAnyStream\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [$setup.isPlaying ? (_openBlock(), _createBlock(_component_VideoPause, {\n          key: 0\n        })) : (_openBlock(), _createBlock(_component_VideoPlay, {\n          key: 1\n        }))]),\n        _: 1 /* STABLE */\n      }), _createTextVNode(\" \" + _toDisplayString($setup.isPlaying ? '暂停' : '播放'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\", \"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.resetAllStreams,\n      disabled: !$setup.hasAnyStream\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_RefreshLeft)]),\n        _: 1 /* STABLE */\n      }), _cache[3] || (_cache[3] = _createTextVNode(\" 重置 \"))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.toggleFullscreen\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_FullScreen)]),\n        _: 1 /* STABLE */\n      }), _cache[4] || (_cache[4] = _createTextVNode(\" 全屏 \"))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 同步状态指示器 \"), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_icon, {\n    class: _normalizeClass({\n      'synced': $setup.isSynced,\n      'unsynced': !$setup.isSynced\n    })\n  }, {\n    default: _withCtx(() => [_createVNode(_component_Connection)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _createElementVNode(\"span\", _hoisted_7, _toDisplayString($setup.isSynced ? '同步' : '异步'), 1 /* TEXT */)])])]), _createCommentVNode(\" 四方向预览网格 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"preview-grid\", {\n      'fullscreen': $setup.isFullscreen\n    }])\n  }, [_createCommentVNode(\" 北向预览 \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Top)]),\n    _: 1 /* STABLE */\n  }), _cache[5] || (_cache[5] = _createElementVNode(\"span\", null, \"北向 (North)\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.getStatusTagType('north'),\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getDirectionStatus('north')), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"])]), _createVNode(_component_RealTimeFrameViewer, {\n    ref: \"northViewer\",\n    \"task-id\": $props.taskId,\n    direction: 'north',\n    \"is-playing\": $setup.isPlaying,\n    onFrameReceived: $setup.onFrameReceived,\n    onStatusChanged: $setup.onDirectionStatusChanged,\n    onError: $setup.onDirectionError,\n    class: \"frame-viewer\"\n  }, null, 8 /* PROPS */, [\"task-id\", \"is-playing\", \"onFrameReceived\", \"onStatusChanged\", \"onError\"]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"span\", null, \"检测数: \" + _toDisplayString($setup.directionStats.north.detectionCount), 1 /* TEXT */), _createElementVNode(\"span\", null, \"车辆数: \" + _toDisplayString($setup.directionStats.north.vehicleCount), 1 /* TEXT */)])]), _createCommentVNode(\" 西向预览 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Back)]),\n    _: 1 /* STABLE */\n  }), _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"西向 (West)\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.getStatusTagType('west'),\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getDirectionStatus('west')), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"])]), _createVNode(_component_RealTimeFrameViewer, {\n    ref: \"westViewer\",\n    \"task-id\": $props.taskId,\n    direction: 'west',\n    \"is-playing\": $setup.isPlaying,\n    onFrameReceived: $setup.onFrameReceived,\n    onStatusChanged: $setup.onDirectionStatusChanged,\n    onError: $setup.onDirectionError,\n    class: \"frame-viewer\"\n  }, null, 8 /* PROPS */, [\"task-id\", \"is-playing\", \"onFrameReceived\", \"onStatusChanged\", \"onError\"]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"span\", null, \"检测数: \" + _toDisplayString($setup.directionStats.west.detectionCount), 1 /* TEXT */), _createElementVNode(\"span\", null, \"车辆数: \" + _toDisplayString($setup.directionStats.west.vehicleCount), 1 /* TEXT */)])]), _createCommentVNode(\" 中心交叉路口状态 \"), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_icon, {\n    size: \"32\",\n    class: \"intersection-icon\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_Grid)]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"p\", _hoisted_17, \"总车辆: \" + _toDisplayString($setup.totalVehicleCount), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_18, _toDisplayString($setup.overallStatus), 1 /* TEXT */), $setup.overallProgress > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createVNode(_component_el_progress, {\n    type: \"circle\",\n    percentage: $setup.overallProgress,\n    width: 60,\n    \"stroke-width\": 4\n  }, null, 8 /* PROPS */, [\"percentage\"])])) : _createCommentVNode(\"v-if\", true)])])]), _createCommentVNode(\" 东向预览 \"), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Right)]),\n    _: 1 /* STABLE */\n  }), _cache[7] || (_cache[7] = _createElementVNode(\"span\", null, \"东向 (East)\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.getStatusTagType('east'),\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getDirectionStatus('east')), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"])]), _createVNode(_component_RealTimeFrameViewer, {\n    ref: \"eastViewer\",\n    \"task-id\": $props.taskId,\n    direction: 'east',\n    \"is-playing\": $setup.isPlaying,\n    onFrameReceived: $setup.onFrameReceived,\n    onStatusChanged: $setup.onDirectionStatusChanged,\n    onError: $setup.onDirectionError,\n    class: \"frame-viewer\"\n  }, null, 8 /* PROPS */, [\"task-id\", \"is-playing\", \"onFrameReceived\", \"onStatusChanged\", \"onError\"]), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"span\", null, \"检测数: \" + _toDisplayString($setup.directionStats.east.detectionCount), 1 /* TEXT */), _createElementVNode(\"span\", null, \"车辆数: \" + _toDisplayString($setup.directionStats.east.vehicleCount), 1 /* TEXT */)])]), _createCommentVNode(\" 南向预览 \"), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Bottom)]),\n    _: 1 /* STABLE */\n  }), _cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"南向 (South)\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.getStatusTagType('south'),\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getDirectionStatus('south')), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"])]), _createVNode(_component_RealTimeFrameViewer, {\n    ref: \"southViewer\",\n    \"task-id\": $props.taskId,\n    direction: 'south',\n    \"is-playing\": $setup.isPlaying,\n    onFrameReceived: $setup.onFrameReceived,\n    onStatusChanged: $setup.onDirectionStatusChanged,\n    onError: $setup.onDirectionError,\n    class: \"frame-viewer\"\n  }, null, 8 /* PROPS */, [\"task-id\", \"is-playing\", \"onFrameReceived\", \"onStatusChanged\", \"onError\"]), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"span\", null, \"检测数: \" + _toDisplayString($setup.directionStats.south.detectionCount), 1 /* TEXT */), _createElementVNode(\"span\", null, \"车辆数: \" + _toDisplayString($setup.directionStats.south.vehicleCount), 1 /* TEXT */)])])], 2 /* CLASS */), _createCommentVNode(\" 同步控制面板 \"), $setup.hasAnyStream ? (_openBlock(), _createElementBlock(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createVNode(_component_el_checkbox, {\n    modelValue: $setup.enableSync,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.enableSync = $event),\n    onChange: $setup.onSyncToggle\n  }, {\n    default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\" 启用四方向同步播放 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"onChange\"]), _createVNode(_component_el_slider, {\n    modelValue: $setup.syncTolerance,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.syncTolerance = $event),\n    min: 100,\n    max: 2000,\n    step: 100,\n    \"show-input\": \"\",\n    \"show-input-controls\": false,\n    class: \"sync-tolerance-slider\"\n  }, {\n    label: _withCtx(() => [_createTextVNode(\" 同步容差 (ms): \" + _toDisplayString($setup.syncTolerance), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_Monitor", "_", "_createTextVNode", "$props", "taskId", "_createBlock", "_component_el_tag", "type", "size", "_toDisplayString", "_hoisted_5", "_component_el_button_group", "_component_el_button", "$setup", "isPlaying", "onClick", "togglePlayback", "disabled", "hasAnyStream", "_component_VideoPause", "_component_VideoPlay", "resetAllStreams", "_component_RefreshLeft", "toggleFullscreen", "_component_FullScreen", "_hoisted_6", "_normalizeClass", "isSynced", "_component_Connection", "_hoisted_7", "isFullscreen", "_hoisted_8", "_hoisted_9", "_component_Top", "getStatusTagType", "getDirectionStatus", "_component_RealTimeFrameViewer", "ref", "direction", "onFrameReceived", "onStatusChanged", "onDirectionStatusChanged", "onError", "onDirectionError", "_hoisted_10", "directionStats", "north", "detectionCount", "vehicleCount", "_hoisted_11", "_hoisted_12", "_component_Back", "_hoisted_13", "west", "_hoisted_14", "_hoisted_15", "_component_Grid", "_hoisted_16", "_hoisted_17", "totalVehicleCount", "_hoisted_18", "overallStatus", "overallProgress", "_hoisted_19", "_component_el_progress", "percentage", "width", "_hoisted_20", "_hoisted_21", "_component_Right", "_hoisted_22", "east", "_hoisted_23", "_hoisted_24", "_component_Bottom", "_hoisted_25", "south", "_hoisted_26", "_hoisted_27", "_component_el_checkbox", "modelValue", "enableSync", "_cache", "$event", "onChange", "onSyncToggle", "_component_el_slider", "syncTolerance", "min", "max", "step", "label"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\FourWayRealTimePreview.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-preview-container\">\n    <!-- 标题和控制栏 -->\n    <div class=\"preview-header\">\n      <div class=\"header-left\">\n        <h3 class=\"preview-title\">\n          <el-icon><Monitor /></el-icon>\n          四方向实时检测预览\n        </h3>\n        <el-tag v-if=\"taskId\" type=\"info\" size=\"small\">任务ID: {{ taskId }}</el-tag>\n      </div>\n      \n      <div class=\"header-controls\">\n        <el-button-group>\n          <el-button \n            :type=\"isPlaying ? 'danger' : 'primary'\" \n            size=\"small\"\n            @click=\"togglePlayback\"\n            :disabled=\"!hasAnyStream\"\n          >\n            <el-icon>\n              <VideoPause v-if=\"isPlaying\" />\n              <VideoPlay v-else />\n            </el-icon>\n            {{ isPlaying ? '暂停' : '播放' }}\n          </el-button>\n          \n          <el-button \n            size=\"small\"\n            @click=\"resetAllStreams\"\n            :disabled=\"!hasAnyStream\"\n          >\n            <el-icon><RefreshLeft /></el-icon>\n            重置\n          </el-button>\n          \n          <el-button \n            size=\"small\"\n            @click=\"toggleFullscreen\"\n          >\n            <el-icon><FullScreen /></el-icon>\n            全屏\n          </el-button>\n        </el-button-group>\n        \n        <!-- 同步状态指示器 -->\n        <div class=\"sync-indicator\">\n          <el-icon :class=\"{ 'synced': isSynced, 'unsynced': !isSynced }\">\n            <Connection />\n          </el-icon>\n          <span class=\"sync-text\">{{ isSynced ? '同步' : '异步' }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 四方向预览网格 -->\n    <div class=\"preview-grid\" :class=\"{ 'fullscreen': isFullscreen }\">\n      <!-- 北向预览 -->\n      <div class=\"preview-item north\">\n        <div class=\"direction-label\">\n          <el-icon><Top /></el-icon>\n          <span>北向 (North)</span>\n          <el-tag :type=\"getStatusTagType('north')\" size=\"small\">\n            {{ getDirectionStatus('north') }}\n          </el-tag>\n        </div>\n        <RealTimeFrameViewer\n          ref=\"northViewer\"\n          :task-id=\"taskId\"\n          :direction=\"'north'\"\n          :is-playing=\"isPlaying\"\n          @frame-received=\"onFrameReceived\"\n          @status-changed=\"onDirectionStatusChanged\"\n          @error=\"onDirectionError\"\n          class=\"frame-viewer\"\n        />\n        <div class=\"detection-stats\">\n          <span>检测数: {{ directionStats.north.detectionCount }}</span>\n          <span>车辆数: {{ directionStats.north.vehicleCount }}</span>\n        </div>\n      </div>\n\n      <!-- 西向预览 -->\n      <div class=\"preview-item west\">\n        <div class=\"direction-label\">\n          <el-icon><Back /></el-icon>\n          <span>西向 (West)</span>\n          <el-tag :type=\"getStatusTagType('west')\" size=\"small\">\n            {{ getDirectionStatus('west') }}\n          </el-tag>\n        </div>\n        <RealTimeFrameViewer\n          ref=\"westViewer\"\n          :task-id=\"taskId\"\n          :direction=\"'west'\"\n          :is-playing=\"isPlaying\"\n          @frame-received=\"onFrameReceived\"\n          @status-changed=\"onDirectionStatusChanged\"\n          @error=\"onDirectionError\"\n          class=\"frame-viewer\"\n        />\n        <div class=\"detection-stats\">\n          <span>检测数: {{ directionStats.west.detectionCount }}</span>\n          <span>车辆数: {{ directionStats.west.vehicleCount }}</span>\n        </div>\n      </div>\n\n      <!-- 中心交叉路口状态 -->\n      <div class=\"intersection-center\">\n        <div class=\"intersection-status\">\n          <el-icon size=\"32\" class=\"intersection-icon\">\n            <Grid />\n          </el-icon>\n          <div class=\"status-info\">\n            <p class=\"total-vehicles\">总车辆: {{ totalVehicleCount }}</p>\n            <p class=\"processing-status\">{{ overallStatus }}</p>\n            <div class=\"progress-ring\" v-if=\"overallProgress > 0\">\n              <el-progress \n                type=\"circle\" \n                :percentage=\"overallProgress\" \n                :width=\"60\"\n                :stroke-width=\"4\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 东向预览 -->\n      <div class=\"preview-item east\">\n        <div class=\"direction-label\">\n          <el-icon><Right /></el-icon>\n          <span>东向 (East)</span>\n          <el-tag :type=\"getStatusTagType('east')\" size=\"small\">\n            {{ getDirectionStatus('east') }}\n          </el-tag>\n        </div>\n        <RealTimeFrameViewer\n          ref=\"eastViewer\"\n          :task-id=\"taskId\"\n          :direction=\"'east'\"\n          :is-playing=\"isPlaying\"\n          @frame-received=\"onFrameReceived\"\n          @status-changed=\"onDirectionStatusChanged\"\n          @error=\"onDirectionError\"\n          class=\"frame-viewer\"\n        />\n        <div class=\"detection-stats\">\n          <span>检测数: {{ directionStats.east.detectionCount }}</span>\n          <span>车辆数: {{ directionStats.east.vehicleCount }}</span>\n        </div>\n      </div>\n\n      <!-- 南向预览 -->\n      <div class=\"preview-item south\">\n        <div class=\"direction-label\">\n          <el-icon><Bottom /></el-icon>\n          <span>南向 (South)</span>\n          <el-tag :type=\"getStatusTagType('south')\" size=\"small\">\n            {{ getDirectionStatus('south') }}\n          </el-tag>\n        </div>\n        <RealTimeFrameViewer\n          ref=\"southViewer\"\n          :task-id=\"taskId\"\n          :direction=\"'south'\"\n          :is-playing=\"isPlaying\"\n          @frame-received=\"onFrameReceived\"\n          @status-changed=\"onDirectionStatusChanged\"\n          @error=\"onDirectionError\"\n          class=\"frame-viewer\"\n        />\n        <div class=\"detection-stats\">\n          <span>检测数: {{ directionStats.south.detectionCount }}</span>\n          <span>车辆数: {{ directionStats.south.vehicleCount }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 同步控制面板 -->\n    <div class=\"sync-controls\" v-if=\"hasAnyStream\">\n      <div class=\"sync-options\">\n        <el-checkbox v-model=\"enableSync\" @change=\"onSyncToggle\">\n          启用四方向同步播放\n        </el-checkbox>\n        <el-slider\n          v-model=\"syncTolerance\"\n          :min=\"100\"\n          :max=\"2000\"\n          :step=\"100\"\n          show-input\n          :show-input-controls=\"false\"\n          class=\"sync-tolerance-slider\"\n        >\n          <template #label>\n            同步容差 (ms): {{ syncTolerance }}\n          </template>\n        </el-slider>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport {\n  Monitor, VideoPlay, VideoPause, RefreshLeft, FullScreen,\n  Top, Bottom, ArrowLeft as Back, Right, Grid, Connection\n} from '@element-plus/icons-vue'\nimport RealTimeFrameViewer from './RealTimeFrameViewer.vue'\n\nexport default {\n  name: 'FourWayRealTimePreview',\n  components: {\n    Monitor, VideoPlay, VideoPause, RefreshLeft, FullScreen,\n    Top, Bottom, Back, Right, Grid, Connection,\n    RealTimeFrameViewer\n  },\n  props: {\n    taskId: {\n      type: String,\n      required: true\n    },\n    autoStart: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['status-changed', 'sync-status-changed', 'error'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isPlaying = ref(false)\n    const isFullscreen = ref(false)\n    const enableSync = ref(true)\n    const syncTolerance = ref(500) // 同步容差，毫秒\n    \n    // 方向状态\n    const directionStatuses = reactive({\n      north: 'waiting',\n      south: 'waiting', \n      east: 'waiting',\n      west: 'waiting'\n    })\n    \n    // 方向统计数据\n    const directionStats = reactive({\n      north: { detectionCount: 0, vehicleCount: 0, lastFrameTime: 0 },\n      south: { detectionCount: 0, vehicleCount: 0, lastFrameTime: 0 },\n      east: { detectionCount: 0, vehicleCount: 0, lastFrameTime: 0 },\n      west: { detectionCount: 0, vehicleCount: 0, lastFrameTime: 0 }\n    })\n    \n    // 引用\n    const northViewer = ref(null)\n    const southViewer = ref(null)\n    const eastViewer = ref(null)\n    const westViewer = ref(null)\n    \n    // 计算属性\n    const hasAnyStream = computed(() => {\n      return Object.values(directionStatuses).some(status => \n        status === 'connected' || status === 'playing'\n      )\n    })\n    \n    const totalVehicleCount = computed(() => {\n      return Object.values(directionStats).reduce((total, stats) => \n        total + stats.vehicleCount, 0\n      )\n    })\n    \n    const overallProgress = computed(() => {\n      const activeDirections = Object.values(directionStatuses).filter(status => \n        status !== 'waiting' && status !== 'error'\n      ).length\n      \n      if (activeDirections === 0) return 0\n      \n      const completedDirections = Object.values(directionStatuses).filter(status => \n        status === 'completed'\n      ).length\n      \n      return Math.round((completedDirections / 4) * 100)\n    })\n    \n    const overallStatus = computed(() => {\n      const statuses = Object.values(directionStatuses)\n      \n      if (statuses.every(status => status === 'waiting')) {\n        return '等待开始'\n      } else if (statuses.some(status => status === 'playing')) {\n        return '正在检测'\n      } else if (statuses.every(status => status === 'completed')) {\n        return '检测完成'\n      } else if (statuses.some(status => status === 'error')) {\n        return '检测异常'\n      } else {\n        return '准备中'\n      }\n    })\n    \n    const isSynced = computed(() => {\n      if (!enableSync.value) return false\n      \n      const frameTimes = Object.values(directionStats).map(stats => stats.lastFrameTime)\n      const validTimes = frameTimes.filter(time => time > 0)\n      \n      if (validTimes.length < 2) return true\n      \n      const maxTime = Math.max(...validTimes)\n      const minTime = Math.min(...validTimes)\n      \n      return (maxTime - minTime) <= syncTolerance.value\n    })\n    \n    // 方法\n    const getDirectionStatus = (direction) => {\n      const status = directionStatuses[direction]\n      const statusMap = {\n        waiting: '等待',\n        connecting: '连接中',\n        connected: '已连接',\n        playing: '检测中',\n        paused: '已暂停',\n        completed: '已完成',\n        error: '错误'\n      }\n      return statusMap[status] || status\n    }\n    \n    const getStatusTagType = (direction) => {\n      const status = directionStatuses[direction]\n      const typeMap = {\n        waiting: 'info',\n        connecting: 'warning',\n        connected: 'success',\n        playing: 'primary',\n        paused: 'warning',\n        completed: 'success',\n        error: 'danger'\n      }\n      return typeMap[status] || 'info'\n    }\n    \n    const togglePlayback = () => {\n      isPlaying.value = !isPlaying.value\n      \n      // 通知所有方向的播放状态变化\n      const viewers = [northViewer.value, southViewer.value, eastViewer.value, westViewer.value]\n      viewers.forEach(viewer => {\n        if (viewer && typeof viewer.togglePlayback === 'function') {\n          viewer.togglePlayback()\n        }\n      })\n      \n      emit('status-changed', {\n        action: isPlaying.value ? 'play' : 'pause',\n        taskId: props.taskId\n      })\n    }\n    \n    const resetAllStreams = () => {\n      const viewers = [northViewer.value, southViewer.value, eastViewer.value, westViewer.value]\n      viewers.forEach(viewer => {\n        if (viewer && typeof viewer.reset === 'function') {\n          viewer.reset()\n        }\n      })\n      \n      // 重置统计数据\n      Object.keys(directionStats).forEach(direction => {\n        directionStats[direction] = { detectionCount: 0, vehicleCount: 0, lastFrameTime: 0 }\n        directionStatuses[direction] = 'waiting'\n      })\n      \n      isPlaying.value = false\n      \n      ElMessage.success('已重置所有方向的预览')\n    }\n    \n    const toggleFullscreen = () => {\n      isFullscreen.value = !isFullscreen.value\n      \n      if (isFullscreen.value) {\n        document.documentElement.requestFullscreen?.()\n      } else {\n        document.exitFullscreen?.()\n      }\n    }\n    \n    const onFrameReceived = (data) => {\n      const { direction, frameData, vehicleCount, timestamp } = data\n      \n      if (directionStats[direction]) {\n        directionStats[direction].detectionCount++\n        directionStats[direction].vehicleCount = vehicleCount || 0\n        directionStats[direction].lastFrameTime = timestamp || Date.now()\n      }\n      \n      // 检查同步状态\n      if (enableSync.value) {\n        checkSyncStatus()\n      }\n    }\n    \n    const onDirectionStatusChanged = (data) => {\n      const { direction, status } = data\n      if (directionStatuses[direction] !== undefined) {\n        directionStatuses[direction] = status\n      }\n      \n      emit('status-changed', {\n        direction,\n        status,\n        taskId: props.taskId\n      })\n    }\n    \n    const onDirectionError = (data) => {\n      const { direction, error } = data\n      directionStatuses[direction] = 'error'\n      \n      ElMessage.error(`${getDirectionName(direction)}检测出错: ${error.message || error}`)\n      \n      emit('error', {\n        direction,\n        error,\n        taskId: props.taskId\n      })\n    }\n    \n    const onSyncToggle = (enabled) => {\n      emit('sync-status-changed', {\n        enabled,\n        tolerance: syncTolerance.value,\n        taskId: props.taskId\n      })\n    }\n    \n    const checkSyncStatus = () => {\n      const currentSyncStatus = isSynced.value\n      emit('sync-status-changed', {\n        enabled: enableSync.value,\n        synced: currentSyncStatus,\n        tolerance: syncTolerance.value,\n        taskId: props.taskId\n      })\n    }\n    \n    const getDirectionName = (direction) => {\n      const names = {\n        east: '东向',\n        south: '南向',\n        west: '西向',\n        north: '北向'\n      }\n      return names[direction] || direction\n    }\n    \n    // 生命周期\n    onMounted(() => {\n      if (props.autoStart) {\n        setTimeout(() => {\n          isPlaying.value = true\n        }, 1000)\n      }\n    })\n    \n    onUnmounted(() => {\n      if (isFullscreen.value) {\n        document.exitFullscreen?.()\n      }\n    })\n    \n    // 监听器\n    watch(() => props.taskId, (newTaskId) => {\n      if (newTaskId) {\n        resetAllStreams()\n      }\n    })\n    \n    return {\n      // 响应式数据\n      isPlaying,\n      isFullscreen,\n      enableSync,\n      syncTolerance,\n      directionStatuses,\n      directionStats,\n      \n      // 引用\n      northViewer,\n      southViewer,\n      eastViewer,\n      westViewer,\n      \n      // 计算属性\n      hasAnyStream,\n      totalVehicleCount,\n      overallProgress,\n      overallStatus,\n      isSynced,\n      \n      // 方法\n      getDirectionStatus,\n      getStatusTagType,\n      togglePlayback,\n      resetAllStreams,\n      toggleFullscreen,\n      onFrameReceived,\n      onDirectionStatusChanged,\n      onDirectionError,\n      onSyncToggle\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-preview-container {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background: #f8f9fa;\n}\n\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  background: white;\n  border-bottom: 1px solid #e4e7ed;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.preview-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin: 0;\n  font-size: 18px;\n  color: #2c3e50;\n}\n\n.header-controls {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.sync-indicator {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n}\n\n.sync-indicator .synced {\n  color: #67c23a;\n}\n\n.sync-indicator .unsynced {\n  color: #f56c6c;\n}\n\n.preview-grid {\n  flex: 1;\n  display: grid;\n  grid-template-columns: 1fr 300px 1fr;\n  grid-template-rows: 1fr 300px 1fr;\n  gap: 16px;\n  padding: 20px;\n  min-height: 600px;\n}\n\n.preview-grid.fullscreen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 9999;\n  background: #000;\n  padding: 10px;\n}\n\n.preview-item {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.preview-item.north {\n  grid-column: 2;\n  grid-row: 1;\n}\n\n.preview-item.west {\n  grid-column: 1;\n  grid-row: 2;\n}\n\n.preview-item.east {\n  grid-column: 3;\n  grid-row: 2;\n}\n\n.preview-item.south {\n  grid-column: 2;\n  grid-row: 3;\n}\n\n.intersection-center {\n  grid-column: 2;\n  grid-row: 2;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.intersection-status {\n  text-align: center;\n}\n\n.intersection-icon {\n  color: #409eff;\n  margin-bottom: 12px;\n}\n\n.status-info p {\n  margin: 4px 0;\n  font-size: 14px;\n}\n\n.total-vehicles {\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.processing-status {\n  color: #909399;\n}\n\n.progress-ring {\n  margin-top: 12px;\n}\n\n.direction-label {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  font-weight: 500;\n  color: #2c3e50;\n}\n\n.frame-viewer {\n  flex: 1;\n  min-height: 200px;\n}\n\n.detection-stats {\n  display: flex;\n  justify-content: space-between;\n  padding: 8px 16px;\n  background: #f8f9fa;\n  border-top: 1px solid #e4e7ed;\n  font-size: 12px;\n  color: #909399;\n}\n\n.sync-controls {\n  padding: 16px 20px;\n  background: white;\n  border-top: 1px solid #e4e7ed;\n}\n\n.sync-options {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.sync-tolerance-slider {\n  width: 200px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .preview-grid {\n    grid-template-columns: 1fr 250px 1fr;\n    grid-template-rows: 1fr 250px 1fr;\n  }\n}\n\n@media (max-width: 768px) {\n  .preview-grid {\n    grid-template-columns: 1fr;\n    grid-template-rows: auto;\n    gap: 12px;\n  }\n  \n  .preview-item {\n    grid-column: 1 !important;\n    grid-row: auto !important;\n  }\n  \n  .intersection-center {\n    grid-column: 1 !important;\n    grid-row: auto !important;\n    order: -1;\n  }\n  \n  .header-controls {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .sync-options {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 12px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA4B;;EAEhCA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;EAClBA,KAAK,EAAC;AAAe;;EAOtBA,KAAK,EAAC;AAAiB;;EAkCrBA,KAAK,EAAC;AAAgB;;EAInBA,KAAK,EAAC;AAAW;;EAQtBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiB;;EAiBvBA,KAAK,EAAC;AAAiB;;EAOzBA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAiB;;EAiBvBA,KAAK,EAAC;AAAiB;;EAOzBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAqB;;EAIzBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAgB;;EACtBA,KAAK,EAAC;AAAmB;;EAnHxCC,GAAA;EAoHiBD,KAAK,EAAC;;;EAaZA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAiB;;EAiBvBA,KAAK,EAAC;AAAiB;;EAOzBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiB;;EAiBvBA,KAAK,EAAC;AAAiB;;EA5KpCC,GAAA;EAoLSD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAc;;;;;;;;;;;;;;;;;;;;;uBApL7BE,mBAAA,CAuMM,OAvMNC,UAuMM,GAtMJC,mBAAA,YAAe,EACfC,mBAAA,CAkDM,OAlDNC,UAkDM,GAjDJD,mBAAA,CAMM,OANNE,UAMM,GALJF,mBAAA,CAGK,MAHLG,UAGK,GAFHC,YAAA,CAA8BC,kBAAA;IANxCC,OAAA,EAAAC,QAAA,CAMmB,MAAW,CAAXH,YAAA,CAAWI,kBAAA,E;IAN9BC,CAAA;gCAAAC,gBAAA,CAMwC,aAEhC,G,GACcC,MAAA,CAAAC,MAAM,I,cAApBC,YAAA,CAA0EC,iBAAA;IATlFlB,GAAA;IAS8BmB,IAAI,EAAC,MAAM;IAACC,IAAI,EAAC;;IAT/CV,OAAA,EAAAC,QAAA,CASuD,MAAM,CAT7DG,gBAAA,CASuD,QAAM,GAAAO,gBAAA,CAAGN,MAAA,CAAAC,MAAM,iB;IATtEH,CAAA;QAAAV,mBAAA,e,GAYMC,mBAAA,CAwCM,OAxCNkB,UAwCM,GAvCJd,YAAA,CA8BkBe,0BAAA;IA3C1Bb,OAAA,EAAAC,QAAA,CAcU,MAWY,CAXZH,YAAA,CAWYgB,oBAAA;MAVTL,IAAI,EAAEM,MAAA,CAAAC,SAAS;MAChBN,IAAI,EAAC,OAAO;MACXO,OAAK,EAAEF,MAAA,CAAAG,cAAc;MACrBC,QAAQ,GAAGJ,MAAA,CAAAK;;MAlBxBpB,OAAA,EAAAC,QAAA,CAoBY,MAGU,CAHVH,YAAA,CAGUC,kBAAA;QAvBtBC,OAAA,EAAAC,QAAA,CAqBM,MACgB,CADUc,MAAA,CAAAC,SAAS,I,cAA3BT,YAAA,CAA+Bc,qBAAA;UArB7C/B,GAAA;QAAA,O,cAsBciB,YAAA,CAAoBe,oBAAA;UAtBlChC,GAAA;QAAA,I;QAAAa,CAAA;UAAAC,gBAAA,CAuBsB,GACV,GAAAO,gBAAA,CAAGI,MAAA,CAAAC,SAAS,+B;MAxBxBb,CAAA;wDA2BUL,YAAA,CAOYgB,oBAAA;MANVJ,IAAI,EAAC,OAAO;MACXO,OAAK,EAAEF,MAAA,CAAAQ,eAAe;MACtBJ,QAAQ,GAAGJ,MAAA,CAAAK;;MA9BxBpB,OAAA,EAAAC,QAAA,CAgCY,MAAkC,CAAlCH,YAAA,CAAkCC,kBAAA;QAhC9CC,OAAA,EAAAC,QAAA,CAgCqB,MAAe,CAAfH,YAAA,CAAe0B,sBAAA,E;QAhCpCrB,CAAA;oCAAAC,gBAAA,CAgC8C,MAEpC,G;MAlCVD,CAAA;gDAoCUL,YAAA,CAMYgB,oBAAA;MALVJ,IAAI,EAAC,OAAO;MACXO,OAAK,EAAEF,MAAA,CAAAU;;MAtCpBzB,OAAA,EAAAC,QAAA,CAwCY,MAAiC,CAAjCH,YAAA,CAAiCC,kBAAA;QAxC7CC,OAAA,EAAAC,QAAA,CAwCqB,MAAc,CAAdH,YAAA,CAAc4B,qBAAA,E;QAxCnCvB,CAAA;oCAAAC,gBAAA,CAwC6C,MAEnC,G;MA1CVD,CAAA;;IAAAA,CAAA;MA6CQV,mBAAA,aAAgB,EAChBC,mBAAA,CAKM,OALNiC,UAKM,GAJJ7B,YAAA,CAEUC,kBAAA;IAFAV,KAAK,EA/CzBuC,eAAA;MAAA,UA+CuCb,MAAA,CAAAc,QAAQ;MAAA,aAAed,MAAA,CAAAc;IAAQ;;IA/CtE7B,OAAA,EAAAC,QAAA,CAgDY,MAAc,CAAdH,YAAA,CAAcgC,qBAAA,E;IAhD1B3B,CAAA;gCAkDUT,mBAAA,CAA2D,QAA3DqC,UAA2D,EAAApB,gBAAA,CAAhCI,MAAA,CAAAc,QAAQ,+B,OAKzCpC,mBAAA,aAAgB,EAChBC,mBAAA,CAyHM;IAzHDL,KAAK,EAxDduC,eAAA,EAwDe,cAAc;MAAA,cAAyBb,MAAA,CAAAiB;IAAY;MAC5DvC,mBAAA,UAAa,EACbC,mBAAA,CAsBM,OAtBNuC,UAsBM,GArBJvC,mBAAA,CAMM,OANNwC,UAMM,GALJpC,YAAA,CAA0BC,kBAAA;IA5DpCC,OAAA,EAAAC,QAAA,CA4DmB,MAAO,CAAPH,YAAA,CAAOqC,cAAA,E;IA5D1BhC,CAAA;gCA6DUT,mBAAA,CAAuB,cAAjB,YAAU,sBAChBI,YAAA,CAESU,iBAAA;IAFAC,IAAI,EAAEM,MAAA,CAAAqB,gBAAgB;IAAW1B,IAAI,EAAC;;IA9DzDV,OAAA,EAAAC,QAAA,CA+DY,MAAiC,CA/D7CG,gBAAA,CAAAO,gBAAA,CA+DeI,MAAA,CAAAsB,kBAAkB,0B;IA/DjClC,CAAA;iCAkEQL,YAAA,CASEwC,8BAAA;IARAC,GAAG,EAAC,aAAa;IAChB,SAAO,EAAElC,MAAA,CAAAC,MAAM;IACfkC,SAAS,EAAE,OAAO;IAClB,YAAU,EAAEzB,MAAA,CAAAC,SAAS;IACrByB,eAAc,EAAE1B,MAAA,CAAA0B,eAAe;IAC/BC,eAAc,EAAE3B,MAAA,CAAA4B,wBAAwB;IACxCC,OAAK,EAAE7B,MAAA,CAAA8B,gBAAgB;IACxBxD,KAAK,EAAC;uGAERK,mBAAA,CAGM,OAHNoD,WAGM,GAFJpD,mBAAA,CAA2D,cAArD,OAAK,GAAAiB,gBAAA,CAAGI,MAAA,CAAAgC,cAAc,CAACC,KAAK,CAACC,cAAc,kBACjDvD,mBAAA,CAAyD,cAAnD,OAAK,GAAAiB,gBAAA,CAAGI,MAAA,CAAAgC,cAAc,CAACC,KAAK,CAACE,YAAY,iB,KAInDzD,mBAAA,UAAa,EACbC,mBAAA,CAsBM,OAtBNyD,WAsBM,GArBJzD,mBAAA,CAMM,OANN0D,WAMM,GALJtD,YAAA,CAA2BC,kBAAA;IArFrCC,OAAA,EAAAC,QAAA,CAqFmB,MAAQ,CAARH,YAAA,CAAQuD,eAAA,E;IArF3BlD,CAAA;gCAsFUT,mBAAA,CAAsB,cAAhB,WAAS,sBACfI,YAAA,CAESU,iBAAA;IAFAC,IAAI,EAAEM,MAAA,CAAAqB,gBAAgB;IAAU1B,IAAI,EAAC;;IAvFxDV,OAAA,EAAAC,QAAA,CAwFY,MAAgC,CAxF5CG,gBAAA,CAAAO,gBAAA,CAwFeI,MAAA,CAAAsB,kBAAkB,yB;IAxFjClC,CAAA;iCA2FQL,YAAA,CASEwC,8BAAA;IARAC,GAAG,EAAC,YAAY;IACf,SAAO,EAAElC,MAAA,CAAAC,MAAM;IACfkC,SAAS,EAAE,MAAM;IACjB,YAAU,EAAEzB,MAAA,CAAAC,SAAS;IACrByB,eAAc,EAAE1B,MAAA,CAAA0B,eAAe;IAC/BC,eAAc,EAAE3B,MAAA,CAAA4B,wBAAwB;IACxCC,OAAK,EAAE7B,MAAA,CAAA8B,gBAAgB;IACxBxD,KAAK,EAAC;uGAERK,mBAAA,CAGM,OAHN4D,WAGM,GAFJ5D,mBAAA,CAA0D,cAApD,OAAK,GAAAiB,gBAAA,CAAGI,MAAA,CAAAgC,cAAc,CAACQ,IAAI,CAACN,cAAc,kBAChDvD,mBAAA,CAAwD,cAAlD,OAAK,GAAAiB,gBAAA,CAAGI,MAAA,CAAAgC,cAAc,CAACQ,IAAI,CAACL,YAAY,iB,KAIlDzD,mBAAA,cAAiB,EACjBC,mBAAA,CAkBM,OAlBN8D,WAkBM,GAjBJ9D,mBAAA,CAgBM,OAhBN+D,WAgBM,GAfJ3D,YAAA,CAEUC,kBAAA;IAFDW,IAAI,EAAC,IAAI;IAACrB,KAAK,EAAC;;IA9GnCW,OAAA,EAAAC,QAAA,CA+GY,MAAQ,CAARH,YAAA,CAAQ4D,eAAA,E;IA/GpBvD,CAAA;MAiHUT,mBAAA,CAWM,OAXNiE,WAWM,GAVJjE,mBAAA,CAA0D,KAA1DkE,WAA0D,EAAhC,OAAK,GAAAjD,gBAAA,CAAGI,MAAA,CAAA8C,iBAAiB,kBACnDnE,mBAAA,CAAoD,KAApDoE,WAAoD,EAAAnD,gBAAA,CAApBI,MAAA,CAAAgD,aAAa,kBACZhD,MAAA,CAAAiD,eAAe,Q,cAAhDzE,mBAAA,CAOM,OAPN0E,WAOM,GANJnE,YAAA,CAKEoE,sBAAA;IAJAzD,IAAI,EAAC,QAAQ;IACZ0D,UAAU,EAAEpD,MAAA,CAAAiD,eAAe;IAC3BI,KAAK,EAAE,EAAE;IACT,cAAY,EAAE;+CAzH/B3E,mBAAA,e,OAgIMA,mBAAA,UAAa,EACbC,mBAAA,CAsBM,OAtBN2E,WAsBM,GArBJ3E,mBAAA,CAMM,OANN4E,WAMM,GALJxE,YAAA,CAA4BC,kBAAA;IAnItCC,OAAA,EAAAC,QAAA,CAmImB,MAAS,CAATH,YAAA,CAASyE,gBAAA,E;IAnI5BpE,CAAA;gCAoIUT,mBAAA,CAAsB,cAAhB,WAAS,sBACfI,YAAA,CAESU,iBAAA;IAFAC,IAAI,EAAEM,MAAA,CAAAqB,gBAAgB;IAAU1B,IAAI,EAAC;;IArIxDV,OAAA,EAAAC,QAAA,CAsIY,MAAgC,CAtI5CG,gBAAA,CAAAO,gBAAA,CAsIeI,MAAA,CAAAsB,kBAAkB,yB;IAtIjClC,CAAA;iCAyIQL,YAAA,CASEwC,8BAAA;IARAC,GAAG,EAAC,YAAY;IACf,SAAO,EAAElC,MAAA,CAAAC,MAAM;IACfkC,SAAS,EAAE,MAAM;IACjB,YAAU,EAAEzB,MAAA,CAAAC,SAAS;IACrByB,eAAc,EAAE1B,MAAA,CAAA0B,eAAe;IAC/BC,eAAc,EAAE3B,MAAA,CAAA4B,wBAAwB;IACxCC,OAAK,EAAE7B,MAAA,CAAA8B,gBAAgB;IACxBxD,KAAK,EAAC;uGAERK,mBAAA,CAGM,OAHN8E,WAGM,GAFJ9E,mBAAA,CAA0D,cAApD,OAAK,GAAAiB,gBAAA,CAAGI,MAAA,CAAAgC,cAAc,CAAC0B,IAAI,CAACxB,cAAc,kBAChDvD,mBAAA,CAAwD,cAAlD,OAAK,GAAAiB,gBAAA,CAAGI,MAAA,CAAAgC,cAAc,CAAC0B,IAAI,CAACvB,YAAY,iB,KAIlDzD,mBAAA,UAAa,EACbC,mBAAA,CAsBM,OAtBNgF,WAsBM,GArBJhF,mBAAA,CAMM,OANNiF,WAMM,GALJ7E,YAAA,CAA6BC,kBAAA;IA5JvCC,OAAA,EAAAC,QAAA,CA4JmB,MAAU,CAAVH,YAAA,CAAU8E,iBAAA,E;IA5J7BzE,CAAA;gCA6JUT,mBAAA,CAAuB,cAAjB,YAAU,sBAChBI,YAAA,CAESU,iBAAA;IAFAC,IAAI,EAAEM,MAAA,CAAAqB,gBAAgB;IAAW1B,IAAI,EAAC;;IA9JzDV,OAAA,EAAAC,QAAA,CA+JY,MAAiC,CA/J7CG,gBAAA,CAAAO,gBAAA,CA+JeI,MAAA,CAAAsB,kBAAkB,0B;IA/JjClC,CAAA;iCAkKQL,YAAA,CASEwC,8BAAA;IARAC,GAAG,EAAC,aAAa;IAChB,SAAO,EAAElC,MAAA,CAAAC,MAAM;IACfkC,SAAS,EAAE,OAAO;IAClB,YAAU,EAAEzB,MAAA,CAAAC,SAAS;IACrByB,eAAc,EAAE1B,MAAA,CAAA0B,eAAe;IAC/BC,eAAc,EAAE3B,MAAA,CAAA4B,wBAAwB;IACxCC,OAAK,EAAE7B,MAAA,CAAA8B,gBAAgB;IACxBxD,KAAK,EAAC;uGAERK,mBAAA,CAGM,OAHNmF,WAGM,GAFJnF,mBAAA,CAA2D,cAArD,OAAK,GAAAiB,gBAAA,CAAGI,MAAA,CAAAgC,cAAc,CAAC+B,KAAK,CAAC7B,cAAc,kBACjDvD,mBAAA,CAAyD,cAAnD,OAAK,GAAAiB,gBAAA,CAAGI,MAAA,CAAAgC,cAAc,CAAC+B,KAAK,CAAC5B,YAAY,iB,sBAKrDzD,mBAAA,YAAe,EACkBsB,MAAA,CAAAK,YAAY,I,cAA7C7B,mBAAA,CAmBM,OAnBNwF,WAmBM,GAlBJrF,mBAAA,CAiBM,OAjBNsF,WAiBM,GAhBJlF,YAAA,CAEcmF,sBAAA;IAxLtBC,UAAA,EAsL8BnE,MAAA,CAAAoE,UAAU;IAtLxC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAsL8BtE,MAAA,CAAAoE,UAAU,GAAAE,MAAA;IAAGC,QAAM,EAAEvE,MAAA,CAAAwE;;IAtLnDvF,OAAA,EAAAC,QAAA,CAsLiE,MAEzDmF,MAAA,QAAAA,MAAA,OAxLRhF,gBAAA,CAsLiE,aAEzD,E;IAxLRD,CAAA;iDAyLQL,YAAA,CAYY0F,oBAAA;IArMpBN,UAAA,EA0LmBnE,MAAA,CAAA0E,aAAa;IA1LhC,uBAAAL,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA0LmBtE,MAAA,CAAA0E,aAAa,GAAAJ,MAAA;IACrBK,GAAG,EAAE,GAAG;IACRC,GAAG,EAAE,IAAI;IACTC,IAAI,EAAE,GAAG;IACV,YAAU,EAAV,EAAU;IACT,qBAAmB,EAAE,KAAK;IAC3BvG,KAAK,EAAC;;IAEKwG,KAAK,EAAA5F,QAAA,CAAC,MACJ,CAnMvBG,gBAAA,CAkM2B,cACJ,GAAAO,gBAAA,CAAGI,MAAA,CAAA0E,aAAa,iB;IAnMvCtF,CAAA;2CAAAV,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}