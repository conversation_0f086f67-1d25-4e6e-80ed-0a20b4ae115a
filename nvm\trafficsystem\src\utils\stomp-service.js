import SockJS from 'sockjs-client';
import { Client } from '@stomp/stompjs';
import { SOCKJS_ENDPOINT, STOMP_TOPIC_PREFIX } from '@/config';

/**
 * STOMP客户端服务
 * 用于管理WebSocket连接和消息订阅
 */
class StompService {
  constructor() {
    this.client = null;
    this.subscriptions = new Map();
    this.connected = false;
    this.connectionPromise = null;
    this.connectAttempts = 0; // 连接尝试计数
    this.debug = true; // 启用详细调试

    // 帧数据管理
    this.frameSubscriptions = new Map(); // 帧数据订阅
    this.frameBuffers = new Map(); // 任务帧缓冲区
    this.maxFrameBufferSize = 50; // 每个任务最大缓冲帧数

    // 网络性能监控
    this.networkStats = {
      frameReceiveRate: 0, // 帧接收速率（帧/秒）
      averageFrameSize: 0, // 平均帧大小（KB）
      connectionQuality: 'good', // 连接质量：good, fair, poor
      lastReceiveTime: 0,
      frameCount: 0,
      totalFrameSize: 0
    };
  }

  /**
   * 记录调试信息
   * @param {string} message - 调试消息
   * @param {Object} data - 附加数据
   */
  log(message, data = null) {
    // 生产环境下静默处理
  }

  /**
   * 记录错误信息
   * @param {string} message - 错误消息
   * @param {Error} error - 错误对象
   */
  logError(message, error = null) {
    // 生产环境下静默处理
  }

  /**
   * 初始化STOMP客户端
   * @returns {Promise} 连接成功的Promise
   */
  init() {
    if (this.connectionPromise) {
      this.log('复用现有连接Promise');
      return this.connectionPromise;
    }

    this.connectAttempts++;
    this.log(`开始初始化STOMP客户端 (尝试 #${this.connectAttempts})`);
    this.log(`连接端点: ${SOCKJS_ENDPOINT}`);

    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        // 创建SockJS连接
        this.log(`尝试创建SockJS连接到 ${SOCKJS_ENDPOINT}`);
        const socket = new SockJS(SOCKJS_ENDPOINT);
        
        socket.onopen = () => {
          this.log('SockJS连接已打开');
        };
        
        socket.onclose = (event) => {
          this.logError(`SockJS连接已关闭，code: ${event.code}, reason: ${event.reason}`);
        };
        
        socket.onerror = (error) => {
          this.logError('SockJS连接错误', error);
        };
        
        // 创建STOMP客户端
        this.log('创建STOMP客户端');
        this.client = new Client({
          webSocketFactory: () => socket,
          debug: (str) => {
            if (this.debug) {
              console.debug('[STOMP DEBUG] ' + str);
            }
          },
          reconnectDelay: 5000, // 5秒重连
          heartbeatIncoming: 4000,
          heartbeatOutgoing: 4000
        });

        // 连接成功回调
        this.client.onConnect = (frame) => {
          this.log('STOMP连接已建立', frame);
          this.connected = true;
          this.connectAttempts = 0; // 重置连接尝试计数
          resolve(true);
        };

        // 连接错误回调
        this.client.onStompError = (frame) => {
          this.logError('STOMP协议错误', frame);
          reject(new Error('STOMP协议错误: ' + frame.headers.message));
        };

        // 连接断开回调
        this.client.onDisconnect = () => {
          this.log('STOMP连接已断开');
          this.connected = false;
          this.connectionPromise = null;
        };
        
        // WebSocket错误回调
        this.client.onWebSocketError = (event) => {
          this.logError('WebSocket错误', event);
        };
        
        // WebSocket关闭回调
        this.client.onWebSocketClose = (event) => {
          this.logError(`WebSocket连接关闭: code=${event.code}, reason=${event.reason}`, event);
        };

        // 激活连接
        this.log('激活STOMP客户端连接');
        this.client.activate();
      } catch (error) {
        this.logError('STOMP初始化过程中发生错误', error);
        this.connectionPromise = null;
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  /**
   * 订阅主题
   * @param {string} topic - 主题名称，不含前缀
   * @param {Function} callback - 收到消息的回调函数
   * @returns {Promise<Object>} 订阅对象
   */
  async subscribe(topic, callback) {
    this.log(`尝试订阅主题: ${topic}`);
    
    if (!this.connected) {
      this.log('STOMP客户端未连接，尝试初始化连接');
      try {
        await this.init();
      } catch (error) {
        this.logError('订阅前初始化STOMP客户端失败', error);
        throw error;
      }
    }

    const fullTopic = `${STOMP_TOPIC_PREFIX}/${topic}`;
    this.log(`完整主题路径: ${fullTopic}`);
    
    if (this.subscriptions.has(fullTopic)) {
      this.log(`已存在对 ${fullTopic} 的订阅，复用现有订阅`);
      return this.subscriptions.get(fullTopic);
    }

    try {
      this.log(`订阅主题: ${fullTopic}`);
      const subscription = this.client.subscribe(fullTopic, (message) => {
        try {
          this.log(`收到主题 ${fullTopic} 的消息`);
          const data = JSON.parse(message.body);
          callback(data);
        } catch (error) {
          this.logError(`解析 ${fullTopic} 的消息失败`, error);
        }
      });

      this.subscriptions.set(fullTopic, subscription);
      this.log(`订阅成功: ${fullTopic}, ID: ${subscription.id}`);
      return subscription;
    } catch (error) {
      this.logError(`订阅 ${fullTopic} 失败`, error);
      throw error;
    }
  }

  /**
   * 取消订阅
   * @param {string} topic - 主题名称，不含前缀
   */
  unsubscribe(topic) {
    const fullTopic = `${STOMP_TOPIC_PREFIX}/${topic}`;
    this.log(`尝试取消订阅: ${fullTopic}`);
    
    if (this.subscriptions.has(fullTopic)) {
      try {
        const subscription = this.subscriptions.get(fullTopic);
        this.log(`找到订阅，ID: ${subscription.id}，开始取消订阅`);
        subscription.unsubscribe();
        this.subscriptions.delete(fullTopic);
        this.log(`已成功取消订阅主题: ${fullTopic}`);
      } catch (error) {
        this.logError(`取消订阅 ${fullTopic} 失败`, error);
        // 即使发生错误，也移除订阅记录
        this.subscriptions.delete(fullTopic);
      }
    } else {
      this.log(`未找到对 ${fullTopic} 的订阅记录，无需取消`);
    }
  }

  /**
   * 订阅任务的实时帧数据
   * @param {string} taskId - 任务ID
   * @param {Function} frameCallback - 接收帧数据的回调函数
   * @returns {Promise<Object>} 订阅对象
   */
  async subscribeFrameUpdates(taskId, frameCallback) {
    this.log(`订阅任务 ${taskId} 的实时帧数据`);

    // 确保主题路径与后端一致
    const frameTopic = `frame-updates/${taskId}`;

    try {
      // 确保WebSocket连接已建立，如果断开则重连
      await this.ensureConnection();

      // 初始化任务的帧缓冲区
      if (!this.frameBuffers.has(taskId)) {
        this.frameBuffers.set(taskId, []);
      }

      this.log(`准备订阅帧更新主题: ${frameTopic}`);

      // 订阅帧更新主题
      const subscription = await this.subscribe(frameTopic, (frameData) => {
        this.log(`收到帧数据: 任务${taskId}, 帧${frameData.frameNumber || 'unknown'}`);
        this.handleFrameData(taskId, frameData, frameCallback);
      });

      // 记录帧订阅
      this.frameSubscriptions.set(taskId, subscription);

      this.log(`✓ 成功订阅任务 ${taskId} 的帧数据，主题: ${frameTopic}`);
      return subscription;

    } catch (error) {
      this.logError(`✗ 订阅任务 ${taskId} 的帧数据失败`, error);

      // 如果是连接问题，尝试重连后再次订阅
      if (error.message && error.message.includes('STOMP connection')) {
        this.log('检测到连接问题，尝试重连...');
        try {
          await this.reconnect();
          return await this.subscribeFrameUpdates(taskId, frameCallback);
        } catch (reconnectError) {
          this.logError('重连失败', reconnectError);
          throw reconnectError;
        }
      }

      throw error;
    }
  }

  /**
   * 订阅任务的进度更新
   * @param {string} taskId - 任务ID
   * @param {Function} progressCallback - 接收进度数据的回调函数
   * @returns {Promise<Object>} 订阅对象
   */
  async subscribeProgressUpdates(taskId, progressCallback) {
    this.log(`订阅任务 ${taskId} 的进度更新`);

    // 确保主题路径与后端一致
    const progressTopic = `video-progress/${taskId}`;

    try {
      // 确保WebSocket连接已建立
      await this.ensureConnection();

      this.log(`准备订阅进度更新主题: ${progressTopic}`);

      // 订阅进度更新主题
      const subscription = await this.subscribe(progressTopic, (progressData) => {
        this.log(`收到进度数据: 任务${taskId}, 进度${progressData.progress || 'unknown'}%`);
        if (typeof progressCallback === 'function') {
          progressCallback(progressData);
        }
      });

      // 记录进度订阅
      const subscriptionKey = `progress_${taskId}`;
      this.frameSubscriptions.set(subscriptionKey, {
        subscription,
        taskId,
        type: 'progress'
      });

      this.log(`✓ 成功订阅任务 ${taskId} 的进度更新，主题: ${progressTopic}`);
      return subscription;

    } catch (error) {
      this.logError(`✗ 订阅任务 ${taskId} 的进度更新失败`, error);
      throw error;
    }
  }

  /**
   * 处理接收到的帧数据
   * @param {string} taskId - 任务ID
   * @param {Object} frameData - 帧数据
   * @param {Function} callback - 回调函数
   */
  handleFrameData(taskId, frameData, callback) {
    try {
      this.log(`处理任务 ${taskId} 的帧数据: 帧${frameData.frameNumber}`);

      // 更新网络性能统计
      this.updateNetworkStats(frameData);

      // 验证帧数据
      if (!this.validateFrameData(frameData)) {
        this.logError(`任务 ${taskId} 的帧数据格式无效`, frameData);
        return;
      }

      // 添加到缓冲区
      this.addFrameToBuffer(taskId, frameData);

      // 调用回调函数
      if (typeof callback === 'function') {
        callback(frameData);
      }

    } catch (error) {
      this.logError(`处理任务 ${taskId} 的帧数据失败`, error);
    }
  }

  /**
   * 更新网络性能统计
   * @param {Object} frameData - 帧数据
   */
  updateNetworkStats(frameData) {
    try {
      const now = Date.now();

      // 计算帧大小（估算）
      const frameSize = frameData.imageData ? frameData.imageData.length * 0.75 / 1024 : 0; // Base64转字节再转KB

      // 更新统计
      this.networkStats.frameCount++;
      this.networkStats.totalFrameSize += frameSize;
      this.networkStats.averageFrameSize = this.networkStats.totalFrameSize / this.networkStats.frameCount;

      // 计算接收速率
      if (this.networkStats.lastReceiveTime > 0) {
        const timeDiff = (now - this.networkStats.lastReceiveTime) / 1000; // 转换为秒
        if (timeDiff > 0) {
          // 使用指数移动平均
          const alpha = 0.3;
          const currentRate = 1 / timeDiff;
          this.networkStats.frameReceiveRate =
            alpha * currentRate + (1 - alpha) * this.networkStats.frameReceiveRate;
        }
      }

      this.networkStats.lastReceiveTime = now;

      // 评估连接质量
      this.evaluateConnectionQuality();

      this.log(`网络统计更新: 速率=${this.networkStats.frameReceiveRate.toFixed(2)}fps, ` +
               `平均大小=${this.networkStats.averageFrameSize.toFixed(1)}KB, ` +
               `质量=${this.networkStats.connectionQuality}`);

    } catch (error) {
      this.logError('更新网络统计失败', error);
    }
  }

  /**
   * 评估连接质量
   */
  evaluateConnectionQuality() {
    const { frameReceiveRate, averageFrameSize } = this.networkStats;

    // 根据帧接收速率和平均帧大小评估质量
    if (frameReceiveRate >= 0.8 && averageFrameSize < 200) {
      this.networkStats.connectionQuality = 'good';
    } else if (frameReceiveRate >= 0.5 && averageFrameSize < 300) {
      this.networkStats.connectionQuality = 'fair';
    } else {
      this.networkStats.connectionQuality = 'poor';
    }
  }

  /**
   * 获取网络性能统计
   * @returns {Object} 网络统计信息
   */
  getNetworkStats() {
    return { ...this.networkStats };
  }

  /**
   * 重置网络统计
   */
  resetNetworkStats() {
    this.networkStats = {
      frameReceiveRate: 0,
      averageFrameSize: 0,
      connectionQuality: 'good',
      lastReceiveTime: 0,
      frameCount: 0,
      totalFrameSize: 0
    };
    this.log('网络统计已重置');
  }

  /**
   * 验证帧数据格式
   * @param {Object} frameData - 帧数据
   * @returns {boolean} 是否有效
   */
  validateFrameData(frameData) {
    return frameData &&
           typeof frameData.taskId === 'string' &&
           typeof frameData.frameNumber === 'number' &&
           typeof frameData.imageData === 'string' &&
           frameData.type === 'frame_update';
  }

  /**
   * 添加帧到缓冲区
   * @param {string} taskId - 任务ID
   * @param {Object} frameData - 帧数据
   */
  addFrameToBuffer(taskId, frameData) {
    try {
      let buffer = this.frameBuffers.get(taskId);
      if (!buffer) {
        buffer = [];
        this.frameBuffers.set(taskId, buffer);
      }

      // 添加帧数据
      buffer.push({
        ...frameData,
        receivedAt: Date.now()
      });

      // 限制缓冲区大小
      while (buffer.length > this.maxFrameBufferSize) {
        buffer.shift(); // 移除最旧的帧
      }

      this.log(`任务 ${taskId} 帧缓冲区大小: ${buffer.length}`);

    } catch (error) {
      this.logError(`添加帧到缓冲区失败`, error);
    }
  }

  /**
   * 获取任务的缓冲帧数据
   * @param {string} taskId - 任务ID
   * @returns {Array} 帧数据数组
   */
  getFrameBuffer(taskId) {
    return this.frameBuffers.get(taskId) || [];
  }

  /**
   * 清理任务的帧数据
   * @param {string} taskId - 任务ID
   */
  clearFrameBuffer(taskId) {
    this.log(`清理任务 ${taskId} 的帧缓冲区`);

    // 取消帧订阅
    if (this.frameSubscriptions.has(taskId)) {
      try {
        const frameTopic = `frame-updates/${taskId}`;
        this.unsubscribe(frameTopic);
        this.frameSubscriptions.delete(taskId);
      } catch (error) {
        this.logError(`取消任务 ${taskId} 的帧订阅失败`, error);
      }
    }

    // 取消进度订阅
    const progressSubscriptionKey = `progress_${taskId}`;
    if (this.frameSubscriptions.has(progressSubscriptionKey)) {
      try {
        const progressTopic = `video-progress/${taskId}`;
        this.unsubscribe(progressTopic);
        this.frameSubscriptions.delete(progressSubscriptionKey);
      } catch (error) {
        this.logError(`取消任务 ${taskId} 的进度订阅失败`, error);
      }
    }

    // 清理帧缓冲区
    this.frameBuffers.delete(taskId);
  }

  /**
   * 获取帧数据统计信息
   * @param {string} taskId - 任务ID
   * @returns {Object} 统计信息
   */
  getFrameStats(taskId) {
    const buffer = this.frameBuffers.get(taskId) || [];
    const hasSubscription = this.frameSubscriptions.has(taskId);

    return {
      taskId,
      frameCount: buffer.length,
      hasSubscription,
      latestFrame: buffer.length > 0 ? buffer[buffer.length - 1] : null,
      oldestFrame: buffer.length > 0 ? buffer[0] : null
    };
  }

  /**
   * 确保WebSocket连接已建立
   */
  async ensureConnection() {
    if (!this.connected || !this.client || !this.client.connected) {
      this.log('连接已断开，尝试重新连接...');
      await this.reconnect();
    }
  }

  /**
   * 重新连接WebSocket
   */
  async reconnect() {
    this.log('开始重新连接WebSocket...');

    try {
      // 先断开现有连接
      if (this.client) {
        try {
          this.client.deactivate();
        } catch (e) {
          // 忽略断开连接时的错误
        }
      }

      // 重置状态
      this.connected = false;
      this.connectionPromise = null;
      this.connectAttempts = 0;

      // 重新初始化连接
      await this.init();

      this.log('✓ WebSocket重连成功');

    } catch (error) {
      this.logError('WebSocket重连失败', error);
      throw error;
    }
  }

  // ==================== 四方向交通分析WebSocket支持 ====================

  /**
   * 订阅四方向任务的实时帧数据
   * @param {string} taskId - 任务ID
   * @param {Function} frameCallback - 接收帧数据的回调函数
   * @param {Function} progressCallback - 接收进度数据的回调函数
   * @param {Function} completeCallback - 接收整体完成消息的回调函数
   * @returns {Promise<Object>} 订阅对象
   */
  async subscribeFourWayFrameUpdates(taskId, frameCallback, progressCallback, completeCallback) {
    this.log(`订阅四方向任务 ${taskId} 的实时帧数据和进度`);

    try {
      // 确保WebSocket连接已建立
      await this.ensureConnection();

      // 初始化四方向任务的帧缓冲区
      const directions = ['east', 'south', 'west', 'north'];
      for (const direction of directions) {
        const directionTaskId = `${direction}_${taskId}`;
        if (!this.frameBuffers.has(directionTaskId)) {
          this.frameBuffers.set(directionTaskId, []);
        }
      }

      // 订阅四方向帧更新主题
      const framesTopic = `four-way-frames/${taskId}`;
      this.log(`准备订阅四方向帧更新主题: ${framesTopic}`);

      const framesSubscription = await this.subscribe(framesTopic, (frameData) => {
        this.log(`收到四方向帧数据: 任务${taskId}, 方向${frameData.direction}, 帧${frameData.frameNumber || 'unknown'}`);
        this.handleFourWayFrameData(taskId, frameData, frameCallback);
      });

      // 订阅四方向进度更新主题
      const progressTopic = `four-way-progress/${taskId}`;
      const progressSubscription = await this.subscribe(progressTopic, (progressData) => {
        this.log(`收到四方向进度数据: 任务${taskId}, 类型${progressData.type}, 方向${progressData.direction || 'all'}`);

        // 检查是否为整体完成消息
        if (progressData.type === 'four_way_analysis_complete') {
          this.log(`✓ 收到四方向整体分析完成消息: 任务${taskId}`);
          if (typeof completeCallback === 'function') {
            completeCallback(progressData);
          }
        } else if (typeof progressCallback === 'function') {
          progressCallback(progressData);
        }
      });

      // 订阅四方向整体完成主题
      const completeTopic = `four-way-analysis-complete/${taskId}`;
      const completeSubscription = await this.subscribe(completeTopic, (completeData) => {
        this.log(`✓ 收到四方向整体分析完成消息: 任务${taskId}, 总车辆数${completeData.summary?.totalVehicles || 'unknown'}`);
        if (typeof completeCallback === 'function') {
          completeCallback(completeData);
        }
      });

      // 记录四方向订阅
      const subscriptionKey = `four_way_${taskId}`;
      this.frameSubscriptions.set(subscriptionKey, {
        frames: framesSubscription,
        progress: progressSubscription,
        complete: completeSubscription,
        taskId,
        type: 'four_way'
      });

      this.log(`✓ 成功订阅四方向任务 ${taskId} 的帧数据、进度和完成消息`);
      return {
        frames: framesSubscription,
        progress: progressSubscription,
        complete: completeSubscription
      };

    } catch (error) {
      this.logError(`✗ 订阅四方向任务 ${taskId} 的数据失败`, error);
      throw error;
    }
  }

  /**
   * 订阅特定方向的帧数据
   * @param {string} taskId - 任务ID
   * @param {string} direction - 方向（east, south, west, north）
   * @param {Function} frameCallback - 接收帧数据的回调函数
   * @returns {Promise<Object>} 订阅对象
   */
  async subscribeDirectionFrameUpdates(taskId, direction, frameCallback) {
    this.log(`订阅任务 ${taskId} 的 ${direction} 方向帧数据`);

    try {
      // 确保WebSocket连接已建立
      await this.ensureConnection();

      // 初始化方向帧缓冲区
      const directionTaskId = `${direction}_${taskId}`;
      if (!this.frameBuffers.has(directionTaskId)) {
        this.frameBuffers.set(directionTaskId, []);
      }

      // 订阅特定方向的帧更新主题
      const directionTopic = `four-way-frames/${taskId}/${direction}`;
      const subscription = await this.subscribe(directionTopic, (frameData) => {
        this.log(`收到${direction}方向帧数据: 任务${taskId}, 帧${frameData.frameNumber || 'unknown'}`);
        this.handleFourWayFrameData(taskId, frameData, frameCallback);
      });

      // 记录方向订阅
      const subscriptionKey = `direction_${direction}_${taskId}`;
      this.frameSubscriptions.set(subscriptionKey, {
        subscription,
        taskId,
        direction,
        type: 'direction'
      });

      this.log(`✓ 成功订阅任务 ${taskId} 的 ${direction} 方向帧数据`);
      return subscription;

    } catch (error) {
      this.logError(`✗ 订阅任务 ${taskId} 的 ${direction} 方向帧数据失败`, error);
      throw error;
    }
  }

  /**
   * 处理接收到的四方向帧数据
   * @param {string} taskId - 任务ID
   * @param {Object} frameData - 帧数据
   * @param {Function} callback - 回调函数
   */
  handleFourWayFrameData(taskId, frameData, callback) {
    try {
      this.log(`处理四方向任务 ${taskId} 的帧数据: 方向${frameData.direction}, 帧${frameData.frameNumber}`);

      // 更新网络性能统计
      this.updateNetworkStats(frameData);

      // 验证四方向帧数据
      if (!this.validateFourWayFrameData(frameData)) {
        this.logError(`任务 ${taskId} 的四方向帧数据格式无效`, frameData);
        return;
      }

      // 添加到方向缓冲区
      const directionTaskId = `${frameData.direction}_${taskId}`;
      this.addFrameToBuffer(directionTaskId, frameData);

      // 调用回调函数
      if (typeof callback === 'function') {
        callback(frameData);
      }

    } catch (error) {
      this.logError(`处理四方向任务 ${taskId} 的帧数据失败`, error);
    }
  }

  /**
   * 验证四方向帧数据格式
   * @param {Object} frameData - 帧数据
   * @returns {boolean} 是否有效
   */
  validateFourWayFrameData(frameData) {
    return frameData &&
           typeof frameData.taskId === 'string' &&
           typeof frameData.direction === 'string' &&
           typeof frameData.frameNumber === 'number' &&
           typeof frameData.imageData === 'string' &&
           frameData.type === 'four_way_frame_update' &&
           ['east', 'south', 'west', 'north'].includes(frameData.direction);
  }

  /**
   * 获取四方向任务的帧数据统计
   * @param {string} taskId - 任务ID
   * @returns {Object} 统计信息
   */
  getFourWayFrameStats(taskId) {
    const directions = ['east', 'south', 'west', 'north'];
    const stats = {
      taskId,
      directions: {},
      totalFrames: 0,
      hasSubscription: this.frameSubscriptions.has(`four_way_${taskId}`)
    };

    for (const direction of directions) {
      const directionTaskId = `${direction}_${taskId}`;
      const buffer = this.frameBuffers.get(directionTaskId) || [];

      stats.directions[direction] = {
        frameCount: buffer.length,
        latestFrame: buffer.length > 0 ? buffer[buffer.length - 1] : null,
        oldestFrame: buffer.length > 0 ? buffer[0] : null
      };

      stats.totalFrames += buffer.length;
    }

    return stats;
  }

  /**
   * 清理四方向任务的帧数据
   * @param {string} taskId - 任务ID
   */
  clearFourWayFrameBuffer(taskId) {
    this.log(`清理四方向任务 ${taskId} 的帧缓冲区`);

    try {
      // 取消四方向订阅
      const fourWaySubscriptionKey = `four_way_${taskId}`;
      if (this.frameSubscriptions.has(fourWaySubscriptionKey)) {
        const subscriptions = this.frameSubscriptions.get(fourWaySubscriptionKey);

        // 取消帧订阅
        if (subscriptions.frames) {
          const framesTopic = `four-way-frames/${taskId}`;
          this.unsubscribe(framesTopic);
        }

        // 取消进度订阅
        if (subscriptions.progress) {
          const progressTopic = `four-way-progress/${taskId}`;
          this.unsubscribe(progressTopic);
        }

        // 取消完成订阅
        if (subscriptions.complete) {
          const completeTopic = `four-way-analysis-complete/${taskId}`;
          this.unsubscribe(completeTopic);
        }

        this.frameSubscriptions.delete(fourWaySubscriptionKey);
      }

      // 取消各方向订阅
      const directions = ['east', 'south', 'west', 'north'];
      for (const direction of directions) {
        const directionSubscriptionKey = `direction_${direction}_${taskId}`;
        if (this.frameSubscriptions.has(directionSubscriptionKey)) {
          const directionTopic = `four-way-frames/${taskId}/${direction}`;
          this.unsubscribe(directionTopic);
          this.frameSubscriptions.delete(directionSubscriptionKey);
        }

        // 清理方向帧缓冲区
        const directionTaskId = `${direction}_${taskId}`;
        this.frameBuffers.delete(directionTaskId);
      }

      this.log(`✓ 已清理四方向任务 ${taskId} 的所有帧数据`);

    } catch (error) {
      this.logError(`清理四方向任务 ${taskId} 的帧数据失败`, error);
    }
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.client && this.connected) {
      this.log('开始断开STOMP客户端连接');

      try {
        // 清理所有帧数据
        this.log(`清理所有帧缓冲区，当前任务数: ${this.frameBuffers.size}`);
        this.frameBuffers.clear();
        this.frameSubscriptions.clear();

        // 取消所有订阅
        this.log(`取消所有订阅，当前订阅数: ${this.subscriptions.size}`);
        this.subscriptions.forEach((subscription, topic) => {
          try {
            this.log(`取消订阅: ${topic}, ID: ${subscription.id}`);
            subscription.unsubscribe();
          } catch (error) {
            this.logError(`取消订阅 ${topic} 失败`, error);
          }
        });
        this.subscriptions.clear();
        this.log('所有订阅已清除');

        // 断开连接
        this.log('执行STOMP客户端断开连接');
        this.client.deactivate();
        this.connected = false;
        this.connectionPromise = null;
        this.log('STOMP客户端已成功断开连接');
      } catch (error) {
        this.logError('断开STOMP连接过程中发生错误', error);
        // 重置状态
        this.connected = false;
        this.connectionPromise = null;
        this.frameBuffers.clear();
        this.frameSubscriptions.clear();
      }
    } else {
      this.log('STOMP客户端未连接或不存在，无需断开');
    }
  }
}

// 导出单例实例
export default new StompService(); 