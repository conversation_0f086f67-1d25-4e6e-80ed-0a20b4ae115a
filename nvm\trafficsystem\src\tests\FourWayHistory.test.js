import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import FourWayHistory from '../views/FourWayHistory.vue'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  }
}))

// Mock fetch API
global.fetch = vi.fn()

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}
global.localStorage = localStorageMock

// Mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/four-way-history', component: FourWayHistory },
    { path: '/four-way-result/:taskId', component: { template: '<div>Result</div>' } },
    { path: '/four-way-report/:taskId', component: { template: '<div>Report</div>' } },
    { path: '/four-way-console', component: { template: '<div>Console</div>' } }
  ]
})

describe('FourWayHistory.vue', () => {
  let wrapper

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    
    // Mock localStorage to return auth token
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'auth_token') return 'mock-token'
      if (key === 'user') return JSON.stringify({ role: 'user' })
      return null
    })

    // Mock successful API response
    global.fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        content: [],
        totalElements: 0,
        totalPages: 0,
        currentPage: 0,
        pageSize: 10,
        hasNext: false,
        hasPrevious: false
      })
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('renders correctly', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    // Check if main elements are rendered
    expect(wrapper.find('.four-way-history-container').exists()).toBe(true)
    expect(wrapper.find('.history-card').exists()).toBe(true)
    expect(wrapper.find('h2').text()).toBe('四方向智能交通分析历史')
  })

  it('displays filter panel', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    // Check filter panel elements
    expect(wrapper.find('.filter-panel').exists()).toBe(true)
    expect(wrapper.find('.search-input').exists()).toBe(true)
    expect(wrapper.find('.date-picker').exists()).toBe(true)
  })

  it('calls API on mount', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // Verify API was called
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/video-analysis/four-way/tasks'),
      expect.objectContaining({
        headers: expect.objectContaining({
          'Authorization': 'Bearer mock-token'
        })
      })
    )
  })

  it('handles search functionality', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    // Find search input and enter text
    const searchInput = wrapper.find('input[placeholder="搜索任务名称"]')
    expect(searchInput.exists()).toBe(true)

    await searchInput.setValue('test search')
    await searchInput.trigger('keyup.enter')

    // Verify search triggered API call
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('search=test%20search'),
      expect.any(Object)
    )
  })

  it('handles pagination', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    // Test pagination methods
    const vm = wrapper.vm
    
    // Test page size change
    await vm.handleSizeChange(20)
    expect(vm.pageSize).toBe(20)
    expect(vm.currentPage).toBe(1)

    // Test page change
    await vm.handleCurrentChange(2)
    expect(vm.currentPage).toBe(2)
  })

  it('handles status filter', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    const vm = wrapper.vm
    
    // Set status filter
    vm.statusFilter = 'completed'
    await vm.handleSearch()

    // Verify API called with status filter
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('status=completed'),
      expect.any(Object)
    )
  })

  it('handles date range filter', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    const vm = wrapper.vm
    
    // Set date range
    vm.dateRange = ['2024-01-01', '2024-12-31']
    await vm.handleSearch()

    // Verify API called with date range
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('startDate=2024-01-01'),
      expect.any(Object)
    )
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('endDate=2024-12-31'),
      expect.any(Object)
    )
  })

  it('handles sorting', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()

    const vm = wrapper.vm
    
    // Test sort change
    await vm.handleSortChange({ prop: 'status', order: 'ascending' })
    
    expect(vm.sortProp).toBe('status')
    expect(vm.sortOrder).toBe('ascending')
  })

  it('handles error states', async () => {
    // Mock API error
    global.fetch.mockRejectedValue(new Error('API Error'))

    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // Check error handling
    expect(wrapper.vm.error).toBeTruthy()
    expect(ElMessage.error).toHaveBeenCalled()
  })

  it('handles empty state', async () => {
    // Mock empty response
    global.fetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        content: [],
        totalElements: 0,
        totalPages: 0,
        currentPage: 0,
        pageSize: 10,
        hasNext: false,
        hasPrevious: false
      })
    })

    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // Check empty state
    expect(wrapper.find('.empty-container').exists()).toBe(true)
  })

  it('formats dates correctly', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    const vm = wrapper.vm
    
    // Test date formatting
    const testDate = '2024-01-15T10:30:00Z'
    const formatted = vm.formatDate(testDate)
    
    expect(formatted).toMatch(/2024/)
    expect(formatted).toMatch(/01/)
    expect(formatted).toMatch(/15/)
  })

  it('handles status type mapping', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    const vm = wrapper.vm
    
    // Test status type mapping
    expect(vm.getStatusType('completed')).toBe('success')
    expect(vm.getStatusType('processing')).toBe('warning')
    expect(vm.getStatusType('queued')).toBe('info')
    expect(vm.getStatusType('failed')).toBe('danger')
  })

  it('handles status text mapping', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    const vm = wrapper.vm
    
    // Test status text mapping
    expect(vm.getStatusText('completed')).toBe('已完成')
    expect(vm.getStatusText('processing')).toBe('处理中')
    expect(vm.getStatusText('queued')).toBe('排队中')
    expect(vm.getStatusText('failed')).toBe('失败')
  })

  it('calculates total vehicles correctly', async () => {
    wrapper = mount(FourWayHistory, {
      global: {
        plugins: [router]
      }
    })

    const vm = wrapper.vm
    
    // Test vehicle count calculation
    const mockTask = {
      directions: {
        EAST: { vehicleCount: 10 },
        SOUTH: { vehicleCount: 15 },
        WEST: { vehicleCount: 8 },
        NORTH: { vehicleCount: 12 }
      }
    }
    
    expect(vm.getTotalVehicles(mockTask)).toBe(45)
  })
})
