{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Connection, Grid, Top, Bottom, ArrowLeft as Back, Right } from '@element-plus/icons-vue';\nimport RealTimeFrameViewer from '@/components/video/RealTimeFrameViewer.vue';\nimport stompService from '@/utils/stomp-service';\nexport default {\n  name: 'FourWayRealtimeViewer',\n  components: {\n    Connection,\n    Grid,\n    Top,\n    Bottom,\n    Back,\n    Right,\n    RealTimeFrameViewer\n  },\n  props: {\n    taskId: {\n      type: String,\n      required: true\n    },\n    autoStart: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['detection-update', 'status-change'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const isConnected = ref(false);\n    const lastUpdateTime = ref('');\n    const frameSubscription = ref(null);\n    const showProgress = ref(true);\n\n    // 方向查看器引用\n    const northViewer = ref(null);\n    const southViewer = ref(null);\n    const eastViewer = ref(null);\n    const westViewer = ref(null);\n\n    // 方向统计数据\n    const directionStats = reactive({\n      north: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null\n      },\n      south: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null\n      },\n      east: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null\n      },\n      west: {\n        vehicleCount: 0,\n        frameRate: '0 fps',\n        status: 'waiting',\n        lastUpdate: null\n      }\n    });\n\n    // 方向帧数据\n    const directionFrameData = reactive({\n      north: null,\n      south: null,\n      east: null,\n      west: null\n    });\n\n    // 计算属性\n    const connectionStatusType = computed(() => {\n      return isConnected.value ? 'success' : 'danger';\n    });\n    const connectionStatusText = computed(() => {\n      return isConnected.value ? '已连接' : '未连接';\n    });\n    const totalVehicleCount = computed(() => {\n      return Object.values(directionStats).reduce((total, stats) => total + stats.vehicleCount, 0);\n    });\n    const overallProgress = computed(() => {\n      const directions = Object.values(directionStats);\n      const activeDirections = directions.filter(d => d.status !== 'waiting');\n      if (activeDirections.length === 0) return 0;\n      const completedDirections = directions.filter(d => d.status === 'completed');\n      return Math.round(completedDirections.length / 4 * 100);\n    });\n    const progressStatus = computed(() => {\n      if (overallProgress.value === 100) return 'success';\n      if (overallProgress.value > 0) return '';\n      return 'exception';\n    });\n\n    // 方法\n    const getDirectionTaskId = direction => {\n      return `${direction}_${props.taskId}`;\n    };\n    const getDirectionStatusType = direction => {\n      const status = directionStats[direction].status;\n      const typeMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'error': 'danger'\n      };\n      return typeMap[status] || 'info';\n    };\n    const getDirectionStatusText = direction => {\n      const status = directionStats[direction].status;\n      const textMap = {\n        'waiting': '等待中',\n        'processing': '检测中',\n        'completed': '已完成',\n        'error': '检测失败'\n      };\n      return textMap[status] || '未知';\n    };\n    const handleFrameReceived = (direction, frameData) => {\n      console.log(`🎬 FourWayRealtimeViewer收到${direction}方向帧数据:`, {\n        frameNumber: frameData.frameNumber,\n        detectionCount: frameData.detectionCount,\n        hasImageData: !!frameData.imageData,\n        imageDataLength: frameData.imageData ? frameData.imageData.length : 0,\n        imageDataPrefix: frameData.imageData ? frameData.imageData.substring(0, 50) + '...' : 'null'\n      });\n\n      // 更新方向统计\n      if (frameData.detectionCount !== undefined) {\n        directionStats[direction].vehicleCount += frameData.detectionCount;\n      }\n      directionStats[direction].status = 'processing';\n      directionStats[direction].lastUpdate = new Date();\n\n      // 更新最后更新时间\n      lastUpdateTime.value = new Date().toLocaleTimeString();\n\n      // 计算帧率（简化版本）\n      const now = Date.now();\n      if (directionStats[direction].lastFrameTime) {\n        const interval = now - directionStats[direction].lastFrameTime;\n        const fps = Math.round(1000 / interval * 10) / 10;\n        directionStats[direction].frameRate = `${fps} fps`;\n      }\n      directionStats[direction].lastFrameTime = now;\n\n      // 更新方向帧数据，这将触发RealTimeFrameViewer组件的响应式更新\n      directionFrameData[direction] = {\n        ...frameData\n      };\n      console.log(`✅ ${direction}方向帧数据已更新到directionFrameData`);\n\n      // 发出检测更新事件\n      emit('detection-update', {\n        direction,\n        frameData,\n        directionStats: directionStats[direction],\n        globalStats: {\n          totalVehicles: totalVehicleCount.value,\n          peakDirection: getPeakDirection(),\n          averageSpeed: getAverageFrameRate(),\n          processingTime: getProcessingTime()\n        }\n      });\n    };\n    const getPeakDirection = () => {\n      let maxCount = 0;\n      let peakDir = '-';\n      Object.entries(directionStats).forEach(([dir, stats]) => {\n        if (stats.vehicleCount > maxCount) {\n          maxCount = stats.vehicleCount;\n          peakDir = getDirectionName(dir);\n        }\n      });\n      return peakDir;\n    };\n    const getDirectionName = direction => {\n      const names = {\n        north: '北向',\n        south: '南向',\n        east: '东向',\n        west: '西向'\n      };\n      return names[direction] || direction;\n    };\n\n    // 获取指定方向的RealTimeFrameViewer组件引用\n    const getDirectionViewerRef = direction => {\n      const refMap = {\n        north: northViewer,\n        south: southViewer,\n        east: eastViewer,\n        west: westViewer\n      };\n      return refMap[direction]?.value;\n    };\n    const getAverageFrameRate = () => {\n      const rates = Object.values(directionStats).map(stats => parseFloat(stats.frameRate)).filter(rate => !isNaN(rate));\n      if (rates.length === 0) return 0;\n      const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length;\n      return Math.round(average * 10) / 10;\n    };\n\n    // 新增的智能分析方法\n    const getRecentIncrease = () => {\n      // 模拟最近增长数据\n      return Math.floor(Math.random() * 5) + 1;\n    };\n    const getPeakDirectionPercentage = () => {\n      const total = totalVehicleCount.value;\n      if (total === 0) return 0;\n      const maxCount = Math.max(...Object.values(directionStats).map(s => s.vehicleCount));\n      return Math.round(maxCount / total * 100);\n    };\n    const getEfficiencyLevel = () => {\n      const avgRate = getAverageFrameRate();\n      if (avgRate >= 25) return '高效';\n      if (avgRate >= 15) return '正常';\n      if (avgRate >= 10) return '较慢';\n      return '低效';\n    };\n    const getCongestionLevel = () => {\n      const total = totalVehicleCount.value;\n      if (total >= 50) return '严重拥堵';\n      if (total >= 30) return '中度拥堵';\n      if (total >= 15) return '轻度拥堵';\n      return '畅通';\n    };\n    const getCongestionTrend = () => {\n      const level = getCongestionLevel();\n      if (level === '严重拥堵') return '↗️ 恶化';\n      if (level === '中度拥堵') return '→ 稳定';\n      if (level === '轻度拥堵') return '↘️ 改善';\n      return '✅ 良好';\n    };\n    const getTrafficFlowBalance = () => {\n      const counts = Object.values(directionStats).map(s => s.vehicleCount);\n      const max = Math.max(...counts);\n      const min = Math.min(...counts);\n      if (max === 0) return 100;\n      const balance = (max - min) / max * 100;\n      return Math.round(100 - balance);\n    };\n    const getBalanceTrend = () => {\n      const balance = getTrafficFlowBalance();\n      if (balance >= 80) return '均衡';\n      if (balance >= 60) return '较均衡';\n      if (balance >= 40) return '不均衡';\n      return '严重不均衡';\n    };\n    const getProcessingTime = () => {\n      const startTimes = Object.values(directionStats).map(stats => stats.lastUpdate).filter(time => time);\n      if (startTimes.length === 0) return 0;\n      const earliest = Math.min(...startTimes.map(time => time.getTime()));\n      return Math.round((Date.now() - earliest) / 1000);\n    };\n    const initializeWebSocketConnection = async () => {\n      try {\n        console.log(`初始化四方向WebSocket连接: ${props.taskId}`);\n\n        // 订阅四方向帧数据\n        frameSubscription.value = await stompService.subscribeFourWayFrameUpdates(props.taskId, frameData => {\n          const direction = frameData.direction;\n          if (direction && directionStats[direction] !== undefined) {\n            handleFrameReceived(direction, frameData);\n          }\n        });\n        isConnected.value = true;\n        ElMessage.success('四方向实时检测连接成功');\n      } catch (error) {\n        console.error('WebSocket连接失败:', error);\n        ElMessage.error('连接失败: ' + error.message);\n        isConnected.value = false;\n      }\n    };\n    const cleanup = () => {\n      // 清理WebSocket订阅\n      if (frameSubscription.value) {\n        stompService.unsubscribe(frameSubscription.value);\n        frameSubscription.value = null;\n      }\n\n      // 清理各方向的帧缓冲\n      const directions = ['north', 'south', 'east', 'west'];\n      directions.forEach(direction => {\n        const taskId = getDirectionTaskId(direction);\n        stompService.clearFrameBuffer(taskId);\n      });\n      isConnected.value = false;\n    };\n\n    // 监听taskId变化\n    watch(() => props.taskId, newTaskId => {\n      if (newTaskId) {\n        cleanup();\n        initializeWebSocketConnection();\n      }\n    });\n\n    // 生命周期\n    onMounted(() => {\n      if (props.taskId && props.autoStart) {\n        initializeWebSocketConnection();\n      }\n    });\n    onUnmounted(() => {\n      cleanup();\n    });\n\n    // 暴露方法给父组件\n    const startDetection = () => {\n      initializeWebSocketConnection();\n    };\n    const stopDetection = () => {\n      cleanup();\n    };\n    return {\n      // 响应式数据\n      isConnected,\n      lastUpdateTime,\n      showProgress,\n      northViewer,\n      southViewer,\n      eastViewer,\n      westViewer,\n      directionStats,\n      // 计算属性\n      connectionStatusType,\n      connectionStatusText,\n      totalVehicleCount,\n      overallProgress,\n      progressStatus,\n      // 方法\n      getDirectionTaskId,\n      getDirectionStatusType,\n      getDirectionStatusText,\n      handleFrameReceived,\n      getPeakDirection,\n      getDirectionName,\n      getAverageFrameRate,\n      getProcessingTime,\n      getRecentIncrease,\n      getPeakDirectionPercentage,\n      getEfficiencyLevel,\n      getCongestionLevel,\n      getCongestionTrend,\n      getTrafficFlowBalance,\n      getBalanceTrend,\n      cleanup,\n      startDetection,\n      stopDetection\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "watch", "ElMessage", "Connection", "Grid", "Top", "Bottom", "ArrowLeft", "Back", "Right", "RealTimeFrameViewer", "stompService", "name", "components", "props", "taskId", "type", "String", "required", "autoStart", "Boolean", "default", "emits", "setup", "emit", "isConnected", "lastUpdateTime", "frameSubscription", "showProgress", "northViewer", "southViewer", "eastViewer", "westViewer", "directionStats", "north", "vehicleCount", "frameRate", "status", "lastUpdate", "south", "east", "west", "directionFrameData", "connectionStatusType", "value", "connectionStatusText", "totalVehicleCount", "Object", "values", "reduce", "total", "stats", "overallProgress", "directions", "activeDirections", "filter", "d", "length", "completedDirections", "Math", "round", "progressStatus", "getDirectionTaskId", "direction", "getDirectionStatusType", "typeMap", "getDirectionStatusText", "textMap", "handleFrameReceived", "frameData", "console", "log", "frameNumber", "detectionCount", "hasImageData", "imageData", "imageDataLength", "imageDataPrefix", "substring", "undefined", "Date", "toLocaleTimeString", "now", "lastFrameTime", "interval", "fps", "globalStats", "totalVehicles", "peakDirection", "getPeakDirection", "averageSpeed", "getAverageFrameRate", "processingTime", "getProcessingTime", "maxCount", "peakDir", "entries", "for<PERSON>ach", "dir", "getDirectionName", "names", "getDirectionViewerRef", "refMap", "rates", "map", "parseFloat", "rate", "isNaN", "average", "sum", "getRecentIncrease", "floor", "random", "getPeakDirectionPercentage", "max", "s", "getEfficiencyLevel", "avgRate", "getCongestionLevel", "getCongestionTrend", "level", "getTrafficFlowBalance", "counts", "min", "balance", "getBalanceTrend", "startTimes", "time", "earliest", "getTime", "initializeWebSocketConnection", "subscribeFourWayFrameUpdates", "success", "error", "message", "cleanup", "unsubscribe", "clear<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newTaskId", "startDetection", "stopDetection"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\FourWayRealtimeViewer.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-realtime-viewer\">\n    <!-- 连接状态指示器 -->\n    <div class=\"connection-status\">\n      <el-tag :type=\"connectionStatusType\" size=\"small\">\n        <el-icon><Connection /></el-icon>\n        {{ connectionStatusText }}\n      </el-tag>\n      <span class=\"last-update\">\n        最后更新: {{ lastUpdateTime || '未连接' }}\n      </span>\n    </div>\n\n    <!-- 四方向检测网格 - 2x2布局 -->\n    <div class=\"detection-grid\">\n      <!-- 上排：北向和东向 -->\n      <!-- 北向检测 - 左上 -->\n      <div class=\"detection-item north\">\n        <div class=\"direction-header\">\n          <el-icon><Top /></el-icon>\n          <span class=\"direction-label\">北向 (North)</span>\n          <el-tag :type=\"getDirectionStatusType('north')\" size=\"small\">\n            {{ getDirectionStatusText('north') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"northViewer\"\n            direction=\"north\"\n            :frame-data=\"directionFrameData.north\"\n            :status=\"directionStats.north.status\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.north.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.north.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 下排：西向和南向 -->\n      <!-- 西向检测 - 左下 -->\n      <div class=\"detection-item west\">\n        <div class=\"direction-header\">\n          <el-icon><Back /></el-icon>\n          <span class=\"direction-label\">西向 (West)</span>\n          <el-tag :type=\"getDirectionStatusType('west')\" size=\"small\">\n            {{ getDirectionStatusText('west') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"westViewer\"\n            direction=\"west\"\n            :frame-data=\"directionFrameData.west\"\n            :status=\"directionStats.west.status\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.west.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.west.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 东向检测 - 右上 -->\n      <div class=\"detection-item east\">\n        <div class=\"direction-header\">\n          <el-icon><Right /></el-icon>\n          <span class=\"direction-label\">东向 (East)</span>\n          <el-tag :type=\"getDirectionStatusType('east')\" size=\"small\">\n            {{ getDirectionStatusText('east') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"eastViewer\"\n            direction=\"east\"\n            :frame-data=\"directionFrameData.east\"\n            :status=\"directionStats.east.status\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.east.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.east.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 南向检测 - 右下 -->\n      <div class=\"detection-item south\">\n        <div class=\"direction-header\">\n          <el-icon><Bottom /></el-icon>\n          <span class=\"direction-label\">南向 (South)</span>\n          <el-tag :type=\"getDirectionStatusType('south')\" size=\"small\">\n            {{ getDirectionStatusText('south') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"southViewer\"\n            direction=\"south\"\n            :frame-data=\"directionFrameData.south\"\n            :status=\"directionStats.south.status\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.south.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.south.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 总览统计 -->\n    <div class=\"overview-stats\">\n      <div class=\"total-vehicles-summary\">\n        <el-icon><Grid /></el-icon>\n        <span class=\"total-count\">{{ totalVehicleCount }}</span>\n        <span class=\"total-label\">总车辆数</span>\n      </div>\n      <div class=\"peak-direction-info\">\n        <span class=\"peak-text\">高峰方向: {{ getPeakDirection() }}</span>\n      </div>\n    </div>\n\n    <!-- 检测进度条 -->\n    <div v-if=\"showProgress\" class=\"detection-progress\">\n      <el-progress\n        :percentage=\"overallProgress\"\n        :status=\"progressStatus\"\n        :stroke-width=\"8\"\n        :show-text=\"true\"\n      >\n        <template #default=\"{ percentage }\">\n          <span class=\"progress-text\">{{ percentage }}% 检测进度</span>\n        </template>\n      </el-progress>\n    </div>\n\n    <!-- 增强的全局统计信息 -->\n    <div class=\"enhanced-global-stats\">\n      <div class=\"stats-header\">\n        <h3>实时交通分析</h3>\n        <div class=\"connection-status-info\">\n          <el-icon :class=\"{ 'connected': isConnected, 'disconnected': !isConnected }\">\n            <Connection />\n          </el-icon>\n          <span>{{ isConnected ? '已连接' : '未连接' }}</span>\n          <el-tag v-if=\"lastUpdateTime\" size=\"small\" type=\"info\">\n            {{ lastUpdateTime }}\n          </el-tag>\n        </div>\n      </div>\n\n      <div class=\"stats-grid\">\n        <div class=\"stat-card primary\">\n          <div class=\"stat-icon\">🚗</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ totalVehicleCount }}</div>\n            <div class=\"stat-label\">总车辆数</div>\n            <div class=\"stat-trend positive\">\n              +{{ getRecentIncrease() }}\n            </div>\n          </div>\n        </div>\n\n        <div class=\"stat-card success\">\n          <div class=\"stat-icon\">📍</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getPeakDirection() }}</div>\n            <div class=\"stat-label\">最繁忙方向</div>\n            <div class=\"stat-trend\">\n              {{ getPeakDirectionPercentage() }}%\n            </div>\n          </div>\n        </div>\n\n        <div class=\"stat-card warning\">\n          <div class=\"stat-icon\">⚡</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getAverageFrameRate() }}</div>\n            <div class=\"stat-label\">平均帧率</div>\n            <div class=\"stat-trend\">实时</div>\n          </div>\n        </div>\n\n        <div class=\"stat-card info\">\n          <div class=\"stat-icon\">⏱️</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getProcessingTime() }}s</div>\n            <div class=\"stat-label\">处理时间</div>\n            <div class=\"stat-trend\">{{ getEfficiencyLevel() }}</div>\n          </div>\n        </div>\n\n        <div class=\"stat-card danger\">\n          <div class=\"stat-icon\">🚨</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getCongestionLevel() }}</div>\n            <div class=\"stat-label\">拥堵等级</div>\n            <div class=\"stat-trend\">{{ getCongestionTrend() }}</div>\n          </div>\n        </div>\n\n        <div class=\"stat-card purple\">\n          <div class=\"stat-icon\">📊</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getTrafficFlowBalance() }}%</div>\n            <div class=\"stat-label\">流量平衡度</div>\n            <div class=\"stat-trend\">{{ getBalanceTrend() }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport {\n  Connection, Grid, Top, Bottom, ArrowLeft as Back, Right\n} from '@element-plus/icons-vue'\nimport RealTimeFrameViewer from '@/components/video/RealTimeFrameViewer.vue'\nimport stompService from '@/utils/stomp-service'\n\nexport default {\n  name: 'FourWayRealtimeViewer',\n  components: {\n    Connection, Grid, Top, Bottom, Back, Right,\n    RealTimeFrameViewer\n  },\n  props: {\n    taskId: {\n      type: String,\n      required: true\n    },\n    autoStart: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['detection-update', 'status-change'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isConnected = ref(false)\n    const lastUpdateTime = ref('')\n    const frameSubscription = ref(null)\n    const showProgress = ref(true)\n    \n    // 方向查看器引用\n    const northViewer = ref(null)\n    const southViewer = ref(null)\n    const eastViewer = ref(null)\n    const westViewer = ref(null)\n    \n    // 方向统计数据\n    const directionStats = reactive({\n      north: { vehicleCount: 0, frameRate: '0 fps', status: 'waiting', lastUpdate: null },\n      south: { vehicleCount: 0, frameRate: '0 fps', status: 'waiting', lastUpdate: null },\n      east: { vehicleCount: 0, frameRate: '0 fps', status: 'waiting', lastUpdate: null },\n      west: { vehicleCount: 0, frameRate: '0 fps', status: 'waiting', lastUpdate: null }\n    })\n\n    // 方向帧数据\n    const directionFrameData = reactive({\n      north: null,\n      south: null,\n      east: null,\n      west: null\n    })\n    \n    // 计算属性\n    const connectionStatusType = computed(() => {\n      return isConnected.value ? 'success' : 'danger'\n    })\n    \n    const connectionStatusText = computed(() => {\n      return isConnected.value ? '已连接' : '未连接'\n    })\n    \n    const totalVehicleCount = computed(() => {\n      return Object.values(directionStats).reduce((total, stats) => total + stats.vehicleCount, 0)\n    })\n    \n    const overallProgress = computed(() => {\n      const directions = Object.values(directionStats)\n      const activeDirections = directions.filter(d => d.status !== 'waiting')\n      if (activeDirections.length === 0) return 0\n      \n      const completedDirections = directions.filter(d => d.status === 'completed')\n      return Math.round((completedDirections.length / 4) * 100)\n    })\n    \n    const progressStatus = computed(() => {\n      if (overallProgress.value === 100) return 'success'\n      if (overallProgress.value > 0) return ''\n      return 'exception'\n    })\n    \n    // 方法\n    const getDirectionTaskId = (direction) => {\n      return `${direction}_${props.taskId}`\n    }\n    \n    const getDirectionStatusType = (direction) => {\n      const status = directionStats[direction].status\n      const typeMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'error': 'danger'\n      }\n      return typeMap[status] || 'info'\n    }\n\n    const getDirectionStatusText = (direction) => {\n      const status = directionStats[direction].status\n      const textMap = {\n        'waiting': '等待中',\n        'processing': '检测中',\n        'completed': '已完成',\n        'error': '检测失败'\n      }\n      return textMap[status] || '未知'\n    }\n    \n    const handleFrameReceived = (direction, frameData) => {\n      console.log(`🎬 FourWayRealtimeViewer收到${direction}方向帧数据:`, {\n        frameNumber: frameData.frameNumber,\n        detectionCount: frameData.detectionCount,\n        hasImageData: !!frameData.imageData,\n        imageDataLength: frameData.imageData ? frameData.imageData.length : 0,\n        imageDataPrefix: frameData.imageData ? frameData.imageData.substring(0, 50) + '...' : 'null'\n      })\n\n      // 更新方向统计\n      if (frameData.detectionCount !== undefined) {\n        directionStats[direction].vehicleCount += frameData.detectionCount\n      }\n\n      directionStats[direction].status = 'processing'\n      directionStats[direction].lastUpdate = new Date()\n\n      // 更新最后更新时间\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n\n      // 计算帧率（简化版本）\n      const now = Date.now()\n      if (directionStats[direction].lastFrameTime) {\n        const interval = now - directionStats[direction].lastFrameTime\n        const fps = Math.round(1000 / interval * 10) / 10\n        directionStats[direction].frameRate = `${fps} fps`\n      }\n      directionStats[direction].lastFrameTime = now\n\n      // 更新方向帧数据，这将触发RealTimeFrameViewer组件的响应式更新\n      directionFrameData[direction] = { ...frameData }\n      console.log(`✅ ${direction}方向帧数据已更新到directionFrameData`)\n\n      // 发出检测更新事件\n      emit('detection-update', {\n        direction,\n        frameData,\n        directionStats: directionStats[direction],\n        globalStats: {\n          totalVehicles: totalVehicleCount.value,\n          peakDirection: getPeakDirection(),\n          averageSpeed: getAverageFrameRate(),\n          processingTime: getProcessingTime()\n        }\n      })\n    }\n    \n    const getPeakDirection = () => {\n      let maxCount = 0\n      let peakDir = '-'\n      \n      Object.entries(directionStats).forEach(([dir, stats]) => {\n        if (stats.vehicleCount > maxCount) {\n          maxCount = stats.vehicleCount\n          peakDir = getDirectionName(dir)\n        }\n      })\n      \n      return peakDir\n    }\n    \n    const getDirectionName = (direction) => {\n      const names = {\n        north: '北向',\n        south: '南向',\n        east: '东向',\n        west: '西向'\n      }\n      return names[direction] || direction\n    }\n\n    // 获取指定方向的RealTimeFrameViewer组件引用\n    const getDirectionViewerRef = (direction) => {\n      const refMap = {\n        north: northViewer,\n        south: southViewer,\n        east: eastViewer,\n        west: westViewer\n      }\n      return refMap[direction]?.value\n    }\n    \n    const getAverageFrameRate = () => {\n      const rates = Object.values(directionStats)\n        .map(stats => parseFloat(stats.frameRate))\n        .filter(rate => !isNaN(rate))\n\n      if (rates.length === 0) return 0\n\n      const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length\n      return Math.round(average * 10) / 10\n    }\n\n    // 新增的智能分析方法\n    const getRecentIncrease = () => {\n      // 模拟最近增长数据\n      return Math.floor(Math.random() * 5) + 1\n    }\n\n    const getPeakDirectionPercentage = () => {\n      const total = totalVehicleCount.value\n      if (total === 0) return 0\n\n      const maxCount = Math.max(...Object.values(directionStats).map(s => s.vehicleCount))\n      return Math.round((maxCount / total) * 100)\n    }\n\n    const getEfficiencyLevel = () => {\n      const avgRate = getAverageFrameRate()\n      if (avgRate >= 25) return '高效'\n      if (avgRate >= 15) return '正常'\n      if (avgRate >= 10) return '较慢'\n      return '低效'\n    }\n\n    const getCongestionLevel = () => {\n      const total = totalVehicleCount.value\n      if (total >= 50) return '严重拥堵'\n      if (total >= 30) return '中度拥堵'\n      if (total >= 15) return '轻度拥堵'\n      return '畅通'\n    }\n\n    const getCongestionTrend = () => {\n      const level = getCongestionLevel()\n      if (level === '严重拥堵') return '↗️ 恶化'\n      if (level === '中度拥堵') return '→ 稳定'\n      if (level === '轻度拥堵') return '↘️ 改善'\n      return '✅ 良好'\n    }\n\n    const getTrafficFlowBalance = () => {\n      const counts = Object.values(directionStats).map(s => s.vehicleCount)\n      const max = Math.max(...counts)\n      const min = Math.min(...counts)\n\n      if (max === 0) return 100\n\n      const balance = ((max - min) / max) * 100\n      return Math.round(100 - balance)\n    }\n\n    const getBalanceTrend = () => {\n      const balance = getTrafficFlowBalance()\n      if (balance >= 80) return '均衡'\n      if (balance >= 60) return '较均衡'\n      if (balance >= 40) return '不均衡'\n      return '严重不均衡'\n    }\n    \n    const getProcessingTime = () => {\n      const startTimes = Object.values(directionStats)\n        .map(stats => stats.lastUpdate)\n        .filter(time => time)\n      \n      if (startTimes.length === 0) return 0\n      \n      const earliest = Math.min(...startTimes.map(time => time.getTime()))\n      return Math.round((Date.now() - earliest) / 1000)\n    }\n    \n    const initializeWebSocketConnection = async () => {\n      try {\n        console.log(`初始化四方向WebSocket连接: ${props.taskId}`)\n        \n        // 订阅四方向帧数据\n        frameSubscription.value = await stompService.subscribeFourWayFrameUpdates(\n          props.taskId,\n          (frameData) => {\n            const direction = frameData.direction\n            if (direction && directionStats[direction] !== undefined) {\n              handleFrameReceived(direction, frameData)\n            }\n          }\n        )\n        \n        isConnected.value = true\n        ElMessage.success('四方向实时检测连接成功')\n        \n      } catch (error) {\n        console.error('WebSocket连接失败:', error)\n        ElMessage.error('连接失败: ' + error.message)\n        isConnected.value = false\n      }\n    }\n    \n    const cleanup = () => {\n      // 清理WebSocket订阅\n      if (frameSubscription.value) {\n        stompService.unsubscribe(frameSubscription.value)\n        frameSubscription.value = null\n      }\n      \n      // 清理各方向的帧缓冲\n      const directions = ['north', 'south', 'east', 'west']\n      directions.forEach(direction => {\n        const taskId = getDirectionTaskId(direction)\n        stompService.clearFrameBuffer(taskId)\n      })\n      \n      isConnected.value = false\n    }\n    \n    // 监听taskId变化\n    watch(() => props.taskId, (newTaskId) => {\n      if (newTaskId) {\n        cleanup()\n        initializeWebSocketConnection()\n      }\n    })\n    \n    // 生命周期\n    onMounted(() => {\n      if (props.taskId && props.autoStart) {\n        initializeWebSocketConnection()\n      }\n    })\n    \n    onUnmounted(() => {\n      cleanup()\n    })\n    \n    // 暴露方法给父组件\n    const startDetection = () => {\n      initializeWebSocketConnection()\n    }\n    \n    const stopDetection = () => {\n      cleanup()\n    }\n    \n    return {\n      // 响应式数据\n      isConnected,\n      lastUpdateTime,\n      showProgress,\n      northViewer,\n      southViewer,\n      eastViewer,\n      westViewer,\n      directionStats,\n      \n      // 计算属性\n      connectionStatusType,\n      connectionStatusText,\n      totalVehicleCount,\n      overallProgress,\n      progressStatus,\n      \n      // 方法\n      getDirectionTaskId,\n      getDirectionStatusType,\n      getDirectionStatusText,\n      handleFrameReceived,\n      getPeakDirection,\n      getDirectionName,\n      getAverageFrameRate,\n      getProcessingTime,\n      getRecentIncrease,\n      getPeakDirectionPercentage,\n      getEfficiencyLevel,\n      getCongestionLevel,\n      getCongestionTrend,\n      getTrafficFlowBalance,\n      getBalanceTrend,\n      cleanup,\n      startDetection,\n      stopDetection\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-realtime-viewer {\n  background: #ffffff;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.connection-status {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding: 12px 16px;\n  background: #f8fafc;\n  border-radius: 8px;\n}\n\n.last-update {\n  font-size: 14px;\n  color: #6b7280;\n}\n\n.detection-grid {\n  display: grid !important;\n  grid-template-columns: 1fr 1fr !important;\n  grid-template-rows: 1fr 1fr !important;\n  gap: 24px !important;\n  margin-bottom: 24px;\n  min-height: 600px;\n}\n\n.detection-item {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 16px;\n  border: 2px solid #e5e7eb;\n  transition: all 0.3s ease;\n}\n\n.detection-item:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.detection-item.north {\n  grid-column: 1 !important;\n  grid-row: 1 !important;\n}\n\n.detection-item.east {\n  grid-column: 2 !important;\n  grid-row: 1 !important;\n}\n\n.detection-item.west {\n  grid-column: 1 !important;\n  grid-row: 2 !important;\n}\n\n.detection-item.south {\n  grid-column: 2 !important;\n  grid-row: 2 !important;\n}\n\n\n\n.direction-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n}\n\n.direction-label {\n  font-weight: 500;\n  color: #374151;\n}\n\n.detection-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.direction-viewer {\n  flex: 1;\n  min-height: 300px;\n  margin-bottom: 12px;\n}\n\n/* 总览统计样式 */\n.overview-stats {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 20px 0;\n  padding: 16px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  color: white;\n}\n\n.total-vehicles-summary {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.total-vehicles-summary .el-icon {\n  font-size: 24px;\n}\n\n.total-count {\n  font-size: 28px;\n  font-weight: 700;\n  line-height: 1;\n}\n\n.total-label {\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.peak-direction-info {\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.peak-text {\n  opacity: 0.95;\n}\n\n.direction-stats {\n  display: flex;\n  justify-content: space-around;\n  padding: 8px;\n  background: #ffffff;\n  border-radius: 6px;\n  border: 1px solid #e5e7eb;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  display: block;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.stat-label {\n  display: block;\n  font-size: 12px;\n  color: #6b7280;\n  margin-top: 2px;\n}\n\n.detection-progress {\n  margin-top: 16px;\n}\n\n.progress-text {\n  font-size: 14px;\n  font-weight: 500;\n}\n\n/* 增强统计信息样式 */\n.enhanced-global-stats {\n  margin-top: 24px;\n  background: #f8fafc;\n  border-radius: 12px;\n  padding: 20px;\n}\n\n.stats-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.stats-header h3 {\n  margin: 0;\n  color: #1f2937;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.connection-status-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n}\n\n.connection-status-info .el-icon {\n  font-size: 16px;\n}\n\n.connection-status-info .el-icon.connected {\n  color: #10b981;\n}\n\n.connection-status-info .el-icon.disconnected {\n  color: #ef4444;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  border-left: 4px solid #e5e7eb;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.stat-card.primary {\n  border-left-color: #3b82f6;\n}\n\n.stat-card.success {\n  border-left-color: #10b981;\n}\n\n.stat-card.warning {\n  border-left-color: #f59e0b;\n}\n\n.stat-card.info {\n  border-left-color: #06b6d4;\n}\n\n.stat-card.danger {\n  border-left-color: #ef4444;\n}\n\n.stat-card.purple {\n  border-left-color: #8b5cf6;\n}\n\n.stat-card {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.stat-icon {\n  font-size: 24px;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f3f4f6;\n  border-radius: 8px;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: 20px;\n  font-weight: 700;\n  color: #1f2937;\n  line-height: 1.2;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #6b7280;\n  margin-top: 2px;\n}\n\n.stat-trend {\n  font-size: 11px;\n  font-weight: 500;\n  margin-top: 4px;\n  padding: 2px 6px;\n  border-radius: 4px;\n  background: #f3f4f6;\n  color: #6b7280;\n}\n\n.stat-trend.positive {\n  background: #dcfce7;\n  color: #16a34a;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .detection-grid {\n    grid-template-columns: 1fr;\n    grid-template-rows: auto;\n  }\n  \n  .detection-item {\n    grid-column: 1 !important;\n    grid-row: auto !important;\n  }\n}\n\n@media (max-width: 768px) {\n  .four-way-realtime-viewer {\n    padding: 16px;\n  }\n  \n  .detection-grid {\n    gap: 16px;\n  }\n  \n  .direction-viewer {\n    min-height: 150px;\n  }\n}\n</style>\n"], "mappings": ";;;;;AAqPA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAI,QAAS,KAAI;AAC3E,SAASC,SAAQ,QAAS,cAAa;AACvC,SACEC,UAAU,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,SAAQ,IAAKC,IAAI,EAAEC,KAAI,QACjD,yBAAwB;AAC/B,OAAOC,mBAAkB,MAAO,4CAA2C;AAC3E,OAAOC,YAAW,MAAO,uBAAsB;AAE/C,eAAe;EACbC,IAAI,EAAE,uBAAuB;EAC7BC,UAAU,EAAE;IACVV,UAAU;IAAEC,IAAI;IAAEC,GAAG;IAAEC,MAAM;IAAEE,IAAI;IAAEC,KAAK;IAC1CC;EACF,CAAC;EACDI,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAEI,OAAO;MACbC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAK,EAAE,CAAC,kBAAkB,EAAE,eAAe,CAAC;EAC5CC,KAAKA,CAACT,KAAK,EAAE;IAAEU;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,WAAU,GAAI7B,GAAG,CAAC,KAAK;IAC7B,MAAM8B,cAAa,GAAI9B,GAAG,CAAC,EAAE;IAC7B,MAAM+B,iBAAgB,GAAI/B,GAAG,CAAC,IAAI;IAClC,MAAMgC,YAAW,GAAIhC,GAAG,CAAC,IAAI;;IAE7B;IACA,MAAMiC,WAAU,GAAIjC,GAAG,CAAC,IAAI;IAC5B,MAAMkC,WAAU,GAAIlC,GAAG,CAAC,IAAI;IAC5B,MAAMmC,UAAS,GAAInC,GAAG,CAAC,IAAI;IAC3B,MAAMoC,UAAS,GAAIpC,GAAG,CAAC,IAAI;;IAE3B;IACA,MAAMqC,cAAa,GAAIpC,QAAQ,CAAC;MAC9BqC,KAAK,EAAE;QAAEC,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE,OAAO;QAAEC,MAAM,EAAE,SAAS;QAAEC,UAAU,EAAE;MAAK,CAAC;MACnFC,KAAK,EAAE;QAAEJ,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE,OAAO;QAAEC,MAAM,EAAE,SAAS;QAAEC,UAAU,EAAE;MAAK,CAAC;MACnFE,IAAI,EAAE;QAAEL,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE,OAAO;QAAEC,MAAM,EAAE,SAAS;QAAEC,UAAU,EAAE;MAAK,CAAC;MAClFG,IAAI,EAAE;QAAEN,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE,OAAO;QAAEC,MAAM,EAAE,SAAS;QAAEC,UAAU,EAAE;MAAK;IACnF,CAAC;;IAED;IACA,MAAMI,kBAAiB,GAAI7C,QAAQ,CAAC;MAClCqC,KAAK,EAAE,IAAI;MACXK,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE;IACR,CAAC;;IAED;IACA,MAAME,oBAAmB,GAAI7C,QAAQ,CAAC,MAAM;MAC1C,OAAO2B,WAAW,CAACmB,KAAI,GAAI,SAAQ,GAAI,QAAO;IAChD,CAAC;IAED,MAAMC,oBAAmB,GAAI/C,QAAQ,CAAC,MAAM;MAC1C,OAAO2B,WAAW,CAACmB,KAAI,GAAI,KAAI,GAAI,KAAI;IACzC,CAAC;IAED,MAAME,iBAAgB,GAAIhD,QAAQ,CAAC,MAAM;MACvC,OAAOiD,MAAM,CAACC,MAAM,CAACf,cAAc,CAAC,CAACgB,MAAM,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKD,KAAI,GAAIC,KAAK,CAAChB,YAAY,EAAE,CAAC;IAC7F,CAAC;IAED,MAAMiB,eAAc,GAAItD,QAAQ,CAAC,MAAM;MACrC,MAAMuD,UAAS,GAAIN,MAAM,CAACC,MAAM,CAACf,cAAc;MAC/C,MAAMqB,gBAAe,GAAID,UAAU,CAACE,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACnB,MAAK,KAAM,SAAS;MACtE,IAAIiB,gBAAgB,CAACG,MAAK,KAAM,CAAC,EAAE,OAAO;MAE1C,MAAMC,mBAAkB,GAAIL,UAAU,CAACE,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACnB,MAAK,KAAM,WAAW;MAC3E,OAAOsB,IAAI,CAACC,KAAK,CAAEF,mBAAmB,CAACD,MAAK,GAAI,CAAC,GAAI,GAAG;IAC1D,CAAC;IAED,MAAMI,cAAa,GAAI/D,QAAQ,CAAC,MAAM;MACpC,IAAIsD,eAAe,CAACR,KAAI,KAAM,GAAG,EAAE,OAAO,SAAQ;MAClD,IAAIQ,eAAe,CAACR,KAAI,GAAI,CAAC,EAAE,OAAO,EAAC;MACvC,OAAO,WAAU;IACnB,CAAC;;IAED;IACA,MAAMkB,kBAAiB,GAAKC,SAAS,IAAK;MACxC,OAAO,GAAGA,SAAS,IAAIjD,KAAK,CAACC,MAAM,EAAC;IACtC;IAEA,MAAMiD,sBAAqB,GAAKD,SAAS,IAAK;MAC5C,MAAM1B,MAAK,GAAIJ,cAAc,CAAC8B,SAAS,CAAC,CAAC1B,MAAK;MAC9C,MAAM4B,OAAM,GAAI;QACd,SAAS,EAAE,MAAM;QACjB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,OAAO,EAAE;MACX;MACA,OAAOA,OAAO,CAAC5B,MAAM,KAAK,MAAK;IACjC;IAEA,MAAM6B,sBAAqB,GAAKH,SAAS,IAAK;MAC5C,MAAM1B,MAAK,GAAIJ,cAAc,CAAC8B,SAAS,CAAC,CAAC1B,MAAK;MAC9C,MAAM8B,OAAM,GAAI;QACd,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE;MACX;MACA,OAAOA,OAAO,CAAC9B,MAAM,KAAK,IAAG;IAC/B;IAEA,MAAM+B,mBAAkB,GAAIA,CAACL,SAAS,EAAEM,SAAS,KAAK;MACpDC,OAAO,CAACC,GAAG,CAAC,6BAA6BR,SAAS,QAAQ,EAAE;QAC1DS,WAAW,EAAEH,SAAS,CAACG,WAAW;QAClCC,cAAc,EAAEJ,SAAS,CAACI,cAAc;QACxCC,YAAY,EAAE,CAAC,CAACL,SAAS,CAACM,SAAS;QACnCC,eAAe,EAAEP,SAAS,CAACM,SAAQ,GAAIN,SAAS,CAACM,SAAS,CAAClB,MAAK,GAAI,CAAC;QACrEoB,eAAe,EAAER,SAAS,CAACM,SAAQ,GAAIN,SAAS,CAACM,SAAS,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,KAAI,GAAI;MACxF,CAAC;;MAED;MACA,IAAIT,SAAS,CAACI,cAAa,KAAMM,SAAS,EAAE;QAC1C9C,cAAc,CAAC8B,SAAS,CAAC,CAAC5B,YAAW,IAAKkC,SAAS,CAACI,cAAa;MACnE;MAEAxC,cAAc,CAAC8B,SAAS,CAAC,CAAC1B,MAAK,GAAI,YAAW;MAC9CJ,cAAc,CAAC8B,SAAS,CAAC,CAACzB,UAAS,GAAI,IAAI0C,IAAI,CAAC;;MAEhD;MACAtD,cAAc,CAACkB,KAAI,GAAI,IAAIoC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;;MAErD;MACA,MAAMC,GAAE,GAAIF,IAAI,CAACE,GAAG,CAAC;MACrB,IAAIjD,cAAc,CAAC8B,SAAS,CAAC,CAACoB,aAAa,EAAE;QAC3C,MAAMC,QAAO,GAAIF,GAAE,GAAIjD,cAAc,CAAC8B,SAAS,CAAC,CAACoB,aAAY;QAC7D,MAAME,GAAE,GAAI1B,IAAI,CAACC,KAAK,CAAC,IAAG,GAAIwB,QAAO,GAAI,EAAE,IAAI,EAAC;QAChDnD,cAAc,CAAC8B,SAAS,CAAC,CAAC3B,SAAQ,GAAI,GAAGiD,GAAG,MAAK;MACnD;MACApD,cAAc,CAAC8B,SAAS,CAAC,CAACoB,aAAY,GAAID,GAAE;;MAE5C;MACAxC,kBAAkB,CAACqB,SAAS,IAAI;QAAE,GAAGM;MAAU;MAC/CC,OAAO,CAACC,GAAG,CAAC,KAAKR,SAAS,6BAA6B;;MAEvD;MACAvC,IAAI,CAAC,kBAAkB,EAAE;QACvBuC,SAAS;QACTM,SAAS;QACTpC,cAAc,EAAEA,cAAc,CAAC8B,SAAS,CAAC;QACzCuB,WAAW,EAAE;UACXC,aAAa,EAAEzC,iBAAiB,CAACF,KAAK;UACtC4C,aAAa,EAAEC,gBAAgB,CAAC,CAAC;UACjCC,YAAY,EAAEC,mBAAmB,CAAC,CAAC;UACnCC,cAAc,EAAEC,iBAAiB,CAAC;QACpC;MACF,CAAC;IACH;IAEA,MAAMJ,gBAAe,GAAIA,CAAA,KAAM;MAC7B,IAAIK,QAAO,GAAI;MACf,IAAIC,OAAM,GAAI,GAAE;MAEhBhD,MAAM,CAACiD,OAAO,CAAC/D,cAAc,CAAC,CAACgE,OAAO,CAAC,CAAC,CAACC,GAAG,EAAE/C,KAAK,CAAC,KAAK;QACvD,IAAIA,KAAK,CAAChB,YAAW,GAAI2D,QAAQ,EAAE;UACjCA,QAAO,GAAI3C,KAAK,CAAChB,YAAW;UAC5B4D,OAAM,GAAII,gBAAgB,CAACD,GAAG;QAChC;MACF,CAAC;MAED,OAAOH,OAAM;IACf;IAEA,MAAMI,gBAAe,GAAKpC,SAAS,IAAK;MACtC,MAAMqC,KAAI,GAAI;QACZlE,KAAK,EAAE,IAAI;QACXK,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;MACR;MACA,OAAO2D,KAAK,CAACrC,SAAS,KAAKA,SAAQ;IACrC;;IAEA;IACA,MAAMsC,qBAAoB,GAAKtC,SAAS,IAAK;MAC3C,MAAMuC,MAAK,GAAI;QACbpE,KAAK,EAAEL,WAAW;QAClBU,KAAK,EAAET,WAAW;QAClBU,IAAI,EAAET,UAAU;QAChBU,IAAI,EAAET;MACR;MACA,OAAOsE,MAAM,CAACvC,SAAS,CAAC,EAAEnB,KAAI;IAChC;IAEA,MAAM+C,mBAAkB,GAAIA,CAAA,KAAM;MAChC,MAAMY,KAAI,GAAIxD,MAAM,CAACC,MAAM,CAACf,cAAc,EACvCuE,GAAG,CAACrD,KAAI,IAAKsD,UAAU,CAACtD,KAAK,CAACf,SAAS,CAAC,EACxCmB,MAAM,CAACmD,IAAG,IAAK,CAACC,KAAK,CAACD,IAAI,CAAC;MAE9B,IAAIH,KAAK,CAAC9C,MAAK,KAAM,CAAC,EAAE,OAAO;MAE/B,MAAMmD,OAAM,GAAIL,KAAK,CAACtD,MAAM,CAAC,CAAC4D,GAAG,EAAEH,IAAI,KAAKG,GAAE,GAAIH,IAAI,EAAE,CAAC,IAAIH,KAAK,CAAC9C,MAAK;MACxE,OAAOE,IAAI,CAACC,KAAK,CAACgD,OAAM,GAAI,EAAE,IAAI,EAAC;IACrC;;IAEA;IACA,MAAME,iBAAgB,GAAIA,CAAA,KAAM;MAC9B;MACA,OAAOnD,IAAI,CAACoD,KAAK,CAACpD,IAAI,CAACqD,MAAM,CAAC,IAAI,CAAC,IAAI;IACzC;IAEA,MAAMC,0BAAyB,GAAIA,CAAA,KAAM;MACvC,MAAM/D,KAAI,GAAIJ,iBAAiB,CAACF,KAAI;MACpC,IAAIM,KAAI,KAAM,CAAC,EAAE,OAAO;MAExB,MAAM4C,QAAO,GAAInC,IAAI,CAACuD,GAAG,CAAC,GAAGnE,MAAM,CAACC,MAAM,CAACf,cAAc,CAAC,CAACuE,GAAG,CAACW,CAAA,IAAKA,CAAC,CAAChF,YAAY,CAAC;MACnF,OAAOwB,IAAI,CAACC,KAAK,CAAEkC,QAAO,GAAI5C,KAAK,GAAI,GAAG;IAC5C;IAEA,MAAMkE,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,OAAM,GAAI1B,mBAAmB,CAAC;MACpC,IAAI0B,OAAM,IAAK,EAAE,EAAE,OAAO,IAAG;MAC7B,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,IAAG;MAC7B,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,IAAG;MAC7B,OAAO,IAAG;IACZ;IAEA,MAAMC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMpE,KAAI,GAAIJ,iBAAiB,CAACF,KAAI;MACpC,IAAIM,KAAI,IAAK,EAAE,EAAE,OAAO,MAAK;MAC7B,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,MAAK;MAC7B,IAAIA,KAAI,IAAK,EAAE,EAAE,OAAO,MAAK;MAC7B,OAAO,IAAG;IACZ;IAEA,MAAMqE,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,MAAMC,KAAI,GAAIF,kBAAkB,CAAC;MACjC,IAAIE,KAAI,KAAM,MAAM,EAAE,OAAO,OAAM;MACnC,IAAIA,KAAI,KAAM,MAAM,EAAE,OAAO,MAAK;MAClC,IAAIA,KAAI,KAAM,MAAM,EAAE,OAAO,OAAM;MACnC,OAAO,MAAK;IACd;IAEA,MAAMC,qBAAoB,GAAIA,CAAA,KAAM;MAClC,MAAMC,MAAK,GAAI3E,MAAM,CAACC,MAAM,CAACf,cAAc,CAAC,CAACuE,GAAG,CAACW,CAAA,IAAKA,CAAC,CAAChF,YAAY;MACpE,MAAM+E,GAAE,GAAIvD,IAAI,CAACuD,GAAG,CAAC,GAAGQ,MAAM;MAC9B,MAAMC,GAAE,GAAIhE,IAAI,CAACgE,GAAG,CAAC,GAAGD,MAAM;MAE9B,IAAIR,GAAE,KAAM,CAAC,EAAE,OAAO,GAAE;MAExB,MAAMU,OAAM,GAAK,CAACV,GAAE,GAAIS,GAAG,IAAIT,GAAG,GAAI,GAAE;MACxC,OAAOvD,IAAI,CAACC,KAAK,CAAC,GAAE,GAAIgE,OAAO;IACjC;IAEA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5B,MAAMD,OAAM,GAAIH,qBAAqB,CAAC;MACtC,IAAIG,OAAM,IAAK,EAAE,EAAE,OAAO,IAAG;MAC7B,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,KAAI;MAC9B,IAAIA,OAAM,IAAK,EAAE,EAAE,OAAO,KAAI;MAC9B,OAAO,OAAM;IACf;IAEA,MAAM/B,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,MAAMiC,UAAS,GAAI/E,MAAM,CAACC,MAAM,CAACf,cAAc,EAC5CuE,GAAG,CAACrD,KAAI,IAAKA,KAAK,CAACb,UAAU,EAC7BiB,MAAM,CAACwE,IAAG,IAAKA,IAAI;MAEtB,IAAID,UAAU,CAACrE,MAAK,KAAM,CAAC,EAAE,OAAO;MAEpC,MAAMuE,QAAO,GAAIrE,IAAI,CAACgE,GAAG,CAAC,GAAGG,UAAU,CAACtB,GAAG,CAACuB,IAAG,IAAKA,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC;MACnE,OAAOtE,IAAI,CAACC,KAAK,CAAC,CAACoB,IAAI,CAACE,GAAG,CAAC,IAAI8C,QAAQ,IAAI,IAAI;IAClD;IAEA,MAAME,6BAA4B,GAAI,MAAAA,CAAA,KAAY;MAChD,IAAI;QACF5D,OAAO,CAACC,GAAG,CAAC,sBAAsBzD,KAAK,CAACC,MAAM,EAAE;;QAEhD;QACAY,iBAAiB,CAACiB,KAAI,GAAI,MAAMjC,YAAY,CAACwH,4BAA4B,CACvErH,KAAK,CAACC,MAAM,EACXsD,SAAS,IAAK;UACb,MAAMN,SAAQ,GAAIM,SAAS,CAACN,SAAQ;UACpC,IAAIA,SAAQ,IAAK9B,cAAc,CAAC8B,SAAS,MAAMgB,SAAS,EAAE;YACxDX,mBAAmB,CAACL,SAAS,EAAEM,SAAS;UAC1C;QACF,CACF;QAEA5C,WAAW,CAACmB,KAAI,GAAI,IAAG;QACvB1C,SAAS,CAACkI,OAAO,CAAC,aAAa;MAEjC,EAAE,OAAOC,KAAK,EAAE;QACd/D,OAAO,CAAC+D,KAAK,CAAC,gBAAgB,EAAEA,KAAK;QACrCnI,SAAS,CAACmI,KAAK,CAAC,QAAO,GAAIA,KAAK,CAACC,OAAO;QACxC7G,WAAW,CAACmB,KAAI,GAAI,KAAI;MAC1B;IACF;IAEA,MAAM2F,OAAM,GAAIA,CAAA,KAAM;MACpB;MACA,IAAI5G,iBAAiB,CAACiB,KAAK,EAAE;QAC3BjC,YAAY,CAAC6H,WAAW,CAAC7G,iBAAiB,CAACiB,KAAK;QAChDjB,iBAAiB,CAACiB,KAAI,GAAI,IAAG;MAC/B;;MAEA;MACA,MAAMS,UAAS,GAAI,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM;MACpDA,UAAU,CAAC4C,OAAO,CAAClC,SAAQ,IAAK;QAC9B,MAAMhD,MAAK,GAAI+C,kBAAkB,CAACC,SAAS;QAC3CpD,YAAY,CAAC8H,gBAAgB,CAAC1H,MAAM;MACtC,CAAC;MAEDU,WAAW,CAACmB,KAAI,GAAI,KAAI;IAC1B;;IAEA;IACA3C,KAAK,CAAC,MAAMa,KAAK,CAACC,MAAM,EAAG2H,SAAS,IAAK;MACvC,IAAIA,SAAS,EAAE;QACbH,OAAO,CAAC;QACRL,6BAA6B,CAAC;MAChC;IACF,CAAC;;IAED;IACAnI,SAAS,CAAC,MAAM;MACd,IAAIe,KAAK,CAACC,MAAK,IAAKD,KAAK,CAACK,SAAS,EAAE;QACnC+G,6BAA6B,CAAC;MAChC;IACF,CAAC;IAEDlI,WAAW,CAAC,MAAM;MAChBuI,OAAO,CAAC;IACV,CAAC;;IAED;IACA,MAAMI,cAAa,GAAIA,CAAA,KAAM;MAC3BT,6BAA6B,CAAC;IAChC;IAEA,MAAMU,aAAY,GAAIA,CAAA,KAAM;MAC1BL,OAAO,CAAC;IACV;IAEA,OAAO;MACL;MACA9G,WAAW;MACXC,cAAc;MACdE,YAAY;MACZC,WAAW;MACXC,WAAW;MACXC,UAAU;MACVC,UAAU;MACVC,cAAc;MAEd;MACAU,oBAAoB;MACpBE,oBAAoB;MACpBC,iBAAiB;MACjBM,eAAe;MACfS,cAAc;MAEd;MACAC,kBAAkB;MAClBE,sBAAsB;MACtBE,sBAAsB;MACtBE,mBAAmB;MACnBqB,gBAAgB;MAChBU,gBAAgB;MAChBR,mBAAmB;MACnBE,iBAAiB;MACjBiB,iBAAiB;MACjBG,0BAA0B;MAC1BG,kBAAkB;MAClBE,kBAAkB;MAClBC,kBAAkB;MAClBE,qBAAqB;MACrBI,eAAe;MACfU,OAAO;MACPI,cAAc;MACdC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}