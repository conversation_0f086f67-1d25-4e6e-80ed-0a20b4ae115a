# 四方向实时检测快速开始指南

## 🚀 快速开始

### 第一步：上传四方向视频

1. **访问上传页面**
   ```
   http://localhost:5173/four-way-upload
   ```

2. **准备视频文件**
   - 东向视频：从西向东行驶的车辆
   - 南向视频：从北向南行驶的车辆  
   - 西向视频：从东向西行驶的车辆
   - 北向视频：从南向北行驶的车辆

3. **上传视频**
   - 分别选择四个方向的视频文件
   - 点击"开始上传"按钮
   - **记录返回的任务ID**（重要！）

### 第二步：开始实时检测

1. **访问实时检测页面**
   ```
   http://localhost:5173/four-way-realtime
   ```

2. **输入任务ID**
   - 在"任务ID"输入框中输入第一步获得的任务ID
   - 点击"开始检测"按钮

3. **查看实时效果**
   - 四个方向的检测画面会同时显示
   - 每检测到车辆就会更新对应方向的图像
   - 查看实时统计信息

## 📱 页面布局说明

```
┌─────────────────────────────────────────┐
│           任务选择区域                    │
│  [任务ID输入框] [开始检测按钮]             │
└─────────────────────────────────────────┘

┌─────────┬─────────┬─────────┐
│         │  北向   │         │
│         │ 检测区  │         │
├─────────┼─────────┼─────────┤
│  西向   │ 十字路口 │  东向   │
│ 检测区  │  中心   │ 检测区  │
├─────────┼─────────┼─────────┤
│         │  南向   │         │
│         │ 检测区  │         │
└─────────┴─────────┴─────────┘

┌─────────────────────────────────────────┐
│              全局统计信息                │
│  总车辆数 | 最繁忙方向 | 平均速度 | 时长   │
└─────────────────────────────────────────┘
```

## 🔧 测试功能

如果想测试功能，可以访问测试页面：
```
http://localhost:5173/four-way-test
```

测试页面提供：
- 模拟任务ID测试
- 连接状态监控
- 详细的调试日志
- 手动控制开始/停止

## 📊 实时信息说明

### 连接状态指示器
- 🟢 **已连接**：WebSocket连接正常，可以接收实时数据
- 🔴 **未连接**：连接异常，请检查网络或重新开始检测

### 方向状态标签
- 🔵 **等待中**：该方向还未开始检测
- 🟡 **检测中**：该方向正在进行车辆检测
- 🟢 **已完成**：该方向检测已完成
- 🔴 **检测失败**：该方向检测出现错误

### 统计信息
- **车辆数**：该方向检测到的车辆总数
- **帧率**：该方向的检测帧率（帧/秒）
- **总车辆**：四个方向的车辆总数
- **最繁忙方向**：车辆数最多的方向

## ⚠️ 注意事项

### 视频要求
- **格式**：支持 MP4、AVI、MOV
- **大小**：单个文件最大 500MB
- **质量**：建议 720p 或更高分辨率
- **时长**：四个方向的视频应为同一时间段

### 网络要求
- **稳定连接**：需要稳定的网络连接
- **WebSocket支持**：确保浏览器支持WebSocket
- **带宽**：实时传输需要足够的网络带宽

### 浏览器要求
- **推荐浏览器**：Chrome、Firefox、Edge
- **JavaScript**：必须启用JavaScript
- **现代浏览器**：支持ES6+语法

## 🐛 常见问题

### Q: 显示"未找到任务"
**A:** 检查任务ID是否正确，确认视频上传是否成功

### Q: 连接状态一直显示"未连接"
**A:** 
1. 检查网络连接
2. 确认后端服务正在运行
3. 刷新页面重试

### Q: 没有实时数据更新
**A:**
1. 确认Python模型服务正在运行（端口5000）
2. 检查视频分析是否已开始
3. 查看浏览器控制台是否有错误

### Q: 页面显示异常
**A:**
1. 刷新页面
2. 清除浏览器缓存
3. 检查浏览器兼容性

## 🔍 调试技巧

### 浏览器控制台命令
```javascript
// 检查WebSocket连接状态
window.stompService.connected

// 查看帧缓冲状态
window.stompService.getFourWayFrameStats('your-task-id')

// 启用详细日志（如果需要）
enableStyleLogs()
```

### 查看网络请求
1. 打开浏览器开发者工具
2. 切换到"Network"标签
3. 查看WebSocket连接状态
4. 检查API请求是否成功

## 📞 获取帮助

如果遇到问题：
1. 查看浏览器控制台错误信息
2. 检查网络连接状态
3. 确认所有服务正在运行
4. 使用测试页面进行诊断

## 🎯 下一步

成功使用四方向实时检测后，您可以：
1. 查看完整的分析报告
2. 导出分析数据
3. 生成交通优化建议
4. 进行历史数据对比分析
