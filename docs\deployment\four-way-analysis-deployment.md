# 四方向智能交通分析系统部署指南

## 概述

本文档详细介绍了四方向智能交通分析系统的部署流程，包括环境准备、服务配置、系统启动和监控等内容。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │    │  后端 (Spring)   │    │  AI服务 (Python) │
│   Port: 3000    │────│   Port: 8080    │────│   Port: 5000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │  数据库 (MongoDB) │
                       │   Port: 27017   │
                       └─────────────────┘
```

## 环境要求

### 硬件要求

**最低配置**:
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 100GB SSD
- 网络: 100Mbps

**推荐配置**:
- CPU: 8核心或更多
- 内存: 16GB RAM或更多
- 存储: 500GB SSD
- 网络: 1Gbps
- GPU: NVIDIA GTX 1060或更高（可选，用于AI加速）

### 软件要求

- **操作系统**: Ubuntu 20.04 LTS / CentOS 8 / Windows Server 2019
- **Java**: OpenJDK 11 或 Oracle JDK 11
- **Node.js**: v16.x 或更高版本
- **Python**: 3.8 或更高版本
- **MongoDB**: 4.4 或更高版本
- **Docker**: 20.10 或更高版本（可选）

## 部署方式

### 方式一：Docker 部署（推荐）

#### 1. 准备 Docker 环境

```bash
# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. 创建 docker-compose.yml

```yaml
version: '3.8'

services:
  # MongoDB 数据库
  mongodb:
    image: mongo:5.0
    container_name: traffic-mongodb
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
      MONGO_INITDB_DATABASE: traffic_analysis
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - traffic-network

  # Python AI 服务
  ai-service:
    build:
      context: ./python-service
      dockerfile: Dockerfile
    container_name: traffic-ai-service
    restart: always
    ports:
      - "5000:5000"
    environment:
      - MONGODB_URI=*****************************************************************
      - MODEL_PATH=/app/models
    volumes:
      - ./models:/app/models
      - ./temp:/app/temp
    depends_on:
      - mongodb
    networks:
      - traffic-network

  # Spring Boot 后端
  backend:
    build:
      context: ./trafficsystem
      dockerfile: Dockerfile
    container_name: traffic-backend
    restart: always
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - SPRING_DATA_MONGODB_URI=*****************************************************************
      - AI_SERVICE_URL=http://ai-service:5000
    depends_on:
      - mongodb
      - ai-service
    networks:
      - traffic-network

  # Vue.js 前端
  frontend:
    build:
      context: ./nvm/trafficsystem
      dockerfile: Dockerfile
    container_name: traffic-frontend
    restart: always
    ports:
      - "80:80"
    environment:
      - VUE_APP_API_BASE_URL=http://localhost:8080
    depends_on:
      - backend
    networks:
      - traffic-network

volumes:
  mongodb_data:

networks:
  traffic-network:
    driver: bridge
```

#### 3. 创建初始化脚本

创建 `init-mongo.js`:

```javascript
// 创建数据库用户
db = db.getSiblingDB('traffic_analysis');

db.createUser({
  user: 'trafficDbUser',
  pwd: 'traffic123',
  roles: [
    {
      role: 'readWrite',
      db: 'traffic_analysis'
    }
  ]
});

// 创建集合
db.createCollection('intersection_analysis_four_way');
db.createCollection('video_analysis');
db.createCollection('users');

// 创建索引
db.intersection_analysis_four_way.createIndex({ "taskId": 1 }, { unique: true });
db.intersection_analysis_four_way.createIndex({ "userId": 1 });
db.intersection_analysis_four_way.createIndex({ "createdAt": 1 });
db.intersection_analysis_four_way.createIndex({ "status": 1 });

print('Database initialized successfully');
```

#### 4. 启动服务

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 方式二：手动部署

#### 1. 部署 MongoDB

```bash
# Ubuntu/Debian
wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# 启动 MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# 创建数据库和用户
mongo --eval "
use traffic_analysis;
db.createUser({
  user: 'trafficDbUser',
  pwd: 'traffic123',
  roles: [{role: 'readWrite', db: 'traffic_analysis'}]
});
"
```

#### 2. 部署 Python AI 服务

```bash
# 创建虚拟环境
cd python-service
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 下载模型文件
mkdir -p models
# 将 YOLOv12x 模型文件放入 models 目录

# 配置环境变量
export MONGODB_URI="*******************************************************************"
export MODEL_PATH="./models"

# 启动服务
python model_api.py
```

#### 3. 部署 Spring Boot 后端

```bash
cd trafficsystem

# 编译项目
./mvnw clean package -DskipTests

# 配置 application-production.yml
cp src/main/resources/application-performance.yml src/main/resources/application-production.yml

# 启动服务
java -jar -Dspring.profiles.active=production target/trafficsystem-1.0.0.jar
```

#### 4. 部署 Vue.js 前端

```bash
cd nvm/trafficsystem

# 安装依赖
npm install

# 构建生产版本
npm run build

# 使用 Nginx 部署
sudo cp -r dist/* /var/www/html/

# 配置 Nginx
sudo tee /etc/nginx/sites-available/traffic-analysis << EOF
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    index index.html;

    location / {
        try_files \$uri \$uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /ws {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

sudo ln -s /etc/nginx/sites-available/traffic-analysis /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 配置文件

### 生产环境配置

创建 `application-production.yml`:

```yaml
server:
  port: 8080
  servlet:
    context-path: /

spring:
  data:
    mongodb:
      uri: *******************************************************************
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 2GB

# AI 服务配置
ai:
  service:
    url: http://localhost:5000
    timeout: 300000

# 日志配置
logging:
  level:
    com.traffic.analysis: INFO
  file:
    name: logs/traffic-analysis.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 四方向分析配置
four-way-analysis:
  video:
    max-concurrent-processing: 4
    max-processing-time: 30
    temp-file-cleanup-interval: 2
  websocket:
    max-connections: 500
    heartbeat-interval: 30
```

### 前端环境配置

创建 `.env.production`:

```bash
VUE_APP_API_BASE_URL=http://your-domain.com
VUE_APP_WS_URL=ws://your-domain.com/ws
VUE_APP_UPLOAD_MAX_SIZE=524288000
VUE_APP_SUPPORTED_FORMATS=mp4,avi,mov
```

## 监控和维护

### 1. 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/actuator/health
curl http://localhost:5000/health

# 检查数据库连接
mongo --eval "db.adminCommand('ping')"
```

### 2. 日志监控

```bash
# 查看应用日志
tail -f logs/traffic-analysis.log

# 查看 AI 服务日志
tail -f python-service/logs/model_api.log

# 查看 MongoDB 日志
sudo tail -f /var/log/mongodb/mongod.log
```

### 3. 性能监控

```bash
# 系统资源监控
htop
iostat -x 1
free -h

# 数据库性能
mongo --eval "db.serverStatus()"
```

### 4. 备份策略

```bash
# 数据库备份
mongodump --uri="*******************************************************************" --out=/backup/$(date +%Y%m%d)

# 自动备份脚本
cat > /etc/cron.daily/mongodb-backup << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/mongodb"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR
mongodump --uri="*******************************************************************" --out=$BACKUP_DIR/$DATE
find $BACKUP_DIR -type d -mtime +7 -exec rm -rf {} +
EOF

chmod +x /etc/cron.daily/mongodb-backup
```

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口是否被占用
   - 验证配置文件格式
   - 查看详细错误日志

2. **数据库连接失败**
   - 确认 MongoDB 服务运行状态
   - 检查用户名密码是否正确
   - 验证网络连接

3. **AI 服务不可用**
   - 检查 Python 环境和依赖
   - 确认模型文件是否存在
   - 查看 GPU 驱动状态（如果使用）

4. **前端页面无法访问**
   - 检查 Nginx 配置
   - 验证静态文件路径
   - 确认代理设置正确

### 性能优化

1. **数据库优化**
   - 创建适当的索引
   - 定期清理过期数据
   - 配置合适的连接池

2. **应用优化**
   - 调整 JVM 参数
   - 配置缓存策略
   - 优化线程池设置

3. **系统优化**
   - 调整文件描述符限制
   - 配置系统缓存
   - 优化网络参数

## 安全配置

### 1. 网络安全

```bash
# 防火墙配置
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 27017/tcp
sudo ufw deny 5000/tcp
sudo ufw enable
```

### 2. SSL/TLS 配置

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 其他配置...
}
```

### 3. 数据库安全

```javascript
// 启用认证
use admin;
db.createUser({
  user: "admin",
  pwd: "strong-password",
  roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase"]
});
```

---

*部署完成后，请及时更新系统和依赖包，定期检查安全更新。*
