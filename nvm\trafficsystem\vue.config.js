const { defineConfig } = require('@vue/cli-service')
const webpack = require('webpack')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 开发服务器配置
  devServer: {
    port: 5173,
    // 配置代理，解决开发过程中的跨域问题
    proxy: process.env.VUE_APP_ENABLE_PROXY !== 'false' ? {
      '/api/media/video': {
        target: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080', // 后端服务器地址
        changeOrigin: true,
        secure: false,
        // 处理代理时的错误
        onError: (err, req, res) => {
          res.writeHead(500, {
            'Content-Type': 'text/plain'
          });
          res.end('媒体代理请求错误');
        }
      },
      '/api': {
        target: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080', // 后端服务器地址
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api' // 保持api路径
        },
        // 处理代理时的错误
        onError: (err, req, res) => {
          if (err.code === 'ECONNREFUSED') {
            res.writeHead(504, {
              'Content-Type': 'application/json'
            });
            res.end(JSON.stringify({
              status: 'error',
              message: '无法连接到后端服务器'
            }));
          } else {
            res.writeHead(500, {
              'Content-Type': 'text/plain'
            });
            res.end('代理请求错误');
          }
        },
        // 关闭HTTPS验证
        secure: false,
        // 尝试重连次数
        retry: 3
      },
      '/auth': {
        target: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080',
        changeOrigin: true,
        pathRewrite: {
          '^/auth': '/api/auth'
        }
      }
    } : undefined,
    client: {
      overlay: {
        runtimeErrors: (error) => {
          return !error.message?.includes('ResizeObserver loop');
        }
      }
    }
  },
  
  configureWebpack: {
    resolve: {
      fallback: {
        path: require.resolve('path-browserify')
      }
    },
    plugins: [
      new webpack.DefinePlugin({
        __VUE_PROD_DEVTOOLS__: false,
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
      })
    ]
  },
  
  // 禁用eslint检查
  lintOnSave: false,
  
  // 生产环境配置
  productionSourceMap: false
})
