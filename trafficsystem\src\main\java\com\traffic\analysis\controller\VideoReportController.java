package com.traffic.analysis.controller;

import com.traffic.analysis.model.VideoAnalysis;
import com.traffic.analysis.model.FourWayIntersectionAnalysis;
import com.traffic.analysis.model.Direction;
import com.traffic.analysis.model.DirectionVideoData;
import com.traffic.analysis.model.TrafficAnalysisResult;
import com.traffic.analysis.service.VideoAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;
import java.util.Optional;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 视频报告控制器
 * 用于处理视频分析报告的访问
 */
@Controller
@RequestMapping("/api/video-analysis")
public class VideoReportController {
    
    private static final Logger log = LoggerFactory.getLogger(VideoReportController.class);
    
    @Autowired
    private VideoAnalysisService videoAnalysisService;

    /**
     * 获取视频分析报告
     * 返回HTML格式的报告，设置允许嵌入iframe的选项
     */
    @GetMapping("/{taskId}/report")
    public ResponseEntity<String> getVideoAnalysisReport(@PathVariable String taskId) {
        try {
            log.info("请求视频分析报告: {}", taskId);
            
            // 获取分析结果
            Optional<VideoAnalysis> taskOpt = videoAnalysisService.findByTaskId(taskId);
            
            if (!taskOpt.isPresent()) {
                log.warn("未找到视频分析任务: {}", taskId);
                return ResponseEntity.notFound().build();
            }
            
            VideoAnalysis task = taskOpt.get();
            
            // 获取分析结果数据
            Map<String, Object> resultData = videoAnalysisService.getVideoAnalysisResult(taskId);
            
            if (resultData == null || resultData.isEmpty() || resultData.containsKey("error")) {
                log.warn("获取视频分析结果失败: {}", resultData != null && resultData.containsKey("error") ? resultData.get("error") : "结果为空");
                return ResponseEntity.badRequest().body("获取分析结果失败");
            }
            
            // 生成HTML报告
            String htmlReport = generateHtmlReport(task, resultData);
            
            // 设置HTTP头，允许在iframe中嵌入
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_HTML);
            headers.set("X-Frame-Options", "SAMEORIGIN");
            headers.set("Content-Security-Policy", "frame-ancestors 'self' http://localhost:* http://127.0.0.1:*");
            
            return ResponseEntity.ok()
                .headers(headers)
                .body(htmlReport);
                
        } catch (Exception e) {
            log.error("获取视频分析报告时出错: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body("处理分析报告时出错");
        }
    }
    
    /**
     * 生成HTML格式的分析报告
     */
    private String generateHtmlReport(VideoAnalysis task, Map<String, Object> resultData) {
        
        // 获取车辆类型统计
        Map<String, Integer> vehicleTypeStats = task.getVehicleTypeStats();
        if (vehicleTypeStats == null) {
            vehicleTypeStats = new HashMap<>();
        }
        
        int carCount = vehicleTypeStats.getOrDefault("car", 0);
        int truckCount = vehicleTypeStats.getOrDefault("truck", 0);
        int busCount = vehicleTypeStats.getOrDefault("bus", 0);
        int motorcycleCount = vehicleTypeStats.getOrDefault("motorcycle", 0);
        int totalVehicles = task.getVehicleCount();
        
        // 创建HTML报告
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html lang='zh-CN'>");
        html.append("<head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<meta name='viewport' content='width=device-width, initial-scale=1.0'>");
        html.append("<title>视频分析报告</title>");
        html.append("<style>");
        html.append("body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; color: #333; }");
        html.append(".container { max-width: 800px; margin: 0 auto; }");
        html.append(".header { padding: 20px 0; border-bottom: 1px solid #eee; }");
        html.append(".header h1 { margin: 0; color: #2c3e50; }");
        html.append(".meta-info { color: #7f8c8d; font-size: 14px; margin-top: 5px; }");
        html.append(".section { margin: 30px 0; }");
        html.append(".section h2 { color: #3498db; border-bottom: 2px solid #f2f2f2; padding-bottom: 10px; }");
        html.append(".stats-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; }");
        html.append(".stat-card { background: #f8f9fa; border-radius: 8px; padding: 15px; text-align: center; border-left: 4px solid #3498db; }");
        html.append(".stat-value { font-size: 32px; font-weight: bold; color: #2c3e50; margin: 10px 0; }");
        html.append(".stat-label { color: #7f8c8d; text-transform: uppercase; font-size: 12px; letter-spacing: 1px; }");
        html.append(".chart-container { margin: 20px 0; height: 300px; }");
        html.append(".footer { margin-top: 40px; text-align: center; color: #95a5a6; font-size: 12px; }");
        html.append("</style>");
        html.append("</head>");
        html.append("<body>");
        html.append("<div class='container'>");
        
        // 标题和元信息
        html.append("<div class='header'>");
        html.append("<h1>视频分析报告</h1>");
        html.append("<div class='meta-info'>");
        html.append("任务ID: ").append(task.getTaskId()).append("<br>");
        html.append("创建时间: ").append(task.getCreatedAt()).append("<br>");
        html.append("完成时间: ").append(task.getCompletedAt()).append("<br>");
        html.append("处理时间: ").append(task.getProcessingTime()).append(" 秒");
        html.append("</div>"); // meta-info end
        html.append("</div>"); // header end
        
        // 分析摘要
        html.append("<div class='section'>");
        html.append("<h2>分析摘要</h2>");
        html.append("<div class='stats-grid'>");
        html.append("<div class='stat-card'>");
        html.append("<div class='stat-label'>总车辆数</div>");
        html.append("<div class='stat-value'>").append(totalVehicles).append("</div>");
        html.append("</div>");
        html.append("<div class='stat-card'>");
        html.append("<div class='stat-label'>小汽车</div>");
        html.append("<div class='stat-value'>").append(carCount).append("</div>");
        html.append("</div>");
        html.append("<div class='stat-card'>");
        html.append("<div class='stat-label'>卡车</div>");
        html.append("<div class='stat-value'>").append(truckCount).append("</div>");
        html.append("</div>");
        html.append("<div class='stat-card'>");
        html.append("<div class='stat-label'>公交车</div>");
        html.append("<div class='stat-value'>").append(busCount).append("</div>");
        html.append("</div>");
        html.append("<div class='stat-card'>");
        html.append("<div class='stat-label'>摩托车</div>");
        html.append("<div class='stat-value'>").append(motorcycleCount).append("</div>");
        html.append("</div>");
        html.append("</div>"); // stats-grid end
        html.append("</div>"); // section end
        
        // 结果图
        if (task.getThumbnailUrl() != null && !task.getThumbnailUrl().isEmpty()) {
            String thumbnailUrl = task.getThumbnailUrl();
            if (thumbnailUrl.matches("[0-9a-f]{24}")) {
                thumbnailUrl = "/api/media/image/" + thumbnailUrl;
            }
            
            html.append("<div class='section'>");
            html.append("<h2>结果截图</h2>");
            html.append("<img src='").append(thumbnailUrl).append("' style='max-width:100%; border-radius:8px;'>");
            html.append("</div>");
        }
        
        // 分析结果视频
        if (task.getResultPath() != null && !task.getResultPath().isEmpty()) {
            String videoUrl = task.getResultPath();
            if (videoUrl.matches("[0-9a-f]{24}")) {
                videoUrl = "/api/media/video/" + videoUrl;
            }
            
            html.append("<div class='section'>");
            html.append("<h2>结果视频</h2>");
            html.append("<video controls style='max-width:100%; border-radius:8px;'>");
            html.append("<source src='").append(videoUrl).append("' type='video/mp4'>");
            html.append("您的浏览器不支持视频标签");
            html.append("</video>");
            html.append("</div>");
        }
        
        // 页脚
        html.append("<div class='footer'>");
        html.append("本报告由交通分析系统自动生成");
        html.append("</div>");
        
        html.append("</div>"); // container end
        html.append("</body>");
        html.append("</html>");
        
        return html.toString();
    }

    // ==================== 四方向智能交通分析报告 ====================

    /**
     * 获取四方向智能交通分析报告
     * 返回HTML格式的报告
     */
    @GetMapping("/four-way/{taskId}/html-report")
    public ResponseEntity<String> getFourWayTrafficAnalysisReport(@PathVariable String taskId) {
        try {
            log.info("请求四方向智能交通分析报告: {}", taskId);

            // 获取四方向分析结果
            Optional<FourWayIntersectionAnalysis> taskOpt = videoAnalysisService.findFourWayAnalysisByTaskId(taskId);

            if (!taskOpt.isPresent()) {
                log.warn("未找到四方向分析任务: {}", taskId);
                return ResponseEntity.notFound().build();
            }

            FourWayIntersectionAnalysis task = taskOpt.get();

            // 检查任务是否已完成
            if (!"completed".equals(task.getStatus())) {
                log.warn("四方向分析任务尚未完成: {}, 状态: {}", taskId, task.getStatus());
                return ResponseEntity.badRequest().body("分析任务尚未完成，无法生成报告");
            }

            // 获取分析结果数据
            Map<String, Object> resultData = videoAnalysisService.getFourWayAnalysisResult(taskId);

            if (resultData == null || resultData.isEmpty()) {
                log.warn("获取四方向分析结果失败: 结果为空");
                return ResponseEntity.badRequest().body("获取分析结果失败");
            }

            // 生成HTML报告
            String htmlReport = generateFourWayHtmlReport(task, resultData);

            // 设置HTTP头，允许在iframe中嵌入
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_HTML);
            headers.set("X-Frame-Options", "SAMEORIGIN");
            headers.set("Content-Security-Policy", "frame-ancestors 'self' http://localhost:* http://127.0.0.1:*");

            return ResponseEntity.ok()
                .headers(headers)
                .body(htmlReport);

        } catch (Exception e) {
            log.error("获取四方向智能交通分析报告时出错: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body("处理分析报告时出错");
        }
    }

    /**
     * 生成四方向智能交通分析HTML报告
     */
    private String generateFourWayHtmlReport(FourWayIntersectionAnalysis task, Map<String, Object> resultData) {

        // 获取基本统计信息
        int totalVehicles = task.getTotalVehicleCount();
        TrafficAnalysisResult trafficAnalysis = task.getTrafficAnalysis();

        // 获取各方向数据
        Map<Direction, DirectionVideoData> directions = task.getDirections();

        // 创建HTML报告
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html lang='zh-CN'>");
        html.append("<head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<meta name='viewport' content='width=device-width, initial-scale=1.0'>");
        html.append("<title>四方向智能交通分析报告</title>");
        html.append("<style>");

        // CSS样式
        html.append("body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; color: #333; background: #f8f9fa; }");
        html.append(".container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }");
        html.append(".header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }");
        html.append(".header h1 { margin: 0; font-size: 28px; }");
        html.append(".header .subtitle { font-size: 16px; opacity: 0.9; margin-top: 8px; }");
        html.append(".meta-info { background: rgba(255,255,255,0.1); border-radius: 6px; padding: 15px; margin-top: 20px; }");
        html.append(".meta-row { display: flex; justify-content: space-between; margin-bottom: 8px; }");
        html.append(".meta-row:last-child { margin-bottom: 0; }");
        html.append(".content { padding: 30px; }");
        html.append(".section { margin: 40px 0; }");
        html.append(".section h2 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; margin-bottom: 25px; font-size: 22px; }");
        html.append(".overview-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }");
        html.append(".overview-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border-radius: 10px; padding: 20px; text-align: center; }");
        html.append(".overview-card.primary { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }");
        html.append(".overview-card.success { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }");
        html.append(".overview-card.warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }");
        html.append(".overview-card.info { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #2c3e50; }");
        html.append(".card-value { font-size: 36px; font-weight: bold; margin: 10px 0; }");
        html.append(".card-label { font-size: 14px; opacity: 0.9; text-transform: uppercase; letter-spacing: 1px; }");
        html.append(".directions-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin: 20px 0; }");
        html.append(".direction-card { border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; background: #f8f9fa; }");
        html.append(".direction-header { display: flex; align-items: center; margin-bottom: 15px; }");
        html.append(".direction-icon { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 12px; }");
        html.append(".direction-icon.east { background: #409eff; }");
        html.append(".direction-icon.south { background: #67c23a; }");
        html.append(".direction-icon.west { background: #e6a23c; }");
        html.append(".direction-icon.north { background: #f56c6c; }");
        html.append(".direction-name { font-size: 18px; font-weight: 600; color: #2c3e50; }");
        html.append(".direction-stats { display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; }");
        html.append(".stat-item { text-align: center; padding: 10px; background: white; border-radius: 6px; }");
        html.append(".stat-value { font-size: 20px; font-weight: bold; color: #2c3e50; }");
        html.append(".stat-label { font-size: 12px; color: #7f8c8d; margin-top: 4px; }");
        html.append(".analysis-section { background: #f8f9fa; border-radius: 8px; padding: 25px; margin: 20px 0; }");
        html.append(".analysis-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }");
        html.append(".analysis-item { background: white; border-radius: 6px; padding: 15px; border-left: 4px solid #3498db; }");
        html.append(".analysis-title { font-weight: 600; color: #2c3e50; margin-bottom: 8px; }");
        html.append(".analysis-value { font-size: 18px; color: #3498db; font-weight: bold; }");
        html.append(".recommendations { background: #e8f5e8; border-radius: 8px; padding: 20px; margin: 20px 0; }");
        html.append(".recommendations h3 { color: #27ae60; margin-top: 0; }");
        html.append(".recommendation-list { list-style: none; padding: 0; }");
        html.append(".recommendation-item { background: white; margin: 10px 0; padding: 15px; border-radius: 6px; border-left: 4px solid #27ae60; }");
        html.append(".footer { text-align: center; padding: 20px; color: #7f8c8d; border-top: 1px solid #e9ecef; margin-top: 40px; }");
        html.append("</style>");
        html.append("</head>");
        html.append("<body>");
        html.append("<div class='container'>");

        // 标题和元信息
        html.append("<div class='header'>");
        html.append("<h1>四方向智能交通分析报告</h1>");
        html.append("<div class='subtitle'>基于AI视觉识别的十字路口交通流量智能分析</div>");
        html.append("<div class='meta-info'>");
        html.append("<div class='meta-row'>");
        html.append("<span>任务ID:</span><span>").append(task.getTaskId()).append("</span>");
        html.append("</div>");
        html.append("<div class='meta-row'>");
        html.append("<span>分析时间:</span><span>").append(task.getCreatedAt()).append("</span>");
        html.append("</div>");
        html.append("<div class='meta-row'>");
        html.append("<span>完成时间:</span><span>").append(task.getUpdatedAt()).append("</span>");
        html.append("</div>");
        html.append("<div class='meta-row'>");
        html.append("<span>处理耗时:</span><span>").append(task.getProcessingDurationSeconds()).append(" 秒</span>");
        html.append("</div>");
        html.append("</div>"); // meta-info end
        html.append("</div>"); // header end

        html.append("<div class='content'>");

        // 分析概览
        html.append("<div class='section'>");
        html.append("<h2>📊 分析概览</h2>");
        html.append("<div class='overview-grid'>");

        html.append("<div class='overview-card primary'>");
        html.append("<div class='card-label'>总车辆数</div>");
        html.append("<div class='card-value'>").append(totalVehicles).append("</div>");
        html.append("</div>");

        if (trafficAnalysis != null) {
            html.append("<div class='overview-card success'>");
            html.append("<div class='card-label'>车流量最大方向</div>");
            html.append("<div class='card-value'>").append(getDirectionDisplayName(trafficAnalysis.getPeakDirection())).append("</div>");
            html.append("</div>");

            html.append("<div class='overview-card warning'>");
            html.append("<div class='card-label'>流量平衡度</div>");
            html.append("<div class='card-value'>").append(String.format("%.1f%%", trafficAnalysis.getTrafficFlowBalance() * 100)).append("</div>");
            html.append("</div>");

            html.append("<div class='overview-card info'>");
            html.append("<div class='card-label'>拥堵等级</div>");
            html.append("<div class='card-value'>").append(trafficAnalysis.getCongestionLevel()).append("</div>");
            html.append("</div>");
        }

        html.append("</div>"); // overview-grid end
        html.append("</div>"); // section end

        // 各方向详细数据
        html.append("<div class='section'>");
        html.append("<h2>🚗 各方向车流量详情</h2>");
        html.append("<div class='directions-grid'>");

        for (Direction direction : Direction.values()) {
            DirectionVideoData directionData = directions.get(direction);
            if (directionData != null) {
                String directionClass = direction.getCode();
                String directionName = getDirectionDisplayName(direction);

                html.append("<div class='direction-card'>");
                html.append("<div class='direction-header'>");
                html.append("<div class='direction-icon ").append(directionClass).append("'>");
                html.append(directionName.substring(0, 1));
                html.append("</div>");
                html.append("<div class='direction-name'>").append(directionName).append("</div>");
                html.append("</div>");

                html.append("<div class='direction-stats'>");
                html.append("<div class='stat-item'>");
                html.append("<div class='stat-value'>").append(directionData.getVehicleCount()).append("</div>");
                html.append("<div class='stat-label'>车辆总数</div>");
                html.append("</div>");
                html.append("<div class='stat-item'>");
                html.append("<div class='stat-value'>").append(directionData.getCrowdLevel() != null ? directionData.getCrowdLevel() : "未知").append("</div>");
                html.append("<div class='stat-label'>拥堵等级</div>");
                html.append("</div>");
                html.append("<div class='stat-item'>");
                html.append("<div class='stat-value'>").append(String.format("%.1f", directionData.getAverageFlowDensity())).append("</div>");
                html.append("<div class='stat-label'>车流密度</div>");
                html.append("</div>");
                html.append("</div>"); // direction-stats end
                html.append("</div>"); // direction-card end
            }
        }

        html.append("</div>"); // directions-grid end
        html.append("</div>"); // section end

        // 智能分析结果
        if (trafficAnalysis != null) {
            html.append("<div class='section'>");
            html.append("<h2>🧠 智能分析结果</h2>");
            html.append("<div class='analysis-section'>");
            html.append("<div class='analysis-grid'>");

            html.append("<div class='analysis-item'>");
            html.append("<div class='analysis-title'>交通流量平衡度</div>");
            html.append("<div class='analysis-value'>").append(String.format("%.1f%%", trafficAnalysis.getTrafficFlowBalance() * 100)).append("</div>");
            html.append("</div>");

            html.append("<div class='analysis-item'>");
            html.append("<div class='analysis-title'>整体拥堵等级</div>");
            html.append("<div class='analysis-value'>").append(trafficAnalysis.getCongestionLevel()).append("</div>");
            html.append("</div>");

            if (trafficAnalysis.getSignalOptimization() != null) {
                html.append("<div class='analysis-item'>");
                html.append("<div class='analysis-title'>推荐信号周期</div>");
                html.append("<div class='analysis-value'>").append(trafficAnalysis.getSignalOptimization().getRecommendedCycle()).append(" 秒</div>");
                html.append("</div>");

                html.append("<div class='analysis-item'>");
                html.append("<div class='analysis-title'>预期改善效果</div>");
                html.append("<div class='analysis-value'>").append(trafficAnalysis.getSignalOptimization().getExpectedImprovement()).append("</div>");
                html.append("</div>");
            }

            html.append("</div>"); // analysis-grid end
            html.append("</div>"); // analysis-section end
            html.append("</div>"); // section end
        }

        // 改进建议
        if (task.getReportData() != null && task.getReportData().getRecommendations() != null && !task.getReportData().getRecommendations().isEmpty()) {
            html.append("<div class='section'>");
            html.append("<div class='recommendations'>");
            html.append("<h3>💡 智能改进建议</h3>");
            html.append("<ul class='recommendation-list'>");

            for (String recommendation : task.getReportData().getRecommendations()) {
                html.append("<li class='recommendation-item'>").append(recommendation).append("</li>");
            }

            html.append("</ul>");
            html.append("</div>");
            html.append("</div>");
        }

        // 页脚
        html.append("<div class='footer'>");
        html.append("本报告由四方向智能交通分析系统自动生成 | 基于AI视觉识别技术");
        html.append("</div>");

        html.append("</div>"); // content end
        html.append("</div>"); // container end
        html.append("</body>");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 获取方向的显示名称
     */
    private String getDirectionDisplayName(Direction direction) {
        if (direction == null) return "未知";
        return direction.getDisplayName();
    }
}