package com.traffic.analysis.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.traffic.analysis.model.FourWayIntersectionAnalysis;
import com.traffic.analysis.service.VideoAnalysisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 四方向历史记录功能集成测试
 */
@SpringBootTest
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class FourWayHistoryIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private VideoAnalysisService videoAnalysisService;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    /**
     * 测试获取四方向任务列表API
     */
    @Test
    void testGetFourWayTaskListAPI() throws Exception {
        // 测试基本的任务列表获取
        mockMvc.perform(get("/api/video-analysis/four-way/tasks")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized()); // 预期未授权，因为没有认证信息
    }

    /**
     * 测试带筛选参数的任务列表API
     */
    @Test
    void testGetFourWayTaskListWithFilters() throws Exception {
        // 测试带筛选参数的任务列表获取
        mockMvc.perform(get("/api/video-analysis/four-way/tasks")
                .param("page", "0")
                .param("size", "10")
                .param("search", "test")
                .param("status", "completed")
                .param("sort", "created_at,desc")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized()); // 预期未授权
    }

    /**
     * 测试批量删除API
     */
    @Test
    void testBatchDeleteAPI() throws Exception {
        Map<String, Object> request = new HashMap<>();
        request.put("taskIds", Arrays.asList("task1", "task2", "task3"));

        mockMvc.perform(post("/api/video-analysis/four-way/batch-delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized()); // 预期未授权
    }

    /**
     * 测试单个任务删除API
     */
    @Test
    void testDeleteSingleTaskAPI() throws Exception {
        mockMvc.perform(delete("/api/video-analysis/four-way/test-task-id")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized()); // 预期未授权
    }

    /**
     * 测试重试分析API
     */
    @Test
    void testRetryAnalysisAPI() throws Exception {
        mockMvc.perform(post("/api/video-analysis/four-way/test-task-id/retry")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized()); // 预期未授权
    }

    /**
     * 测试服务层的筛选功能
     */
    @Test
    void testServiceLayerFiltering() {
        // 创建查询参数
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("currentUserId", "test-user");
        queryParams.put("currentUserRole", "user");
        queryParams.put("search", "test");
        queryParams.put("statusFilter", "completed");

        Pageable pageable = PageRequest.of(0, 10);

        // 测试服务层方法
        Page<FourWayIntersectionAnalysis> result = videoAnalysisService.getFourWayTaskListWithFilters(queryParams, pageable);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getTotalElements() >= 0);
        assertTrue(result.getContent().size() <= 10);
    }

    /**
     * 测试批量删除服务层功能
     */
    @Test
    void testServiceLayerBatchDelete() {
        List<String> taskIds = Arrays.asList("non-existent-task-1", "non-existent-task-2");
        
        // 测试普通用户批量删除
        int deletedCount = videoAnalysisService.batchDeleteFourWayTasks(taskIds, "test-user", "user");
        assertEquals(0, deletedCount); // 应该删除0个，因为任务不存在

        // 测试管理员批量删除
        int adminDeletedCount = videoAnalysisService.batchDeleteFourWayTasks(taskIds, "admin-user", "admin");
        assertEquals(0, adminDeletedCount); // 应该删除0个，因为任务不存在
    }

    /**
     * 测试API端点路径正确性
     */
    @Test
    void testAPIEndpointPaths() throws Exception {
        // 测试所有主要端点是否存在（即使返回401也说明端点存在）
        
        // 获取任务列表
        mockMvc.perform(get("/api/video-analysis/four-way/tasks"))
                .andExpect(status().isUnauthorized());

        // 批量删除
        mockMvc.perform(post("/api/video-analysis/four-way/batch-delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isUnauthorized());

        // 单个删除
        mockMvc.perform(delete("/api/video-analysis/four-way/test-id"))
                .andExpect(status().isUnauthorized());

        // 重试分析
        mockMvc.perform(post("/api/video-analysis/four-way/test-id/retry"))
                .andExpect(status().isUnauthorized());
    }

    /**
     * 测试参数验证
     */
    @Test
    void testParameterValidation() throws Exception {
        // 测试无效的分页参数
        mockMvc.perform(get("/api/video-analysis/four-way/tasks")
                .param("page", "-1")
                .param("size", "0"))
                .andExpect(status().isUnauthorized()); // 仍然是未授权，但参数会被处理

        // 测试批量删除空任务列表
        Map<String, Object> emptyRequest = new HashMap<>();
        emptyRequest.put("taskIds", Collections.emptyList());

        mockMvc.perform(post("/api/video-analysis/four-way/batch-delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(emptyRequest)))
                .andExpect(status().isUnauthorized());
    }

    /**
     * 测试排序参数解析
     */
    @Test
    void testSortParameterParsing() throws Exception {
        // 测试不同的排序参数格式
        String[] sortParams = {
            "created_at,desc",
            "created_at,asc", 
            "status,desc",
            "username,asc",
            "invalid_field,desc"
        };

        for (String sortParam : sortParams) {
            mockMvc.perform(get("/api/video-analysis/four-way/tasks")
                    .param("sort", sortParam))
                    .andExpect(status().isUnauthorized()); // 端点存在但未授权
        }
    }

    /**
     * 测试日期筛选参数
     */
    @Test
    void testDateFilterParameters() throws Exception {
        mockMvc.perform(get("/api/video-analysis/four-way/tasks")
                .param("startDate", "2024-01-01")
                .param("endDate", "2024-12-31"))
                .andExpect(status().isUnauthorized());

        // 测试无效日期格式
        mockMvc.perform(get("/api/video-analysis/four-way/tasks")
                .param("startDate", "invalid-date")
                .param("endDate", "2024-12-31"))
                .andExpect(status().isUnauthorized());
    }

    /**
     * 验证响应格式
     */
    @Test
    void testResponseFormat() throws Exception {
        // 虽然会返回401，但我们可以验证Content-Type等基本响应格式
        mockMvc.perform(get("/api/video-analysis/four-way/tasks"))
                .andExpect(status().isUnauthorized())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }
}
