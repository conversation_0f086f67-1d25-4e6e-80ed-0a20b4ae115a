/**
 * 应用全局配置 - 统一配置文件
 */

// 环境判断
const isProduction = process.env.NODE_ENV === 'production';

// 基础API URL配置
export const API_BASE_URL = process.env.VUE_APP_BASE_API || (isProduction ? '' : '/api');

// 后端服务URL配置
export const BACKEND_URL = isProduction ? '' : 'http://localhost:8080';

// 文件上传配置
export const FILE_SIZE_LIMIT = 500 * 1024 * 1024; // 500MB
export const UPLOAD_IMAGE_URL = `${API_BASE_URL}/api/upload/image`;
export const UPLOAD_VIDEO_URL = `${API_BASE_URL}/api/upload/video`;

// 请求超时设置
export const REQUEST_TIMEOUT = 60000; // 60秒

// 存储键名
export const TOKEN_KEY = 'auth_token';
export const USER_INFO_KEY = 'user';

// 默认配置
export const DEFAULT_PAGE_SIZE = 10;
export const DEFAULT_AVATAR = '/img/default-avatar.png';

// WebSocket配置
export const WS_BASE_URL = process.env.NODE_ENV === 'production'
  ? 'ws://api-server.example.com'
  : window.location.protocol === 'https:'
    ? `wss://${window.location.hostname}:${window.location.port || 443}`
    : `ws://${window.location.hostname}:${window.location.port || 8080}`;

// SockJS和STOMP配置
export const SOCKJS_ENDPOINT = process.env.NODE_ENV === 'production'
  ? 'https://api-server.example.com/api/ws'
  : `${window.location.protocol}//${window.location.hostname}:8080/api/ws`;

export const STOMP_TOPIC_PREFIX = '/topic';
export const STOMP_VIDEO_PROGRESS = 'video-progress';

// 视频相关配置
export const VIDEO_CONFIG = {
  defaultPlayMode: 'proxy',
  debug: !isProduction,
  apiPath: '/api/media/video/'
};

// 应用信息
export const APP_NAME = '交通数据分析系统';
export const APP_VERSION = '1.0.0';
export const SYSTEM_NAME = '交通分析系统';

// API路径配置
export const API_PATHS = {
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    REGISTER: '/auth/register',
  },
  USER: {
    PROFILE: '/user/profile',
    CHANGE_PASSWORD: '/user/change-password',
  },
  VIDEO: {
    UPLOAD: '/video-analysis/upload',
    UPLOAD_INTERSECTION: '/video-analysis/upload/intersection',
    STATUS: '/video-analysis/status',
    RESULT: '/video-analysis/result',
    HISTORY: '/video-analysis/history',
  }
} 