/**
 * 全局日志配置管理器
 * 统一管理所有前端脚本的日志输出
 */

// 全局日志配置
window.GLOBAL_LOG_CONFIG = {
  // 主开关 - 设置为 false 可以完全禁用所有样式脚本的日志
  enabled: false,
  
  // 日志级别: 'DEBUG', 'INFO', 'WARN', 'ERROR'
  // DEBUG: 显示所有日志
  // INFO: 显示信息、警告和错误
  // WARN: 只显示警告和错误  
  // ERROR: 只显示错误
  level: 'ERROR',
  
  // 特定功能开关
  showSuccess: false,    // 是否显示成功消息
  showProgress: false,   // 是否显示进度消息
  showStyleLogs: false,  // 是否显示样式相关日志
  showWidthLogs: false,  // 是否显示宽度优化日志
  
  // 防抖配置
  debounceTime: 5000,    // 相同消息的防抖时间（毫秒）
  
  // 开发模式检测
  isDevelopment: window.location.hostname === 'localhost' || 
                 window.location.hostname === '127.0.0.1' ||
                 window.location.port === '8080'
};

// 如果是开发环境，可以启用更多日志
if (window.GLOBAL_LOG_CONFIG.isDevelopment) {
  // 开发环境下可以取消注释以下行来启用日志
  // window.GLOBAL_LOG_CONFIG.enabled = true;
  // window.GLOBAL_LOG_CONFIG.level = 'INFO';
  // window.GLOBAL_LOG_CONFIG.showStyleLogs = true;
}

// 提供控制台命令让用户可以动态调整日志
window.enableStyleLogs = function() {
  window.GLOBAL_LOG_CONFIG.enabled = true;
  window.GLOBAL_LOG_CONFIG.level = 'INFO';
  window.GLOBAL_LOG_CONFIG.showStyleLogs = true;
  window.GLOBAL_LOG_CONFIG.showWidthLogs = true;
  console.log('✅ 样式日志已启用');
};

window.disableStyleLogs = function() {
  window.GLOBAL_LOG_CONFIG.enabled = false;
  console.log('❌ 样式日志已禁用');
};

window.setLogLevel = function(level) {
  const validLevels = ['DEBUG', 'INFO', 'WARN', 'ERROR'];
  if (validLevels.includes(level.toUpperCase())) {
    window.GLOBAL_LOG_CONFIG.level = level.toUpperCase();
    window.GLOBAL_LOG_CONFIG.enabled = true;
    console.log(`📊 日志级别已设置为: ${level.toUpperCase()}`);
  } else {
    console.error('❌ 无效的日志级别。有效值: DEBUG, INFO, WARN, ERROR');
  }
};

// 在控制台显示使用说明
console.log(`
🎛️ 样式日志控制命令:
- enableStyleLogs()  : 启用样式日志
- disableStyleLogs() : 禁用样式日志  
- setLogLevel('INFO'): 设置日志级别

当前状态: ${window.GLOBAL_LOG_CONFIG.enabled ? '启用' : '禁用'}
当前级别: ${window.GLOBAL_LOG_CONFIG.level}
`);
