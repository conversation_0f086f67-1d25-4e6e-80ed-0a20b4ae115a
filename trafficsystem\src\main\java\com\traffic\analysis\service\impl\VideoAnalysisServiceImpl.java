package com.traffic.analysis.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.client.result.UpdateResult;
import com.traffic.analysis.controller.VideoProgressWebSocketController;
import com.traffic.analysis.model.VideoAnalysis;
import com.traffic.analysis.model.FourWayIntersectionAnalysis;
import com.traffic.analysis.model.Direction;
import com.traffic.analysis.model.DirectionVideoData;
import com.traffic.analysis.model.TrafficAnalysisResult;
import com.traffic.analysis.service.VideoAnalysisService;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.gridfs.GridFsOperations;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.data.support.PageableExecutionUtils;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// 导入JavaCV相关类
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import org.bson.Document;
import com.mongodb.client.gridfs.model.GridFSFile;

/**
 * 视频分析服务实现类
 */
@Service
public class VideoAnalysisServiceImpl implements VideoAnalysisService {

    private static final Logger log = LoggerFactory.getLogger(VideoAnalysisServiceImpl.class);
    
    @Autowired
    private MongoTemplate mongoTemplate;

    @Value("${traffic.analysis.video.upload-dir:D:/code/trafficsystem/trafficsystem/static/video/uploads}")
    private String uploadDir;

    @Value("${traffic.analysis.video.results-dir:D:/code/trafficsystem/trafficsystem/static/video/results}")
    private String resultsDir;

    @Value("${traffic.analysis.video.thumbnails-dir:D:/code/trafficsystem/trafficsystem/src/main/resources/static/images/thumbnails}")
    private String thumbnailsDir;

    // 线程池用于异步处理视频
    private final ExecutorService executorService = Executors.newFixedThreadPool(4);

    // 四方向分析专用执行器
    @Autowired
    @Qualifier("fourWayAnalysisExecutor")
    private Executor fourWayAnalysisExecutor;

    // 线程池监控器
    @Autowired
    private com.traffic.analysis.config.ThreadPoolMonitor threadPoolMonitor;

    // 添加WebSocket控制器
    @Autowired(required = false)
    private VideoProgressWebSocketController webSocketController;
    
    // 添加GridFS相关依赖
    @Autowired
    private GridFsTemplate gridFsTemplate;

    @Autowired
    private GridFsOperations gridFsOperations;

    // 添加WebSocket消息模板
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    
    @Override
    public VideoAnalysis saveVideoAnalysis(VideoAnalysis videoAnalysis) {
        if (videoAnalysis.getCreatedAt() == null) {
            videoAnalysis.setCreatedAt(LocalDateTime.now());
        }
        videoAnalysis.setUpdatedAt(LocalDateTime.now());
        return mongoTemplate.save(videoAnalysis);
    }

    @Override
    public Optional<VideoAnalysis> findById(String id) {
        return Optional.ofNullable(mongoTemplate.findById(id, VideoAnalysis.class));
    }

    @Override
    public Optional<VideoAnalysis> findByTaskId(String taskId) {
        Query query = Query.query(Criteria.where("task_id").is(taskId));
        VideoAnalysis result = mongoTemplate.findOne(query, VideoAnalysis.class);
        return Optional.ofNullable(result);
    }

    @Override
    public Page<VideoAnalysis> findByUserIdAndRole(String userId, String role, Pageable pageable) {
        Query query;
        
        // 管理员可以查看所有视频分析任务
        if ("admin".equalsIgnoreCase(role)) {
            query = new Query();
        } else {
            // 普通用户只能查看自己的视频分析任务
            query = Query.query(Criteria.where("user_id").is(userId));
        }
        
        // 总数
        long total = mongoTemplate.count(query, VideoAnalysis.class);
        
        // 分页查询
        query.with(pageable);
        List<VideoAnalysis> content = mongoTemplate.find(query, VideoAnalysis.class);
        
        return PageableExecutionUtils.getPage(content, pageable, () -> total);
    }

    @Override
    public Page<VideoAnalysis> findByDirectionAndRole(String direction, String role, Pageable pageable) {
        Query query;
        
        if (role != null) {
            // 查询指定方向和角色的任务
            query = Query.query(
                Criteria.where("direction").is(direction)
                    .and("role").is(role)
            );
        } else {
            // 仅查询指定方向的任务
            query = Query.query(Criteria.where("direction").is(direction));
        }
        
        // 总数
        long total = mongoTemplate.count(query, VideoAnalysis.class);
        
        // 分页查询
        query.with(pageable);
        List<VideoAnalysis> content = mongoTemplate.find(query, VideoAnalysis.class);
        
        return PageableExecutionUtils.getPage(content, pageable, () -> total);
    }
    
    @Override
    public Page<VideoAnalysis> findByUserIdDirectionAndRole(String userId, String direction, String role, Pageable pageable) {
        Query query;
        
        // 管理员可以查看所有指定方向的任务
        if ("admin".equalsIgnoreCase(role)) {
            query = Query.query(Criteria.where("direction").is(direction));
        } else {
            // 普通用户只能查看自己创建的指定方向的任务
            query = Query.query(
                Criteria.where("direction").is(direction)
                    .and("user_id").is(userId)
            );
        }
        
        // 总数
        long total = mongoTemplate.count(query, VideoAnalysis.class);
        
        // 分页查询
        query.with(pageable);
        List<VideoAnalysis> content = mongoTemplate.find(query, VideoAnalysis.class);
        
        return PageableExecutionUtils.getPage(content, pageable, () -> total);
    }

    @Override
    public VideoAnalysis uploadAndAnalyzeVideo(
            MultipartFile videoFile, 
            String userId, 
            String username, 
            String role,
            String direction) throws IOException {
        // 生成UUID格式的任务ID
        String taskId = UUID.randomUUID().toString();
        
        // 检查上传目录是否存在，不存在则创建
        File dir = new File(uploadDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        
        // 创建文件名
        String timestamp = String.valueOf(System.currentTimeMillis()/1000);
        String filename = "video_" + timestamp + "_" + taskId.substring(0, 8) + ".mp4";
        
        String savedFileId;
        double videoDuration = 0.0;
        
        // 使用GridFS存储视频文件，但先尝试获取视频时长
        File tempFile = null;
        try {
            // 创建临时文件
            tempFile = File.createTempFile("video_duration_", ".mp4");
            videoFile.transferTo(tempFile);
            
            // 获取视频时长
            videoDuration = getVideoDuration(tempFile);
            log.info("成功获取视频时长: {}秒", videoDuration);
            
            // 使用GridFS存储视频文件
            try (InputStream inputStream = new FileInputStream(tempFile)) {
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("contentType", videoFile.getContentType());
                metadata.put("filename", filename);
                metadata.put("userId", userId);
                metadata.put("username", username);
                metadata.put("uploadTime", new Date());
                metadata.put("taskId", taskId);
                if (videoDuration > 0) {
                    metadata.put("duration", videoDuration);
                }
                
                ObjectId objectId = gridFsTemplate.store(
                    inputStream, filename, videoFile.getContentType(), 
                    new Document(metadata)
                );
                savedFileId = objectId.toString();
                log.info("视频文件保存到GridFS: {}, ID: {}, 时长: {}秒", filename, savedFileId, videoDuration);
            }
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                try {
                    tempFile.delete();
                } catch (Exception e) {
                    log.warn("清理临时文件失败: {}", e.getMessage());
                }
            }
        }
        
        // 创建视频分析任务
        VideoAnalysis videoAnalysis = new VideoAnalysis();
        videoAnalysis.setTaskId(taskId);
        videoAnalysis.setResultId(taskId); // 默认结果ID与任务ID相同
        videoAnalysis.setUserId(userId);
        videoAnalysis.setUsername(username);
        videoAnalysis.setRole(role);
        videoAnalysis.setVideoFilename(filename);
        videoAnalysis.setVideoName(""); // 确保videoName默认为空字符串
        videoAnalysis.setVideoPath(savedFileId);
        videoAnalysis.setDirection(direction);
        videoAnalysis.setStatus("queued");
        videoAnalysis.setProgress(0);
        videoAnalysis.setMessage("视频上传成功，等待分析...");
        videoAnalysis.setCreatedAt(LocalDateTime.now());
        videoAnalysis.setUpdatedAt(LocalDateTime.now());
        
        // 设置视频时长 - 确保使用setter方法
        if (videoDuration > 0) {
            videoAnalysis.setVideoDuration(videoDuration);
            log.info("设置视频分析任务的视频时长: {}秒", videoDuration);
        }
        
        // 保存分析任务
        final VideoAnalysis savedAnalysis = saveVideoAnalysis(videoAnalysis);
        
        // 验证保存是否成功
        log.info("保存视频分析任务到数据库，任务ID: {}, 视频时长: {}秒", 
                savedAnalysis.getTaskId(), savedAnalysis.getVideoDuration());
        
        // 异步处理视频分析
        executorService.submit(() -> processVideo(savedAnalysis));
        
        return savedAnalysis;
    }

    @Override
    public VideoAnalysis processIntersectionVideos(
            MultipartFile horizontalFile, 
            MultipartFile verticalFile, 
            String userId, 
            String username,
            String role) throws IOException {
        // 生成UUID格式的任务ID
        String taskId = UUID.randomUUID().toString();
        
        // 检查上传目录是否存在，不存在则创建
        File dir = new File(uploadDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        
        // 创建水平方向文件名
        String timestamp = String.valueOf(System.currentTimeMillis()/1000);
        String horizontalFilename = "horizontal_" + timestamp + "_" + taskId.substring(0, 8) + ".mp4";
        
        // 创建垂直方向文件名
        String verticalFilename = "vertical_" + timestamp + "_" + taskId.substring(0, 8) + ".mp4";
        
        String horizontalFileId;
        String verticalFileId;
        double horizontalVideoDuration = 0.0;
        double verticalVideoDuration = 0.0;
        double totalVideoDuration = 0.0;
        
        // 处理水平方向视频
        File horizontalTempFile = null;
        try {
            // 创建临时文件以获取时长
            horizontalTempFile = File.createTempFile("horizontal_video_", ".mp4");
            horizontalFile.transferTo(horizontalTempFile);
            
            // 获取视频时长
            horizontalVideoDuration = getVideoDuration(horizontalTempFile);
            log.info("成功获取水平方向视频时长: {}秒", horizontalVideoDuration);
            
            // 使用GridFS存储水平方向视频
            try (InputStream inputStream = new FileInputStream(horizontalTempFile)) {
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("contentType", horizontalFile.getContentType());
                metadata.put("filename", horizontalFilename);
                metadata.put("userId", userId);
                metadata.put("username", username);
                metadata.put("uploadTime", new Date());
                metadata.put("taskId", taskId);
                metadata.put("direction", "horizontal");
                if (horizontalVideoDuration > 0) {
                    metadata.put("duration", horizontalVideoDuration);
                }
                
                ObjectId objectId = gridFsTemplate.store(
                    inputStream, horizontalFilename, horizontalFile.getContentType(), 
                    new Document(metadata)
                );
                horizontalFileId = objectId.toString();
                log.info("水平方向视频文件保存到GridFS: {}, ID: {}, 时长: {}秒", 
                        horizontalFilename, horizontalFileId, horizontalVideoDuration);
            }
        } finally {
            // 清理临时文件
            if (horizontalTempFile != null && horizontalTempFile.exists()) {
                try {
                    horizontalTempFile.delete();
                } catch (Exception e) {
                    log.warn("清理水平方向视频临时文件失败: {}", e.getMessage());
                }
            }
        }
        
        // 处理垂直方向视频
        File verticalTempFile = null;
        try {
            // 创建临时文件以获取时长
            verticalTempFile = File.createTempFile("vertical_video_", ".mp4");
            verticalFile.transferTo(verticalTempFile);
            
            // 获取视频时长
            verticalVideoDuration = getVideoDuration(verticalTempFile);
            log.info("成功获取垂直方向视频时长: {}秒", verticalVideoDuration);
        
            // 使用GridFS存储垂直方向视频
            try (InputStream inputStream = new FileInputStream(verticalTempFile)) {
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("contentType", verticalFile.getContentType());
                metadata.put("filename", verticalFilename);
                metadata.put("userId", userId);
                metadata.put("username", username);
                metadata.put("uploadTime", new Date());
                metadata.put("taskId", taskId);
                metadata.put("direction", "vertical");
                if (verticalVideoDuration > 0) {
                    metadata.put("duration", verticalVideoDuration);
                }
                
                ObjectId objectId = gridFsTemplate.store(
                    inputStream, verticalFilename, verticalFile.getContentType(), 
                    new Document(metadata)
                );
                verticalFileId = objectId.toString();
                log.info("垂直方向视频文件保存到GridFS: {}, ID: {}, 时长: {}秒", 
                        verticalFilename, verticalFileId, verticalVideoDuration);
            }
        } finally {
            // 清理临时文件
            if (verticalTempFile != null && verticalTempFile.exists()) {
                try {
                    verticalTempFile.delete();
                } catch (Exception e) {
                    log.warn("清理垂直方向视频临时文件失败: {}", e.getMessage());
                }
            }
        }
        
        // 总视频时长取两个视频的最大值
        totalVideoDuration = Math.max(horizontalVideoDuration, verticalVideoDuration);
        log.info("十字路口总视频时长(取最大值): {}秒", totalVideoDuration);
        
        // 创建视频分析任务
        VideoAnalysis videoAnalysis = new VideoAnalysis();
        videoAnalysis.setTaskId(taskId);
        videoAnalysis.setResultId(taskId); // 默认结果ID与任务ID相同
        videoAnalysis.setUserId(userId);
        videoAnalysis.setUsername(username);
        videoAnalysis.setRole(role);
        videoAnalysis.setVideoFilename(horizontalFilename + " + " + verticalFilename);
        videoAnalysis.setVideoName(""); // 确保videoName默认为空字符串
        videoAnalysis.setDirection("intersection");
        videoAnalysis.setStatus("queued");
        videoAnalysis.setProgress(0);
        videoAnalysis.setMessage("十字路口视频上传成功，等待分析...");
        videoAnalysis.setCreatedAt(LocalDateTime.now());
        videoAnalysis.setUpdatedAt(LocalDateTime.now());
        videoAnalysis.setMode("intersection");
        
        // 设置视频时长 - 直接使用setter方法
        if (totalVideoDuration > 0) {
            videoAnalysis.setVideoDuration(totalVideoDuration);
            log.info("设置十字路口分析任务的视频时长: {}秒", totalVideoDuration);
        }
        
        // 使用自定义字段存储两个视频的路径
        Map<String, Object> horizontalData = new HashMap<>();
        horizontalData.put("video_path", horizontalFileId);
        horizontalData.put("filename", horizontalFilename);
        horizontalData.put("duration", horizontalVideoDuration);
        videoAnalysis.setHorizontalData(horizontalData);
        
        Map<String, Object> verticalData = new HashMap<>();
        verticalData.put("video_path", verticalFileId);
        verticalData.put("filename", verticalFilename);
        verticalData.put("duration", verticalVideoDuration);
        videoAnalysis.setVerticalData(verticalData);
        
        // 保存分析任务
        final VideoAnalysis savedAnalysis = saveVideoAnalysis(videoAnalysis);
        
        // 验证保存是否成功
        log.info("保存十字路口视频分析任务到数据库，任务ID: {}, 视频时长: {}秒", 
                savedAnalysis.getTaskId(), savedAnalysis.getVideoDuration());
        
        // 异步处理视频分析
        executorService.submit(() -> processIntersection(savedAnalysis));
        
        return savedAnalysis;
    }

    @Override
    public VideoAnalysis updateProgress(String taskId, int progress) {
        Query query = Query.query(Criteria.where("task_id").is(taskId));
        Update update = new Update()
                .set("progress", progress)
                .set("updated_at", LocalDateTime.now());
        
        // 任务完成时，设置状态为已完成，并添加resultId
        if (progress >= 100) {
            update.set("status", "completed")
                  .set("completed_at", LocalDateTime.now())
                  .set("result_id", taskId); // 使用taskId作为resultId
        }
        
        mongoTemplate.updateFirst(query, update, VideoAnalysis.class);
        
        // 获取更新后的视频分析对象
        VideoAnalysis task = findByTaskId(taskId).orElse(null);
        
        // 通过WebSocket发送进度更新
        if (task != null && webSocketController != null) {
            try {
                if (progress >= 100) {
                    // 任务完成时，发送包含resultId的消息
                    Map<String, Object> message = new HashMap<>();
                    message.put("type", "processing_progress");
                    message.put("status", "completed");
                    message.put("progress", progress);
                    message.put("resultId", taskId); // 重要：添加resultId
                    message.put("message", task.getMessage());
                    webSocketController.sendProgressUpdate(taskId, message);
                } else {
                    // 常规进度更新使用旧方法
                    webSocketController.sendProgressUpdate(
                        taskId, 
                        progress, 
                        task.getStatus(), 
                        task.getMessage()
                    );
                }
            } catch (Exception e) {
                log.error("通过WebSocket发送进度更新失败: {}", e.getMessage(), e);
            }
        }
        
        return task;
    }

    @Override
    public VideoAnalysis updateStatus(String taskId, String status, String message) {
        Query query = Query.query(Criteria.where("task_id").is(taskId));
        Update update = new Update()
                .set("status", status)
                .set("updated_at", LocalDateTime.now());
        
        if (message != null && !message.isEmpty()) {
            update.set("message", message);
        }
        
        // 如果状态为已完成，设置完成时间
        if ("completed".equals(status)) {
            update.set("completed_at", LocalDateTime.now());
        }
                
        mongoTemplate.updateFirst(query, update, VideoAnalysis.class);
        
        VideoAnalysis task = findByTaskId(taskId).orElse(null);
        
        // 通过WebSocket发送状态更新
        if (task != null && webSocketController != null) {
            try {
                webSocketController.sendProgressUpdate(
                    taskId, 
                    task.getProgress(), 
                    status, 
                    message != null ? message : task.getMessage()
                );
            } catch (Exception e) {
                log.error("通过WebSocket发送状态更新失败: {}", e.getMessage(), e);
            }
        }
        
        return task;
    }

    @Override
    public VideoAnalysis completeAnalysis(String taskId, Map<String, Object> resultData) {
        Query query = Query.query(Criteria.where("task_id").is(taskId));
        VideoAnalysis task = mongoTemplate.findOne(query, VideoAnalysis.class);
        
        if (task == null) {
            log.warn("无法完成分析，任务不存在: {}", taskId);
            return null;
        }
        
        // 更新任务状态
        task.setStatus("completed");
        task.setProgress(100);
        task.setCompletedAt(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        task.setResultId(taskId); // 设置resultId为taskId
        
        // 更新结果数据
        if (resultData != null) {
            // 车辆计数
            if (resultData.containsKey("vehicleCount")) {
                task.setVehicleCount((Integer) resultData.get("vehicleCount"));
            }
            
            // 车辆类型统计
            if (resultData.containsKey("vehicleTypeStats") && resultData.get("vehicleTypeStats") instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Integer> stats = (Map<String, Integer>) resultData.get("vehicleTypeStats");
                task.setVehicleTypeStats(stats);
            }
            
            // 处理时间 - 仅在当前值为0或null时才设置
            if (resultData.containsKey("processingTime")) {
                // 检查当前处理时间是否已设置
                Double currentProcessingTime = task.getProcessingTime();
                if (currentProcessingTime == null || currentProcessingTime == 0) {
                    Double processingTime = (Double) resultData.get("processingTime");
                    task.setProcessingTime(processingTime);
                    log.info("设置初始处理时间: {}秒 (来自resultData)", processingTime);
                } else {
                    // 保留现有值，可能是前端传递的实际测量值
                    log.info("保留现有处理时间: {}秒 (忽略resultData中的值)", task.getProcessingTime());
                }
            }
            
            // 结果路径
            if (resultData.containsKey("resultPath")) {
                task.setResultPath((String) resultData.get("resultPath"));
            }
            
            // 结果视频文件名
            if (resultData.containsKey("resultVideoFilename")) {
                task.setResultVideoFilename((String) resultData.get("resultVideoFilename"));
            } else if (task.getResultPath() != null) {
                // 如果没有提供结果视频文件名，但有结果路径，创建一个默认的文件名
                task.setResultVideoFilename("analyzed_video_" + task.getTaskId() + ".mp4");
            }
            
            // 缩略图URL
            if (resultData.containsKey("thumbnailUrl")) {
                task.setThumbnailUrl((String) resultData.get("thumbnailUrl"));
            }
            
            // 如果是十字路口分析
            if ("intersection".equals(task.getMode())) {
                // 更新横向数据
                if (resultData.containsKey("horizontal_data") && resultData.get("horizontal_data") instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> hData = (Map<String, Object>) resultData.get("horizontal_data");
                    task.setHorizontalData(hData);
                }
                
                // 更新纵向数据
                if (resultData.containsKey("vertical_data") && resultData.get("vertical_data") instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> vData = (Map<String, Object>) resultData.get("vertical_data");
                    task.setVerticalData(vData);
                }
            }
            
            // 保存结果数据中的mode字段
            if (resultData.containsKey("mode")) {
                task.setMode((String) resultData.get("mode"));
            }
        }
        
        // 保存更新后的任务
        VideoAnalysis updatedTask = saveVideoAnalysis(task);
        
        // 通过WebSocket发送任务完成消息
        if (webSocketController != null) {
            try {
                Map<String, Object> message = new HashMap<>();
                message.put("type", "processing_progress");
                message.put("status", "completed");
                message.put("progress", 100);
                message.put("resultId", taskId); // 关键：添加resultId
                message.put("message", "视频分析已完成");
                webSocketController.sendProgressUpdate(taskId, message);
            } catch (Exception e) {
                log.error("通过WebSocket发送任务完成消息失败: {}", e.getMessage(), e);
            }
        }

        // 清理任务相关资源
        cleanupTaskResources(taskId);

        return updatedTask;
    }

    @Override
    public Map<String, Object> getVideoAnalysisResult(String resultId) {
        Optional<VideoAnalysis> taskOpt = findByTaskId(resultId);
        
        if (!taskOpt.isPresent()) {
            return Collections.singletonMap("error", "视频分析结果不存在");
        }
        
        VideoAnalysis task = taskOpt.get();
        Map<String, Object> result = new HashMap<>();
        
        // 基本信息
        result.put("id", task.getId());
        result.put("taskId", task.getTaskId());
        result.put("status", task.getStatus());
        result.put("progress", task.getProgress());
        result.put("vehicleCount", task.getVehicleCount());
        result.put("vehicleTypeStats", task.getVehicleTypeStats());
        result.put("createdAt", task.getCreatedAt());
        result.put("completedAt", task.getCompletedAt());
        result.put("processingTime", task.getProcessingTime());
        result.put("direction", task.getDirection());
        result.put("mode", task.getMode());
        result.put("videoName", task.getVideoName()); // 添加视频自定义名称
        
        // 添加视频时长字段到直接结果数据
        result.put("videoDuration", task.getVideoDuration());
        log.info("视频结果查询: 任务ID={}, 视频时长={}秒", resultId, task.getVideoDuration());
        
        // 处理GridFS中的视频和图像路径
        String resultPath = task.getResultPath();
        String resultVideoFilename = task.getResultVideoFilename();
        String thumbnailUrl = task.getThumbnailUrl();
        
        // 如果路径是ObjectId格式，则添加API路径前缀
        if (resultPath != null) {
            result.put("resultPath", resultPath); // 保存原始ID用于数据库查询
            result.put("resultVideoFilename", resultVideoFilename); // 添加结果视频文件名
            
            if (resultPath.matches("[0-9a-f]{24}")) {
                // GridFS ID
                result.put("videoUrl", "/api/media/video/" + resultPath); // 直接使用MediaController访问
            } else if (resultPath.startsWith("/")) {
                // 已经是相对URL路径
                result.put("videoUrl", resultPath);
            } else {
                // 本地路径，转换为URL
                result.put("videoUrl", "/api/static/videos/" + Paths.get(resultPath).getFileName().toString());
            }
        }
        
        // 如果缩略图是ObjectId格式，则添加API路径前缀
        if (thumbnailUrl != null) {
            if (thumbnailUrl.matches("[0-9a-f]{24}")) {
                result.put("thumbnailUrl", thumbnailUrl); // 保存原始ID用于数据库查询
                result.put("thumbnailImageUrl", "/api/media/image/" + thumbnailUrl); // 添加API路径用于前端访问
            } else if (thumbnailUrl.startsWith("/")) {
                // 已经是相对URL路径
                result.put("thumbnailUrl", thumbnailUrl);
                result.put("thumbnailImageUrl", thumbnailUrl);
            } else {
                // 本地路径，保持原样（兼容现有代码）
                result.put("thumbnailUrl", thumbnailUrl);
            }
        }
        
        // === 添加前端期望的数据结构 ===
        
        // 1. 视频信息
        // 如果没有视频信息，创建默认值
        Map<String, Object> videoInfo = new HashMap<>();
        videoInfo.put("filename", task.getVideoFilename());
        
        // 根据方向设置方向字段
        String displayDirection = "horizontal"; // 默认为横向
        if (task.getDirection() != null) {
            if ("intersection".equals(task.getDirection())) {
                displayDirection = "intersection";
            } else {
                switch (task.getDirection().toLowerCase()) {
                    case "north":
                    case "south":
                    case "vertical":
                        displayDirection = "vertical";
                        break;
                    default:
                        displayDirection = "horizontal";
                }
            }
        }
        videoInfo.put("direction", displayDirection);
        
        // 使用实际获取的视频时长，如果有
        if (task.getVideoDuration() > 0) {
            videoInfo.put("duration_seconds", task.getVideoDuration());
        } else {
            // 如果没有视频时长，提供默认值
        videoInfo.put("duration_seconds", 60); // 默认60秒
        }
        
        videoInfo.put("width", 1280);
        videoInfo.put("height", 720);
        
        // 添加存储类型信息，便于前端区分处理方式
        boolean isGridFs = resultPath != null && resultPath.matches("[0-9a-f]{24}");
        videoInfo.put("storage_type", isGridFs ? "gridfs" : "local");
        videoInfo.put("gridfs_id", isGridFs ? resultPath : null);
        
        // 添加结果视频文件名
        if (resultVideoFilename != null && !resultVideoFilename.isEmpty()) {
            videoInfo.put("result_video_filename", resultVideoFilename);
        } else if (task.getTaskId() != null) {
            // 如果没有设置结果视频文件名，生成一个默认的
            videoInfo.put("result_video_filename", "analyzed_video_" + task.getTaskId() + ".mp4");
        }
        
        result.put("video_info", videoInfo);
        
        // 2. 分析摘要
        Map<String, Object> analysisSummary = new HashMap<>();
        int totalVehicles = 30; // 默认值
        
        // 如果有车辆数量数据，使用它
        Integer vehicleCount = task.getVehicleCount();
        if (vehicleCount != null) {
            totalVehicles = vehicleCount;
        }
        
        analysisSummary.put("total_vehicles", totalVehicles);
        analysisSummary.put("traffic_density", 0.5); // 默认值
        
        // 默认高峰期
        List<Integer> peakTimes = new ArrayList<>();
        peakTimes.add(30); // 默认30秒为高峰
        analysisSummary.put("peak_times", peakTimes);
        
        // 默认方向分布
        Map<String, Integer> directionDistribution = new HashMap<>();
        directionDistribution.put("eastbound", totalVehicles / 3);
        directionDistribution.put("westbound", totalVehicles / 3);
        directionDistribution.put("northbound", totalVehicles / 6);
        directionDistribution.put("southbound", totalVehicles / 6);
        
        analysisSummary.put("direction_distribution", directionDistribution);
        result.put("analysis_summary", analysisSummary);
        
        // 3. 时间序列数据
        Map<String, Object> timeSeriesData = new HashMap<>();
        List<Integer> timestamps = new ArrayList<>();
        List<Integer> vehicleCounts = new ArrayList<>();
        Random random = new Random();
        
        for (int i = 0; i < 10; i++) {
            timestamps.add(i * 10); // 0, 10, 20...秒
            vehicleCounts.add(random.nextInt(15) + 5); // 随机车辆数
        }
        timeSeriesData.put("timestamps", timestamps);
        timeSeriesData.put("vehicle_counts", vehicleCounts);
        result.put("time_series_data", timeSeriesData);
        
        // 4. 车型分布 (使用vehicleTypeStats或创建默认值)
        if (task.getVehicleTypeStats() != null && !task.getVehicleTypeStats().isEmpty()) {
            result.put("vehicle_types", task.getVehicleTypeStats());
        } else {
            // 创建默认车型分布
            Map<String, Integer> vehicleTypes = new HashMap<>();
            vehicleTypes.put("car", totalVehicles * 3 / 5);
            vehicleTypes.put("truck", totalVehicles / 10);
            vehicleTypes.put("bus", totalVehicles / 20);
            vehicleTypes.put("motorcycle", totalVehicles / 10);
            vehicleTypes.put("bicycle", totalVehicles / 20);
            result.put("vehicle_types", vehicleTypes);
        }
        
        // 5. 优化建议
        List<Map<String, Object>> suggestions = new ArrayList<>();
        
        Map<String, Object> suggestion = new HashMap<>();
        suggestion.put("type", "traffic_flow");
        suggestion.put("message", "基于视频分析，建议优化交通信号灯配时");
        suggestion.put("severity", "medium");
        suggestions.add(suggestion);
        
        result.put("optimization_suggestions", suggestions);
        
        // 十字路口特定数据
        if ("intersection".equals(task.getMode())) {
            // 如果有存储的横向和纵向数据，使用它
            if (task.getHorizontalData() != null) {
                result.put("horizontal_data", task.getHorizontalData());
            } else {
                // 创建默认横向数据
                Map<String, Object> horizontalData = new HashMap<>();
                horizontalData.put("vehicleCount", totalVehicles / 2);
                horizontalData.put("trafficDensity", 0.6);
                
                // 创建时间序列数据
                Map<String, Object> hTimeSeriesData = new HashMap<>();
                List<Integer> hTimestamps = new ArrayList<>();
                List<Integer> hVehicleCounts = new ArrayList<>();
                
                for (int i = 0; i < 10; i++) {
                    hTimestamps.add(i * 10); // 0, 10, 20...秒
                    hVehicleCounts.add(random.nextInt(20) + 10); // 随机车辆数
                }
                
                hTimeSeriesData.put("timestamps", hTimestamps);
                hTimeSeriesData.put("vehicle_counts", hVehicleCounts);
                horizontalData.put("time_series_data", hTimeSeriesData);
                
                result.put("horizontal_data", horizontalData);
            }
            
            if (task.getVerticalData() != null) {
                result.put("vertical_data", task.getVerticalData());
            } else {
                // 创建默认纵向数据
                Map<String, Object> verticalData = new HashMap<>();
                verticalData.put("vehicleCount", totalVehicles / 2);
                verticalData.put("trafficDensity", 0.4);
                
                // 创建时间序列数据
                Map<String, Object> vTimeSeriesData = new HashMap<>();
                List<Integer> vTimestamps = new ArrayList<>();
                List<Integer> vVehicleCounts = new ArrayList<>();
                
                for (int i = 0; i < 10; i++) {
                    vTimestamps.add(i * 10); // 0, 10, 20...秒
                    vVehicleCounts.add(random.nextInt(15) + 5); // 随机车辆数
                }
                
                vTimeSeriesData.put("timestamps", vTimestamps);
                vTimeSeriesData.put("vehicle_counts", vVehicleCounts);
                verticalData.put("time_series_data", vTimeSeriesData);
                
                result.put("vertical_data", verticalData);
            }
            
            // 补充横向数据中的time_series_data
            if (result.containsKey("horizontal_data") && 
                result.get("horizontal_data") instanceof Map && 
                !((Map<?,?>)result.get("horizontal_data")).containsKey("time_series_data")) {
                
                @SuppressWarnings("unchecked")
                Map<String, Object> horizontalData = (Map<String, Object>) result.get("horizontal_data");
                
                Map<String, Object> hTimeSeriesData = new HashMap<>();
                List<Integer> hTimestamps = new ArrayList<>();
                List<Integer> hVehicleCounts = new ArrayList<>();
                
                for (int i = 0; i < 10; i++) {
                    hTimestamps.add(i * 10); // 0, 10, 20...秒
                    hVehicleCounts.add(random.nextInt(20) + 10); // 随机车辆数
                }
                
                hTimeSeriesData.put("timestamps", hTimestamps);
                hTimeSeriesData.put("vehicle_counts", hVehicleCounts);
                horizontalData.put("time_series_data", hTimeSeriesData);
            }
            
            // 补充纵向数据中的time_series_data
            if (result.containsKey("vertical_data") && 
                result.get("vertical_data") instanceof Map && 
                !((Map<?,?>)result.get("vertical_data")).containsKey("time_series_data")) {
                
                @SuppressWarnings("unchecked")
                Map<String, Object> verticalData = (Map<String, Object>) result.get("vertical_data");
                
                Map<String, Object> vTimeSeriesData = new HashMap<>();
                List<Integer> vTimestamps = new ArrayList<>();
                List<Integer> vVehicleCounts = new ArrayList<>();
                
                for (int i = 0; i < 10; i++) {
                    vTimestamps.add(i * 10); // 0, 10, 20...秒
                    vVehicleCounts.add(random.nextInt(15) + 5); // 随机车辆数
                }
                
                vTimeSeriesData.put("timestamps", vTimestamps);
                vTimeSeriesData.put("vehicle_counts", vVehicleCounts);
                verticalData.put("time_series_data", vTimeSeriesData);
            }
        }
        
        return result;
    }

    @Override
    public VideoAnalysis retryAnalysis(String taskId) {
        Optional<VideoAnalysis> taskOpt = findByTaskId(taskId);
        
        if (!taskOpt.isPresent()) {
            log.warn("无法重新分析，任务不存在: {}", taskId);
            return null;
        }
        
        VideoAnalysis task = taskOpt.get();
        
        // 重置任务状态
        task.setStatus("queued");
        task.setProgress(0);
        task.setUpdatedAt(LocalDateTime.now());
        task.setCompletedAt(null);
        task.setMessage(null);
        
        // 保存更新的任务
        VideoAnalysis updatedTask = saveVideoAnalysis(task);
        
        // 重新启动分析任务
        if ("intersection".equals(task.getMode())) {
            CompletableFuture.runAsync(() -> processIntersection(updatedTask), executorService);
        } else {
            CompletableFuture.runAsync(() -> processVideo(updatedTask), executorService);
        }
        
        return updatedTask;
    }

    @Override
    public boolean deleteAnalysis(String taskId, String userId, String role) {
        Query query;

        // 管理员可以删除任何任务，普通用户只能删除自己的任务
        if ("admin".equalsIgnoreCase(role)) {
            query = Query.query(Criteria.where("task_id").is(taskId));
        } else {
            query = Query.query(
                Criteria.where("task_id").is(taskId)
                    .and("user_id").is(userId)
            );
        }

        VideoAnalysis task = mongoTemplate.findAndRemove(query, VideoAnalysis.class);

        if (task != null) {
            log.info("开始删除视频分析任务及相关文件: taskId={}", taskId);

            // 删除任务相关的文件
            try {
                // 删除原始视频文件
                if (task.getVideoPath() != null && !task.getVideoPath().isEmpty()) {
                    deleteVideoFile(task.getVideoPath(), "原始视频");
                }

                // 删除结果视频文件
                if (task.getResultPath() != null && !task.getResultPath().isEmpty()) {
                    deleteVideoFile(task.getResultPath(), "结果视频");
                }

                // 删除缩略图文件
                if (task.getThumbnailUrl() != null && !task.getThumbnailUrl().isEmpty()) {
                    deleteVideoFile(task.getThumbnailUrl(), "缩略图");
                }

                log.info("视频分析任务删除成功: taskId={}", taskId);
                return true;
            } catch (Exception e) {
                log.error("删除视频分析任务文件失败: taskId={}, error={}", taskId, e.getMessage(), e);
                return false;
            }
        }

        log.warn("未找到要删除的视频分析任务: taskId={}", taskId);
        return false;
    }

    /**
     * 删除视频文件（支持GridFS和本地文件）
     */
    private void deleteVideoFile(String filePath, String fileType) {
        if (filePath == null || filePath.isEmpty()) {
            return;
        }

        try {
            // 检查是否为GridFS文件ID (24位十六进制字符串)
            if (filePath.matches("[0-9a-f]{24}")) {
                log.info("删除GridFS中的{}文件: {}", fileType, filePath);
                deleteFileFromGridFS(filePath);
            } else {
                // 本地文件路径
                log.info("删除本地{}文件: {}", fileType, filePath);
                Files.deleteIfExists(Paths.get(filePath));
            }
        } catch (Exception e) {
            log.error("删除{}文件失败: {}, error={}", fileType, filePath, e.getMessage(), e);
        }
    }

    /**
     * 从GridFS删除文件
     */
    private void deleteFileFromGridFS(String fileId) {
        try {
            ObjectId objectId = new ObjectId(fileId);

            // 检查文件是否存在
            Query fileQuery = Query.query(Criteria.where("_id").is(objectId));
            boolean fileExists = mongoTemplate.exists(fileQuery, "fs.files");

            if (!fileExists) {
                log.warn("GridFS中不存在文件: {}", fileId);
                return;
            }

            // 删除fs.files集合中的文件记录
            long filesDeleted = mongoTemplate.remove(fileQuery, "fs.files").getDeletedCount();

            // 删除对应的chunks
            Query chunksQuery = Query.query(Criteria.where("files_id").is(objectId));
            long chunksDeleted = mongoTemplate.remove(chunksQuery, "fs.chunks").getDeletedCount();

            log.info("成功从GridFS删除文件: fileId={}, 删除文件记录数={}, 删除块数={}",
                    fileId, filesDeleted, chunksDeleted);
        } catch (Exception e) {
            log.error("从GridFS删除文件失败: fileId={}, error={}", fileId, e.getMessage(), e);
        }
    }
    
    @Override
    public boolean renameVideo(String taskId, String videoName) {
        try {
            // 先尝试使用task_id字段查询
            Query query = Query.query(Criteria.where("task_id").is(taskId));
            VideoAnalysis video = mongoTemplate.findOne(query, VideoAnalysis.class);
            
            // 如果找不到，再尝试使用_id字段查询
            if (video == null) {
                log.info("使用task_id未找到记录，尝试使用_id查询: {}", taskId);
                try {
                    ObjectId objectId = new ObjectId(taskId);
                    query = Query.query(Criteria.where("_id").is(objectId));
                    video = mongoTemplate.findOne(query, VideoAnalysis.class);
                } catch (Exception e) {
                    log.warn("无法将ID转换为ObjectId: {}", e.getMessage());
                }
            }
            
            // 如果仍未找到，尝试使用result_id查询
            if (video == null) {
                log.info("使用_id未找到记录，尝试使用result_id查询: {}", taskId);
                query = Query.query(Criteria.where("result_id").is(taskId));
                video = mongoTemplate.findOne(query, VideoAnalysis.class);
            }
            
            if (video == null) {
                log.warn("未找到要重命名的视频：taskId={}", taskId);
                return false;
            }
            
            Update update = new Update().set("video_name", videoName);
            // 使用当前查询条件 - 此时query变量已经设置为成功找到记录的查询条件
            UpdateResult result = mongoTemplate.updateFirst(query, update, VideoAnalysis.class);
            
            if (result.getModifiedCount() > 0) {
                log.info("视频重命名成功：taskId={}, 新名称={}", taskId, videoName);
                return true;
            } else {
                log.warn("视频重命名未修改任何记录：taskId={}", taskId);
                return false;
            }
        } catch (Exception e) {
            log.error("视频重命名失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 处理单个视频（异步）
     * @param task 视频分析任务
     */
    private void processVideo(VideoAnalysis task) {
        log.info("开始处理视频任务: {}", task.getTaskId());
        
        try {
            // 更新状态为处理中
            task.setStatus("processing");
            task.setMessage("正在处理视频...");
            task.setProgress(5);
            saveVideoAnalysis(task);
            updateTaskProgress(task);
            
            // 检查路径是否为GridFS ID
            String videoPath = task.getVideoPath();
            
            if (videoPath == null || videoPath.isEmpty()) {
                log.error("视频路径为空，无法处理视频");
                task.setStatus("failed");
                task.setMessage("视频路径为空，无法处理视频");
                saveVideoAnalysis(task);
                updateTaskProgress(task);
                return;
            }
            
            // 判断是否为GridFS ID
            boolean isGridFsVideo = videoPath.matches("[0-9a-f]{24}");
            
            log.info("准备调用Python视频分析API，视频ID: {}, 是否GridFS: {}", videoPath, isGridFsVideo);
            
            // 更新进度
            task.setProgress(10);
            task.setMessage("视频准备完成，开始分析...");
            saveVideoAnalysis(task);
            updateTaskProgress(task);
                        
            // 调用Python API执行视频分析
            String apiUrl = "http://localhost:5001/analyze_video_from_gridfs";
            
            // 创建HTTP客户端，添加超时配置
            java.net.http.HttpClient client = java.net.http.HttpClient.newBuilder()
                .version(java.net.http.HttpClient.Version.HTTP_1_1)
                .connectTimeout(java.time.Duration.ofSeconds(30))
                .build();
            
            // 准备请求数据
            String requestBody = String.format(
                "{\"video_id\": \"%s\", \"task_id\": \"%s\"}",
                videoPath, task.getTaskId()
            );
            
            // 创建HTTP请求，添加超时配置
            java.net.http.HttpRequest request = java.net.http.HttpRequest.newBuilder()
                .uri(java.net.URI.create(apiUrl))
                .header("Content-Type", "application/json")
                .timeout(java.time.Duration.ofMinutes(10))
                .POST(java.net.http.HttpRequest.BodyPublishers.ofString(requestBody))
                .build();
            
            log.info("发送视频分析请求到Python API: {}", apiUrl);
            
            // 更新状态为分析中
            task.setProgress(20);
            task.setMessage("Python模型正在分析视频...");
            saveVideoAnalysis(task);
            updateTaskProgress(task);

            // 初始化实时帧推送功能
            initializeRealtimeFramePush(task.getTaskId());
            
            // 发送请求
            java.net.http.HttpResponse<String> response = client.send(
                request, java.net.http.HttpResponse.BodyHandlers.ofString()
            );
            
            // 检查响应状态
            if (response.statusCode() != 200) {
                log.error("Python API返回错误状态码: {}, 响应内容: {}", 
                         response.statusCode(), response.body());
                throw new IOException("Python API返回错误: " + response.body());
            }
            
            // 解析响应JSON
            ObjectMapper mapper = new ObjectMapper();
            Map<String, Object> result = mapper.readValue(response.body(), Map.class);
            
            // 检查分析状态
            if (!"success".equals(result.get("status"))) {
                String errorMessage = (String) result.getOrDefault("message", "未知错误");
                log.error("视频分析失败: {}", errorMessage);
                    task.setStatus("failed");
                task.setMessage("视频分析失败: " + errorMessage);
                    saveVideoAnalysis(task);
                    updateTaskProgress(task);
                    return;
            }
            
            log.info("视频分析成功，结果: {}", response.body());
            
            // 更新进度
            task.setProgress(80);
            task.setMessage("视频分析完成，保存结果...");
            saveVideoAnalysis(task);
            updateTaskProgress(task);
            
            // 从结果中提取数据
            String resultFileId = (String) result.get("result_file_id");
            Integer vehicleCount = (Integer) result.get("vehicle_count");
            Double processingTime = (Double) result.get("processing_time");
            Map<String, Integer> vehicleTypes = (Map<String, Integer>) result.get("vehicle_types");
            
            // 设置结果视频文件名
            String resultVideoFilename = "analyzed_video_" + task.getTaskId() + ".mp4";
            
            // 生成结果数据
            Map<String, Object> resultData = new HashMap<>();
            resultData.put("vehicleCount", vehicleCount);
            resultData.put("vehicleTypeStats", vehicleTypes);
            resultData.put("processingTime", processingTime);
            resultData.put("resultPath", resultFileId);
            resultData.put("resultVideoFilename", resultVideoFilename);
            
            // 如果有缩略图，更新缩略图URL
            if (task.getThumbnailUrl() == null || task.getThumbnailUrl().isEmpty()) {
                // 保存第一帧作为缩略图
                String thumbnailUrl = saveVideoThumbnail(videoPath);
                if (thumbnailUrl != null) {
                    resultData.put("thumbnailUrl", thumbnailUrl);
                    task.setThumbnailUrl(thumbnailUrl);
                }
            } else {
                resultData.put("thumbnailUrl", task.getThumbnailUrl());
            }
            
            // 更新进度为100%
            task.setProgress(100);
            task.setMessage("视频分析已完成");
            saveVideoAnalysis(task);
            updateTaskProgress(task);
            
            // 完成分析
            completeAnalysis(task.getTaskId(), resultData);
            
            log.info("视频分析任务完成: taskId={}, 结果视频ID={}, 车辆数={}", 
                    task.getTaskId(), resultFileId, vehicleCount);
            
        } catch (Exception e) {
            log.error("处理视频时出错: {}", e.getMessage(), e);
            task.setStatus("failed");
            task.setMessage("处理视频时出错: " + e.getMessage());
            saveVideoAnalysis(task);
            updateTaskProgress(task);
        }
    }
    
    /**
     * 创建结果视频文件
     * 支持将原始视频另存为结果视频，并保存到GridFS
     */
    private String createResultVideo(VideoAnalysis task) {
        try {
            // 创建结果文件名
            String resultFileName = "result_" + task.getTaskId() + ".mp4";
            
            // 检查视频路径
            if (task.getVideoPath() == null || task.getVideoPath().isEmpty()) {
                log.error("视频路径为空，无法创建结果视频");
                return null;
            }
            
            String videoPath = task.getVideoPath();
            File tempVideoFile = null;
            boolean isGridFsVideo = false;
            
            // 检查视频是否存储在GridFS
            if (videoPath.matches("[0-9a-f]{24}")) {
                isGridFsVideo = true;
                log.info("原始视频存储在GridFS: {}", videoPath);
                
                // 获取视频的GridFS资源
                GridFsResource gridFsResource = getVideoFromGridFs(videoPath);
                if (gridFsResource == null || !gridFsResource.exists()) {
                    log.error("无法从GridFS获取原始视频: {}", videoPath);
                    return null;
                }
                
                // 为了处理视频，需要先创建临时文件
                tempVideoFile = File.createTempFile("source_video_", ".mp4");
                tempVideoFile.deleteOnExit();
                
                // 将GridFS视频保存到临时文件
                try (InputStream inputStream = gridFsResource.getInputStream();
                     FileOutputStream outputStream = new FileOutputStream(tempVideoFile)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                }
                
                log.info("已将GridFS视频保存为临时文件: {}", tempVideoFile.getAbsolutePath());
                videoPath = tempVideoFile.getAbsolutePath();
            }
            
            try {
                // 确认视频文件存在
                File sourceVideoFile = new File(videoPath);
                if (!sourceVideoFile.exists()) {
                    log.error("源视频文件不存在: {}", videoPath);
                    return null;
                }
                
                // 准备元数据
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("type", "result_video");
                metadata.put("taskId", task.getTaskId());
                metadata.put("userId", task.getUserId());
                metadata.put("originalFilename", task.getVideoFilename());
                metadata.put("direction", task.getDirection());
                metadata.put("analysisDate", new Date());
                
                // 将视频复制并保存到GridFS
                String fileId = saveVideoToGridFs(sourceVideoFile.getAbsolutePath(), resultFileName, metadata);
                
                if (fileId != null) {
                    log.info("已创建结果视频并保存到GridFS，文件ID: {}", fileId);
                    
                    // 更新数据库中的视频路径
                    Query query = Query.query(Criteria.where("task_id").is(task.getTaskId()));
                    Update update = new Update().set("result_path", fileId);
                    mongoTemplate.updateFirst(query, update, VideoAnalysis.class);
                    
                    return fileId;
                } else {
                    log.error("保存视频到GridFS失败");
                }
            } finally {
                // 清理临时文件
                if (tempVideoFile != null && tempVideoFile.exists()) {
                    tempVideoFile.delete();
                    log.info("已删除临时视频文件: {}", tempVideoFile.getAbsolutePath());
                }
            }
            
            log.warn("无法创建结果视频");
            return null;
        } catch (Exception e) {
            log.error("创建结果视频失败: {}", e.getMessage(), e);
            return null;
        }
    }
    


    /**
     * 处理十字路口视频（异步）
     * @param task 视频分析任务
     */
    private void processIntersection(VideoAnalysis task) {
        try {
            // 更新任务状态为处理中
            updateStatus(task.getTaskId(), "processing", "正在处理十字路口视频");
            
            // 获取视频路径数据
            Map<String, Object> horizontalData = task.getHorizontalData();
            Map<String, Object> verticalData = task.getVerticalData();
            
            if (horizontalData == null || verticalData == null) {
                log.error("缺少水平或垂直方向视频数据");
                updateStatus(task.getTaskId(), "failed", "缺少水平或垂直方向视频数据");
                return;
            }
            
            String horizontalVideoId = (String) horizontalData.get("video_path");
            String verticalVideoId = (String) verticalData.get("video_path");
            
            if (horizontalVideoId == null || verticalVideoId == null) {
                log.error("无法获取十字路口视频路径");
                updateStatus(task.getTaskId(), "failed", "无法获取十字路口视频路径");
                return;
            }
            
            // 生成缩略图（使用水平方向视频）
            if (task.getThumbnailUrl() == null || task.getThumbnailUrl().isEmpty()) {
                String thumbnailUrl = saveVideoThumbnail(horizontalVideoId);
                    if (thumbnailUrl != null) {
                        log.info("为十字路口视频生成缩略图: {}", thumbnailUrl);
                        // 更新任务状态
                        Query query = Query.query(Criteria.where("task_id").is(task.getTaskId()));
                        Update update = new Update().set("thumbnail_url", thumbnailUrl);
                        mongoTemplate.updateFirst(query, update, VideoAnalysis.class);
                        task.setThumbnailUrl(thumbnailUrl);
                    }
                }
            
            // 更新进度
            task.setProgress(10);
            task.setMessage("准备分析水平方向视频...");
            saveVideoAnalysis(task);
            updateTaskProgress(task);
            
            // 处理水平方向视频
            ObjectMapper mapper = new ObjectMapper();
            java.net.http.HttpClient client = java.net.http.HttpClient.newBuilder()
                .version(java.net.http.HttpClient.Version.HTTP_1_1)
                .connectTimeout(java.time.Duration.ofSeconds(30))
                .build();
            
            // 准备水平方向视频分析请求
            String apiUrl = "http://localhost:5001/analyze_video_from_gridfs";
            String horizontalRequestBody = String.format(
                "{\"video_id\": \"%s\", \"task_id\": \"h_%s\"}",
                horizontalVideoId, task.getTaskId()
            );
            
            log.info("发送水平方向视频分析请求...");
            
            // 更新进度
            task.setProgress(20);
            task.setMessage("同时分析水平和垂直方向视频...");
            saveVideoAnalysis(task);
            updateTaskProgress(task);

            // 初始化实时帧推送功能
            initializeRealtimeFramePush(task.getTaskId());

            // 准备垂直方向视频分析请求
            String verticalRequestBody = String.format(
                "{\"video_id\": \"%s\", \"task_id\": \"v_%s\"}",
                verticalVideoId, task.getTaskId()
            );

            log.info("同时发送水平和垂直方向视频分析请求...");

            // 创建两个并行的HTTP请求，添加超时配置
            java.net.http.HttpRequest horizontalRequest = java.net.http.HttpRequest.newBuilder()
                .uri(java.net.URI.create(apiUrl))
                .header("Content-Type", "application/json")
                .timeout(java.time.Duration.ofMinutes(10))
                .POST(java.net.http.HttpRequest.BodyPublishers.ofString(horizontalRequestBody))
                .build();

            java.net.http.HttpRequest verticalRequest = java.net.http.HttpRequest.newBuilder()
                .uri(java.net.URI.create(apiUrl))
                .header("Content-Type", "application/json")
                .timeout(java.time.Duration.ofMinutes(10))
                .POST(java.net.http.HttpRequest.BodyPublishers.ofString(verticalRequestBody))
                .build();

            // 使用CompletableFuture并行发送请求
            CompletableFuture<java.net.http.HttpResponse<String>> horizontalFuture =
                client.sendAsync(horizontalRequest, java.net.http.HttpResponse.BodyHandlers.ofString());

            CompletableFuture<java.net.http.HttpResponse<String>> verticalFuture =
                client.sendAsync(verticalRequest, java.net.http.HttpResponse.BodyHandlers.ofString());

            // 等待两个请求都完成
            CompletableFuture.allOf(horizontalFuture, verticalFuture).join();

            // 获取响应结果
            java.net.http.HttpResponse<String> horizontalResponse = horizontalFuture.get();
            java.net.http.HttpResponse<String> verticalResponse = verticalFuture.get();
            
            // 检查响应状态
            if (horizontalResponse.statusCode() != 200) {
                log.error("Python API返回错误状态码: {}, 响应内容: {}", 
                         horizontalResponse.statusCode(), horizontalResponse.body());
                throw new IOException("水平方向视频分析失败: " + horizontalResponse.body());
            }
            
            // 解析水平方向分析结果
            Map<String, Object> horizontalResult = mapper.readValue(horizontalResponse.body(), Map.class);
            
            if (!"success".equals(horizontalResult.get("status"))) {
                String errorMessage = (String) horizontalResult.getOrDefault("message", "未知错误");
                log.error("水平方向视频分析失败: {}", errorMessage);
                throw new IOException("水平方向视频分析失败: " + errorMessage);
            }
            
            // 提取水平方向分析结果
            String horizontalResultFileId = (String) horizontalResult.get("result_file_id");
            Integer horizontalVehicleCount = (Integer) horizontalResult.get("vehicle_count");
            Map<String, Integer> horizontalVehicleTypes = (Map<String, Integer>) horizontalResult.get("vehicle_types");
            
            log.info("水平方向视频分析完成: 车辆数={}, 结果ID={}", 
                    horizontalVehicleCount, horizontalResultFileId);
            
            // 更新水平方向数据
            horizontalData.put("result_file_id", horizontalResultFileId);
            horizontalData.put("vehicle_count", horizontalVehicleCount);
            horizontalData.put("vehicle_types", horizontalVehicleTypes);
            
            // 检查水平方向响应状态
            if (horizontalResponse.statusCode() != 200) {
                log.error("Python API返回错误状态码: {}, 响应内容: {}",
                         horizontalResponse.statusCode(), horizontalResponse.body());
                throw new IOException("水平方向视频分析失败: " + horizontalResponse.body());
            }

            // 检查垂直方向响应状态
            if (verticalResponse.statusCode() != 200) {
                log.error("Python API返回错误状态码: {}, 响应内容: {}",
                         verticalResponse.statusCode(), verticalResponse.body());
                throw new IOException("垂直方向视频分析失败: " + verticalResponse.body());
            }

            // 更新进度
            task.setProgress(60);
            task.setMessage("处理分析结果...");
            saveVideoAnalysis(task);
            updateTaskProgress(task);
            
            // 解析垂直方向分析结果
            Map<String, Object> verticalResult = mapper.readValue(verticalResponse.body(), Map.class);
            
            if (!"success".equals(verticalResult.get("status"))) {
                String errorMessage = (String) verticalResult.getOrDefault("message", "未知错误");
                log.error("垂直方向视频分析失败: {}", errorMessage);
                throw new IOException("垂直方向视频分析失败: " + errorMessage);
            }
            
            // 提取垂直方向分析结果
            String verticalResultFileId = (String) verticalResult.get("result_file_id");
            Integer verticalVehicleCount = (Integer) verticalResult.get("vehicle_count");
            Map<String, Integer> verticalVehicleTypes = (Map<String, Integer>) verticalResult.get("vehicle_types");
            
            log.info("垂直方向视频分析完成: 车辆数={}, 结果ID={}", 
                    verticalVehicleCount, verticalResultFileId);
            
            // 更新垂直方向数据
            verticalData.put("result_file_id", verticalResultFileId);
            verticalData.put("vehicle_count", verticalVehicleCount);
            verticalData.put("vehicle_types", verticalVehicleTypes);
            
            // 更新任务进度
            task.setProgress(90);
            task.setMessage("合并分析结果...");
            saveVideoAnalysis(task);
            updateTaskProgress(task);
            
            // 合并结果数据
            Map<String, Object> resultData = new HashMap<>();
            
            // 总体车辆计数（水平+垂直）
            int totalVehicles = horizontalVehicleCount + verticalVehicleCount;
            resultData.put("vehicleCount", totalVehicles);
            
            // 合并车辆类型统计
            Map<String, Integer> totalVehicleTypes = new HashMap<>();
            
            // 添加水平方向数据
            for (Map.Entry<String, Integer> entry : horizontalVehicleTypes.entrySet()) {
                totalVehicleTypes.put(entry.getKey(), entry.getValue());
            }
            
            // 添加垂直方向数据（累加相同类型）
            for (Map.Entry<String, Integer> entry : verticalVehicleTypes.entrySet()) {
                String key = entry.getKey();
                Integer value = entry.getValue();
                
                if (totalVehicleTypes.containsKey(key)) {
                    totalVehicleTypes.put(key, totalVehicleTypes.get(key) + value);
                } else {
                    totalVehicleTypes.put(key, value);
                }
            }
            
            resultData.put("vehicleTypeStats", totalVehicleTypes);
            
            // 计算总处理时间
            Double horizontalProcessingTime = (Double) horizontalResult.get("processing_time");
            Double verticalProcessingTime = (Double) verticalResult.get("processing_time");
            Double totalProcessingTime = horizontalProcessingTime + verticalProcessingTime;
            resultData.put("processingTime", totalProcessingTime);
            
            // 使用水平方向视频作为主要结果视频
            resultData.put("resultPath", horizontalResultFileId);
            
            // 设置缩略图
            resultData.put("thumbnailUrl", task.getThumbnailUrl());
            
            // 保存单独的方向数据
            resultData.put("horizontal_data", horizontalData);
            resultData.put("vertical_data", verticalData);
            
            // 设置分析模式
            resultData.put("mode", "intersection");
            
            // 完成分析
            task.setProgress(100);
            task.setMessage("十字路口视频分析已完成");
            saveVideoAnalysis(task);
            updateTaskProgress(task);
            
            completeAnalysis(task.getTaskId(), resultData);
            
            log.info("十字路口视频分析完成: 任务ID={}, 总车辆数={}", task.getTaskId(), totalVehicles);
            
        } catch (Exception e) {
            log.error("处理十字路口视频失败: {}", e.getMessage(), e);
            updateStatus(task.getTaskId(), "failed", "处理失败: " + e.getMessage());
        }
    }

    /**
     * 创建十字路口结果视频（复制原始视频作为结果）
     */
    private String createIntersectionResultVideo(VideoAnalysis task) {
        try {
            // 确保结果目录存在
            File resultDirFile = new File(resultsDir);
            if (!resultDirFile.exists()) {
                resultDirFile.mkdirs();
            }
            
            // 创建结果文件路径
            String resultFileName = "intersection_" + task.getTaskId() + ".mp4";
            Path resultPath = Paths.get(resultsDir, resultFileName);
            
            // 获取水平视频路径（作为主要视频）
            String videoPathString = task.getVideoPath();
            if (videoPathString != null && videoPathString.contains(";")) {
                String[] paths = videoPathString.split(";");
                if (paths.length >= 1) {
                    Path sourcePath = Paths.get(paths[0]); // 使用第一个视频（水平视频）
                    if (Files.exists(sourcePath)) {
                        Files.copy(sourcePath, resultPath);
                        log.info("已创建十字路口结果视频: {}", resultPath);
                        return resultPath.toString();
                    }
                }
            }
            
            log.warn("无法创建十字路口结果视频");
            return null;
        } catch (Exception e) {
            log.error("创建十字路口结果视频失败: {}", e.getMessage(), e);
            return null;
        }
    }
    


    /**
     * 从视频中提取缩略图并保存到GridFS
     * @param videoPath 视频文件路径或GridFS文件ID
     * @return 缩略图文件ID
     */
    private String saveVideoThumbnail(String videoPath) {
        if (videoPath == null || videoPath.isEmpty()) {
            log.warn("视频路径为空，无法生成缩略图");
            return null;
        }
        
        try {
            // 生成唯一文件名
            String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            String fileName = "thumbnail_" + timestamp + ".jpg";
            
            // 确定视频源 - 可能是文件路径或GridFS ID
            String actualVideoPath = videoPath;
            File tempVideoFile = null;
            
            // 检查是否为GridFS ID (24位十六进制字符串)
            if (videoPath.matches("[0-9a-f]{24}")) {
                log.info("视频源是GridFS ID，需要获取临时文件: {}", videoPath);
                
                try {
                    // 从GridFS获取视频文件
                    GridFsResource gridFsResource = getVideoFromGridFs(videoPath);
                    
                    if (gridFsResource != null && gridFsResource.exists()) {
                        // 创建临时文件
                        tempVideoFile = File.createTempFile("video_", ".mp4");
                        tempVideoFile.deleteOnExit();
                        
                        // 将GridFS文件保存为临时文件
                        try (InputStream inputStream = gridFsResource.getInputStream();
                             java.io.FileOutputStream outputStream = new java.io.FileOutputStream(tempVideoFile)) {
                            byte[] buffer = new byte[4096];
                            int bytesRead;
                            while ((bytesRead = inputStream.read(buffer)) != -1) {
                                outputStream.write(buffer, 0, bytesRead);
                            }
                        }
                        
                        log.info("已从GridFS获取视频并保存为临时文件: {}", tempVideoFile.getAbsolutePath());
                        actualVideoPath = tempVideoFile.getAbsolutePath();
                    } else {
                        log.error("在GridFS中未找到视频: {}", videoPath);
                        return null;
                    }
                } catch (Exception e) {
                    log.error("从GridFS获取视频时出错: {}", e.getMessage(), e);
                    return null;
                }
            } else {
                // 检查本地文件是否存在
                File videoFile = new File(videoPath);
                if (!videoFile.exists()) {
                    log.error("本地视频文件不存在: {}", videoPath);
                    return null;
                }
            }
            
            // 使用JavaCV提取缩略图
            log.info("使用JavaCV从视频提取缩略图: {}", actualVideoPath);
            FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(actualVideoPath);
            grabber.start();
            
            // 跳到视频的第一秒
            grabber.setTimestamp(1000000); // 微秒单位，1秒 = 1000000微秒
            Frame frame = grabber.grabImage();
            
            try {
                if (frame != null) {
                    Java2DFrameConverter converter = new Java2DFrameConverter();
                    BufferedImage bufferedImage = converter.convert(frame);
                    grabber.stop();
                    
                    if (bufferedImage != null) {
                        // 准备元数据
                        Map<String, Object> metadata = new HashMap<>();
                        metadata.put("type", "thumbnail");
                        metadata.put("sourceVideo", videoPath);
                        metadata.put("timestamp", timestamp);
                        
                        // 保存到GridFS
                        String fileId = saveThumbnailToGridFs(bufferedImage, fileName, metadata);
                        if (fileId != null) {
                            log.info("缩略图已生成并保存到GridFS，ID: {}", fileId);
                            
                            // 清理临时文件
                            if (tempVideoFile != null && tempVideoFile.exists()) {
                                tempVideoFile.delete();
                                log.info("已删除临时视频文件: {}", tempVideoFile.getAbsolutePath());
                            }
                            
                            return fileId;  // 返回文件ID
                        }
                    }
                } else {
                    log.warn("无法从视频提取帧");
                }
            } finally {
                // 确保关闭grabber
                if (grabber != null) {
                    try {
                        grabber.stop();
                        grabber.release();
                    } catch (Exception e) {
                        log.warn("关闭视频grabber时出错", e);
                    }
                }
                
                // 清理临时文件
                if (tempVideoFile != null && tempVideoFile.exists()) {
                    tempVideoFile.delete();
                    log.info("已删除临时视频文件: {}", tempVideoFile.getAbsolutePath());
                }
            }
            
            return null;
        } catch (Exception e) {
            log.error("生成视频缩略图时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    // 添加保存视频到GridFS的方法
    private String saveVideoToGridFs(String filePath, String filename, Map<String, Object> metadata) {
        try {
            log.info("开始将视频保存到GridFS: {}", filePath);
            
            File videoFile = new File(filePath);
            if (!videoFile.exists()) {
                log.error("视频文件不存在: {}", filePath);
                return null;
            }
            
            // 创建元数据对象
            DBObject metaData = new BasicDBObject();
            if (metadata != null) {
                metadata.forEach(metaData::put);
            }
            metaData.put("contentType", "video/mp4");
            metaData.put("uploadDate", new Date());
            
            // 保存文件到GridFS
            try (InputStream inputStream = new FileInputStream(videoFile)) {
                ObjectId id = gridFsTemplate.store(
                    inputStream,
                    filename,
                    "video/mp4",
                    metaData
                );
                
                log.info("视频已保存到GridFS，文件ID: {}", id.toString());
                return id.toString();
            }
        } catch (Exception e) {
            log.error("保存视频到GridFS失败", e);
            return null;
        }
    }
    
    // 添加保存图像到GridFS的方法
    private String saveThumbnailToGridFs(BufferedImage image, String filename, Map<String, Object> metadata) {
        try {
            log.info("开始将缩略图保存到GridFS: {}", filename);
            
            // 将BufferedImage转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "jpg", baos);
            byte[] imageBytes = baos.toByteArray();
            
            // 创建元数据对象
            DBObject metaData = new BasicDBObject();
            if (metadata != null) {
                metadata.forEach(metaData::put);
            }
            metaData.put("contentType", "image/jpeg");
            metaData.put("uploadDate", new Date());
            
            // 保存图像到GridFS
            ObjectId id = gridFsTemplate.store(
                new ByteArrayInputStream(imageBytes),
                filename,
                "image/jpeg",
                metaData
            );
            
            log.info("图像已保存到GridFS，文件ID: {}", id.toString());
            return id.toString();
        } catch (Exception e) {
            log.error("保存图像到GridFS失败", e);
            return null;
        }
    }
    
    // 从GridFS中获取视频
    private GridFsResource getVideoFromGridFs(String fileId) {
        try {
            ObjectId objectId = new ObjectId(fileId);
            return gridFsOperations.getResource(
                gridFsTemplate.findOne(new Query(Criteria.where("_id").is(objectId)))
            );
        } catch (Exception e) {
            log.error("从GridFS获取视频失败", e);
            return null;
        }
    }
    
    // 从GridFS中获取图像
    private GridFsResource getImageFromGridFs(String fileId) {
        try {
            ObjectId objectId = new ObjectId(fileId);
            return gridFsOperations.getResource(
                gridFsTemplate.findOne(new Query(Criteria.where("_id").is(objectId)))
            );
        } catch (Exception e) {
            log.error("从GridFS获取图像失败", e);
            return null;
        }
    }

    /**
     * 更新任务进度并通过WebSocket发送通知
     * @param task 视频分析任务
     */
    private void updateTaskProgress(VideoAnalysis task) {
        // 如果WebSocket控制器存在，发送进度更新
        if (webSocketController != null) {
            try {
                // 创建进度消息
                Map<String, Object> progressMessage = new HashMap<>();
                progressMessage.put("taskId", task.getTaskId());
                progressMessage.put("status", task.getStatus());
                progressMessage.put("progress", task.getProgress());
                progressMessage.put("message", task.getMessage());

                // 发送进度更新
                webSocketController.sendProgressUpdate(task.getTaskId(), progressMessage);
            } catch (Exception e) {
                log.error("通过WebSocket发送任务进度失败", e);
            }
        }
    }

    /**
     * 初始化实时帧推送功能
     * @param taskId 任务ID
     */
    private void initializeRealtimeFramePush(String taskId) {
        if (webSocketController != null) {
            try {
                log.info("为任务 {} 初始化实时帧推送功能", taskId);

                // 发送初始化消息，通知前端准备接收帧数据
                Map<String, Object> initMessage = new HashMap<>();
                initMessage.put("type", "frame_push_init");
                initMessage.put("taskId", taskId);
                initMessage.put("message", "实时帧推送已启动");
                initMessage.put("timestamp", java.time.LocalDateTime.now().toString());

                webSocketController.sendProgressUpdate(taskId, initMessage);

            } catch (Exception e) {
                log.error("初始化实时帧推送失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 清理任务相关的缓存和资源
     * @param taskId 任务ID
     */
    private void cleanupTaskResources(String taskId) {
        if (webSocketController != null) {
            try {
                log.info("清理任务 {} 的相关资源", taskId);

                // 清理帧缓存
                webSocketController.clearTaskCache(taskId);

                // 发送清理完成消息
                Map<String, Object> cleanupMessage = new HashMap<>();
                cleanupMessage.put("type", "frame_push_cleanup");
                cleanupMessage.put("taskId", taskId);
                cleanupMessage.put("message", "实时帧推送已结束");
                cleanupMessage.put("timestamp", java.time.LocalDateTime.now().toString());

                webSocketController.sendProgressUpdate(taskId, cleanupMessage);

            } catch (Exception e) {
                log.error("清理任务资源失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 使用FFmpeg获取视频时长的工具方法
     * @param videoFile 视频文件
     * @return 视频时长（秒）
     */
    private double getVideoDuration(File videoFile) {
        double duration = 0;
        
        if (videoFile == null || !videoFile.exists()) {
            log.error("视频文件不存在或为空");
            return duration;
        }
        
        log.info("开始获取视频时长，文件路径: {}, 文件大小: {}字节", 
                videoFile.getAbsolutePath(), videoFile.length());
        
        try {
            // 首先尝试使用JavaCV获取时长（更可靠）
            try {
                FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(videoFile);
                grabber.start();
                duration = grabber.getLengthInTime() / 1000000.0; // 微秒转秒
                grabber.stop();
                grabber.release();
                
                if (duration > 0) {
                    log.info("使用JavaCV成功获取视频时长: {}秒", duration);
                    return duration;
                } else {
                    log.warn("JavaCV获取视频时长为0，尝试使用FFmpeg命令行");
                }
            } catch (Exception e) {
                log.warn("使用JavaCV获取视频时长失败: {}, 尝试使用FFmpeg命令行", e.getMessage());
            }
            
            // 备选方案：使用FFmpeg命令行
            List<String> command = new ArrayList<>();
            command.add("ffmpeg");
            command.add("-i");
            command.add(videoFile.getAbsolutePath());
            
            ProcessBuilder pb = new ProcessBuilder(command);
            pb.redirectErrorStream(true);
            Process process = pb.start();
            
            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    if (line.contains("Duration:")) {
                        // 提取时长，格式通常是 "Duration: 00:00:30.57"
                        log.info("找到时长行: {}", line);
                        int durationIndex = line.indexOf("Duration:") + 10;
                        String durationStr = line.substring(durationIndex, durationIndex + 11).trim();
                        log.info("提取的时长字符串: {}", durationStr);
                        
                        String[] parts = durationStr.split(":");
                        if (parts.length >= 3) {
                            try {
                                double hours = Double.parseDouble(parts[0]);
                                double minutes = Double.parseDouble(parts[1]);
                                double seconds = Double.parseDouble(parts[2]);
                                duration = hours * 3600 + minutes * 60 + seconds;
                                log.info("成功解析视频时长: {}秒 ({}:{}:{})", 
                                        duration, parts[0], parts[1], parts[2]);
                                break;
                            } catch (NumberFormatException e) {
                                log.error("解析时长字符串失败: {} - {}", durationStr, e.getMessage());
                            }
                        }
                    }
                }
            }
            
            // 如果没有找到时长信息，记录完整输出以便调试
            if (duration <= 0) {
                log.warn("未能从FFmpeg输出中提取时长，完整输出: {}", output.toString());
            }
            
            // 等待进程完成
            boolean completed = process.waitFor(10, TimeUnit.SECONDS);
            if (!completed) {
                log.warn("FFmpeg进程在10秒内未完成，强制终止");
                process.destroyForcibly();
            }
            
        } catch (Exception e) {
            log.error("获取视频时长时发生异常: {}", e.getMessage(), e);
        }
        
        log.info("最终获取的视频时长: {}秒", duration);
        return duration;
    }

    // ==================== 四方向交通分析实现方法 ====================

    @Override
    public FourWayIntersectionAnalysis processFourWayIntersectionVideos(
            MultipartFile eastFile,
            MultipartFile southFile,
            MultipartFile westFile,
            MultipartFile northFile,
            String userId,
            String username,
            String role) throws IOException {

        // 生成UUID格式的任务ID
        String taskId = UUID.randomUUID().toString();
        log.info("开始处理四方向十字路口视频分析，任务ID: {}", taskId);

        // 检查上传目录是否存在，不存在则创建
        File dir = new File(uploadDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        // 创建四方向分析任务
        FourWayIntersectionAnalysis analysis = new FourWayIntersectionAnalysis();
        analysis.setTaskId(taskId);
        analysis.setUserId(userId);
        analysis.setUsername(username);
        analysis.setRole(role);
        analysis.setStatus("queued");
        analysis.setProgress(0);
        analysis.setMessage("四方向视频上传成功，等待分析...");
        analysis.setCreatedAt(LocalDateTime.now());
        analysis.setUpdatedAt(LocalDateTime.now());
        analysis.initializeDirections();

        // 处理四个方向的视频文件
        Map<Direction, MultipartFile> videoFiles = new HashMap<>();
        videoFiles.put(Direction.EAST, eastFile);
        videoFiles.put(Direction.SOUTH, southFile);
        videoFiles.put(Direction.WEST, westFile);
        videoFiles.put(Direction.NORTH, northFile);

        // 为每个方向处理视频文件
        for (Map.Entry<Direction, MultipartFile> entry : videoFiles.entrySet()) {
            Direction direction = entry.getKey();
            MultipartFile videoFile = entry.getValue();

            if (videoFile != null && !videoFile.isEmpty()) {
                DirectionVideoData directionData = processDirectionVideo(
                    videoFile, direction, taskId, userId, username);
                analysis.setDirectionData(direction, directionData);
                log.info("成功处理{}方向视频: {}", direction.getDisplayName(),
                        directionData.getFilename());
            } else {
                log.warn("{}方向视频文件为空或未提供", direction.getDisplayName());
            }
        }

        // 保存分析任务到数据库
        FourWayIntersectionAnalysis savedAnalysis = saveFourWayAnalysis(analysis);
        log.info("保存四方向分析任务到数据库，任务ID: {}", savedAnalysis.getTaskId());

        // 异步处理四方向视频分析
        executorService.submit(() -> processFourWayAnalysis(savedAnalysis));

        return savedAnalysis;
    }

    @Override
    public Optional<FourWayIntersectionAnalysis> findFourWayAnalysisByTaskId(String taskId) {
        Query query = Query.query(Criteria.where("task_id").is(taskId));
        FourWayIntersectionAnalysis result = mongoTemplate.findOne(
            query, FourWayIntersectionAnalysis.class, "intersection_analysis_four_way");
        return Optional.ofNullable(result);
    }

    @Override
    public FourWayIntersectionAnalysis updateFourWayAnalysisStatus(String taskId, String status, String message) {
        Query query = Query.query(Criteria.where("task_id").is(taskId));
        Update update = new Update()
                .set("status", status)
                .set("message", message)
                .set("updated_at", LocalDateTime.now());

        if ("processing".equals(status)) {
            update.set("processing_start_time", LocalDateTime.now());
        } else if ("completed".equals(status) || "failed".equals(status)) {
            update.set("processing_end_time", LocalDateTime.now());
        }

        mongoTemplate.updateFirst(query, update,
            FourWayIntersectionAnalysis.class, "intersection_analysis_four_way");

        return findFourWayAnalysisByTaskId(taskId).orElse(null);
    }

    @Override
    public FourWayIntersectionAnalysis updateDirectionProgress(
            String taskId, Direction direction, int progress, String status) {

        // 获取现有的分析任务
        FourWayIntersectionAnalysis analysis = findFourWayAnalysisByTaskId(taskId).orElse(null);
        if (analysis == null) {
            log.error("未找到四方向分析任务: {}", taskId);
            return null;
        }

        // 更新指定方向的进度和状态
        DirectionVideoData directionData = analysis.getDirectionData(direction);
        if (directionData != null) {
            directionData.setProgress(progress);
            directionData.setStatus(status);

            log.info("更新{}方向进度: 任务ID={}, 进度={}%, 状态={}",
                    direction.getDisplayName(), taskId, progress, status);
        } else {
            log.warn("未找到{}方向的数据: 任务ID={}", direction.getDisplayName(), taskId);
        }

        // 重新计算整体进度
        analysis.calculateOverallProgress();
        analysis.setUpdatedAt(LocalDateTime.now());

        // 保存更新后的分析任务
        return saveFourWayAnalysis(analysis);
    }

    @Override
    public FourWayIntersectionAnalysis completeFourWayAnalysis(
            String taskId, Map<Direction, Map<String, Object>> directionsResults) {

        FourWayIntersectionAnalysis analysis = findFourWayAnalysisByTaskId(taskId).orElse(null);
        if (analysis == null) {
            log.error("未找到四方向分析任务: {}", taskId);
            return null;
        }

        // 更新各方向的检测结果
        for (Map.Entry<Direction, Map<String, Object>> entry : directionsResults.entrySet()) {
            Direction direction = entry.getKey();
            Map<String, Object> result = entry.getValue();

            DirectionVideoData directionData = analysis.getDirectionData(direction);
            if (directionData != null) {
                // 更新检测结果
                directionData.setVehicleCount((Integer) result.getOrDefault("vehicle_count", 0));
                directionData.setStatus("completed");
                directionData.setProgress(100);
                directionData.setProcessingEndTime(LocalDateTime.now());

                // 更新车辆类型统计
                @SuppressWarnings("unchecked")
                Map<String, Integer> vehicleTypes = (Map<String, Integer>) result.get("vehicle_types");
                if (vehicleTypes != null) {
                    directionData.setVehicleTypes(vehicleTypes);
                }
            }
        }

        // 生成智能交通分析结果
        TrafficAnalysisResult trafficAnalysis = generateTrafficAnalysis(analysis);
        analysis.setTrafficAnalysis(trafficAnalysis);

        // 更新任务状态
        analysis.updateStatus("completed");
        analysis.setMessage("四方向交通分析完成");
        analysis.setProgress(100);

        return saveFourWayAnalysis(analysis);
    }

    @Override
    public Map<String, Object> getFourWayAnalysisResult(String taskId) {
        FourWayIntersectionAnalysis analysis = findFourWayAnalysisByTaskId(taskId).orElse(null);
        if (analysis == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("taskId", analysis.getTaskId());
        result.put("status", analysis.getStatus());
        result.put("progress", analysis.getProgress());
        result.put("message", analysis.getMessage());
        result.put("createdAt", analysis.getCreatedAt());
        result.put("updatedAt", analysis.getUpdatedAt());
        result.put("processingDuration", analysis.getProcessingDurationSeconds());

        // 各方向数据
        Map<String, Object> directionsData = new HashMap<>();
        for (Direction direction : Direction.values()) {
            DirectionVideoData directionData = analysis.getDirectionData(direction);
            if (directionData != null) {
                Map<String, Object> directionResult = new HashMap<>();
                directionResult.put("vehicleCount", directionData.getVehicleCount());
                directionResult.put("vehicleTypes", directionData.getVehicleTypes());
                directionResult.put("status", directionData.getStatus());
                directionResult.put("progress", directionData.getProgress());
                directionResult.put("crowdLevel", directionData.getCrowdLevel());
                directionResult.put("averageFlowDensity", directionData.getAverageFlowDensity());
                directionsData.put(direction.getCode(), directionResult);
            }
        }
        result.put("directions", directionsData);

        // 智能交通分析结果
        if (analysis.getTrafficAnalysis() != null) {
            result.put("trafficAnalysis", analysis.getTrafficAnalysis());
        }

        return result;
    }

    @Override
    public Map<String, Object> generateFourWayTrafficReport(String taskId) {
        FourWayIntersectionAnalysis analysis = findFourWayAnalysisByTaskId(taskId).orElse(null);
        if (analysis == null) {
            return null;
        }

        Map<String, Object> report = new HashMap<>();
        report.put("taskId", taskId);
        report.put("generatedAt", LocalDateTime.now());
        report.put("title", "四方向智能交通分析报告");

        // 基本统计信息
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalVehicleCount", analysis.getTotalVehicleCount());
        summary.put("completedDirections", analysis.getCompletedDirectionsCount());
        summary.put("analysisStatus", analysis.getStatus());

        // 各方向详细数据
        Map<String, Object> directionsReport = new HashMap<>();
        for (Direction direction : Direction.values()) {
            DirectionVideoData directionData = analysis.getDirectionData(direction);
            if (directionData != null) {
                Map<String, Object> directionReport = new HashMap<>();
                directionReport.put("direction", direction.getDisplayName());
                directionReport.put("vehicleCount", directionData.getVehicleCount());
                directionReport.put("vehicleTypes", directionData.getVehicleTypes());
                directionReport.put("crowdLevel", directionData.getCrowdLevel());
                directionReport.put("averageFlowDensity", directionData.getAverageFlowDensity());
                directionReport.put("peakFlowCount", directionData.getPeakFlowCount());
                directionsReport.put(direction.getCode(), directionReport);
            }
        }

        report.put("summary", summary);
        report.put("directions", directionsReport);

        // 智能分析结果
        if (analysis.getTrafficAnalysis() != null) {
            TrafficAnalysisResult trafficAnalysis = analysis.getTrafficAnalysis();
            Map<String, Object> intelligentAnalysis = new HashMap<>();
            intelligentAnalysis.put("peakDirection", trafficAnalysis.getPeakDirection());
            intelligentAnalysis.put("trafficFlowBalance", trafficAnalysis.getTrafficFlowBalance());
            intelligentAnalysis.put("congestionLevel", trafficAnalysis.getCongestionLevel());
            intelligentAnalysis.put("flowCorrelation", trafficAnalysis.getFlowCorrelation());

            if (trafficAnalysis.getSignalOptimization() != null) {
                intelligentAnalysis.put("signalOptimization", trafficAnalysis.getSignalOptimization());
            }

            report.put("intelligentAnalysis", intelligentAnalysis);
        }

        // 生成建议
        List<String> recommendations = generateRecommendations(analysis);
        report.put("recommendations", recommendations);

        return report;
    }

    // ==================== 辅助方法 ====================

    /**
     * 保存四方向分析任务到数据库
     */
    private FourWayIntersectionAnalysis saveFourWayAnalysis(FourWayIntersectionAnalysis analysis) {
        if (analysis.getCreatedAt() == null) {
            analysis.setCreatedAt(LocalDateTime.now());
        }
        analysis.setUpdatedAt(LocalDateTime.now());
        return mongoTemplate.save(analysis, "intersection_analysis_four_way");
    }

    /**
     * 处理单个方向的视频文件
     */
    private DirectionVideoData processDirectionVideo(
            MultipartFile videoFile, Direction direction, String taskId,
            String userId, String username) throws IOException {

        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String filename = direction.getCode() + "_" + timestamp + "_" + taskId.substring(0, 8) + ".mp4";

        DirectionVideoData directionData = new DirectionVideoData();
        directionData.setFilename(filename);
        directionData.setStatus("queued");
        directionData.setProgress(0);
        directionData.setProcessingStartTime(LocalDateTime.now());

        // 获取视频时长并保存到GridFS
        File tempFile = null;
        try {
            tempFile = File.createTempFile(direction.getCode() + "_video_", ".mp4");
            videoFile.transferTo(tempFile);

            double duration = getVideoDuration(tempFile);
            directionData.setDuration(duration);

            // 保存到GridFS
            try (InputStream inputStream = new FileInputStream(tempFile)) {
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("contentType", videoFile.getContentType());
                metadata.put("filename", filename);
                metadata.put("userId", userId);
                metadata.put("username", username);
                metadata.put("uploadTime", new Date());
                metadata.put("taskId", taskId);
                metadata.put("direction", direction.getCode());
                metadata.put("duration", duration);

                ObjectId objectId = gridFsTemplate.store(
                    inputStream, filename, videoFile.getContentType(),
                    new Document(metadata)
                );
                directionData.setVideoPath(objectId.toString());

                log.info("{}方向视频保存到GridFS: {}, ID: {}, 时长: {}秒",
                        direction.getDisplayName(), filename, objectId.toString(), duration);
            }
        } finally {
            if (tempFile != null && tempFile.exists()) {
                try {
                    tempFile.delete();
                } catch (Exception e) {
                    log.warn("清理{}方向临时文件失败: {}", direction.getDisplayName(), e.getMessage());
                }
            }
        }

        return directionData;
    }



    /**
     * 处理四方向特定方向的视频分析
     */
    private void processFourWayDirectionVideo(String taskId, Direction direction, DirectionVideoData directionData) throws Exception {
        String videoId = directionData.getVideoPath();
        if (videoId == null || videoId.isEmpty()) {
            throw new IllegalArgumentException("视频ID为空");
        }

        log.info("开始处理{}方向视频分析，任务ID: {}, 视频ID: {}", direction.getDisplayName(), taskId, videoId);

        // 更新进度
        directionData.setProgress(20);
        updateDirectionProgress(taskId, direction, 20, "processing");

        // 发送WebSocket进度更新
        sendFourWayProgressUpdate(taskId, "processing", 20,
            String.format("正在分析%s方向视频", direction.getDisplayName()));

        // 调用Python API执行视频分析 - 根据方向选择不同的端口
        int port = getPortForDirection(direction);
        String apiUrl = String.format("http://localhost:%d/analyze_four_way_direction_video", port);

        // 创建HTTP客户端，添加超时配置以支持长视频处理
        java.net.http.HttpClient client = java.net.http.HttpClient.newBuilder()
            .version(java.net.http.HttpClient.Version.HTTP_1_1)
            .connectTimeout(java.time.Duration.ofSeconds(30))  // 连接超时30秒
            .build();

        // 准备请求数据
        String requestBody = String.format(
            "{\"video_id\": \"%s\", \"task_id\": \"%s\", \"direction\": \"%s\"}",
            videoId, taskId, direction.name().toLowerCase()
        );

        // 创建HTTP请求，添加读取超时以支持长视频处理
        java.net.http.HttpRequest request = java.net.http.HttpRequest.newBuilder()
            .uri(java.net.URI.create(apiUrl))
            .header("Content-Type", "application/json")
            .timeout(java.time.Duration.ofMinutes(10))  // 读取超时10分钟，支持长视频处理
            .POST(java.net.http.HttpRequest.BodyPublishers.ofString(requestBody))
            .build();

        log.info("发送{}方向视频分析请求到Python API: {}", direction.getDisplayName(), apiUrl);

        // 更新状态为分析中
        directionData.setProgress(30);
        updateDirectionProgress(taskId, direction, 30, "processing");

        // 发送请求
        java.net.http.HttpResponse<String> response = client.send(
            request, java.net.http.HttpResponse.BodyHandlers.ofString()
        );

        if (response.statusCode() != 200) {
            throw new RuntimeException("Python API调用失败，状态码: " + response.statusCode() + ", 响应: " + response.body());
        }

        // 解析响应
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> result = objectMapper.readValue(response.body(), Map.class);

        if (!"success".equals(result.get("status"))) {
            throw new RuntimeException("Python API返回错误: " + result.get("message"));
        }

        // 更新分析结果
        Map<String, Object> analysisResult = (Map<String, Object>) result.get("result");
        if (analysisResult != null) {
            Integer vehicleCount = (Integer) analysisResult.get("vehicle_count");
            if (vehicleCount != null) {
                directionData.setVehicleCount(vehicleCount);
            }

            Map<String, Integer> vehicleTypes = (Map<String, Integer>) analysisResult.get("vehicle_types");
            if (vehicleTypes != null) {
                directionData.setVehicleTypes(vehicleTypes);
            }

            // 设置拥挤等级
            directionData.setCrowdLevel(determineCrowdLevel(directionData.getVehicleCount()));
            directionData.calculateAverageFlowDensity();
        }

        // 完成分析
        directionData.setProgress(100);
        directionData.setStatus("completed");
        updateDirectionProgress(taskId, direction, 100, "completed");

        // 发送方向完成的WebSocket消息
        Map<String, Object> directionResult = new HashMap<>();
        directionResult.put("vehicleCount", directionData.getVehicleCount());
        directionResult.put("vehicleTypes", directionData.getVehicleTypes());
        directionResult.put("crowdLevel", directionData.getCrowdLevel());
        sendFourWayDirectionComplete(taskId, direction.name().toLowerCase(), directionResult);

        log.info("{}方向视频分析完成，任务ID: {}, 车辆数: {}", direction.getDisplayName(), taskId, directionData.getVehicleCount());
    }

    /**
     * 异步处理四方向视频分析
     */
    private void processFourWayAnalysis(FourWayIntersectionAnalysis analysis) {
        try {
            log.info("开始异步处理四方向视频分析，任务ID: {}", analysis.getTaskId());

            // 记录线程池状态
            threadPoolMonitor.logThreadPoolStatus();

            // 检查四方向分析执行器是否有足够的可用线程
            if (!threadPoolMonitor.isFourWayAnalysisExecutorAvailable()) {
                log.warn("四方向分析执行器线程不足，当前状态: {}",
                        threadPoolMonitor.getFourWayAnalysisExecutorStatus());
            }

            // 更新状态为处理中
            updateFourWayAnalysisStatus(analysis.getTaskId(), "processing", "正在分析四方向视频...");

            // 初始化四方向实时帧推送功能
            initializeFourWayRealtimeFramePush(analysis.getTaskId());

            // 发送开始分析的WebSocket消息
            sendFourWayProgressUpdate(analysis.getTaskId(), "processing", 10, "开始四方向视频分析");

            // 并行处理四个方向的视频 - 使用CompletableFuture实现并行调用
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (Direction direction : Direction.values()) {
                DirectionVideoData directionData = analysis.getDirectionData(direction);
                if (directionData != null && "queued".equals(directionData.getStatus())) {
                    log.info("开始并行处理{}方向视频，视频ID: {}", direction.getDisplayName(), directionData.getVideoPath());

                    // 更新方向状态
                    updateDirectionProgress(analysis.getTaskId(), direction, 10, "processing");

                    // 创建异步任务 - 使用专门的四方向分析执行器
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            log.info("开始并行处理{}方向视频，使用四方向专用执行器", direction.getDisplayName());

                            // 调用Python API进行实际的视频分析
                            processFourWayDirectionVideo(analysis.getTaskId(), direction, directionData);

                            // 完成该方向
                            updateDirectionProgress(analysis.getTaskId(), direction, 100, "completed");
                            log.info("{}方向视频分析完成，车辆数: {}", direction.getDisplayName(), directionData.getVehicleCount());

                        } catch (Exception e) {
                            log.error("{}方向视频分析失败: {}", direction.getDisplayName(), e.getMessage(), e);
                            directionData.setStatus("failed");
                            directionData.setErrorMessage(e.getMessage());
                            updateDirectionProgress(analysis.getTaskId(), direction, 0, "failed");
                        }
                    }, fourWayAnalysisExecutor);

                    futures.add(future);
                }
            }

            // 等待所有方向的视频分析完成
            log.info("等待所有四个方向的视频分析完成...");

            // 记录开始等待时的线程池状态
            threadPoolMonitor.logThreadPoolStatus();

            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.join(); // 阻塞等待所有任务完成

            log.info("所有四个方向的视频分析已完成");

            // 记录完成后的线程池状态
            threadPoolMonitor.logThreadPoolStatus();

            // 生成智能分析结果
            Map<Direction, Map<String, Object>> results = new HashMap<>();
            int totalVehicles = 0;
            for (Direction direction : Direction.values()) {
                DirectionVideoData directionData = analysis.getDirectionData(direction);
                if (directionData != null) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("vehicle_count", directionData.getVehicleCount());
                    result.put("vehicle_types", directionData.getVehicleTypes());
                    results.put(direction, result);
                    totalVehicles += directionData.getVehicleCount();
                }
            }

            // 完成分析
            completeFourWayAnalysis(analysis.getTaskId(), results);

            // 发送四方向整体完成的WebSocket消息
            sendFourWayAnalysisCompleteMessage(analysis.getTaskId(), analysis, totalVehicles);

            log.info("四方向视频分析完成，任务ID: {}", analysis.getTaskId());

        } catch (Exception e) {
            log.error("四方向视频分析失败，任务ID: {}, 错误: {}",
                    analysis.getTaskId(), e.getMessage(), e);
            updateFourWayAnalysisStatus(analysis.getTaskId(), "failed",
                    "分析失败: " + e.getMessage());
        }
    }

    /**
     * 生成智能交通分析结果
     */
    private TrafficAnalysisResult generateTrafficAnalysis(FourWayIntersectionAnalysis analysis) {
        TrafficAnalysisResult result = new TrafficAnalysisResult();

        // 计算总车辆数
        int totalVehicles = analysis.getTotalVehicleCount();
        result.setTotalVehicleCount(totalVehicles);

        // 找出车流量最大的方向
        Direction peakDirection = null;
        int maxVehicles = 0;
        Map<Direction, Integer> directionCounts = new HashMap<>();

        for (Direction direction : Direction.values()) {
            DirectionVideoData directionData = analysis.getDirectionData(direction);
            if (directionData != null) {
                int count = directionData.getVehicleCount();
                directionCounts.put(direction, count);
                if (count > maxVehicles) {
                    maxVehicles = count;
                    peakDirection = direction;
                }
            }
        }

        result.setPeakDirection(peakDirection);

        // 计算交通流量平衡度
        result.calculateTrafficFlowBalance(directionCounts);

        // 确定拥堵等级
        double averageVehicles = totalVehicles / 4.0;
        result.determineCongestionLevel(totalVehicles, averageVehicles);

        // 生成信号灯优化建议
        TrafficAnalysisResult.SignalOptimization signalOpt = new TrafficAnalysisResult.SignalOptimization();
        signalOpt.setRecommendedCycle(120); // 推荐120秒周期

        // 根据各方向车流量分配绿灯时间
        Map<String, Integer> greenTimeAllocation = new HashMap<>();
        int totalGreenTime = 80; // 总绿灯时间80秒

        for (Direction direction : Direction.values()) {
            int count = directionCounts.getOrDefault(direction, 0);
            int greenTime = totalVehicles > 0 ? (count * totalGreenTime / totalVehicles) : 20;
            greenTime = Math.max(15, Math.min(35, greenTime)); // 限制在15-35秒之间
            greenTimeAllocation.put(direction.name().toLowerCase(), greenTime);
        }

        signalOpt.setGreenTimeAllocation(greenTimeAllocation);
        signalOpt.setReason("根据各方向车流量动态分配绿灯时间");
        signalOpt.setExpectedImprovement("预计可减少平均等待时间20-30%");

        result.setSignalOptimization(signalOpt);

        return result;
    }

    /**
     * 生成改进建议
     */
    private List<String> generateRecommendations(FourWayIntersectionAnalysis analysis) {
        List<String> recommendations = new ArrayList<>();

        if (analysis.getTrafficAnalysis() != null) {
            TrafficAnalysisResult trafficAnalysis = analysis.getTrafficAnalysis();

            // 根据拥堵等级给出建议
            String congestionLevel = trafficAnalysis.getCongestionLevel();
            switch (congestionLevel) {
                case "严重拥堵":
                    recommendations.add("建议增加交通疏导人员");
                    recommendations.add("考虑实施分时段限行措施");
                    recommendations.add("优化信号灯配时，延长主要方向绿灯时间");
                    break;
                case "中度拥堵":
                    recommendations.add("调整信号灯配时，提高通行效率");
                    recommendations.add("加强交通监控，及时发现异常情况");
                    break;
                case "轻度拥堵":
                    recommendations.add("保持现有交通管理措施");
                    recommendations.add("定期监测交通流量变化");
                    break;
                default:
                    recommendations.add("交通状况良好，维持现状");
            }

            // 根据流量平衡度给出建议
            double balance = trafficAnalysis.getTrafficFlowBalance();
            if (balance < 0.6) {
                recommendations.add("各方向车流量不均衡，建议优化信号配时");
                recommendations.add("考虑在车流量大的方向增设车道");
            }

            // 根据峰值方向给出建议
            if (trafficAnalysis.getPeakDirection() != null) {
                recommendations.add("重点关注" + trafficAnalysis.getPeakDirection().getDisplayName() +
                                 "的交通状况，适当延长该方向绿灯时间");
            }
        }

        return recommendations;
    }

    /**
     * 根据车辆数量确定拥挤等级
     */
    private String determineCrowdLevel(int vehicleCount) {
        if (vehicleCount < 10) {
            return "畅通";
        } else if (vehicleCount < 20) {
            return "轻度拥堵";
        } else if (vehicleCount < 35) {
            return "中度拥堵";
        } else {
            return "严重拥堵";
        }
    }

    // ==================== 扩展的四方向分析方法实现 ====================

    @Override
    public Page<FourWayIntersectionAnalysis> getFourWayTaskList(String userId, Pageable pageable) {
        try {
            log.info("获取用户四方向任务列表: userId={}", userId);

            Query query = Query.query(Criteria.where("userId").is(userId))
                    .with(Sort.by(Sort.Direction.DESC, "createdAt"));

            long total = mongoTemplate.count(query, FourWayIntersectionAnalysis.class);

            query.with(pageable);
            List<FourWayIntersectionAnalysis> tasks = mongoTemplate.find(query, FourWayIntersectionAnalysis.class);

            return new PageImpl<>(tasks, pageable, total);
        } catch (Exception e) {
            log.error("获取四方向任务列表失败: {}", e.getMessage(), e);
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

    @Override
    public Page<FourWayIntersectionAnalysis> getFourWayTaskListWithFilters(Map<String, Object> queryParams, Pageable pageable) {
        try {
            String currentUserId = (String) queryParams.get("currentUserId");
            String currentUserRole = (String) queryParams.get("currentUserRole");
            String search = (String) queryParams.get("search");
            String userIdFilter = (String) queryParams.get("userIdFilter");
            String statusFilter = (String) queryParams.get("statusFilter");
            String startDate = (String) queryParams.get("startDate");
            String endDate = (String) queryParams.get("endDate");

            log.info("获取四方向任务列表（带筛选）: currentUserId={}, role={}, search={}, userIdFilter={}, statusFilter={}, startDate={}, endDate={}",
                    currentUserId, currentUserRole, search, userIdFilter, statusFilter, startDate, endDate);

            // 构建查询条件
            Criteria criteria = new Criteria();
            List<Criteria> andCriteria = new ArrayList<>();

            // 权限控制：非管理员只能看到自己的任务
            if (!"admin".equalsIgnoreCase(currentUserRole)) {
                andCriteria.add(Criteria.where("userId").is(currentUserId));
            } else if (userIdFilter != null && !userIdFilter.trim().isEmpty()) {
                // 管理员可以按用户筛选
                andCriteria.add(Criteria.where("userId").is(userIdFilter));
            }

            // 搜索条件（任务ID或用户名）
            if (search != null && !search.trim().isEmpty()) {
                Criteria searchCriteria = new Criteria().orOperator(
                        Criteria.where("taskId").regex(search, "i"),
                        Criteria.where("username").regex(search, "i")
                );
                andCriteria.add(searchCriteria);
            }

            // 状态筛选
            if (statusFilter != null && !statusFilter.trim().isEmpty()) {
                andCriteria.add(Criteria.where("status").is(statusFilter));
            }

            // 日期范围筛选
            if (startDate != null && !startDate.trim().isEmpty()) {
                try {
                    LocalDateTime startDateTime = LocalDate.parse(startDate).atStartOfDay();
                    andCriteria.add(Criteria.where("createdAt").gte(startDateTime));
                } catch (Exception e) {
                    log.warn("解析开始日期失败: {}", startDate);
                }
            }

            if (endDate != null && !endDate.trim().isEmpty()) {
                try {
                    LocalDateTime endDateTime = LocalDate.parse(endDate).atTime(23, 59, 59);
                    andCriteria.add(Criteria.where("createdAt").lte(endDateTime));
                } catch (Exception e) {
                    log.warn("解析结束日期失败: {}", endDate);
                }
            }

            // 组合所有条件
            if (!andCriteria.isEmpty()) {
                criteria = criteria.andOperator(andCriteria.toArray(new Criteria[0]));
            }

            Query query = Query.query(criteria);

            // 计算总数
            long total = mongoTemplate.count(query, FourWayIntersectionAnalysis.class);

            // 应用分页和排序
            query.with(pageable);
            List<FourWayIntersectionAnalysis> tasks = mongoTemplate.find(query, FourWayIntersectionAnalysis.class);

            log.info("成功获取四方向任务列表: 总数={}, 当前页大小={}", total, tasks.size());
            return new PageImpl<>(tasks, pageable, total);

        } catch (Exception e) {
            log.error("获取四方向任务列表（带筛选）失败: {}", e.getMessage(), e);
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

    @Override
    public boolean deleteFourWayTask(String taskId, String userId) {
        try {
            log.info("删除四方向分析任务: taskId={}, userId={}", taskId, userId);

            Query query = Query.query(Criteria.where("taskId").is(taskId).and("userId").is(userId));
            FourWayIntersectionAnalysis task = mongoTemplate.findOne(query, FourWayIntersectionAnalysis.class);

            if (task == null) {
                log.warn("未找到要删除的四方向任务: taskId={}", taskId);
                return false;
            }

            // 删除相关的视频文件
            if (task.getDirections() != null) {
                for (DirectionVideoData direction : task.getDirections().values()) {
                    if (direction.getVideoPath() != null) {
                        deleteVideoFile(direction.getVideoPath(), "四方向视频");
                    }
                }
            }

            // 删除数据库记录
            mongoTemplate.remove(query, FourWayIntersectionAnalysis.class);

            log.info("四方向分析任务删除成功: taskId={}", taskId);
            return true;
        } catch (Exception e) {
            log.error("删除四方向分析任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public FourWayIntersectionAnalysis retryFourWayAnalysis(String taskId) {
        try {
            log.info("重新分析四方向视频: taskId={}", taskId);

            Optional<FourWayIntersectionAnalysis> taskOpt = findFourWayAnalysisByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                log.warn("未找到要重新分析的四方向任务: taskId={}", taskId);
                return null;
            }

            FourWayIntersectionAnalysis task = taskOpt.get();

            // 重置任务状态
            task.setStatus("queued");
            task.setMessage("重新排队等待分析");
            task.setProgress(0);
            task.setProcessingStartTime(null);
            task.setProcessingEndTime(null);
            task.setTrafficAnalysis(null);

            // 重置各方向状态
            if (task.getDirections() != null) {
                for (DirectionVideoData direction : task.getDirections().values()) {
                    direction.setStatus("queued");
                    direction.setProgress(0);
                    direction.setMessage("等待重新分析");
                }
            }

            // 保存更新
            mongoTemplate.save(task);

            // 重新提交分析任务
            processFourWayIntersectionAsync(task);

            log.info("四方向视频重新分析已启动: taskId={}", taskId);
            return task;
        } catch (Exception e) {
            log.error("重新分析四方向视频失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getFourWayRealtimeData(String taskId) {
        try {
            log.info("获取四方向实时检测数据: taskId={}", taskId);

            Optional<FourWayIntersectionAnalysis> taskOpt = findFourWayAnalysisByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                return createErrorResponse("未找到指定的四方向分析任务");
            }

            FourWayIntersectionAnalysis task = taskOpt.get();

            Map<String, Object> realtimeData = new HashMap<>();
            realtimeData.put("taskId", taskId);
            realtimeData.put("status", task.getStatus());
            realtimeData.put("progress", task.getProgress());
            realtimeData.put("message", task.getMessage());

            // 各方向实时数据
            Map<String, Object> directionsData = new HashMap<>();
            if (task.getDirections() != null) {
                for (Map.Entry<Direction, DirectionVideoData> entry : task.getDirections().entrySet()) {
                    Direction direction = entry.getKey();
                    DirectionVideoData data = entry.getValue();

                    Map<String, Object> directionInfo = new HashMap<>();
                    directionInfo.put("status", data.getStatus());
                    directionInfo.put("progress", data.getProgress());
                    directionInfo.put("message", data.getMessage());
                    directionInfo.put("vehicleCount", data.getVehicleCount());
                    directionInfo.put("lastFrameTime", data.getLastFrameTime());

                    directionsData.put(direction.name().toLowerCase(), directionInfo);
                }
            }
            realtimeData.put("directions", directionsData);

            // 交通分析数据
            if (task.getTrafficAnalysis() != null) {
                TrafficAnalysisResult analysis = task.getTrafficAnalysis();
                Map<String, Object> analysisData = new HashMap<>();
                analysisData.put("totalVehicleCount", analysis.getTotalVehicleCount());
                analysisData.put("peakDirection", analysis.getPeakDirection());
                analysisData.put("trafficFlowBalance", analysis.getTrafficFlowBalance());
                analysisData.put("congestionLevel", analysis.getCongestionLevel());

                realtimeData.put("trafficAnalysis", analysisData);
            }

            realtimeData.put("timestamp", System.currentTimeMillis());
            realtimeData.put("success", true);

            return realtimeData;
        } catch (Exception e) {
            log.error("获取四方向实时检测数据失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return createErrorResponse("获取实时数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getFourWayIntelligentRecommendations(String taskId) {
        try {
            log.info("获取四方向智能分析建议: taskId={}", taskId);

            Optional<FourWayIntersectionAnalysis> taskOpt = findFourWayAnalysisByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                return createErrorResponse("未找到指定的四方向分析任务");
            }

            FourWayIntersectionAnalysis task = taskOpt.get();

            Map<String, Object> recommendations = new HashMap<>();
            recommendations.put("taskId", taskId);

            // 生成智能建议
            List<Map<String, Object>> intelligentRecommendations = new ArrayList<>();

            if (task.getTrafficAnalysis() != null) {
                TrafficAnalysisResult analysis = task.getTrafficAnalysis();

                // 信号灯优化建议
                if (analysis.getSignalOptimization() != null) {
                    Map<String, Object> signalRec = new HashMap<>();
                    signalRec.put("type", "signal");
                    signalRec.put("title", "信号灯配时优化");
                    signalRec.put("description", analysis.getSignalOptimization().getReason());
                    signalRec.put("priority", "high");
                    signalRec.put("expectedImprovement", analysis.getSignalOptimization().getExpectedImprovement());
                    intelligentRecommendations.add(signalRec);
                }

                // 根据拥堵等级生成建议
                String congestionLevel = analysis.getCongestionLevel();
                if ("严重拥堵".equals(congestionLevel)) {
                    Map<String, Object> congestionRec = new HashMap<>();
                    congestionRec.put("type", "management");
                    congestionRec.put("title", "拥堵疏导措施");
                    congestionRec.put("description", "建议增加交通疏导人员，实施分时段限行");
                    congestionRec.put("priority", "urgent");
                    congestionRec.put("expectedImprovement", "减少拥堵30-40%");
                    intelligentRecommendations.add(congestionRec);
                }

                // 流量平衡建议
                if (analysis.getTrafficFlowBalance() < 0.6) {
                    Map<String, Object> balanceRec = new HashMap<>();
                    balanceRec.put("type", "infrastructure");
                    balanceRec.put("title", "流量平衡优化");
                    balanceRec.put("description", "各方向车流量不均衡，建议优化车道配置");
                    balanceRec.put("priority", "medium");
                    balanceRec.put("expectedImprovement", "提升通行效率15-25%");
                    intelligentRecommendations.add(balanceRec);
                }
            }

            recommendations.put("recommendations", intelligentRecommendations);
            recommendations.put("generatedAt", System.currentTimeMillis());
            recommendations.put("success", true);

            return recommendations;
        } catch (Exception e) {
            log.error("获取四方向智能分析建议失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return createErrorResponse("获取智能建议失败: " + e.getMessage());
        }
    }

    @Override
    public FourWayIntersectionAnalysis updateFourWayTaskConfig(String taskId, Map<String, Object> config) {
        try {
            log.info("更新四方向任务配置: taskId={}, config={}", taskId, config);

            Query query = Query.query(Criteria.where("taskId").is(taskId));
            Update update = new Update();

            // 更新配置字段
            if (config.containsKey("analysisMode")) {
                update.set("analysisMode", config.get("analysisMode"));
            }
            if (config.containsKey("sensitivityLevel")) {
                update.set("sensitivityLevel", config.get("sensitivityLevel"));
            }
            if (config.containsKey("enablePrediction")) {
                update.set("enablePrediction", config.get("enablePrediction"));
            }
            if (config.containsKey("enableAlerts")) {
                update.set("enableAlerts", config.get("enableAlerts"));
            }

            update.set("updatedAt", LocalDateTime.now());

            mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);

            return findFourWayAnalysisByTaskId(taskId).orElse(null);
        } catch (Exception e) {
            log.error("更新四方向任务配置失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> getFourWaySystemStatus() {
        try {
            log.info("获取四方向系统状态");

            Map<String, Object> systemStatus = new HashMap<>();

            // 统计任务数量
            long totalTasks = mongoTemplate.count(new Query(), FourWayIntersectionAnalysis.class);
            long processingTasks = mongoTemplate.count(
                Query.query(Criteria.where("status").is("processing")),
                FourWayIntersectionAnalysis.class
            );
            long completedTasks = mongoTemplate.count(
                Query.query(Criteria.where("status").is("completed")),
                FourWayIntersectionAnalysis.class
            );
            long failedTasks = mongoTemplate.count(
                Query.query(Criteria.where("status").is("failed")),
                FourWayIntersectionAnalysis.class
            );

            systemStatus.put("totalTasks", totalTasks);
            systemStatus.put("processingTasks", processingTasks);
            systemStatus.put("completedTasks", completedTasks);
            systemStatus.put("failedTasks", failedTasks);
            systemStatus.put("queuedTasks", totalTasks - processingTasks - completedTasks - failedTasks);

            // 系统健康状态
            String healthStatus = "healthy";
            if (failedTasks > totalTasks * 0.1) {
                healthStatus = "warning";
            }
            if (failedTasks > totalTasks * 0.3) {
                healthStatus = "critical";
            }

            systemStatus.put("healthStatus", healthStatus);
            systemStatus.put("timestamp", System.currentTimeMillis());
            systemStatus.put("success", true);

            return systemStatus;
        } catch (Exception e) {
            log.error("获取四方向系统状态失败: error={}", e.getMessage(), e);
            return createErrorResponse("获取系统状态失败: " + e.getMessage());
        }
    }

    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", message);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }

    // 继续添加剩余的方法实现
    @Override
    public Map<String, Object> getFourWayStatistics(String startTime, String endTime) {
        try {
            log.info("获取四方向统计数据: startTime={}, endTime={}", startTime, endTime);

            Map<String, Object> statistics = new HashMap<>();

            // 这里可以根据时间范围查询统计数据
            // 暂时返回模拟数据
            statistics.put("totalAnalyses", 156);
            statistics.put("averageProcessingTime", 45.2);
            statistics.put("successRate", 94.5);
            statistics.put("peakHours", "08:00-09:00, 17:00-18:00");

            statistics.put("success", true);
            statistics.put("timestamp", System.currentTimeMillis());

            return statistics;
        } catch (Exception e) {
            log.error("获取四方向统计数据失败: error={}", e.getMessage(), e);
            return createErrorResponse("获取统计数据失败: " + e.getMessage());
        }
    }

    @Override
    public String exportFourWayAnalysisData(String taskId, String format) {
        try {
            log.info("导出四方向分析数据: taskId={}, format={}", taskId, format);

            // 这里实现数据导出逻辑
            // 暂时返回模拟路径
            String exportPath = "/exports/" + taskId + "." + format;

            log.info("四方向分析数据导出完成: {}", exportPath);
            return exportPath;
        } catch (Exception e) {
            log.error("导出四方向分析数据失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Page<FourWayIntersectionAnalysis> getFourWayAnalysisHistory(String userId, Pageable pageable) {
        try {
            log.info("获取四方向分析历史记录: userId={}", userId);

            Query query = Query.query(Criteria.where("userId").is(userId))
                    .with(Sort.by(Sort.Direction.DESC, "createdAt"));

            long total = mongoTemplate.count(query, FourWayIntersectionAnalysis.class);

            query.with(pageable);
            List<FourWayIntersectionAnalysis> history = mongoTemplate.find(query, FourWayIntersectionAnalysis.class);

            return new PageImpl<>(history, pageable, total);
        } catch (Exception e) {
            log.error("获取四方向分析历史记录失败: userId={}, error={}", userId, e.getMessage(), e);
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

    @Override
    public int batchDeleteFourWayTasks(List<String> taskIds, String userId, String userRole) {
        try {
            log.info("批量删除四方向分析任务: taskIds={}, userId={}, userRole={}", taskIds, userId, userRole);

            int deletedCount = 0;
            for (String taskId : taskIds) {
                try {
                    // 管理员可以删除任何任务，普通用户只能删除自己的任务
                    Query query;
                    if ("admin".equalsIgnoreCase(userRole)) {
                        query = Query.query(Criteria.where("taskId").is(taskId));
                    } else {
                        query = Query.query(Criteria.where("taskId").is(taskId).and("userId").is(userId));
                    }

                    FourWayIntersectionAnalysis task = mongoTemplate.findOne(query, FourWayIntersectionAnalysis.class);

                    if (task != null) {
                        mongoTemplate.remove(query, FourWayIntersectionAnalysis.class);
                        deletedCount++;
                        log.info("成功删除四方向任务: taskId={}", taskId);
                    } else {
                        log.warn("未找到可删除的四方向任务: taskId={}, userId={}", taskId, userId);
                    }
                } catch (Exception e) {
                    log.error("删除单个四方向任务失败: taskId={}, error={}", taskId, e.getMessage());
                }
            }

            log.info("批量删除完成，成功删除{}个任务", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("批量删除四方向分析任务失败: error={}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getFourWayTaskDetails(String taskId) {
        try {
            log.info("获取四方向分析任务详情: taskId={}", taskId);

            Optional<FourWayIntersectionAnalysis> taskOpt = findFourWayAnalysisByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                return createErrorResponse("未找到指定的四方向分析任务");
            }

            FourWayIntersectionAnalysis task = taskOpt.get();

            Map<String, Object> details = new HashMap<>();
            details.put("taskId", task.getTaskId());
            details.put("status", task.getStatus());
            details.put("progress", task.getProgress());
            details.put("message", task.getMessage());
            details.put("createdAt", task.getCreatedAt());
            details.put("updatedAt", task.getUpdatedAt());
            details.put("userId", task.getUserId());
            details.put("username", task.getUsername());

            // 方向详情
            if (task.getDirections() != null) {
                Map<String, Object> directionsDetails = new HashMap<>();
                for (Map.Entry<Direction, DirectionVideoData> entry : task.getDirections().entrySet()) {
                    Direction direction = entry.getKey();
                    DirectionVideoData data = entry.getValue();

                    Map<String, Object> directionInfo = new HashMap<>();
                    directionInfo.put("status", data.getStatus());
                    directionInfo.put("progress", data.getProgress());
                    directionInfo.put("vehicleCount", data.getVehicleCount());
                    directionInfo.put("videoPath", data.getVideoPath());
                    directionInfo.put("filename", data.getFilename());

                    directionsDetails.put(direction.name().toLowerCase(), directionInfo);
                }
                details.put("directions", directionsDetails);
            }

            // 交通分析结果
            if (task.getTrafficAnalysis() != null) {
                TrafficAnalysisResult analysis = task.getTrafficAnalysis();
                Map<String, Object> analysisDetails = new HashMap<>();
                analysisDetails.put("totalVehicleCount", analysis.getTotalVehicleCount());
                analysisDetails.put("peakDirection", analysis.getPeakDirection());
                analysisDetails.put("trafficFlowBalance", analysis.getTrafficFlowBalance());
                analysisDetails.put("congestionLevel", analysis.getCongestionLevel());

                details.put("trafficAnalysis", analysisDetails);
            }

            details.put("success", true);
            details.put("timestamp", System.currentTimeMillis());

            return details;
        } catch (Exception e) {
            log.error("获取四方向分析任务详情失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return createErrorResponse("获取任务详情失败: " + e.getMessage());
        }
    }

    @Override
    public boolean pauseFourWayTask(String taskId) {
        try {
            log.info("暂停四方向分析任务: taskId={}", taskId);

            Query query = Query.query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("status", "paused")
                    .set("message", "任务已暂停")
                    .set("updatedAt", LocalDateTime.now());

            UpdateResult result = mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);

            boolean success = result.getModifiedCount() > 0;
            if (success) {
                log.info("四方向分析任务暂停成功: taskId={}", taskId);
            } else {
                log.warn("四方向分析任务暂停失败，未找到任务: taskId={}", taskId);
            }

            return success;
        } catch (Exception e) {
            log.error("暂停四方向分析任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean resumeFourWayTask(String taskId) {
        try {
            log.info("恢复四方向分析任务: taskId={}", taskId);

            Query query = Query.query(Criteria.where("taskId").is(taskId).and("status").is("paused"));
            Update update = new Update()
                    .set("status", "processing")
                    .set("message", "任务已恢复")
                    .set("updatedAt", LocalDateTime.now());

            UpdateResult result = mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);

            boolean success = result.getModifiedCount() > 0;
            if (success) {
                log.info("四方向分析任务恢复成功: taskId={}", taskId);
            } else {
                log.warn("四方向分析任务恢复失败，任务不存在或状态不正确: taskId={}", taskId);
            }

            return success;
        } catch (Exception e) {
            log.error("恢复四方向分析任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean cancelFourWayTask(String taskId) {
        try {
            log.info("取消四方向分析任务: taskId={}", taskId);

            Query query = Query.query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("status", "cancelled")
                    .set("message", "任务已取消")
                    .set("updatedAt", LocalDateTime.now());

            UpdateResult result = mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);

            boolean success = result.getModifiedCount() > 0;
            if (success) {
                log.info("四方向分析任务取消成功: taskId={}", taskId);
            } else {
                log.warn("四方向分析任务取消失败，未找到任务: taskId={}", taskId);
            }

            return success;
        } catch (Exception e) {
            log.error("取消四方向分析任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 异步处理四方向交叉口分析
     */
    private void processFourWayIntersectionAsync(FourWayIntersectionAnalysis task) {
        try {
            log.info("开始异步处理四方向交叉口分析: taskId={}", task.getTaskId());

            // 更新任务状态为处理中
            Query query = Query.query(Criteria.where("taskId").is(task.getTaskId()));
            Update update = new Update()
                    .set("status", "processing")
                    .set("message", "正在分析四方向视频")
                    .set("progress", 10)
                    .set("updatedAt", LocalDateTime.now());

            mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);

            // 这里可以添加实际的视频分析逻辑
            // 暂时使用模拟处理
            simulateFourWayVideoProcessing(task);

        } catch (Exception e) {
            log.error("异步处理四方向交叉口分析失败: taskId={}, error={}", task.getTaskId(), e.getMessage(), e);

            // 更新任务状态为失败
            Query query = Query.query(Criteria.where("taskId").is(task.getTaskId()));
            Update update = new Update()
                    .set("status", "failed")
                    .set("message", "分析失败: " + e.getMessage())
                    .set("updatedAt", LocalDateTime.now());

            mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);
        }
    }

    /**
     * 模拟四方向视频处理
     */
    private void simulateFourWayVideoProcessing(FourWayIntersectionAnalysis task) {
        try {
            // 模拟处理过程
            for (int progress = 20; progress <= 100; progress += 20) {
                Thread.sleep(2000); // 模拟处理时间

                Query query = Query.query(Criteria.where("taskId").is(task.getTaskId()));
                Update update = new Update()
                        .set("progress", progress)
                        .set("message", "正在分析第" + (progress/20) + "个方向的视频")
                        .set("updatedAt", LocalDateTime.now());

                mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);
            }

            // 完成处理
            Query query = Query.query(Criteria.where("taskId").is(task.getTaskId()));
            Update update = new Update()
                    .set("status", "completed")
                    .set("progress", 100)
                    .set("message", "四方向视频分析完成")
                    .set("completedAt", LocalDateTime.now())
                    .set("updatedAt", LocalDateTime.now());

            mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);

            log.info("四方向视频分析完成: taskId={}", task.getTaskId());

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("四方向视频处理被中断: taskId={}", task.getTaskId());
        } catch (Exception e) {
            log.error("模拟四方向视频处理失败: taskId={}, error={}", task.getTaskId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 初始化四方向实时帧推送功能
     */
    private void initializeFourWayRealtimeFramePush(String taskId) {
        try {
            log.info("初始化四方向实时帧推送: taskId={}", taskId);

            // 发送初始化消息到WebSocket
            Map<String, Object> initMessage = new HashMap<>();
            initMessage.put("type", "four_way_init");
            initMessage.put("taskId", taskId);
            initMessage.put("status", "initialized");
            initMessage.put("message", "四方向实时检测已初始化");
            initMessage.put("timestamp", System.currentTimeMillis());

            // 发送到四方向进度主题
            messagingTemplate.convertAndSend("/topic/four-way-progress/" + taskId, initMessage);

            log.info("四方向实时帧推送初始化完成: taskId={}", taskId);
        } catch (Exception e) {
            log.error("初始化四方向实时帧推送失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 发送四方向进度更新
     */
    private void sendFourWayProgressUpdate(String taskId, String status, int progress, String message) {
        try {
            Map<String, Object> progressMessage = new HashMap<>();
            progressMessage.put("type", "four_way_progress_update");
            progressMessage.put("taskId", taskId);
            progressMessage.put("status", status);
            progressMessage.put("progress", progress);
            progressMessage.put("message", message);
            progressMessage.put("timestamp", System.currentTimeMillis());

            // 发送到四方向进度主题
            messagingTemplate.convertAndSend("/topic/four-way-progress/" + taskId, progressMessage);

            log.debug("发送四方向进度更新: taskId={}, status={}, progress={}%", taskId, status, progress);
        } catch (Exception e) {
            log.error("发送四方向进度更新失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 发送四方向帧数据
     */
    private void sendFourWayFrameUpdate(String taskId, String direction, String imageData, int frameNumber, Map<String, Object> detectionData) {
        try {
            Map<String, Object> frameMessage = new HashMap<>();
            frameMessage.put("type", "four_way_frame_update");
            frameMessage.put("taskId", taskId);
            frameMessage.put("direction", direction);
            frameMessage.put("frameNumber", frameNumber);
            frameMessage.put("imageData", imageData);
            frameMessage.put("detectionData", detectionData);
            frameMessage.put("timestamp", System.currentTimeMillis());

            // 发送到四方向帧主题
            messagingTemplate.convertAndSend("/topic/four-way-frames/" + taskId, frameMessage);

            log.debug("发送四方向帧数据: taskId={}, direction={}, frame={}", taskId, direction, frameNumber);
        } catch (Exception e) {
            log.error("发送四方向帧数据失败: taskId={}, direction={}, error={}", taskId, direction, e.getMessage(), e);
        }
    }

    /**
     * 发送四方向方向完成消息
     */
    private void sendFourWayDirectionComplete(String taskId, String direction, Map<String, Object> result) {
        try {
            Map<String, Object> completeMessage = new HashMap<>();
            completeMessage.put("type", "four_way_direction_complete");
            completeMessage.put("taskId", taskId);
            completeMessage.put("direction", direction);
            completeMessage.put("result", result);
            completeMessage.put("timestamp", System.currentTimeMillis());

            // 发送到四方向进度主题
            messagingTemplate.convertAndSend("/topic/four-way-progress/" + taskId, completeMessage);

            log.info("发送四方向方向完成消息: taskId={}, direction={}", taskId, direction);
        } catch (Exception e) {
            log.error("发送四方向方向完成消息失败: taskId={}, direction={}, error={}", taskId, direction, e.getMessage(), e);
        }
    }

    /**
     * 发送四方向整体分析完成消息
     */
    private void sendFourWayAnalysisCompleteMessage(String taskId, FourWayIntersectionAnalysis analysis, int totalVehicles) {
        try {
            log.info("发送四方向整体分析完成消息: taskId={}, totalVehicles={}", taskId, totalVehicles);

            // 构建分析结果摘要
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalVehicles", totalVehicles);

            // 各方向统计
            Map<String, Object> directions = new HashMap<>();
            for (Direction direction : Direction.values()) {
                DirectionVideoData directionData = analysis.getDirectionData(direction);
                if (directionData != null) {
                    Map<String, Object> directionSummary = new HashMap<>();
                    directionSummary.put("vehicleCount", directionData.getVehicleCount());
                    directionSummary.put("status", directionData.getStatus());
                    directionSummary.put("crowdLevel", directionData.getCrowdLevel());
                    directions.put(direction.name().toLowerCase(), directionSummary);
                }
            }
            summary.put("directions", directions);

            // 计算处理时间
            if (analysis.getProcessingStartTime() != null) {
                long processingTimeMs = System.currentTimeMillis() -
                    analysis.getProcessingStartTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                long minutes = processingTimeMs / (1000 * 60);
                long seconds = (processingTimeMs % (1000 * 60)) / 1000;
                summary.put("processingTime", String.format("%02d:%02d", minutes, seconds));
            }

            summary.put("nextStep", "intelligent_analysis");

            // 调用WebSocket控制器发送整体完成消息
            if (webSocketController != null) {
                webSocketController.sendFourWayAnalysisComplete(taskId, summary);
            }

            log.info("四方向整体分析完成消息发送成功: taskId={}", taskId);
        } catch (Exception e) {
            log.error("发送四方向整体分析完成消息失败: taskId={}, error={}", taskId, e.getMessage(), e);
        }
    }

    /**
     * 根据方向获取对应的Python服务端口
     */
    private int getPortForDirection(Direction direction) {
        switch (direction) {
            case EAST:
                return 5001; // 主服务端口
            case SOUTH:
                return 5002; // South方向服务端口
            case WEST:
                return 5003; // West方向服务端口
            case NORTH:
                return 5004; // North方向服务端口
            default:
                log.warn("未知方向: {}, 使用默认端口5001", direction);
                return 5001;
        }
    }

    @Override
    public boolean restartDirectionAnalysis(String taskId, String direction) {
        try {
            log.info("重新启动方向分析: taskId={}, direction={}", taskId, direction);

            // 查找任务
            Optional<FourWayIntersectionAnalysis> taskOpt = findFourWayAnalysisByTaskId(taskId);
            if (!taskOpt.isPresent()) {
                log.warn("未找到四方向分析任务: taskId={}", taskId);
                return false;
            }

            FourWayIntersectionAnalysis task = taskOpt.get();

            // 将方向字符串转换为Direction枚举
            Direction directionEnum;
            try {
                directionEnum = Direction.valueOf(direction.toUpperCase());
            } catch (IllegalArgumentException e) {
                log.warn("无效的方向参数: {}", direction);
                return false;
            }

            // 获取方向数据
            DirectionVideoData directionData = task.getDirectionData(directionEnum);
            if (directionData == null) {
                log.warn("未找到方向数据: taskId={}, direction={}", taskId, direction);
                return false;
            }

            // 更新方向状态为processing
            Query query = Query.query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("directions." + directionEnum.name() + ".status", "processing")
                    .set("directions." + directionEnum.name() + ".progress", 10)
                    .set("updatedAt", LocalDateTime.now());

            UpdateResult result = mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);

            if (result.getModifiedCount() > 0) {
                log.info("成功重新启动方向分析: taskId={}, direction={}", taskId, direction);

                // 异步启动方向分析处理
                CompletableFuture.runAsync(() -> {
                    try {
                        processDirectionAnalysis(taskId, directionEnum, directionData);
                    } catch (Exception e) {
                        log.error("异步处理方向分析失败: taskId={}, direction={}, error={}",
                                taskId, direction, e.getMessage(), e);
                    }
                });

                return true;
            } else {
                log.warn("重新启动方向分析失败，未更新任何记录: taskId={}, direction={}", taskId, direction);
                return false;
            }

        } catch (Exception e) {
            log.error("重新启动方向分析失败: taskId={}, direction={}, error={}",
                    taskId, direction, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理单个方向的分析
     */
    private void processDirectionAnalysis(String taskId, Direction direction, DirectionVideoData directionData) {
        try {
            log.info("开始处理方向分析: taskId={}, direction={}", taskId, direction);

            // 模拟分析过程
            for (int progress = 20; progress <= 100; progress += 20) {
                Thread.sleep(1000); // 模拟处理时间

                // 更新进度
                Query query = Query.query(Criteria.where("taskId").is(taskId));
                Update update = new Update()
                        .set("directions." + direction.name() + ".progress", progress)
                        .set("updatedAt", LocalDateTime.now());

                mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);

                log.debug("方向分析进度更新: taskId={}, direction={}, progress={}%",
                        taskId, direction, progress);
            }

            // 完成分析
            Query query = Query.query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("directions." + direction.name() + ".status", "completed")
                    .set("directions." + direction.name() + ".progress", 100)
                    .set("directions." + direction.name() + ".vehicleCount",
                         (int)(Math.random() * 50) + 10) // 模拟车辆数量
                    .set("updatedAt", LocalDateTime.now());

            mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);

            log.info("方向分析完成: taskId={}, direction={}", taskId, direction);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("方向分析被中断: taskId={}, direction={}", taskId, direction);
        } catch (Exception e) {
            log.error("处理方向分析失败: taskId={}, direction={}, error={}",
                    taskId, direction, e.getMessage(), e);

            // 更新状态为失败
            Query query = Query.query(Criteria.where("taskId").is(taskId));
            Update update = new Update()
                    .set("directions." + direction.name() + ".status", "failed")
                    .set("updatedAt", LocalDateTime.now());

            mongoTemplate.updateFirst(query, update, FourWayIntersectionAnalysis.class);
        }
    }
}