package com.traffic.analysis.exception;

import lombok.Getter;

/**
 * 四方向交通分析业务异常
 */
@Getter
public class FourWayAnalysisException extends RuntimeException {

    private final String taskId;
    private final String errorCode;
    private final Object details;

    public FourWayAnalysisException(String message) {
        super(message);
        this.taskId = null;
        this.errorCode = "FOUR_WAY_ANALYSIS_ERROR";
        this.details = null;
    }

    public FourWayAnalysisException(String message, String taskId) {
        super(message);
        this.taskId = taskId;
        this.errorCode = "FOUR_WAY_ANALYSIS_ERROR";
        this.details = null;
    }

    public FourWayAnalysisException(String message, String taskId, String errorCode) {
        super(message);
        this.taskId = taskId;
        this.errorCode = errorCode;
        this.details = null;
    }

    public FourWayAnalysisException(String message, String taskId, String errorCode, Object details) {
        super(message);
        this.taskId = taskId;
        this.errorCode = errorCode;
        this.details = details;
    }

    public FourWayAnalysisException(String message, Throwable cause) {
        super(message, cause);
        this.taskId = null;
        this.errorCode = "FOUR_WAY_ANALYSIS_ERROR";
        this.details = null;
    }

    public FourWayAnalysisException(String message, String taskId, Throwable cause) {
        super(message, cause);
        this.taskId = taskId;
        this.errorCode = "FOUR_WAY_ANALYSIS_ERROR";
        this.details = null;
    }

    public FourWayAnalysisException(String message, String taskId, String errorCode, Throwable cause) {
        super(message, cause);
        this.taskId = taskId;
        this.errorCode = errorCode;
        this.details = null;
    }

    // 静态工厂方法
    public static FourWayAnalysisException uploadFailed(String message, String taskId) {
        return new FourWayAnalysisException(message, taskId, "FOUR_WAY_UPLOAD_FAILED");
    }

    public static FourWayAnalysisException processingFailed(String message, String taskId) {
        return new FourWayAnalysisException(message, taskId, "FOUR_WAY_PROCESSING_FAILED");
    }

    public static FourWayAnalysisException taskNotFound(String taskId) {
        return new FourWayAnalysisException("四方向分析任务未找到: " + taskId, taskId, "FOUR_WAY_TASK_NOT_FOUND");
    }
}

/**
 * 视频上传异常
 */
class VideoUploadException extends RuntimeException {
    
    private String direction;
    
    public VideoUploadException(String message, String direction) {
        super(message);
        this.direction = direction;
    }
    
    public VideoUploadException(String message, String direction, Throwable cause) {
        super(message, cause);
        this.direction = direction;
    }
    
    public String getDirection() {
        return direction;
    }
}

/**
 * 视频处理异常
 */
class VideoProcessingException extends RuntimeException {
    
    private String taskId;
    private String direction;
    private String processingStage;
    
    public VideoProcessingException(String message, String taskId, String direction, String processingStage) {
        super(message);
        this.taskId = taskId;
        this.direction = direction;
        this.processingStage = processingStage;
    }
    
    public VideoProcessingException(String message, String taskId, String direction, String processingStage, Throwable cause) {
        super(message, cause);
        this.taskId = taskId;
        this.direction = direction;
        this.processingStage = processingStage;
    }
    
    public String getTaskId() {
        return taskId;
    }
    
    public String getDirection() {
        return direction;
    }
    
    public String getProcessingStage() {
        return processingStage;
    }
}

/**
 * 任务未找到异常
 */
class TaskNotFoundException extends RuntimeException {
    
    private String taskId;
    
    public TaskNotFoundException(String taskId) {
        super("四方向分析任务未找到: " + taskId);
        this.taskId = taskId;
    }
    
    public TaskNotFoundException(String message, String taskId) {
        super(message);
        this.taskId = taskId;
    }
    
    public String getTaskId() {
        return taskId;
    }
}

/**
 * 数据库连接异常
 */
class DatabaseConnectionException extends RuntimeException {
    
    public DatabaseConnectionException(String message) {
        super(message);
    }
    
    public DatabaseConnectionException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * WebSocket连接异常
 */
class WebSocketConnectionException extends RuntimeException {
    
    private String sessionId;
    
    public WebSocketConnectionException(String message, String sessionId) {
        super(message);
        this.sessionId = sessionId;
    }
    
    public WebSocketConnectionException(String message, String sessionId, Throwable cause) {
        super(message, cause);
        this.sessionId = sessionId;
    }
    
    public String getSessionId() {
        return sessionId;
    }
}

/**
 * 模型服务异常
 */
class ModelServiceException extends RuntimeException {
    
    private String serviceUrl;
    
    public ModelServiceException(String message, String serviceUrl) {
        super(message);
        this.serviceUrl = serviceUrl;
    }
    
    public ModelServiceException(String message, String serviceUrl, Throwable cause) {
        super(message, cause);
        this.serviceUrl = serviceUrl;
    }
    
    public String getServiceUrl() {
        return serviceUrl;
    }
}
