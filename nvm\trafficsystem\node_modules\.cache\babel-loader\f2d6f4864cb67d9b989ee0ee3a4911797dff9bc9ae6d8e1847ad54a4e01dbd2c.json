{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, createTextVNode as _createTextVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"real-time-frame-viewer\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"no-frame-placeholder\"\n};\nconst _hoisted_3 = {\n  key: 1,\n  class: \"frame-display\"\n};\nconst _hoisted_4 = [\"src\", \"alt\"];\nconst _hoisted_5 = {\n  class: \"detection-overlay\"\n};\nconst _hoisted_6 = {\n  class: \"detection-info\"\n};\nconst _hoisted_7 = {\n  class: \"vehicle-count\"\n};\nconst _hoisted_8 = {\n  class: \"frame-info\"\n};\nconst _hoisted_9 = {\n  class: \"frame-number\"\n};\nconst _hoisted_10 = {\n  class: \"direction-header\"\n};\nconst _hoisted_11 = {\n  class: \"direction-info\"\n};\nconst _hoisted_12 = {\n  class: \"direction-name\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"stats-panel\"\n};\nconst _hoisted_14 = {\n  class: \"vehicle-types\"\n};\nconst _hoisted_15 = {\n  class: \"type-name\"\n};\nconst _hoisted_16 = {\n  class: \"type-count\"\n};\nconst _hoisted_17 = {\n  key: 1,\n  class: \"controls\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_VideoCamera = _resolveComponent(\"VideoCamera\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 视频显示区域 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"video-container\", {\n      'has-frame': $setup.hasCurrentFrame\n    }])\n  }, [!$setup.hasCurrentFrame ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_icon, {\n    size: \"48\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_VideoCamera)]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"p\", null, _toDisplayString($setup.getWaitingMessage()), 1 /* TEXT */), $props.progress > 0 ? (_openBlock(), _createBlock(_component_el_progress, {\n    key: 0,\n    percentage: $props.progress,\n    \"stroke-width\": 6,\n    \"show-text\": false\n  }, null, 8 /* PROPS */, [\"percentage\"])) : _createCommentVNode(\"v-if\", true)])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"img\", {\n    src: $setup.currentFrame.imageData,\n    alt: `${$setup.directionName}方向检测结果`,\n    class: \"frame-image\",\n    onLoad: _cache[0] || (_cache[0] = (...args) => $setup.onImageLoad && $setup.onImageLoad(...args)),\n    onError: _cache[1] || (_cache[1] = (...args) => $setup.onImageError && $setup.onImageError(...args))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_4), _createCommentVNode(\" 检测信息覆盖层 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"span\", _hoisted_7, _toDisplayString($setup.currentFrame.detectionCount || 0), 1 /* TEXT */), _cache[2] || (_cache[2] = _createElementVNode(\"span\", {\n    class: \"vehicle-label\"\n  }, \"辆车\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"span\", _hoisted_9, _toDisplayString($setup.currentFrame.frameNumber || 0) + \" / \" + _toDisplayString($setup.currentFrame.totalFrames || 0), 1 /* TEXT */)])])]))], 2 /* CLASS */), _createCommentVNode(\" 方向标识 \"), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"direction-icon\", $props.direction])\n  }, _toDisplayString($setup.directionName.substring(0, 1)), 3 /* TEXT, CLASS */), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"h4\", _hoisted_12, _toDisplayString($setup.directionName), 1 /* TEXT */), _createElementVNode(\"p\", {\n    class: _normalizeClass([\"direction-status\", $setup.statusClass])\n  }, _toDisplayString($setup.statusText), 3 /* TEXT, CLASS */)])]), _createCommentVNode(\" 统计信息 \"), $setup.hasCurrentFrame && $setup.currentFrame.vehicleTypes ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_cache[3] || (_cache[3] = _createElementVNode(\"h5\", null, \"车辆类型统计\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.currentFrame.vehicleTypes, (count, type) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: type,\n      class: \"vehicle-type-item\"\n    }, [_createElementVNode(\"span\", _hoisted_15, _toDisplayString($setup.getVehicleTypeName(type)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_16, _toDisplayString(count), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 控制按钮 \"), $props.showControls ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createVNode(_component_el_button, {\n    size: \"small\",\n    onClick: $setup.togglePause,\n    icon: $setup.isPaused ? _ctx.VideoPlay : _ctx.VideoPause\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.isPaused ? '播放' : '暂停'), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"icon\"]), _createVNode(_component_el_button, {\n    size: \"small\",\n    onClick: $setup.saveFrame,\n    disabled: !$setup.hasCurrentFrame,\n    icon: \"Download\"\n  }, {\n    default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\" 保存帧 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"disabled\"])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "$setup", "hasCurrentFrame", "_hoisted_2", "_createVNode", "_component_el_icon", "size", "default", "_withCtx", "_component_VideoCamera", "_", "_toDisplayString", "getWaitingMessage", "$props", "progress", "_createBlock", "_component_el_progress", "percentage", "_hoisted_3", "src", "currentFrame", "imageData", "alt", "directionName", "onLoad", "_cache", "args", "onImageLoad", "onError", "onImageError", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "detectionCount", "_hoisted_8", "_hoisted_9", "frameNumber", "totalFrames", "_hoisted_10", "direction", "substring", "_hoisted_11", "_hoisted_12", "statusClass", "statusText", "vehicleTypes", "_hoisted_13", "_hoisted_14", "_Fragment", "_renderList", "count", "type", "_hoisted_15", "getVehicleTypeName", "_hoisted_16", "showControls", "_hoisted_17", "_component_el_button", "onClick", "toggle<PERSON><PERSON>e", "icon", "isPaused", "_ctx", "VideoPlay", "VideoPause", "_createTextVNode", "saveFrame", "disabled"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\RealTimeFrameViewer.vue"], "sourcesContent": ["<template>\n  <div class=\"real-time-frame-viewer\">\n    <!-- 视频显示区域 -->\n    <div class=\"video-container\" :class=\"{ 'has-frame': hasCurrentFrame }\">\n      <div v-if=\"!hasCurrentFrame\" class=\"no-frame-placeholder\">\n        <el-icon size=\"48\"><VideoCamera /></el-icon>\n        <p>{{ getWaitingMessage() }}</p>\n        <el-progress \n          v-if=\"progress > 0\"\n          :percentage=\"progress\" \n          :stroke-width=\"6\"\n          :show-text=\"false\"\n        />\n      </div>\n      \n      <div v-else class=\"frame-display\">\n        <img \n          :src=\"currentFrame.imageData\" \n          :alt=\"`${directionName}方向检测结果`\"\n          class=\"frame-image\"\n          @load=\"onImageLoad\"\n          @error=\"onImageError\"\n        />\n        \n        <!-- 检测信息覆盖层 -->\n        <div class=\"detection-overlay\">\n          <div class=\"detection-info\">\n            <span class=\"vehicle-count\">{{ currentFrame.detectionCount || 0 }}</span>\n            <span class=\"vehicle-label\">辆车</span>\n          </div>\n          \n          <div class=\"frame-info\">\n            <span class=\"frame-number\">\n              {{ currentFrame.frameNumber || 0 }} / {{ currentFrame.totalFrames || 0 }}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 方向标识 -->\n    <div class=\"direction-header\">\n      <div class=\"direction-icon\" :class=\"direction\">\n        {{ directionName.substring(0, 1) }}\n      </div>\n      <div class=\"direction-info\">\n        <h4 class=\"direction-name\">{{ directionName }}</h4>\n        <p class=\"direction-status\" :class=\"statusClass\">{{ statusText }}</p>\n      </div>\n    </div>\n    \n    <!-- 统计信息 -->\n    <div class=\"stats-panel\" v-if=\"hasCurrentFrame && currentFrame.vehicleTypes\">\n      <h5>车辆类型统计</h5>\n      <div class=\"vehicle-types\">\n        <div \n          v-for=\"(count, type) in currentFrame.vehicleTypes\" \n          :key=\"type\"\n          class=\"vehicle-type-item\"\n        >\n          <span class=\"type-name\">{{ getVehicleTypeName(type) }}</span>\n          <span class=\"type-count\">{{ count }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 控制按钮 -->\n    <div class=\"controls\" v-if=\"showControls\">\n      <el-button \n        size=\"small\" \n        @click=\"togglePause\"\n        :icon=\"isPaused ? VideoPlay : VideoPause\"\n      >\n        {{ isPaused ? '播放' : '暂停' }}\n      </el-button>\n      \n      <el-button \n        size=\"small\" \n        @click=\"saveFrame\"\n        :disabled=\"!hasCurrentFrame\"\n        icon=\"Download\"\n      >\n        保存帧\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, watch } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport { \n  VideoCamera, VideoPlay, VideoPause, Download \n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'RealTimeFrameViewer',\n  components: {\n    VideoCamera, VideoPlay, VideoPause, Download\n  },\n  props: {\n    direction: {\n      type: String,\n      required: true,\n      validator: (value) => ['east', 'south', 'west', 'north'].includes(value)\n    },\n    frameData: {\n      type: Object,\n      default: null\n    },\n    status: {\n      type: String,\n      default: 'waiting',\n      validator: (value) => ['waiting', 'processing', 'completed', 'error'].includes(value)\n    },\n    progress: {\n      type: Number,\n      default: 0\n    },\n    showControls: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['pause-toggled', 'frame-saved'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const currentFrame = ref(null)\n    const isPaused = ref(false)\n    const imageLoading = ref(false)\n    \n    // 计算属性\n    const directionName = computed(() => {\n      const names = {\n        east: '东向',\n        south: '南向',\n        west: '西向',\n        north: '北向'\n      }\n      return names[props.direction] || props.direction\n    })\n    \n    const hasCurrentFrame = computed(() => {\n      return currentFrame.value && currentFrame.value.imageData\n    })\n    \n    const statusClass = computed(() => {\n      return {\n        'status-waiting': props.status === 'waiting',\n        'status-processing': props.status === 'processing',\n        'status-completed': props.status === 'completed',\n        'status-error': props.status === 'error'\n      }\n    })\n    \n    const statusText = computed(() => {\n      const statusMap = {\n        waiting: '等待处理',\n        processing: '正在处理',\n        completed: '处理完成',\n        error: '处理失败'\n      }\n      return statusMap[props.status] || '未知状态'\n    })\n    \n    // 监听帧数据变化\n    watch(() => props.frameData, (newFrameData) => {\n      if (newFrameData && !isPaused.value) {\n        updateFrame(newFrameData)\n      }\n    }, { immediate: true })\n    \n    // 方法\n    const updateFrame = (frameData) => {\n      if (!frameData) return\n      \n      // 验证帧数据\n      if (!frameData.imageData || !frameData.imageData.startsWith('data:image/')) {\n        console.warn('无效的帧数据:', frameData)\n        return\n      }\n      \n      currentFrame.value = {\n        ...frameData,\n        timestamp: new Date().toISOString()\n      }\n    }\n    \n    const getVehicleTypeName = (type) => {\n      const typeNames = {\n        car: '小汽车',\n        truck: '卡车',\n        bus: '公交车',\n        motorcycle: '摩托车'\n      }\n      return typeNames[type] || type\n    }\n    \n    const togglePause = () => {\n      isPaused.value = !isPaused.value\n      emit('pause-toggled', {\n        direction: props.direction,\n        isPaused: isPaused.value\n      })\n    }\n    \n    const saveFrame = () => {\n      if (!hasCurrentFrame.value) {\n        ElMessage.warning('没有可保存的帧数据')\n        return\n      }\n      \n      try {\n        // 创建下载链接\n        const link = document.createElement('a')\n        link.href = currentFrame.value.imageData\n        link.download = `${props.direction}_frame_${currentFrame.value.frameNumber || 'unknown'}_${Date.now()}.jpg`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        \n        ElMessage.success('帧图像已保存')\n        emit('frame-saved', {\n          direction: props.direction,\n          frameData: currentFrame.value\n        })\n        \n      } catch (error) {\n        console.error('保存帧失败:', error)\n        ElMessage.error('保存帧失败')\n      }\n    }\n    \n    const onImageLoad = () => {\n      imageLoading.value = false\n    }\n    \n    const onImageError = () => {\n      imageLoading.value = false\n      console.error('帧图像加载失败')\n    }\n\n    const getWaitingMessage = () => {\n      if (props.status === 'processing') {\n        return '正在处理视频，请稍候...'\n      } else if (props.status === 'waiting') {\n        return '等待视频帧数据...'\n      } else if (props.status === 'error') {\n        return '视频处理失败'\n      }\n      return '等待视频帧数据...'\n    }\n\n    return {\n      currentFrame,\n      isPaused,\n      imageLoading,\n      directionName,\n      hasCurrentFrame,\n      statusClass,\n      statusText,\n      getVehicleTypeName,\n      getWaitingMessage,\n      togglePause,\n      saveFrame,\n      onImageLoad,\n      onImageError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.real-time-frame-viewer {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  overflow: hidden;\n  background: #fff;\n}\n\n.video-container {\n  flex: 1;\n  position: relative;\n  background: #f5f7fa;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 200px;\n}\n\n.no-frame-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #c0c4cc;\n  text-align: center;\n  padding: 20px;\n}\n\n.no-frame-placeholder p {\n  margin: 12px 0 16px 0;\n  font-size: 14px;\n}\n\n.frame-display {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.frame-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 4px;\n}\n\n.detection-overlay {\n  position: absolute;\n  top: 8px;\n  left: 8px;\n  right: 8px;\n  display: flex;\n  justify-content: space-between;\n  pointer-events: none;\n}\n\n.detection-info {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 6px 12px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.vehicle-count {\n  font-size: 18px;\n  font-weight: bold;\n}\n\n.vehicle-label {\n  font-size: 12px;\n}\n\n.frame-info {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.direction-header {\n  display: flex;\n  align-items: center;\n  padding: 12px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #fafafa;\n}\n\n.direction-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  font-size: 14px;\n  margin-right: 12px;\n}\n\n.direction-icon.east {\n  background: #409eff;\n}\n\n.direction-icon.south {\n  background: #67c23a;\n}\n\n.direction-icon.west {\n  background: #e6a23c;\n}\n\n.direction-icon.north {\n  background: #f56c6c;\n}\n\n.direction-info {\n  flex: 1;\n}\n\n.direction-name {\n  margin: 0 0 4px 0;\n  font-size: 16px;\n  color: #2c3e50;\n}\n\n.direction-status {\n  margin: 0;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.status-waiting {\n  color: #909399;\n}\n\n.status-processing {\n  color: #e6a23c;\n}\n\n.status-completed {\n  color: #67c23a;\n}\n\n.status-error {\n  color: #f56c6c;\n}\n\n.stats-panel {\n  padding: 12px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #fafafa;\n}\n\n.stats-panel h5 {\n  margin: 0 0 8px 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.vehicle-types {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.vehicle-type-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n}\n\n.type-name {\n  color: #606266;\n}\n\n.type-count {\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.controls {\n  display: flex;\n  gap: 8px;\n  padding: 12px;\n  background: #fafafa;\n}\n\n.controls .el-button {\n  flex: 1;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .detection-overlay {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .direction-header {\n    padding: 8px;\n  }\n  \n  .direction-icon {\n    width: 28px;\n    height: 28px;\n    font-size: 12px;\n  }\n  \n  .direction-name {\n    font-size: 14px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EADrCC,GAAA;EAImCD,KAAK,EAAC;;;EAJzCC,GAAA;EAekBD,KAAK,EAAC;;mBAfxB;;EAyBaA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAe;;EAIxBA,KAAK,EAAC;AAAY;;EACfA,KAAK,EAAC;AAAc;;EAS7BA,KAAK,EAAC;AAAkB;;EAItBA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAgB;;EA9ClCC,GAAA;EAoDSD,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAe;;EAMhBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAY;;EA7DlCC,GAAA;EAmESD,KAAK,EAAC;;;;;;;uBAlEbE,mBAAA,CAoFM,OApFNC,UAoFM,GAnFJC,mBAAA,YAAe,EACfC,mBAAA,CAmCM;IAnCDL,KAAK,EAHdM,eAAA,EAGe,iBAAiB;MAAA,aAAwBC,MAAA,CAAAC;IAAe;OACrDD,MAAA,CAAAC,eAAe,I,cAA3BN,mBAAA,CASM,OATNO,UASM,GARJC,YAAA,CAA4CC,kBAAA;IAAnCC,IAAI,EAAC;EAAI;IAL1BC,OAAA,EAAAC,QAAA,CAK2B,MAAe,CAAfJ,YAAA,CAAeK,sBAAA,E;IAL1CC,CAAA;MAMQX,mBAAA,CAAgC,WAAAY,gBAAA,CAA1BV,MAAA,CAAAW,iBAAiB,oBAEfC,MAAA,CAAAC,QAAQ,Q,cADhBC,YAAA,CAKEC,sBAAA;IAZVrB,GAAA;IASWsB,UAAU,EAAEJ,MAAA,CAAAC,QAAQ;IACpB,cAAY,EAAE,CAAC;IACf,WAAS,EAAE;6CAXtBhB,mBAAA,e,oBAeMF,mBAAA,CAsBM,OAtBNsB,UAsBM,GArBJnB,mBAAA,CAME;IALCoB,GAAG,EAAElB,MAAA,CAAAmB,YAAY,CAACC,SAAS;IAC3BC,GAAG,KAAKrB,MAAA,CAAAsB,aAAa;IACtB7B,KAAK,EAAC,aAAa;IAClB8B,MAAI,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEzB,MAAA,CAAA0B,WAAA,IAAA1B,MAAA,CAAA0B,WAAA,IAAAD,IAAA,CAAW;IACjBE,OAAK,EAAAH,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEzB,MAAA,CAAA4B,YAAA,IAAA5B,MAAA,CAAA4B,YAAA,IAAAH,IAAA,CAAY;2CArB9BI,UAAA,GAwBQhC,mBAAA,aAAgB,EAChBC,mBAAA,CAWM,OAXNgC,UAWM,GAVJhC,mBAAA,CAGM,OAHNiC,UAGM,GAFJjC,mBAAA,CAAyE,QAAzEkC,UAAyE,EAAAtB,gBAAA,CAA1CV,MAAA,CAAAmB,YAAY,CAACc,cAAc,uB,0BAC1DnC,mBAAA,CAAqC;IAA/BL,KAAK,EAAC;EAAe,GAAC,IAAE,qB,GAGhCK,mBAAA,CAIM,OAJNoC,UAIM,GAHJpC,mBAAA,CAEO,QAFPqC,UAEO,EAAAzB,gBAAA,CADFV,MAAA,CAAAmB,YAAY,CAACiB,WAAW,SAAQ,KAAG,GAAA1B,gBAAA,CAAGV,MAAA,CAAAmB,YAAY,CAACkB,WAAW,sB,yBAO3ExC,mBAAA,UAAa,EACbC,mBAAA,CAQM,OARNwC,WAQM,GAPJxC,mBAAA,CAEM;IAFDL,KAAK,EA1ChBM,eAAA,EA0CiB,gBAAgB,EAASa,MAAA,CAAA2B,SAAS;sBACxCvC,MAAA,CAAAsB,aAAa,CAACkB,SAAS,+BAE5B1C,mBAAA,CAGM,OAHN2C,WAGM,GAFJ3C,mBAAA,CAAmD,MAAnD4C,WAAmD,EAAAhC,gBAAA,CAArBV,MAAA,CAAAsB,aAAa,kBAC3CxB,mBAAA,CAAqE;IAAlEL,KAAK,EA/ChBM,eAAA,EA+CiB,kBAAkB,EAASC,MAAA,CAAA2C,WAAW;sBAAK3C,MAAA,CAAA4C,UAAU,wB,KAIlE/C,mBAAA,UAAa,EACkBG,MAAA,CAAAC,eAAe,IAAID,MAAA,CAAAmB,YAAY,CAAC0B,YAAY,I,cAA3ElD,mBAAA,CAYM,OAZNmD,WAYM,G,0BAXJhD,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CASM,OATNiD,WASM,I,kBARJpD,mBAAA,CAOMqD,SAAA,QA9DdC,WAAA,CAwDkCjD,MAAA,CAAAmB,YAAY,CAAC0B,YAAY,EAxD3D,CAwDkBK,KAAK,EAAEC,IAAI;yBADrBxD,mBAAA,CAOM;MALHD,GAAG,EAAEyD,IAAI;MACV1D,KAAK,EAAC;QAENK,mBAAA,CAA6D,QAA7DsD,WAA6D,EAAA1C,gBAAA,CAAlCV,MAAA,CAAAqD,kBAAkB,CAACF,IAAI,mBAClDrD,mBAAA,CAA2C,QAA3CwD,WAA2C,EAAA5C,gBAAA,CAAfwC,KAAK,iB;wCA7D3CrD,mBAAA,gBAkEIA,mBAAA,UAAa,EACee,MAAA,CAAA2C,YAAY,I,cAAxC5D,mBAAA,CAiBM,OAjBN6D,WAiBM,GAhBJrD,YAAA,CAMYsD,oBAAA;IALVpD,IAAI,EAAC,OAAO;IACXqD,OAAK,EAAE1D,MAAA,CAAA2D,WAAW;IAClBC,IAAI,EAAE5D,MAAA,CAAA6D,QAAQ,GAAGC,IAAA,CAAAC,SAAS,GAAGD,IAAA,CAAAE;;IAvEtC1D,OAAA,EAAAC,QAAA,CAyEQ,MAA4B,CAzEpC0D,gBAAA,CAAAvD,gBAAA,CAyEWV,MAAA,CAAA6D,QAAQ,+B;IAzEnBpD,CAAA;0CA4EMN,YAAA,CAOYsD,oBAAA;IANVpD,IAAI,EAAC,OAAO;IACXqD,OAAK,EAAE1D,MAAA,CAAAkE,SAAS;IAChBC,QAAQ,GAAGnE,MAAA,CAAAC,eAAe;IAC3B2D,IAAI,EAAC;;IAhFbtD,OAAA,EAAAC,QAAA,CAiFO,MAEDiB,MAAA,QAAAA,MAAA,OAnFNyC,gBAAA,CAiFO,OAED,E;IAnFNxD,CAAA;kDAAAZ,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}