package com.traffic.analysis.interceptor;

import com.traffic.analysis.config.PerformanceConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * 性能监控拦截器
 */
@Slf4j
@Component
public class PerformanceInterceptor implements HandlerInterceptor {

    @Autowired
    private PerformanceConfig.PerformanceMonitor performanceMonitor;

    private static final String REQUEST_ID_ATTRIBUTE = "requestId";
    private static final String START_TIME_ATTRIBUTE = "startTime";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 生成请求ID
        String requestId = generateRequestId(request);
        request.setAttribute(REQUEST_ID_ATTRIBUTE, requestId);
        request.setAttribute(START_TIME_ATTRIBUTE, System.currentTimeMillis());

        // 记录请求开始
        performanceMonitor.recordRequestStart(requestId);

        // 添加响应头
        response.setHeader("X-Request-ID", requestId);
        response.setHeader("X-Response-Time", "0");

        // 记录请求信息
        log.debug("请求开始: {} {} - RequestID: {}", 
                request.getMethod(), request.getRequestURI(), requestId);

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, 
                          Object handler, ModelAndView modelAndView) throws Exception {
        // 在视图渲染前执行
        String requestId = (String) request.getAttribute(REQUEST_ID_ATTRIBUTE);
        if (requestId != null) {
            long duration = performanceMonitor.recordRequestEnd(requestId);
            
            // 更新响应时间头
            response.setHeader("X-Response-Time", String.valueOf(duration));
            
            // 记录慢请求
            if (duration > 1000) { // 超过1秒的请求
                log.warn("慢请求检测: {} {} - 耗时: {}ms - RequestID: {}", 
                        request.getMethod(), request.getRequestURI(), duration, requestId);
            }
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) throws Exception {
        // 请求完全完成后执行
        String requestId = (String) request.getAttribute(REQUEST_ID_ATTRIBUTE);
        Long startTime = (Long) request.getAttribute(START_TIME_ATTRIBUTE);
        
        if (requestId != null && startTime != null) {
            long totalDuration = System.currentTimeMillis() - startTime;
            
            // 记录请求完成信息
            log.debug("请求完成: {} {} - 总耗时: {}ms - 状态码: {} - RequestID: {}", 
                    request.getMethod(), request.getRequestURI(), totalDuration, 
                    response.getStatus(), requestId);
            
            // 记录异常信息
            if (ex != null) {
                log.error("请求异常: {} {} - RequestID: {} - 异常: {}", 
                        request.getMethod(), request.getRequestURI(), requestId, ex.getMessage(), ex);
            }
            
            // 记录错误状态码
            if (response.getStatus() >= 400) {
                log.warn("请求错误: {} {} - 状态码: {} - RequestID: {}", 
                        request.getMethod(), request.getRequestURI(), response.getStatus(), requestId);
            }
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId(HttpServletRequest request) {
        String endpoint = extractEndpoint(request);
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        return endpoint + "-" + uuid;
    }

    /**
     * 提取端点名称
     */
    private String extractEndpoint(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String method = request.getMethod();
        
        // 简化URI，移除参数和ID
        String simplifiedUri = uri.replaceAll("/\\d+", "/{id}")
                                 .replaceAll("/[a-f0-9-]{36}", "/{uuid}")
                                 .replaceAll("/[a-f0-9-]{8,}", "/{id}");
        
        return method + simplifiedUri;
    }
}

/**
 * 拦截器配置
 */
@Component
class InterceptorConfig implements org.springframework.web.servlet.config.annotation.WebMvcConfigurer {

    @Autowired
    private PerformanceInterceptor performanceInterceptor;

    @Override
    public void addInterceptors(org.springframework.web.servlet.config.annotation.InterceptorRegistry registry) {
        registry.addInterceptor(performanceInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns(
                    "/api/health",
                    "/api/metrics",
                    "/api/actuator/**"
                );
    }
}

/**
 * 响应压缩配置
 */
@Component
class CompressionConfig {

    @org.springframework.context.annotation.Bean
    public org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory tomcatFactory() {
        org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory factory = 
            new org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory();
        
        factory.addConnectorCustomizers(connector -> {
            // 启用压缩
            connector.setProperty("compression", "on");
            connector.setProperty("compressionMinSize", "1024");
            connector.setProperty("compressableMimeType", 
                "text/html,text/xml,text/plain,text/css,text/javascript," +
                "application/javascript,application/json,application/xml");
        });
        
        return factory;
    }
}

/**
 * 缓存控制过滤器
 */
@Component
class CacheControlFilter implements jakarta.servlet.Filter {

    @Override
    public void doFilter(jakarta.servlet.ServletRequest request,
                        jakarta.servlet.ServletResponse response,
                        jakarta.servlet.FilterChain chain)
                        throws java.io.IOException, jakarta.servlet.ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String uri = httpRequest.getRequestURI();
        
        // 为静态资源设置缓存
        if (uri.matches(".*\\.(css|js|png|jpg|jpeg|gif|ico|woff|woff2|ttf|eot)$")) {
            // 静态资源缓存1天
            httpResponse.setHeader("Cache-Control", "public, max-age=86400");
            httpResponse.setHeader("Expires", 
                new java.util.Date(System.currentTimeMillis() + 86400000).toString());
        } else if (uri.startsWith("/api/")) {
            // API响应不缓存
            httpResponse.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            httpResponse.setHeader("Pragma", "no-cache");
            httpResponse.setHeader("Expires", "0");
        }
        
        chain.doFilter(request, response);
    }
}

/**
 * 请求限流过滤器
 */
@Component
class RateLimitFilter implements jakarta.servlet.Filter {

    private final java.util.concurrent.ConcurrentHashMap<String, java.util.concurrent.atomic.AtomicInteger> requestCounts =
        new java.util.concurrent.ConcurrentHashMap<>();

    private final java.util.concurrent.ConcurrentHashMap<String, Long> lastResetTime =
        new java.util.concurrent.ConcurrentHashMap<>();

    private static final int MAX_REQUESTS_PER_MINUTE = 100;
    private static final long RESET_INTERVAL = 60000; // 1分钟

    @Override
    public void doFilter(jakarta.servlet.ServletRequest request,
                        jakarta.servlet.ServletResponse response,
                        jakarta.servlet.FilterChain chain)
                        throws java.io.IOException, jakarta.servlet.ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String clientIp = getClientIp(httpRequest);
        
        // 检查请求频率
        if (isRateLimited(clientIp)) {
            httpResponse.setStatus(429); // Too Many Requests
            httpResponse.setContentType("application/json");
            httpResponse.getWriter().write("{\"error\":\"请求过于频繁，请稍后重试\"}");
            return;
        }
        
        chain.doFilter(request, response);
    }
    
    private boolean isRateLimited(String clientIp) {
        long currentTime = System.currentTimeMillis();
        
        // 重置计数器
        Long lastReset = lastResetTime.get(clientIp);
        if (lastReset == null || currentTime - lastReset > RESET_INTERVAL) {
            requestCounts.put(clientIp, new java.util.concurrent.atomic.AtomicInteger(0));
            lastResetTime.put(clientIp, currentTime);
        }
        
        // 增加请求计数
        java.util.concurrent.atomic.AtomicInteger count = requestCounts.get(clientIp);
        if (count == null) {
            count = new java.util.concurrent.atomic.AtomicInteger(0);
            requestCounts.put(clientIp, count);
        }
        
        return count.incrementAndGet() > MAX_REQUESTS_PER_MINUTE;
    }
    
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
