/**
 * WebSocket调试工具
 * 用于排查实时帧推送的连接问题
 */

class WebSocketDebugger {
  constructor() {
    this.logs = []
    this.isDebugging = true
  }

  log(level, message, data = null) {
    if (!this.isDebugging) return
    
    const timestamp = new Date().toLocaleTimeString()
    const logEntry = {
      timestamp,
      level,
      message,
      data
    }
    
    this.logs.push(logEntry)
    
    // 控制台输出
    const emoji = {
      'info': 'ℹ️',
      'success': '✅',
      'warning': '⚠️',
      'error': '❌',
      'debug': '🔍'
    }
    
    console.log(`${emoji[level] || '📝'} [${timestamp}] ${message}`, data || '')
    
    // 限制日志数量
    if (this.logs.length > 100) {
      this.logs = this.logs.slice(-50)
    }
  }

  // 测试WebSocket连接
  async testWebSocketConnection() {
    this.log('info', '开始测试WebSocket连接...')
    
    try {
      // 检查STOMP服务
      if (window.stompService) {
        this.log('success', 'STOMP服务已加载')
        this.log('info', `STOMP连接状态: ${window.stompService.connected}`)
        
        if (!window.stompService.connected) {
          this.log('warning', '尝试初始化STOMP连接...')
          await window.stompService.init()
          this.log('success', 'STOMP连接初始化完成')
        }
      } else {
        this.log('error', 'STOMP服务未加载')
        return false
      }
      
      return true
    } catch (error) {
      this.log('error', 'WebSocket连接测试失败', error)
      return false
    }
  }

  // 测试帧数据订阅
  async testFrameSubscription(taskId) {
    this.log('info', `测试帧数据订阅: ${taskId}`)
    
    try {
      if (!window.stompService) {
        this.log('error', 'STOMP服务不可用')
        return false
      }

      // 订阅帧数据
      const subscription = await window.stompService.subscribeFrameUpdates(taskId, (frameData) => {
        this.log('success', '收到测试帧数据', {
          frameNumber: frameData.frameNumber,
          detectionCount: frameData.detectionCount,
          hasImageData: !!frameData.imageData
        })
      })

      this.log('success', '帧数据订阅成功', subscription)
      return true

    } catch (error) {
      this.log('error', '帧数据订阅失败', error)
      return false
    }
  }

  // 获取网络统计
  getNetworkStats() {
    try {
      if (window.stompService && window.stompService.getNetworkStats) {
        const stats = window.stompService.getNetworkStats()
        this.log('info', '网络统计', stats)
        return stats
      } else {
        this.log('warning', '网络统计不可用')
        return null
      }
    } catch (error) {
      this.log('error', '获取网络统计失败', error)
      return null
    }
  }

  // 检查RealTimeFrameViewer组件
  checkFrameViewer() {
    this.log('info', '检查RealTimeFrameViewer组件...')
    
    // 查找组件实例
    const frameViewers = document.querySelectorAll('[data-component="RealTimeFrameViewer"]')
    this.log('info', `找到 ${frameViewers.length} 个RealTimeFrameViewer组件`)
    
    // 检查Vue组件实例
    if (window.Vue && window.Vue.version) {
      this.log('info', `Vue版本: ${window.Vue.version}`)
    }
    
    return frameViewers.length > 0
  }

  // 模拟发送测试帧数据
  async sendTestFrame(taskId) {
    this.log('info', `发送测试帧数据: ${taskId}`)
    
    try {
      const testFrameData = {
        type: "frame_update",
        taskId: taskId,
        frameNumber: 999,
        totalFrames: 1000,
        imageData: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
        detectionCount: 5,
        timestamp: new Date().toISOString(),
        quality: 75
      }

      const response = await fetch('/api/video-progress/frame-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testFrameData)
      })

      if (response.ok) {
        this.log('success', '测试帧数据发送成功')
        return true
      } else {
        this.log('error', `测试帧数据发送失败: HTTP ${response.status}`)
        return false
      }

    } catch (error) {
      this.log('error', '发送测试帧数据异常', error)
      return false
    }
  }

  // 运行完整诊断
  async runFullDiagnostic(taskId) {
    this.log('info', '🚀 开始运行完整诊断...')
    this.log('info', `任务ID: ${taskId}`)
    
    const results = {
      websocket: false,
      subscription: false,
      component: false,
      testFrame: false
    }

    // 1. 测试WebSocket连接
    this.log('info', '1️⃣ 测试WebSocket连接')
    results.websocket = await this.testWebSocketConnection()

    // 2. 检查组件
    this.log('info', '2️⃣ 检查前端组件')
    results.component = this.checkFrameViewer()

    // 3. 测试订阅
    if (results.websocket) {
      this.log('info', '3️⃣ 测试帧数据订阅')
      results.subscription = await this.testFrameSubscription(taskId)
    }

    // 4. 发送测试帧
    this.log('info', '4️⃣ 发送测试帧数据')
    results.testFrame = await this.sendTestFrame(taskId)

    // 5. 获取网络统计
    this.log('info', '5️⃣ 获取网络统计')
    this.getNetworkStats()

    // 总结
    this.log('info', '📊 诊断结果', results)
    
    const successCount = Object.values(results).filter(Boolean).length
    const totalCount = Object.keys(results).length
    
    if (successCount === totalCount) {
      this.log('success', '🎉 所有诊断项目都通过了！')
    } else {
      this.log('warning', `⚠️ ${successCount}/${totalCount} 项诊断通过`)
    }

    return results
  }

  // 获取所有日志
  getLogs() {
    return this.logs
  }

  // 清空日志
  clearLogs() {
    this.logs = []
    this.log('info', '日志已清空')
  }

  // 导出日志
  exportLogs() {
    const logText = this.logs.map(log => 
      `[${log.timestamp}] ${log.level.toUpperCase()}: ${log.message} ${log.data ? JSON.stringify(log.data) : ''}`
    ).join('\n')
    
    const blob = new Blob([logText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `websocket-debug-${Date.now()}.log`
    a.click()
    URL.revokeObjectURL(url)
    
    this.log('success', '日志已导出')
  }
}

// 创建全局调试器实例
window.wsDebugger = new WebSocketDebugger()

// 添加快捷方法到控制台
window.debugWebSocket = (taskId) => {
  return window.wsDebugger.runFullDiagnostic(taskId || '7640209d-a9bf-499b-8f1f-bc7fe875abea')
}

window.wsDebugger.log('success', '🔧 WebSocket调试工具已加载')
window.wsDebugger.log('info', '💡 使用 debugWebSocket("任务ID") 来运行诊断')

export default WebSocketDebugger
