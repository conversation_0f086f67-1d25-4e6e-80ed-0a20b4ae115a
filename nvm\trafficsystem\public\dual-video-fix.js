/**
 * 双视频实时预览快速修复脚本
 * 用于立即启用双视频预览功能
 */

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
  console.log('🔧 双视频预览修复脚本已加载');
  
  // 等待Vue应用初始化
  setTimeout(() => {
    setupDualVideoPreview();
  }, 3000);
});

function setupDualVideoPreview() {
  console.log('🎬 设置双视频预览功能...');
  
  // 检查stompService是否可用
  if (typeof window.stompService === 'undefined') {
    console.log('⚠️ stompService未找到，等待加载...');
    setTimeout(setupDualVideoPreview, 1000);
    return;
  }
  
  // 获取当前任务ID
  const taskId = getCurrentTaskId();
  if (!taskId) {
    console.log('⚠️ 未找到任务ID');
    return;
  }
  
  console.log(`🎯 当前任务ID: ${taskId}`);
  
  // 订阅双视频帧数据
  subscribeDualVideoFrames(taskId);
}

function subscribeDualVideoFrames(taskId) {
  const stompService = window.stompService;
  
  try {
    console.log(`📺 开始订阅双视频帧数据...`);
    
    // 订阅水平方向帧数据
    stompService.subscribeFrameUpdates(`h_${taskId}`, (frameData) => {
      console.log(`🔄 水平方向帧数据: 帧${frameData.frameNumber}, 车辆${frameData.detectionCount}`);
      
      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('horizontalFrameReceived', {
        detail: { taskId, frameData }
      }));
    }).then(() => {
      console.log('✅ 水平方向帧数据订阅成功');
    }).catch(error => {
      console.error('❌ 水平方向帧数据订阅失败:', error);
    });
    
    // 订阅垂直方向帧数据
    stompService.subscribeFrameUpdates(`v_${taskId}`, (frameData) => {
      console.log(`🔄 垂直方向帧数据: 帧${frameData.frameNumber}, 车辆${frameData.detectionCount}`);
      
      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('verticalFrameReceived', {
        detail: { taskId, frameData }
      }));
    }).then(() => {
      console.log('✅ 垂直方向帧数据订阅成功');
    }).catch(error => {
      console.error('❌ 垂直方向帧数据订阅失败:', error);
    });
    
    console.log('✅ 双视频帧数据订阅已设置');
    
  } catch (error) {
    console.error('❌ 设置双视频订阅失败:', error);
  }
}

function getCurrentTaskId() {
  // 方法1: 从URL参数获取
  const urlParams = new URLSearchParams(window.location.search);
  let taskId = urlParams.get('taskId');

  if (!taskId) {
    // 方法2: 从路径获取
    const pathParts = window.location.pathname.split('/');
    const taskIndex = pathParts.indexOf('video-status');
    if (taskIndex !== -1 && pathParts[taskIndex + 1]) {
      taskId = pathParts[taskIndex + 1];
    }
  }

  if (!taskId) {
    // 方法3: 从sessionStorage获取
    try {
      const uploadState = sessionStorage.getItem('uploadState');
      if (uploadState) {
        const state = JSON.parse(uploadState);
        taskId = state.taskId;
      }
    } catch (e) {
      console.warn('无法从sessionStorage获取任务ID:', e);
    }
  }

  if (!taskId) {
    // 方法4: 从页面元素获取
    const taskElements = document.querySelectorAll('[data-task-id]');
    if (taskElements.length > 0) {
      taskId = taskElements[0].getAttribute('data-task-id');
    }
  }

  if (!taskId) {
    // 方法5: 从控制台日志中提取
    const logs = console.history || [];
    for (const log of logs) {
      const match = log.match(/任务ID[：:]\s*([a-f0-9-]{36})/i);
      if (match) {
        taskId = match[1];
        break;
      }
    }
  }

  return taskId;
}

// 创建双视频预览界面
function createDualVideoPreviewUI(taskId) {
  console.log('🎨 创建双视频预览界面...');
  
  // 查找现有的预览容器
  let previewContainer = document.querySelector('.realtime-preview-section');
  
  if (!previewContainer) {
    // 如果没有找到，创建一个新的
    previewContainer = document.createElement('div');
    previewContainer.className = 'realtime-preview-section';
    
    // 插入到合适的位置
    const uploadForm = document.querySelector('.upload-form');
    if (uploadForm) {
      uploadForm.appendChild(previewContainer);
    } else {
      document.body.appendChild(previewContainer);
    }
  }
  
  // 创建双视频预览HTML
  previewContainer.innerHTML = `
    <h3 class="preview-title">
      <span>📹</span>
      实时视频分析预览
    </h3>

    <div class="video-preview-grid">
      <div class="video-preview-container">
        <div class="video-header">
          <h4>水平方向视频</h4>
          <span class="el-tag el-tag--primary el-tag--small" style="background: #3b82f6; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; border: none;">实时分析中</span>
        </div>
        <div id="horizontal-preview" class="video-preview-area waiting">
          <div class="loading-spinner"></div>
          等待水平方向视频数据...
        </div>
        <div id="horizontal-stats" class="video-stats">
          <div class="stat-item">
            <span class="stat-value">0</span>
            <span class="stat-label">帧数</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">0</span>
            <span class="stat-label">车辆</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">--</span>
            <span class="stat-label">状态</span>
          </div>
        </div>
      </div>

      <div class="video-preview-container">
        <div class="video-header">
          <h4>垂直方向视频</h4>
          <span class="el-tag el-tag--success el-tag--small" style="background: #10b981; color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; border: none;">实时分析中</span>
        </div>
        <div id="vertical-preview" class="video-preview-area waiting">
          <div class="loading-spinner"></div>
          等待垂直方向视频数据...
        </div>
        <div id="vertical-stats" class="video-stats">
          <div class="stat-item">
            <span class="stat-value">0</span>
            <span class="stat-label">帧数</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">0</span>
            <span class="stat-label">车辆</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">--</span>
            <span class="stat-label">状态</span>
          </div>
        </div>
      </div>
    </div>
  `;
  
  // 监听帧数据事件
  window.addEventListener('horizontalFrameReceived', (event) => {
    updatePreview('horizontal', event.detail.frameData);
  });
  
  window.addEventListener('verticalFrameReceived', (event) => {
    updatePreview('vertical', event.detail.frameData);
  });
  
  console.log('✅ 双视频预览界面已创建');
}

function updatePreview(direction, frameData) {
  const previewElement = document.getElementById(`${direction}-preview`);
  const statsElement = document.getElementById(`${direction}-stats`);

  if (previewElement && frameData.imageData) {
    // 检查是否有车辆检测
    const hasDetection = frameData.detectionCount > 0;

    if (hasDetection) {
      // 显示检测帧（定格显示）
      previewElement.className = 'detection-frame-container';
      previewElement.innerHTML = `
        <img src="data:image/jpeg;base64,${frameData.imageData}" class="detection-frame-image" />
        <div class="detection-overlay">
          <div class="detection-header">
            <span class="el-tag el-tag--${getDetectionTagType(frameData.detectionCount)} el-tag--small" style="padding: 4px 12px; border-radius: 20px; font-size: 11px; border: none;">
              🚗 检测到 ${frameData.detectionCount} 辆车
            </span>
            <span class="detection-time">${formatTime(frameData.timestamp)}</span>
          </div>
          <div class="detection-info">
            <span class="frame-number">帧: ${frameData.frameNumber}</span>
            <span class="detection-status">最新检测结果 (定格显示)</span>
          </div>
        </div>
        <div class="success-indicator"></div>
      `;
    } else {
      // 显示常规帧
      previewElement.className = 'video-preview-area';
      previewElement.innerHTML = `<img src="data:image/jpeg;base64,${frameData.imageData}" style="max-width: 100%; max-height: 100%; object-fit: contain; border-radius: 6px;" />`;
    }

    // 更新统计信息
    if (statsElement) {
      const frameValue = statsElement.querySelector('.stat-item:nth-child(1) .stat-value');
      const vehicleValue = statsElement.querySelector('.stat-item:nth-child(2) .stat-value');
      const statusValue = statsElement.querySelector('.stat-item:nth-child(3) .stat-value');

      if (frameValue) frameValue.textContent = frameData.frameNumber;
      if (vehicleValue) vehicleValue.textContent = frameData.detectionCount;
      if (statusValue) statusValue.textContent = hasDetection ? '检测中' : '分析中';
    }
  }
}

// 获取检测标签类型
function getDetectionTagType(detectionCount) {
  if (detectionCount === 0) return 'info';
  if (detectionCount <= 2) return 'success';
  if (detectionCount <= 5) return 'warning';
  return 'danger';
}

// 格式化时间
function formatTime(timestamp) {
  if (!timestamp) return '';
  try {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  } catch (e) {
    return timestamp;
  }
}

// 导出函数供其他脚本使用
window.setupDualVideoPreview = setupDualVideoPreview;
window.createDualVideoPreviewUI = createDualVideoPreviewUI;

// 自动创建预览界面
setTimeout(() => {
  const taskId = getCurrentTaskId();
  if (taskId) {
    createDualVideoPreviewUI(taskId);
  }
}, 5000);
