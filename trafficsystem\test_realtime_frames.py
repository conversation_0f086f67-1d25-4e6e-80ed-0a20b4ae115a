#!/usr/bin/env python3
"""
实时帧推送功能测试脚本
用于验证实时帧推送的稳定性和性能
"""

import os
import sys
import time
import json
import requests
import threading
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from python_model.model_api import (
    send_frame_to_backend,
    prepare_frame_for_push,
    should_push_frame,
    get_adaptive_quality,
    update_network_performance,
    network_performance
)

class RealtimeFrameTest:
    """实时帧推送测试类"""
    
    def __init__(self):
        self.test_results = {
            "total_frames_sent": 0,
            "successful_sends": 0,
            "failed_sends": 0,
            "average_response_time": 0,
            "network_quality_changes": [],
            "smart_selection_efficiency": 0
        }
        self.start_time = None
        
    def test_frame_push_performance(self, num_frames=100):
        """
        测试帧推送性能
        
        Args:
            num_frames: 测试帧数量
        """
        print(f"开始帧推送性能测试，测试帧数: {num_frames}")
        self.start_time = time.time()
        
        # 模拟帧数据
        test_task_id = "test_task_" + str(int(time.time()))
        
        for frame_num in range(1, num_frames + 1):
            # 模拟检测数量变化
            detection_count = (frame_num % 10) + (1 if frame_num % 20 == 0 else 0)
            
            # 测试智能帧选择
            if should_push_frame(frame_num, detection_count, num_frames):
                # 创建测试帧数据
                test_frame_data = self._create_test_frame_data(frame_num, detection_count)
                
                if test_frame_data:
                    # 发送帧数据
                    start_time = time.time()
                    try:
                        self._send_test_frame(test_task_id, test_frame_data, frame_num, num_frames, detection_count)
                        self.test_results["successful_sends"] += 1
                        
                        # 模拟网络响应时间
                        response_time = time.time() - start_time
                        update_network_performance(response_time, True)
                        
                    except Exception as e:
                        print(f"发送帧 {frame_num} 失败: {e}")
                        self.test_results["failed_sends"] += 1
                        update_network_performance(2.0, False)
                    
                    self.test_results["total_frames_sent"] += 1
            
            # 记录网络质量变化
            current_quality = get_adaptive_quality()
            if len(self.test_results["network_quality_changes"]) == 0 or \
               self.test_results["network_quality_changes"][-1] != current_quality:
                self.test_results["network_quality_changes"].append(current_quality)
            
            # 模拟处理延迟
            time.sleep(0.1)
        
        # 计算测试结果
        self._calculate_test_results()
        
    def test_network_adaptation(self):
        """测试网络自适应功能"""
        print("开始网络自适应测试...")
        
        # 模拟不同网络条件
        network_conditions = [
            {"response_time": 0.3, "success_rate": 0.98, "name": "良好网络"},
            {"response_time": 1.5, "success_rate": 0.85, "name": "一般网络"},
            {"response_time": 3.0, "success_rate": 0.60, "name": "较差网络"},
            {"response_time": 0.5, "success_rate": 0.95, "name": "恢复网络"}
        ]
        
        for condition in network_conditions:
            print(f"测试 {condition['name']} 条件...")
            
            # 模拟网络条件
            for _ in range(10):
                update_network_performance(condition["response_time"], 
                                         True if time.time() % 1 < condition["success_rate"] else False)
            
            quality = get_adaptive_quality()
            print(f"  网络条件: {condition['name']}")
            print(f"  响应时间: {condition['response_time']}s")
            print(f"  成功率: {condition['success_rate']*100}%")
            print(f"  自适应质量: {quality}")
            print(f"  网络性能统计: {network_performance}")
            print()
    
    def test_smart_frame_selection(self):
        """测试智能帧选择算法"""
        print("开始智能帧选择测试...")
        
        total_frames = 200
        selected_frames = 0
        
        # 模拟不同场景的检测数量变化
        scenarios = [
            {"name": "稳定场景", "detection_pattern": lambda f: 5},
            {"name": "变化场景", "detection_pattern": lambda f: f % 10},
            {"name": "突发场景", "detection_pattern": lambda f: 15 if f % 30 == 0 else 3},
            {"name": "渐变场景", "detection_pattern": lambda f: int(f / 20)}
        ]
        
        for scenario in scenarios:
            print(f"测试 {scenario['name']}...")
            scenario_selected = 0
            
            for frame_num in range(1, total_frames + 1):
                detection_count = scenario["detection_pattern"](frame_num)
                
                if should_push_frame(frame_num, detection_count, total_frames):
                    scenario_selected += 1
                    selected_frames += 1
            
            efficiency = (scenario_selected / total_frames) * 100
            print(f"  选择帧数: {scenario_selected}/{total_frames}")
            print(f"  选择效率: {efficiency:.1f}%")
            print()
        
        overall_efficiency = (selected_frames / (total_frames * len(scenarios))) * 100
        self.test_results["smart_selection_efficiency"] = overall_efficiency
        print(f"总体选择效率: {overall_efficiency:.1f}%")
    
    def test_integration_compatibility(self):
        """测试与现有功能的兼容性"""
        print("开始集成兼容性测试...")
        
        # 测试API端点可用性
        endpoints = [
            "http://localhost:8080/api/video-progress/frame-update",
            "http://localhost:8080/api/video/upload",
            "http://localhost:5000/analyze_video_from_gridfs"
        ]
        
        for endpoint in endpoints:
            try:
                # 发送测试请求（GET请求检查可用性）
                response = requests.get(endpoint.replace('/frame-update', '').replace('/upload', '').replace('/analyze_video_from_gridfs', '/health'), 
                                      timeout=5)
                print(f"✓ {endpoint} - 可访问")
            except requests.exceptions.RequestException as e:
                print(f"✗ {endpoint} - 不可访问: {e}")
    
    def _create_test_frame_data(self, frame_num, detection_count):
        """创建测试帧数据"""
        # 创建一个简单的测试图像数据（Base64编码的小图片）
        import base64
        
        # 1x1像素的透明PNG图片的Base64编码
        test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        
        return test_image_base64
    
    def _send_test_frame(self, task_id, frame_data, frame_number, total_frames, detection_count):
        """发送测试帧数据"""
        # 这里只是模拟发送，不实际发送到后端
        # 在实际测试中可以启用真实发送
        print(f"模拟发送帧 {frame_number}: 检测数量={detection_count}, 质量={get_adaptive_quality()}")
    
    def _calculate_test_results(self):
        """计算测试结果"""
        total_time = time.time() - self.start_time if self.start_time else 0
        
        if self.test_results["total_frames_sent"] > 0:
            success_rate = (self.test_results["successful_sends"] / self.test_results["total_frames_sent"]) * 100
        else:
            success_rate = 0
        
        self.test_results["success_rate"] = success_rate
        self.test_results["total_test_time"] = total_time
        self.test_results["frames_per_second"] = self.test_results["total_frames_sent"] / total_time if total_time > 0 else 0
    
    def print_test_results(self):
        """打印测试结果"""
        print("\n" + "="*50)
        print("实时帧推送功能测试结果")
        print("="*50)
        
        print(f"总发送帧数: {self.test_results['total_frames_sent']}")
        print(f"成功发送: {self.test_results['successful_sends']}")
        print(f"发送失败: {self.test_results['failed_sends']}")
        print(f"成功率: {self.test_results.get('success_rate', 0):.1f}%")
        print(f"测试总时间: {self.test_results.get('total_test_time', 0):.2f}秒")
        print(f"发送速率: {self.test_results.get('frames_per_second', 0):.2f} 帧/秒")
        print(f"智能选择效率: {self.test_results['smart_selection_efficiency']:.1f}%")
        print(f"网络质量变化次数: {len(self.test_results['network_quality_changes'])}")
        print(f"最终网络性能: {network_performance}")
        
        print("\n测试结论:")
        if self.test_results.get('success_rate', 0) >= 90:
            print("✓ 实时帧推送功能运行稳定")
        else:
            print("✗ 实时帧推送功能需要优化")
        
        if self.test_results['smart_selection_efficiency'] >= 10 and self.test_results['smart_selection_efficiency'] <= 30:
            print("✓ 智能帧选择算法效率合理")
        else:
            print("✗ 智能帧选择算法需要调整")

def main():
    """主测试函数"""
    print("实时帧推送功能测试开始...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 创建测试实例
    test = RealtimeFrameTest()
    
    try:
        # 运行各项测试
        test.test_frame_push_performance(50)
        print()
        
        test.test_network_adaptation()
        print()
        
        test.test_smart_frame_selection()
        print()
        
        test.test_integration_compatibility()
        print()
        
        # 打印测试结果
        test.print_test_results()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
