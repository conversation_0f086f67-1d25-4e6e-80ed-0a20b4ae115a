package com.traffic.analysis.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 分析报告数据实体类
 * 对应MongoDB的report_data集合
 */
@Data
@Document(collection = "report_data")
public class ReportData {
    
    @Id
    private String id;
    
    /**
     * 关联的任务ID
     */
    @Field("task_id")
    private String taskId;
    
    /**
     * 关联的分析结果ID
     */
    @Field("analysis_result_id")
    private String analysisResultId;
    
    /**
     * 报告类型：summary, detailed, technical
     */
    @Field("report_type")
    private String reportType;
    
    /**
     * 报告标题
     */
    @Field("title")
    private String title;
    
    /**
     * 分析摘要
     */
    @Field("summary")
    private String summary;
    
    /**
     * 执行摘要
     */
    @Field("executive_summary")
    private ExecutiveSummary executiveSummary;
    
    /**
     * 改进建议列表
     */
    @Field("recommendations")
    private List<Recommendation> recommendations = new ArrayList<>();
    
    /**
     * 图表数据
     */
    @Field("charts_data")
    private Map<String, Object> chartsData = new HashMap<>();
    
    /**
     * 关键指标
     */
    @Field("key_metrics")
    private Map<String, Object> keyMetrics = new HashMap<>();
    
    /**
     * 技术指标
     */
    @Field("technical_metrics")
    private TechnicalMetrics technicalMetrics;
    
    /**
     * 报告生成时间
     */
    @Field("generated_at")
    private LocalDateTime generatedAt;
    
    /**
     * 报告版本
     */
    @Field("version")
    private String version = "1.0";
    
    /**
     * 报告状态：draft, final, archived
     */
    @Field("status")
    private String status = "draft";
    
    /**
     * 生成者ID
     */
    @Field("generated_by")
    private String generatedBy;
    
    /**
     * 报告格式：pdf, html, json
     */
    @Field("format")
    private String format;
    
    /**
     * 报告文件路径
     */
    @Field("file_path")
    private String filePath;
    
    /**
     * 报告大小（字节）
     */
    @Field("file_size")
    private long fileSize;
    
    /**
     * 执行摘要内部类
     */
    @Data
    public static class ExecutiveSummary {
        /**
         * 总车辆数
         */
        @Field("total_vehicles")
        private int totalVehicles;
        
        /**
         * 车辆增长率
         */
        @Field("vehicle_increase")
        private double vehicleIncrease;
        
        /**
         * 处理时长（秒）
         */
        @Field("processing_duration")
        private long processingDuration;
        
        /**
         * 系统效率
         */
        @Field("efficiency")
        private double efficiency;
        
        /**
         * 最繁忙方向
         */
        @Field("peak_direction")
        private String peakDirection;
        
        /**
         * 最繁忙方向占比
         */
        @Field("peak_percentage")
        private double peakPercentage;
        
        /**
         * 拥堵等级
         */
        @Field("congestion_level")
        private String congestionLevel;
        
        /**
         * 拥堵趋势
         */
        @Field("congestion_trend")
        private String congestionTrend;
    }
    
    /**
     * 建议内部类
     */
    @Data
    public static class Recommendation {
        /**
         * 建议类型：signal, infrastructure, management, technology
         */
        @Field("type")
        private String type;
        
        /**
         * 建议标题
         */
        @Field("title")
        private String title;
        
        /**
         * 建议描述
         */
        @Field("description")
        private String description;
        
        /**
         * 优先级：low, medium, high
         */
        @Field("priority")
        private String priority;
        
        /**
         * 预期改善效果
         */
        @Field("expected_improvement")
        private String expectedImprovement;
        
        /**
         * 实施成本：low, medium, high
         */
        @Field("implementation_cost")
        private String implementationCost;
        
        /**
         * 实施时间（天）
         */
        @Field("implementation_time")
        private int implementationTime;
        
        /**
         * 影响范围
         */
        @Field("impact_scope")
        private String impactScope;
    }
    
    /**
     * 技术指标内部类
     */
    @Data
    public static class TechnicalMetrics {
        /**
         * 检测精度
         */
        @Field("accuracy")
        private double accuracy;
        
        /**
         * 处理速度（FPS）
         */
        @Field("processing_speed")
        private double processingSpeed;
        
        /**
         * 系统稳定性
         */
        @Field("stability")
        private double stability;
        
        /**
         * 数据完整性
         */
        @Field("data_integrity")
        private double dataIntegrity;
        
        /**
         * 响应时间（毫秒）
         */
        @Field("response_time")
        private long responseTime;
        
        /**
         * 内存使用率
         */
        @Field("memory_usage")
        private double memoryUsage;
        
        /**
         * CPU使用率
         */
        @Field("cpu_usage")
        private double cpuUsage;
    }
    
    /**
     * 生成报告
     */
    public void generateReport() {
        this.generatedAt = LocalDateTime.now();
        this.status = "final";
    }
    
    /**
     * 添加建议
     */
    public void addRecommendation(String type, String title, String description, String priority) {
        Recommendation recommendation = new Recommendation();
        recommendation.setType(type);
        recommendation.setTitle(title);
        recommendation.setDescription(description);
        recommendation.setPriority(priority);
        this.recommendations.add(recommendation);
    }
    
    /**
     * 设置关键指标
     */
    public void setKeyMetric(String key, Object value) {
        this.keyMetrics.put(key, value);
    }
    
    /**
     * 设置图表数据
     */
    public void setChartData(String chartName, Object data) {
        this.chartsData.put(chartName, data);
    }
    
    /**
     * 获取高优先级建议数量
     */
    public long getHighPriorityRecommendationsCount() {
        return recommendations.stream()
                .filter(r -> "high".equals(r.getPriority()))
                .count();
    }
    
    /**
     * 检查报告是否完整
     */
    public boolean isComplete() {
        return summary != null && !summary.isEmpty() &&
               executiveSummary != null &&
               !recommendations.isEmpty() &&
               !keyMetrics.isEmpty();
    }
    
    /**
     * 归档报告
     */
    public void archiveReport() {
        this.status = "archived";
    }
    
    /**
     * 获取报告摘要信息
     */
    public String getReportSummaryInfo() {
        return String.format("报告类型: %s, 生成时间: %s, 建议数量: %d, 状态: %s",
                reportType, generatedAt, recommendations.size(), status);
    }
}
