<template>
  <div class="four-way-preview-container">
    <!-- 标题和控制栏 -->
    <div class="preview-header">
      <div class="header-left">
        <h3 class="preview-title">
          <el-icon><Monitor /></el-icon>
          四方向实时检测预览
        </h3>
        <el-tag v-if="taskId" type="info" size="small">任务ID: {{ taskId }}</el-tag>
      </div>
      
      <div class="header-controls">
        <el-button-group>
          <el-button 
            :type="isPlaying ? 'danger' : 'primary'" 
            size="small"
            @click="togglePlayback"
            :disabled="!hasAnyStream"
          >
            <el-icon>
              <VideoPause v-if="isPlaying" />
              <VideoPlay v-else />
            </el-icon>
            {{ isPlaying ? '暂停' : '播放' }}
          </el-button>
          
          <el-button 
            size="small"
            @click="resetAllStreams"
            :disabled="!hasAnyStream"
          >
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
          
          <el-button 
            size="small"
            @click="toggleFullscreen"
          >
            <el-icon><FullScreen /></el-icon>
            全屏
          </el-button>
        </el-button-group>
        
        <!-- 同步状态指示器 -->
        <div class="sync-indicator">
          <el-icon :class="{ 'synced': isSynced, 'unsynced': !isSynced }">
            <Connection />
          </el-icon>
          <span class="sync-text">{{ isSynced ? '同步' : '异步' }}</span>
        </div>
      </div>
    </div>

    <!-- 四方向预览网格 -->
    <div class="preview-grid" :class="{ 'fullscreen': isFullscreen }">
      <!-- 北向预览 -->
      <div class="preview-item north">
        <div class="direction-label">
          <el-icon><Top /></el-icon>
          <span>北向 (North)</span>
          <el-tag :type="getStatusTagType('north')" size="small">
            {{ getDirectionStatus('north') }}
          </el-tag>
        </div>
        <RealTimeFrameViewer
          ref="northViewer"
          :task-id="taskId"
          :direction="'north'"
          :is-playing="isPlaying"
          @frame-received="onFrameReceived"
          @status-changed="onDirectionStatusChanged"
          @error="onDirectionError"
          class="frame-viewer"
        />
        <div class="detection-stats">
          <span>检测数: {{ directionStats.north.detectionCount }}</span>
          <span>车辆数: {{ directionStats.north.vehicleCount }}</span>
        </div>
      </div>

      <!-- 西向预览 -->
      <div class="preview-item west">
        <div class="direction-label">
          <el-icon><Back /></el-icon>
          <span>西向 (West)</span>
          <el-tag :type="getStatusTagType('west')" size="small">
            {{ getDirectionStatus('west') }}
          </el-tag>
        </div>
        <RealTimeFrameViewer
          ref="westViewer"
          :task-id="taskId"
          :direction="'west'"
          :is-playing="isPlaying"
          @frame-received="onFrameReceived"
          @status-changed="onDirectionStatusChanged"
          @error="onDirectionError"
          class="frame-viewer"
        />
        <div class="detection-stats">
          <span>检测数: {{ directionStats.west.detectionCount }}</span>
          <span>车辆数: {{ directionStats.west.vehicleCount }}</span>
        </div>
      </div>

      <!-- 中心交叉路口状态 -->
      <div class="intersection-center">
        <div class="intersection-status">
          <el-icon size="32" class="intersection-icon">
            <Grid />
          </el-icon>
          <div class="status-info">
            <p class="total-vehicles">总车辆: {{ totalVehicleCount }}</p>
            <p class="processing-status">{{ overallStatus }}</p>
            <div class="progress-ring" v-if="overallProgress > 0">
              <el-progress 
                type="circle" 
                :percentage="overallProgress" 
                :width="60"
                :stroke-width="4"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 东向预览 -->
      <div class="preview-item east">
        <div class="direction-label">
          <el-icon><Right /></el-icon>
          <span>东向 (East)</span>
          <el-tag :type="getStatusTagType('east')" size="small">
            {{ getDirectionStatus('east') }}
          </el-tag>
        </div>
        <RealTimeFrameViewer
          ref="eastViewer"
          :task-id="taskId"
          :direction="'east'"
          :is-playing="isPlaying"
          @frame-received="onFrameReceived"
          @status-changed="onDirectionStatusChanged"
          @error="onDirectionError"
          class="frame-viewer"
        />
        <div class="detection-stats">
          <span>检测数: {{ directionStats.east.detectionCount }}</span>
          <span>车辆数: {{ directionStats.east.vehicleCount }}</span>
        </div>
      </div>

      <!-- 南向预览 -->
      <div class="preview-item south">
        <div class="direction-label">
          <el-icon><Bottom /></el-icon>
          <span>南向 (South)</span>
          <el-tag :type="getStatusTagType('south')" size="small">
            {{ getDirectionStatus('south') }}
          </el-tag>
        </div>
        <RealTimeFrameViewer
          ref="southViewer"
          :task-id="taskId"
          :direction="'south'"
          :is-playing="isPlaying"
          @frame-received="onFrameReceived"
          @status-changed="onDirectionStatusChanged"
          @error="onDirectionError"
          class="frame-viewer"
        />
        <div class="detection-stats">
          <span>检测数: {{ directionStats.south.detectionCount }}</span>
          <span>车辆数: {{ directionStats.south.vehicleCount }}</span>
        </div>
      </div>
    </div>

    <!-- 同步控制面板 -->
    <div class="sync-controls" v-if="hasAnyStream">
      <div class="sync-options">
        <el-checkbox v-model="enableSync" @change="onSyncToggle">
          启用四方向同步播放
        </el-checkbox>
        <el-slider
          v-model="syncTolerance"
          :min="100"
          :max="2000"
          :step="100"
          show-input
          :show-input-controls="false"
          class="sync-tolerance-slider"
        >
          <template #label>
            同步容差 (ms): {{ syncTolerance }}
          </template>
        </el-slider>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Monitor, VideoPlay, VideoPause, RefreshLeft, FullScreen,
  Top, Bottom, ArrowLeft as Back, Right, Grid, Connection
} from '@element-plus/icons-vue'
import RealTimeFrameViewer from './RealTimeFrameViewer.vue'

export default {
  name: 'FourWayRealTimePreview',
  components: {
    Monitor, VideoPlay, VideoPause, RefreshLeft, FullScreen,
    Top, Bottom, Back, Right, Grid, Connection,
    RealTimeFrameViewer
  },
  props: {
    taskId: {
      type: String,
      required: true
    },
    autoStart: {
      type: Boolean,
      default: false
    }
  },
  emits: ['status-changed', 'sync-status-changed', 'error'],
  setup(props, { emit }) {
    // 响应式数据
    const isPlaying = ref(false)
    const isFullscreen = ref(false)
    const enableSync = ref(true)
    const syncTolerance = ref(500) // 同步容差，毫秒
    
    // 方向状态
    const directionStatuses = reactive({
      north: 'waiting',
      south: 'waiting', 
      east: 'waiting',
      west: 'waiting'
    })
    
    // 方向统计数据
    const directionStats = reactive({
      north: { detectionCount: 0, vehicleCount: 0, lastFrameTime: 0 },
      south: { detectionCount: 0, vehicleCount: 0, lastFrameTime: 0 },
      east: { detectionCount: 0, vehicleCount: 0, lastFrameTime: 0 },
      west: { detectionCount: 0, vehicleCount: 0, lastFrameTime: 0 }
    })
    
    // 引用
    const northViewer = ref(null)
    const southViewer = ref(null)
    const eastViewer = ref(null)
    const westViewer = ref(null)
    
    // 计算属性
    const hasAnyStream = computed(() => {
      return Object.values(directionStatuses).some(status => 
        status === 'connected' || status === 'playing'
      )
    })
    
    const totalVehicleCount = computed(() => {
      return Object.values(directionStats).reduce((total, stats) => 
        total + stats.vehicleCount, 0
      )
    })
    
    const overallProgress = computed(() => {
      const activeDirections = Object.values(directionStatuses).filter(status => 
        status !== 'waiting' && status !== 'error'
      ).length
      
      if (activeDirections === 0) return 0
      
      const completedDirections = Object.values(directionStatuses).filter(status => 
        status === 'completed'
      ).length
      
      return Math.round((completedDirections / 4) * 100)
    })
    
    const overallStatus = computed(() => {
      const statuses = Object.values(directionStatuses)
      
      if (statuses.every(status => status === 'waiting')) {
        return '等待开始'
      } else if (statuses.some(status => status === 'playing')) {
        return '正在检测'
      } else if (statuses.every(status => status === 'completed')) {
        return '检测完成'
      } else if (statuses.some(status => status === 'error')) {
        return '检测异常'
      } else {
        return '准备中'
      }
    })
    
    const isSynced = computed(() => {
      if (!enableSync.value) return false
      
      const frameTimes = Object.values(directionStats).map(stats => stats.lastFrameTime)
      const validTimes = frameTimes.filter(time => time > 0)
      
      if (validTimes.length < 2) return true
      
      const maxTime = Math.max(...validTimes)
      const minTime = Math.min(...validTimes)
      
      return (maxTime - minTime) <= syncTolerance.value
    })
    
    // 方法
    const getDirectionStatus = (direction) => {
      const status = directionStatuses[direction]
      const statusMap = {
        waiting: '等待',
        connecting: '连接中',
        connected: '已连接',
        playing: '检测中',
        paused: '已暂停',
        completed: '已完成',
        error: '错误'
      }
      return statusMap[status] || status
    }
    
    const getStatusTagType = (direction) => {
      const status = directionStatuses[direction]
      const typeMap = {
        waiting: 'info',
        connecting: 'warning',
        connected: 'success',
        playing: 'primary',
        paused: 'warning',
        completed: 'success',
        error: 'danger'
      }
      return typeMap[status] || 'info'
    }
    
    const togglePlayback = () => {
      isPlaying.value = !isPlaying.value
      
      // 通知所有方向的播放状态变化
      const viewers = [northViewer.value, southViewer.value, eastViewer.value, westViewer.value]
      viewers.forEach(viewer => {
        if (viewer && typeof viewer.togglePlayback === 'function') {
          viewer.togglePlayback()
        }
      })
      
      emit('status-changed', {
        action: isPlaying.value ? 'play' : 'pause',
        taskId: props.taskId
      })
    }
    
    const resetAllStreams = () => {
      const viewers = [northViewer.value, southViewer.value, eastViewer.value, westViewer.value]
      viewers.forEach(viewer => {
        if (viewer && typeof viewer.reset === 'function') {
          viewer.reset()
        }
      })
      
      // 重置统计数据
      Object.keys(directionStats).forEach(direction => {
        directionStats[direction] = { detectionCount: 0, vehicleCount: 0, lastFrameTime: 0 }
        directionStatuses[direction] = 'waiting'
      })
      
      isPlaying.value = false
      
      ElMessage.success('已重置所有方向的预览')
    }
    
    const toggleFullscreen = () => {
      isFullscreen.value = !isFullscreen.value
      
      if (isFullscreen.value) {
        document.documentElement.requestFullscreen?.()
      } else {
        document.exitFullscreen?.()
      }
    }
    
    const onFrameReceived = (data) => {
      const { direction, frameData, vehicleCount, timestamp } = data
      
      if (directionStats[direction]) {
        directionStats[direction].detectionCount++
        directionStats[direction].vehicleCount = vehicleCount || 0
        directionStats[direction].lastFrameTime = timestamp || Date.now()
      }
      
      // 检查同步状态
      if (enableSync.value) {
        checkSyncStatus()
      }
    }
    
    const onDirectionStatusChanged = (data) => {
      const { direction, status } = data
      if (directionStatuses[direction] !== undefined) {
        directionStatuses[direction] = status
      }
      
      emit('status-changed', {
        direction,
        status,
        taskId: props.taskId
      })
    }
    
    const onDirectionError = (data) => {
      const { direction, error } = data
      directionStatuses[direction] = 'error'
      
      ElMessage.error(`${getDirectionName(direction)}检测出错: ${error.message || error}`)
      
      emit('error', {
        direction,
        error,
        taskId: props.taskId
      })
    }
    
    const onSyncToggle = (enabled) => {
      emit('sync-status-changed', {
        enabled,
        tolerance: syncTolerance.value,
        taskId: props.taskId
      })
    }
    
    const checkSyncStatus = () => {
      const currentSyncStatus = isSynced.value
      emit('sync-status-changed', {
        enabled: enableSync.value,
        synced: currentSyncStatus,
        tolerance: syncTolerance.value,
        taskId: props.taskId
      })
    }
    
    const getDirectionName = (direction) => {
      const names = {
        east: '东向',
        south: '南向',
        west: '西向',
        north: '北向'
      }
      return names[direction] || direction
    }
    
    // 生命周期
    onMounted(() => {
      if (props.autoStart) {
        setTimeout(() => {
          isPlaying.value = true
        }, 1000)
      }
    })
    
    onUnmounted(() => {
      if (isFullscreen.value) {
        document.exitFullscreen?.()
      }
    })
    
    // 监听器
    watch(() => props.taskId, (newTaskId) => {
      if (newTaskId) {
        resetAllStreams()
      }
    })
    
    return {
      // 响应式数据
      isPlaying,
      isFullscreen,
      enableSync,
      syncTolerance,
      directionStatuses,
      directionStats,
      
      // 引用
      northViewer,
      southViewer,
      eastViewer,
      westViewer,
      
      // 计算属性
      hasAnyStream,
      totalVehicleCount,
      overallProgress,
      overallStatus,
      isSynced,
      
      // 方法
      getDirectionStatus,
      getStatusTagType,
      togglePlayback,
      resetAllStreams,
      toggleFullscreen,
      onFrameReceived,
      onDirectionStatusChanged,
      onDirectionError,
      onSyncToggle
    }
  }
}
</script>

<style scoped>
.four-way-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  color: #2c3e50;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sync-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.sync-indicator .synced {
  color: #67c23a;
}

.sync-indicator .unsynced {
  color: #f56c6c;
}

.preview-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 300px 1fr;
  grid-template-rows: 1fr 300px 1fr;
  gap: 16px;
  padding: 20px;
  min-height: 600px;
}

.preview-grid.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: #000;
  padding: 10px;
}

.preview-item {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.preview-item.north {
  grid-column: 2;
  grid-row: 1;
}

.preview-item.west {
  grid-column: 1;
  grid-row: 2;
}

.preview-item.east {
  grid-column: 3;
  grid-row: 2;
}

.preview-item.south {
  grid-column: 2;
  grid-row: 3;
}

.intersection-center {
  grid-column: 2;
  grid-row: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.intersection-status {
  text-align: center;
}

.intersection-icon {
  color: #409eff;
  margin-bottom: 12px;
}

.status-info p {
  margin: 4px 0;
  font-size: 14px;
}

.total-vehicles {
  font-weight: 600;
  color: #2c3e50;
}

.processing-status {
  color: #909399;
}

.progress-ring {
  margin-top: 12px;
}

.direction-label {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 500;
  color: #2c3e50;
}

.frame-viewer {
  flex: 1;
  min-height: 200px;
}

.detection-stats {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
  font-size: 12px;
  color: #909399;
}

.sync-controls {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #e4e7ed;
}

.sync-options {
  display: flex;
  align-items: center;
  gap: 20px;
}

.sync-tolerance-slider {
  width: 200px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .preview-grid {
    grid-template-columns: 1fr 250px 1fr;
    grid-template-rows: 1fr 250px 1fr;
  }
}

@media (max-width: 768px) {
  .preview-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: 12px;
  }
  
  .preview-item {
    grid-column: 1 !important;
    grid-row: auto !important;
  }
  
  .intersection-center {
    grid-column: 1 !important;
    grid-row: auto !important;
    order: -1;
  }
  
  .header-controls {
    flex-direction: column;
    gap: 8px;
  }
  
  .sync-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
