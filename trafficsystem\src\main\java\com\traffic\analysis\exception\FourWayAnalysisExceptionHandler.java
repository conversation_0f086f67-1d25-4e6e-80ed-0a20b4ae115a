package com.traffic.analysis.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartException;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 四方向交通分析系统异常处理器
 * 统一处理四方向分析相关的异常
 */
@Slf4j
@RestControllerAdvice
public class FourWayAnalysisExceptionHandler {

    /**
     * 处理四方向分析业务异常
     */
    @ExceptionHandler(FourWayAnalysisException.class)
    public ResponseEntity<Map<String, Object>> handleFourWayAnalysisException(
            FourWayAnalysisException ex, HttpServletRequest request) {
        
        log.error("四方向分析业务异常: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "FOUR_WAY_ANALYSIS_ERROR",
            ex.getMessage(),
            request.getRequestURI(),
            ex.getTaskId()
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * 处理视频上传异常
     */
    @ExceptionHandler(VideoUploadException.class)
    public ResponseEntity<Map<String, Object>> handleVideoUploadException(
            VideoUploadException ex, HttpServletRequest request) {
        
        log.error("四方向视频上传异常: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "VIDEO_UPLOAD_ERROR",
            ex.getMessage(),
            request.getRequestURI(),
            ex.getDirection()
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * 处理视频处理异常
     */
    @ExceptionHandler(VideoProcessingException.class)
    public ResponseEntity<Map<String, Object>> handleVideoProcessingException(
            VideoProcessingException ex, HttpServletRequest request) {
        
        log.error("四方向视频处理异常: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "VIDEO_PROCESSING_ERROR",
            ex.getMessage(),
            request.getRequestURI(),
            Map.of(
                "taskId", ex.getTaskId(),
                "direction", ex.getDirection(),
                "processingStage", ex.getProcessingStage()
            )
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * 处理任务未找到异常
     */
    @ExceptionHandler(TaskNotFoundException.class)
    public ResponseEntity<Map<String, Object>> handleTaskNotFoundException(
            TaskNotFoundException ex, HttpServletRequest request) {
        
        log.warn("四方向分析任务未找到: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI());
        
        Map<String, Object> errorResponse = createErrorResponse(
            "TASK_NOT_FOUND",
            ex.getMessage(),
            request.getRequestURI(),
            ex.getTaskId()
        );
        
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<Map<String, Object>> handleMaxUploadSizeExceededException(
            MaxUploadSizeExceededException ex, HttpServletRequest request) {
        
        log.error("四方向视频文件大小超限: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI());
        
        String message = String.format("上传的视频文件总大小超过限制。最大允许大小: %d MB", 
                                     ex.getMaxUploadSize() / (1024 * 1024));
        
        Map<String, Object> errorResponse = createErrorResponse(
            "FILE_SIZE_EXCEEDED",
            message,
            request.getRequestURI(),
            Map.of("maxSize", ex.getMaxUploadSize())
        );
        
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(errorResponse);
    }

    /**
     * 处理文件上传异常
     */
    @ExceptionHandler(MultipartException.class)
    public ResponseEntity<Map<String, Object>> handleMultipartException(
            MultipartException ex, HttpServletRequest request) {
        
        log.error("四方向视频文件上传异常: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        String message = "视频文件上传失败，请检查文件格式和大小";
        if (ex.getCause() instanceof MaxUploadSizeExceededException) {
            message = "上传的视频文件大小超过限制，请压缩后重试";
        }
        
        Map<String, Object> errorResponse = createErrorResponse(
            "MULTIPART_UPLOAD_ERROR",
            message,
            request.getRequestURI(),
            null
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * 处理IO异常
     */
    @ExceptionHandler(IOException.class)
    public ResponseEntity<Map<String, Object>> handleIOException(
            IOException ex, HttpServletRequest request) {
        
        log.error("四方向分析IO异常: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "IO_ERROR",
            "文件操作失败，请重试",
            request.getRequestURI(),
            null
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * 处理数据库连接异常
     */
    @ExceptionHandler(DatabaseConnectionException.class)
    public ResponseEntity<Map<String, Object>> handleDatabaseConnectionException(
            DatabaseConnectionException ex, HttpServletRequest request) {
        
        log.error("四方向分析数据库连接异常: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "DATABASE_CONNECTION_ERROR",
            "数据库连接失败，请稍后重试",
            request.getRequestURI(),
            null
        );
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(errorResponse);
    }

    /**
     * 处理WebSocket连接异常
     */
    @ExceptionHandler(WebSocketConnectionException.class)
    public ResponseEntity<Map<String, Object>> handleWebSocketConnectionException(
            WebSocketConnectionException ex, HttpServletRequest request) {
        
        log.error("四方向分析WebSocket连接异常: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "WEBSOCKET_CONNECTION_ERROR",
            "实时连接失败，请刷新页面重试",
            request.getRequestURI(),
            Map.of("sessionId", ex.getSessionId())
        );
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(errorResponse);
    }

    /**
     * 处理模型服务异常
     */
    @ExceptionHandler(ModelServiceException.class)
    public ResponseEntity<Map<String, Object>> handleModelServiceException(
            ModelServiceException ex, HttpServletRequest request) {
        
        log.error("四方向分析模型服务异常: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "MODEL_SERVICE_ERROR",
            "AI模型服务暂时不可用，请稍后重试",
            request.getRequestURI(),
            Map.of("serviceUrl", ex.getServiceUrl())
        );
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(errorResponse);
    }

    /**
     * 处理通用运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Map<String, Object>> handleRuntimeException(
            RuntimeException ex, HttpServletRequest request) {
        
        log.error("四方向分析运行时异常: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "RUNTIME_ERROR",
            "系统内部错误，请联系管理员",
            request.getRequestURI(),
            null
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(
            Exception ex, HttpServletRequest request) {
        
        log.error("四方向分析未知异常: {}, 请求路径: {}", ex.getMessage(), request.getRequestURI(), ex);
        
        Map<String, Object> errorResponse = createErrorResponse(
            "UNKNOWN_ERROR",
            "系统发生未知错误，请联系技术支持",
            request.getRequestURI(),
            null
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * 创建统一的错误响应格式
     */
    private Map<String, Object> createErrorResponse(String errorCode, String message, 
                                                   String path, Object details) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("errorCode", errorCode);
        errorResponse.put("message", message);
        errorResponse.put("timestamp", LocalDateTime.now());
        errorResponse.put("path", path);
        
        if (details != null) {
            errorResponse.put("details", details);
        }
        
        return errorResponse;
    }
}
