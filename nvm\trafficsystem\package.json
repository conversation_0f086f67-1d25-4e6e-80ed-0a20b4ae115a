{"name": "trafficsystem", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@stomp/stompjs": "^7.1.1", "axios": "^1.9.0", "bootstrap": "^5.3.6", "bootstrap-icons": "^1.12.1", "chart.js": "^3.9.1", "core-js": "^3.8.3", "echarts": "^5.4.3", "element-plus": "^2.9.9", "file-saver": "^2.0.5", "html2pdf.js": "^0.10.3", "sockjs-client": "^1.6.1", "vue": "^3.2.13", "vue-chartjs": "^4.1.2", "vue-loading-overlay": "^6.0.6", "vue-router": "^4.5.1", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "path-browserify": "^1.0.1", "sass": "^1.87.0", "sass-loader": "^16.0.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential"], "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}