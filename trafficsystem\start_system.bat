@echo off
chcp 65001
echo ========================================
echo 启动交通分析系统
echo ========================================

REM 设置颜色
color 0A

echo.
echo [1/4] 检查环境...

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java环境未找到，请确保已安装Java 8或更高版本
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python环境未找到，请确保已安装Python 3.7或更高版本
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

REM 检查MongoDB连接（可选）
echo [2/4] 检查MongoDB连接...
ping localhost -n 1 >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  无法连接到localhost，请确保MongoDB服务正在运行
) else (
    echo ✅ 网络连接正常
)

echo.
echo [3/4] 检查Python依赖...
cd python_model
if exist requirements.txt (
    echo 检查Python依赖包...
    pip install -r requirements.txt >nul 2>&1
    if %errorlevel% neq 0 (
        echo ⚠️  安装Python依赖包时出现问题，但将继续启动
    ) else (
        echo ✅ Python依赖包检查完成
    )
) else (
    echo ⚠️  未找到requirements.txt文件
)
cd ..

echo.
echo [4/4] 启动Spring Boot应用...
echo.
echo 🚀 正在启动交通分析系统...
echo    - Spring Boot应用将在端口8080启动
echo    - Python模型服务将自动在端口5001启动
echo    - Python API控制器将自动在端口5000启动
echo.
echo 📝 启动日志将显示在下方
echo 📊 系统启动后可访问: http://localhost:8080
echo 🔧 服务状态监控: http://localhost:8080/api/service/status
echo.
echo ========================================

REM 启动Spring Boot应用（Python服务将自动启动）
mvn spring-boot:run

echo.
echo ========================================
echo 系统已停止
echo ========================================
pause
