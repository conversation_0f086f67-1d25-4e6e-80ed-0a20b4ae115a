<template>
  <div class="four-way-history-container">
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <h2>四方向智能交通分析历史</h2>
          <div class="header-actions">
            <div class="selection-controls" v-if="fourWayTasks.length > 0">
              <el-checkbox 
                v-model="selectAll" 
                @change="toggleSelectAll"
                :indeterminate="isIndeterminate"
                class="wider-checkbox"
              >全选</el-checkbox>
            </div>
            <el-button 
              class="custom-btn-outline" 
              :disabled="!hasSelected" 
              @click="handleBatchDelete"
            >
              <el-icon><delete /></el-icon> 批量删除
            </el-button>
            <!-- 四方向上传页面已删除，暂时隐藏此按钮 -->
            <!-- <el-button class="custom-btn-primary" disabled>
              <el-icon><plus /></el-icon> 新建四方向分析
            </el-button> -->
          </div>
        </div>
      </template>
      
      <!-- 筛选面板 -->
      <div class="filter-panel">
        <div class="filter-row">
          <div class="filter-item">
            <span class="filter-label">搜索：</span>
            <el-input
              v-model="searchQuery"
              placeholder="搜索任务名称"
              clearable
              @clear="handleSearch"
              @keyup.enter="handleSearch"
              size="small"
              class="search-input"
            >
              <template #prefix>
                <el-icon class="el-input__icon"><search /></el-icon>
              </template>
            </el-input>
          </div>
          
          <!-- 管理员用户筛选功能 -->
          <div v-if="isAdmin" class="filter-item">
            <span class="filter-label">用户：</span>
            <el-select
              v-model="userFilter"
              placeholder="用户筛选"
              clearable
              @change="handleSearch"
              size="small"
              :popper-append-to-body="true"
              :reserve-keyword="false"
            >
              <el-option label="全部用户" value=""></el-option>
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="user.username"
                :value="user.id"
              ></el-option>
            </el-select>
          </div>
          
          <div class="filter-item">
            <span class="filter-label">状态：</span>
            <el-select
              v-model="statusFilter"
              placeholder="状态筛选"
              clearable
              @change="handleSearch"
              size="small"
            >
              <el-option label="全部状态" value=""></el-option>
              <el-option label="已完成" value="completed"></el-option>
              <el-option label="处理中" value="processing"></el-option>
              <el-option label="排队中" value="queued"></el-option>
              <el-option label="失败" value="failed"></el-option>
            </el-select>
          </div>
          
          <div class="filter-item">
            <span class="filter-label">日期：</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :shortcuts="dateShortcuts"
              :editable="false"
              size="small"
              class="date-picker"
            >
            </el-date-picker>
          </div>
          
          <div class="filter-actions">
            <el-button type="primary" size="small" @click="handleSearch" class="filter-btn">应用</el-button>
            <el-button size="small" @click="resetFilters" class="reset-btn">重置</el-button>
            <el-button size="small" @click="fetchFourWayTasks" class="refresh-btn">
              <el-icon><refresh /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      
      <div v-else-if="error" class="error-container">
        <el-alert
          title="获取历史记录失败"
          type="error"
          :description="error"
          show-icon
        />
        <div class="error-actions">
          <el-button type="primary" @click="fetchFourWayTasks">重试</el-button>
        </div>
      </div>
      
      <div v-else-if="fourWayTasks.length === 0" class="empty-container">
        <el-empty description="暂无四方向分析记录">
          <!-- 四方向上传页面已删除，暂时隐藏此按钮 -->
          <!-- <el-button class="custom-btn-primary" disabled>开始四方向分析</el-button> -->
        </el-empty>
      </div>
      
      <div v-else>
        <el-table
          :data="fourWayTasks"
          style="width: 100%"
          border
          stripe
          :default-sort="{ prop: sortProp, order: sortOrder }"
          @sort-change="handleSortChange"
          @selection-change="handleSelectionChange"
          v-loading="tableLoading"
          max-height="calc(100vh - 350px)"
          class="custom-table"
        >
          <el-table-column type="selection" width="55">
            <template #header>
              <el-checkbox 
                v-model="selectAll" 
                @change="toggleSelectAll"
                :indeterminate="isIndeterminate"
              />
            </template>
          </el-table-column>
          
          <el-table-column prop="task_id" label="任务ID" min-width="200" sortable>
            <template #default="scope">
              <div class="task-id-container">
                <span class="task-id">{{ scope.row.task_id || scope.row.taskId }}</span>
                <el-button 
                  type="primary" 
                  size="small" 
                  circle 
                  @click="copyTaskId(scope.row.task_id || scope.row.taskId)"
                  class="copy-button"
                  title="复制任务ID"
                >
                  <el-icon><DocumentCopy /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="创建时间" width="180" sortable>
            <template #default="scope">
              {{ formatDate(scope.row.created_at || scope.row.createdAt) }}
            </template>
          </el-table-column>
          
          <!-- 添加分析人列 -->
          <el-table-column prop="username" label="分析人" width="120">
            <template #default="scope">
              {{ scope.row.username || '未知用户' }}
            </template>
          </el-table-column>
          
          <el-table-column prop="status" label="状态" width="120" sortable>
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="progress" label="进度" width="150">
            <template #default="scope">
              <el-progress 
                :percentage="scope.row.progress || 0" 
                :status="getProgressStatus(scope.row.status)"
                :stroke-width="10"
              />
            </template>
          </el-table-column>
          
          <el-table-column label="车辆统计" width="200">
            <template #default="scope">
              <div v-if="scope.row.status === 'completed' && scope.row.directions" class="vehicle-stats">
                <div class="total-vehicles">
                  总计: {{ getTotalVehicles(scope.row) }} 辆
                </div>
                <div class="direction-breakdown">
                  <span v-for="(direction, key) in scope.row.directions" :key="key" class="direction-stat">
                    {{ getDirectionName(key) }}: {{ direction.vehicleCount || 0 }}
                  </span>
                </div>
              </div>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="scope">
              <div class="table-actions">
                <el-button 
                  size="small" 
                  class="custom-btn-primary" 
                  v-if="scope.row.status === 'completed'"
                  @click="viewResult(scope.row)"
                >
                  查看结果
                </el-button>
                
                <el-button 
                  size="small" 
                  class="action-btn-success" 
                  v-if="scope.row.status === 'completed'"
                  @click="viewReport(scope.row)"
                >
                  智能报告
                </el-button>
                
                <el-button 
                  size="small" 
                  class="action-btn"
                  v-if="['queued', 'processing'].includes(scope.row.status)"
                  @click="checkStatus(scope.row)"
                >
                  查看进度
                </el-button>
                
                <el-button 
                  size="small" 
                  class="action-btn-warning" 
                  v-if="scope.row.status === 'failed'"
                  @click="handleRetryAnalysis(scope.row)"
                >
                  重试
                </el-button>
                
                <el-button 
                  size="small" 
                  class="custom-btn-outline"
                  @click="showDeleteConfirm(scope.row)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <div class="batch-actions">
            <el-checkbox v-if="fourWayTasks.length > 0" v-model="selectAll" @change="toggleSelectAll" class="me-2">全选</el-checkbox>
            <span v-if="selectedRows.length > 0" class="selected-info">
              已选择 {{ selectedRows.length }} 项
            </span>
            <el-button 
              v-if="selectedRows.length > 0" 
              class="custom-btn-outline"
              size="small" 
              @click="handleBatchDelete"
            >
              批量删除
            </el-button>
          </div>
          <div class="pagination-wrapper">
            <div class="pagination-info">共 {{ total }} 个记录</div>
            <el-pagination
              background
              layout="prev, pager, next, sizes"
              :total="total"
              :page-size="pageSize"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              prev-text="上一页"
              next-text="下一页"
            />
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 批量删除确认对话框 -->
    <el-dialog
      title="批量删除"
      v-model="batchDeleteDialogVisible"
      width="400px"
      class="dark-theme-dialog"
      :close-on-click-modal="false"
    >
      <span>确定要删除选中的{{ selectedRows.length }}条记录吗？此操作不可恢复！</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="confirm-delete-btn" @click="confirmBatchDelete" :loading="batchOperationLoading">
            确认删除
          </el-button>
          <el-button class="cancel-btn" @click="batchDeleteDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 单条记录删除确认对话框 -->
    <el-dialog
      title="确认删除"
      v-model="deleteDialogVisible"
      width="400px"
      class="dark-theme-dialog"
      :close-on-click-modal="false"
    >
      <span>确定要删除这条分析记录吗？此操作无法恢复。</span>
      <template #footer>
        <div class="dialog-footer">
          <el-button class="confirm-delete-btn" @click="confirmDelete">
            确定删除
          </el-button>
          <el-button class="cancel-btn" @click="deleteDialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Search, Delete, Plus, Refresh, DocumentCopy } from '@element-plus/icons-vue';
import { useStore } from 'vuex';

export default {
  name: 'FourWayHistory',
  components: {
    Search,
    Delete,
    Plus,
    Refresh,
    DocumentCopy
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    const fourWayTasks = ref([]);
    const loading = ref(true);
    const tableLoading = ref(false);
    const error = ref('');
    
    // 分页参数
    const total = ref(0);
    const pageSize = ref(10);
    const currentPage = ref(1);
    
    // 排序参数
    const sortProp = ref('created_at');
    const sortOrder = ref('descending');
    
    // 筛选参数
    const searchQuery = ref('');
    const userFilter = ref('');
    const statusFilter = ref('');
    const userList = ref([]);
    const dateRange = ref([]);
    
    // 日期快捷选项
    const dateShortcuts = [
      {
        text: '最近一周',
        value: () => {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          return [formatToYYYYMMDD(start), formatToYYYYMMDD(end)];
        },
      },
      {
        text: '最近一个月',
        value: () => {
          const end = new Date();
          const start = new Date();
          start.setMonth(start.getMonth() - 1);
          return [formatToYYYYMMDD(start), formatToYYYYMMDD(end)];
        },
      },
      {
        text: '最近三个月',
        value: () => {
          const end = new Date();
          const start = new Date();
          start.setMonth(start.getMonth() - 3);
          return [formatToYYYYMMDD(start), formatToYYYYMMDD(end)];
        },
      }
    ];

    // 格式化日期为YYYY-MM-DD
    const formatToYYYYMMDD = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };
    
    // 选择相关
    const selectedRows = ref([]);
    const hasSelected = computed(() => selectedRows.value.length > 0);
    const selectAll = ref(false);
    const isIndeterminate = ref(false);
    
    // 批量操作相关
    const batchDeleteDialogVisible = ref(false);
    const batchOperationLoading = ref(false);
    
    // 单条删除相关
    const deleteDialogVisible = ref(false);
    const currentDeleteTask = ref(null);
    
    // 全选/取消全选
    const toggleSelectAll = (val) => {
      if (val) {
        selectedRows.value = [...fourWayTasks.value];
      } else {
        selectedRows.value = [];
      }
      isIndeterminate.value = false;
    };
    
    // 用户权限相关计算属性
    const isAdmin = computed(() => {
      try {
        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
        const role = storedUser?.role?.toLowerCase() || store.state.user?.role?.toLowerCase();
        return role === 'admin' || role === 'administrator';
      } catch (e) {
        console.error('解析用户角色出错:', e);
        const role = store.state.user?.role?.toLowerCase();
        return role === 'admin' || role === 'administrator';
      }
    });
    
    // 监听筛选条件变化
    watch([searchQuery, userFilter, statusFilter, dateRange], () => {
      currentPage.value = 1;
      fetchFourWayTasks();
    }, { deep: true });

    return {
      fourWayTasks,
      loading,
      tableLoading,
      error,
      total,
      pageSize,
      currentPage,
      sortProp,
      sortOrder,
      searchQuery,
      userFilter,
      statusFilter,
      userList,
      dateRange,
      dateShortcuts,
      selectedRows,
      hasSelected,
      selectAll,
      isIndeterminate,
      batchDeleteDialogVisible,
      batchOperationLoading,
      deleteDialogVisible,
      currentDeleteTask,
      isAdmin,
      
      // 方法
      toggleSelectAll,
      fetchFourWayTasks,
      handleSearch,
      resetFilters,
      handleSortChange,
      handleSelectionChange,
      handleSizeChange,
      handleCurrentChange,
      handleBatchDelete,
      confirmBatchDelete,
      showDeleteConfirm,
      confirmDelete,
      viewResult,
      viewReport,
      checkStatus,
      handleRetryAnalysis,
      copyTaskId,
      formatDate,
      getStatusType,
      getStatusText,
      getProgressStatus,
      getTotalVehicles,
      getDirectionName
    };

    // 获取四方向任务列表
    async function fetchFourWayTasks() {
      loading.value = true;
      tableLoading.value = true;
      error.value = '';

      try {
        const token = localStorage.getItem('auth_token');
        if (!token) {
          throw new Error('未登录或认证令牌已过期');
        }

        // 构建查询参数
        const params = new URLSearchParams({
          page: currentPage.value - 1,
          size: pageSize.value,
          sort: `${sortProp.value},${sortOrder.value === 'ascending' ? 'asc' : 'desc'}`
        });

        if (searchQuery.value) {
          params.append('search', searchQuery.value);
        }
        if (userFilter.value) {
          params.append('userId', userFilter.value);
        }
        if (statusFilter.value) {
          params.append('status', statusFilter.value);
        }
        if (dateRange.value && dateRange.value.length === 2) {
          params.append('startDate', dateRange.value[0]);
          params.append('endDate', dateRange.value[1]);
        }

        const response = await fetch(`/api/video-analysis/four-way/tasks?${params}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        fourWayTasks.value = data.content || [];
        total.value = data.totalElements || 0;

        // 更新选择状态
        updateSelectionState();

      } catch (err) {
        console.error('获取四方向任务列表失败:', err);
        error.value = err.message || '获取数据失败';
        ElMessage.error('获取四方向分析历史失败: ' + error.value);
      } finally {
        loading.value = false;
        tableLoading.value = false;
      }
    }

    // 搜索处理
    function handleSearch() {
      currentPage.value = 1;
      fetchFourWayTasks();
    }

    // 重置筛选条件
    function resetFilters() {
      searchQuery.value = '';
      userFilter.value = '';
      statusFilter.value = '';
      dateRange.value = [];
      currentPage.value = 1;
      fetchFourWayTasks();
    }

    // 排序变化处理
    function handleSortChange({ prop, order }) {
      sortProp.value = prop || 'created_at';
      sortOrder.value = order || 'descending';
      fetchFourWayTasks();
    }

    // 选择变化处理
    function handleSelectionChange(selection) {
      selectedRows.value = selection;
      updateSelectionState();
    }

    // 更新选择状态
    function updateSelectionState() {
      const selectedCount = selectedRows.value.length;
      const totalCount = fourWayTasks.value.length;

      if (selectedCount === 0) {
        selectAll.value = false;
        isIndeterminate.value = false;
      } else if (selectedCount === totalCount) {
        selectAll.value = true;
        isIndeterminate.value = false;
      } else {
        selectAll.value = false;
        isIndeterminate.value = true;
      }
    }

    // 分页大小变化
    function handleSizeChange(newSize) {
      pageSize.value = newSize;
      currentPage.value = 1;
      fetchFourWayTasks();
    }

    // 当前页变化
    function handleCurrentChange(newPage) {
      currentPage.value = newPage;
      fetchFourWayTasks();
    }

    // 批量删除处理
    function handleBatchDelete() {
      if (selectedRows.value.length === 0) {
        ElMessage.warning('请先选择要删除的记录');
        return;
      }
      batchDeleteDialogVisible.value = true;
    }

    // 确认批量删除
    async function confirmBatchDelete() {
      batchOperationLoading.value = true;
      try {
        const taskIds = selectedRows.value.map(row => row.task_id || row.taskId);

        const token = localStorage.getItem('auth_token');
        const response = await fetch('/api/video-analysis/four-way/batch-delete', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ taskIds })
        });

        if (!response.ok) {
          throw new Error('批量删除失败');
        }

        ElMessage.success(`成功删除 ${taskIds.length} 条记录`);
        batchDeleteDialogVisible.value = false;
        selectedRows.value = [];
        fetchFourWayTasks();

      } catch (err) {
        console.error('批量删除失败:', err);
        ElMessage.error('批量删除失败: ' + err.message);
      } finally {
        batchOperationLoading.value = false;
      }
    }

    // 显示删除确认
    function showDeleteConfirm(task) {
      currentDeleteTask.value = task;
      deleteDialogVisible.value = true;
    }

    // 确认删除单条记录
    async function confirmDelete() {
      if (!currentDeleteTask.value) return;

      try {
        const taskId = currentDeleteTask.value.task_id || currentDeleteTask.value.taskId;
        const token = localStorage.getItem('auth_token');

        const response = await fetch(`/api/video-analysis/four-way/${taskId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error('删除失败');
        }

        ElMessage.success('删除成功');
        deleteDialogVisible.value = false;
        currentDeleteTask.value = null;
        fetchFourWayTasks();

      } catch (err) {
        console.error('删除失败:', err);
        ElMessage.error('删除失败: ' + err.message);
      }
    }

    // 查看结果
    function viewResult(task) {
      const taskId = task.task_id || task.taskId;
      router.push(`/four-way-result/${taskId}`);
    }

    // 查看智能报告
    function viewReport(task) {
      const taskId = task.task_id || task.taskId;
      router.push(`/four-way-report/${taskId}`);
    }

    // 查看状态
    function checkStatus(task) {
      const taskId = task.task_id || task.taskId;
      router.push(`/four-way-console?taskId=${taskId}`);
    }

    // 重试分析
    async function handleRetryAnalysis(task) {
      try {
        const taskId = task.task_id || task.taskId;
        const token = localStorage.getItem('auth_token');

        const response = await fetch(`/api/video-analysis/four-way/${taskId}/retry`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error('重试失败');
        }

        ElMessage.success('已重新开始分析');
        fetchFourWayTasks();

      } catch (err) {
        console.error('重试失败:', err);
        ElMessage.error('重试失败: ' + err.message);
      }
    }

    // 复制任务ID
    function copyTaskId(taskId) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(taskId).then(() => {
          ElMessage.success('任务ID已复制到剪贴板');
        }).catch(() => {
          ElMessage.error('复制失败');
        });
      } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = taskId;
        document.body.appendChild(textArea);
        textArea.select();
        try {
          document.execCommand('copy');
          ElMessage.success('任务ID已复制到剪贴板');
        } catch (err) {
          ElMessage.error('复制失败');
        }
        document.body.removeChild(textArea);
      }
    }

    // 格式化日期
    function formatDate(dateString) {
      if (!dateString) return '-';
      try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });
      } catch (e) {
        return dateString;
      }
    }

    // 获取状态类型
    function getStatusType(status) {
      const statusMap = {
        'completed': 'success',
        'processing': 'warning',
        'queued': 'info',
        'failed': 'danger'
      };
      return statusMap[status] || 'info';
    }

    // 获取状态文本
    function getStatusText(status) {
      const statusMap = {
        'completed': '已完成',
        'processing': '处理中',
        'queued': '排队中',
        'failed': '失败'
      };
      return statusMap[status] || '未知';
    }

    // 获取进度状态
    function getProgressStatus(status) {
      if (status === 'completed') return 'success';
      if (status === 'failed') return 'exception';
      return undefined;
    }

    // 获取总车辆数
    function getTotalVehicles(task) {
      if (!task.directions) return 0;
      return Object.values(task.directions).reduce((total, direction) => {
        return total + (direction.vehicleCount || 0);
      }, 0);
    }

    // 获取方向名称
    function getDirectionName(direction) {
      const directionMap = {
        'EAST': '东',
        'SOUTH': '南',
        'WEST': '西',
        'NORTH': '北'
      };
      return directionMap[direction] || direction;
    }

    // 组件挂载时获取数据
    onMounted(() => {
      fetchFourWayTasks();
    });
  }
};
</script>

<style scoped>
.four-way-history-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.history-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.card-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selection-controls {
  display: flex;
  align-items: center;
}

.wider-checkbox {
  margin-right: 16px;
}

.custom-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.custom-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.custom-btn-outline {
  border: 2px solid #e74c3c;
  color: #e74c3c;
  background: transparent;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.custom-btn-outline:hover {
  background: #e74c3c;
  color: white;
  transform: translateY(-2px);
}

.filter-panel {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-weight: 500;
  color: #555;
  white-space: nowrap;
}

.search-input {
  width: 200px;
}

.date-picker {
  width: 240px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.filter-btn {
  background: #3498db;
  border: none;
  color: white;
}

.reset-btn {
  background: #95a5a6;
  border: none;
  color: white;
}

.refresh-btn {
  background: #2ecc71;
  border: none;
  color: white;
}

.loading-container, .error-container, .empty-container {
  padding: 40px;
  text-align: center;
}

.error-actions {
  margin-top: 16px;
}

.custom-table {
  border-radius: 8px;
  overflow: hidden;
}

.task-id-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
}

.copy-button {
  width: 24px;
  height: 24px;
  padding: 0;
  min-height: auto;
}

.vehicle-stats {
  font-size: 12px;
}

.total-vehicles {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.direction-breakdown {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.direction-stat {
  background: #ecf0f1;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  color: #555;
}

.table-actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.action-btn {
  background: #3498db;
  border: none;
  color: white;
  border-radius: 4px;
}

.action-btn-success {
  background: #2ecc71;
  border: none;
  color: white;
  border-radius: 4px;
}

.action-btn-warning {
  background: #f39c12;
  border: none;
  color: white;
  border-radius: 4px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 16px 0;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-info {
  color: #666;
  font-size: 14px;
}

.pagination-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.dark-theme-dialog {
  border-radius: 12px;
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.confirm-delete-btn {
  background: #e74c3c;
  border: none;
  color: white;
  border-radius: 6px;
}

.cancel-btn {
  background: #95a5a6;
  border: none;
  color: white;
  border-radius: 6px;
}

.text-muted {
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .four-way-history-container {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-input, .date-picker {
    width: 100%;
  }

  .pagination-container {
    flex-direction: column;
    gap: 16px;
  }

  .table-actions {
    flex-direction: column;
  }
}
</style>
