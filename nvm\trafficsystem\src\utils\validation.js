/**
 * 前端数据验证工具
 */

// 支持的视频格式
const SUPPORTED_VIDEO_FORMATS = [
  'video/mp4',
  'video/avi', 
  'video/mov',
  'video/wmv',
  'video/flv',
  'video/webm',
  'video/mkv'
];

// 支持的视频扩展名
const SUPPORTED_VIDEO_EXTENSIONS = [
  'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'
];

// 文件大小限制
const MAX_VIDEO_SIZE = 500 * 1024 * 1024; // 500MB
const MIN_VIDEO_SIZE = 1024; // 1KB
const MAX_TOTAL_SIZE = 2 * 1024 * 1024 * 1024; // 2GB

/**
 * 验证视频文件
 * @param {File} file - 视频文件
 * @returns {Object} 验证结果
 */
export function validateVideoFile(file) {
  const result = {
    valid: true,
    errors: []
  };

  // 检查文件是否存在
  if (!file) {
    result.valid = false;
    result.errors.push('请选择视频文件');
    return result;
  }

  // 检查文件大小
  if (file.size < MIN_VIDEO_SIZE) {
    result.valid = false;
    result.errors.push('视频文件太小，最小大小为1KB');
  }

  if (file.size > MAX_VIDEO_SIZE) {
    result.valid = false;
    result.errors.push('视频文件太大，最大大小为500MB');
  }

  // 检查文件类型
  if (!SUPPORTED_VIDEO_FORMATS.includes(file.type)) {
    result.valid = false;
    result.errors.push(`不支持的视频格式，支持的格式: ${SUPPORTED_VIDEO_FORMATS.join(', ')}`);
  }

  // 检查文件扩展名
  const extension = getFileExtension(file.name);
  if (!extension || !SUPPORTED_VIDEO_EXTENSIONS.includes(extension.toLowerCase())) {
    result.valid = false;
    result.errors.push(`不支持的文件扩展名，支持的扩展名: ${SUPPORTED_VIDEO_EXTENSIONS.join(', ')}`);
  }

  // 检查文件名
  const fileNameValidation = validateFileName(file.name);
  if (!fileNameValidation.valid) {
    result.valid = false;
    result.errors.push(...fileNameValidation.errors);
  }

  return result;
}

/**
 * 验证四方向视频文件
 * @param {Object} videos - 四方向视频文件对象
 * @returns {Object} 验证结果
 */
export function validateFourWayVideos(videos) {
  const result = {
    valid: true,
    errors: [],
    warnings: []
  };

  const directions = ['east', 'south', 'west', 'north'];
  const directionNames = {
    east: '东方向',
    south: '南方向', 
    west: '西方向',
    north: '北方向'
  };

  let totalSize = 0;
  let validVideoCount = 0;

  // 验证每个方向的视频
  for (const direction of directions) {
    const video = videos[direction];
    
    if (!video) {
      result.valid = false;
      result.errors.push(`${directionNames[direction]}视频文件不能为空`);
      continue;
    }

    const videoValidation = validateVideoFile(video);
    if (!videoValidation.valid) {
      result.valid = false;
      videoValidation.errors.forEach(error => {
        result.errors.push(`${directionNames[direction]}: ${error}`);
      });
    } else {
      validVideoCount++;
      totalSize += video.size;
    }
  }

  // 检查总文件大小
  if (totalSize > MAX_TOTAL_SIZE) {
    result.valid = false;
    result.errors.push(`视频文件总大小超过限制，最大总大小为2GB，当前大小为${formatFileSize(totalSize)}`);
  }

  // 添加警告信息
  if (totalSize > MAX_TOTAL_SIZE * 0.8) {
    result.warnings.push('视频文件总大小较大，上传可能需要较长时间');
  }

  if (validVideoCount > 0 && validVideoCount < 4) {
    result.warnings.push(`只有${validVideoCount}个有效视频文件，建议上传所有4个方向的视频以获得最佳分析效果`);
  }

  return result;
}

/**
 * 验证四方向分析配置
 * @param {Object} config - 分析配置
 * @returns {Object} 验证结果
 */
export function validateFourWayConfig(config) {
  const result = {
    valid: true,
    errors: []
  };

  // 验证分析类型
  if (!config.analysisType) {
    result.valid = false;
    result.errors.push('请选择分析类型');
  } else if (!['basic', 'advanced', 'intelligent'].includes(config.analysisType)) {
    result.valid = false;
    result.errors.push('分析类型必须是basic、advanced或intelligent');
  }

  // 验证敏感度
  if (config.sensitivityLevel !== undefined) {
    const sensitivity = parseFloat(config.sensitivityLevel);
    if (isNaN(sensitivity) || sensitivity < 0.1 || sensitivity > 1.0) {
      result.valid = false;
      result.errors.push('检测敏感度必须在0.1到1.0之间');
    }
  }

  // 验证分析时长
  if (config.analysisHours !== undefined) {
    const hours = parseInt(config.analysisHours);
    if (isNaN(hours) || hours < 1 || hours > 24) {
      result.valid = false;
      result.errors.push('分析时长必须在1到24小时之间');
    }
  }

  // 验证描述长度
  if (config.description && config.description.length > 500) {
    result.valid = false;
    result.errors.push('分析描述不能超过500个字符');
  }

  // 验证交叉口名称长度
  if (config.intersectionName && config.intersectionName.length > 100) {
    result.valid = false;
    result.errors.push('交叉口名称不能超过100个字符');
  }

  return result;
}

/**
 * 验证用户输入
 * @param {Object} userData - 用户数据
 * @returns {Object} 验证结果
 */
export function validateUserData(userData) {
  const result = {
    valid: true,
    errors: []
  };

  // 验证用户名
  if (!userData.username) {
    result.valid = false;
    result.errors.push('用户名不能为空');
  } else if (userData.username.length < 3 || userData.username.length > 50) {
    result.valid = false;
    result.errors.push('用户名长度必须在3到50个字符之间');
  } else if (!/^[a-zA-Z0-9_]+$/.test(userData.username)) {
    result.valid = false;
    result.errors.push('用户名只能包含字母、数字和下划线');
  }

  // 验证邮箱
  if (userData.email) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(userData.email)) {
      result.valid = false;
      result.errors.push('邮箱格式不正确');
    }
  }

  // 验证密码
  if (userData.password) {
    if (userData.password.length < 6) {
      result.valid = false;
      result.errors.push('密码长度不能少于6个字符');
    }
    if (userData.password.length > 100) {
      result.valid = false;
      result.errors.push('密码长度不能超过100个字符');
    }
  }

  return result;
}

/**
 * 验证文件名
 * @param {string} filename - 文件名
 * @returns {Object} 验证结果
 */
function validateFileName(filename) {
  const result = {
    valid: true,
    errors: []
  };

  if (!filename) {
    result.valid = false;
    result.errors.push('文件名不能为空');
    return result;
  }

  // 检查文件名长度
  if (filename.length > 255) {
    result.valid = false;
    result.errors.push('文件名太长，最大长度为255个字符');
  }

  // 检查非法字符
  const illegalChars = /[<>:"/\\|?*]/;
  if (illegalChars.test(filename)) {
    result.valid = false;
    result.errors.push('文件名包含非法字符');
  }

  // 检查控制字符
  if (/[\x00-\x1f\x80-\x9f]/.test(filename)) {
    result.valid = false;
    result.errors.push('文件名包含控制字符');
  }

  return result;
}

/**
 * 获取文件扩展名
 * @param {string} filename - 文件名
 * @returns {string|null} 扩展名
 */
function getFileExtension(filename) {
  if (!filename) return null;
  
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === filename.length - 1) {
    return null;
  }
  
  return filename.substring(lastDotIndex + 1);
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 验证表单数据
 * @param {Object} formData - 表单数据
 * @param {Object} rules - 验证规则
 * @returns {Object} 验证结果
 */
export function validateForm(formData, rules) {
  const result = {
    valid: true,
    errors: {},
    messages: []
  };

  for (const [field, rule] of Object.entries(rules)) {
    const value = formData[field];
    const fieldErrors = [];

    // 必填验证
    if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      fieldErrors.push(rule.requiredMessage || `${field}不能为空`);
    }

    // 长度验证
    if (value && rule.minLength && value.length < rule.minLength) {
      fieldErrors.push(rule.minLengthMessage || `${field}长度不能少于${rule.minLength}个字符`);
    }

    if (value && rule.maxLength && value.length > rule.maxLength) {
      fieldErrors.push(rule.maxLengthMessage || `${field}长度不能超过${rule.maxLength}个字符`);
    }

    // 正则验证
    if (value && rule.pattern && !rule.pattern.test(value)) {
      fieldErrors.push(rule.patternMessage || `${field}格式不正确`);
    }

    // 自定义验证
    if (value && rule.validator && typeof rule.validator === 'function') {
      const customResult = rule.validator(value);
      if (!customResult.valid) {
        fieldErrors.push(...customResult.errors);
      }
    }

    if (fieldErrors.length > 0) {
      result.valid = false;
      result.errors[field] = fieldErrors;
      result.messages.push(...fieldErrors);
    }
  }

  return result;
}

// 导出常量
export {
  SUPPORTED_VIDEO_FORMATS,
  SUPPORTED_VIDEO_EXTENSIONS,
  MAX_VIDEO_SIZE,
  MIN_VIDEO_SIZE,
  MAX_TOTAL_SIZE
};
