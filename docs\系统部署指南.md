# 交通分析系统部署指南

## 概述

本文档详细介绍了交通分析系统的完整部署流程，包括环境准备、依赖安装、配置设置和系统启动等步骤。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │    │  后端 (Spring)   │    │  AI服务 (Python) │
│   Port: 3000    │◄──►│   Port: 8080    │◄──►│   Port: 5000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx代理     │    │   MongoDB       │    │   文件存储       │
│   Port: 80      │    │   Port: 27017   │    │   GridFS        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 环境要求

### 硬件要求
- **CPU**: 8核心以上，推荐16核心
- **内存**: 16GB以上，推荐32GB
- **存储**: 500GB以上SSD硬盘
- **GPU**: NVIDIA GTX 1060以上（可选，用于AI加速）

### 软件要求
- **操作系统**: Ubuntu 20.04 LTS / CentOS 8 / Windows 10/11
- **Java**: OpenJDK 11或以上
- **Node.js**: 16.x或以上
- **Python**: 3.8或以上
- **MongoDB**: 5.0或以上
- **Nginx**: 1.18或以上（可选）

## 安装步骤

### 1. 环境准备

#### Ubuntu/CentOS 系统
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y  # Ubuntu
sudo yum update -y                      # CentOS

# 安装基础工具
sudo apt install -y curl wget git unzip  # Ubuntu
sudo yum install -y curl wget git unzip  # CentOS
```

#### Windows 系统
1. 安装 Git for Windows
2. 安装 Windows Subsystem for Linux (WSL2) - 推荐
3. 或使用 PowerShell 进行操作

### 2. Java 环境安装

#### Ubuntu/CentOS
```bash
# 安装 OpenJDK 11
sudo apt install -y openjdk-11-jdk  # Ubuntu
sudo yum install -y java-11-openjdk-devel  # CentOS

# 验证安装
java -version
javac -version
```

#### Windows
1. 下载 OpenJDK 11: https://adoptopenjdk.net/
2. 安装并配置环境变量 JAVA_HOME
3. 验证: `java -version`

### 3. Node.js 环境安装

#### 使用 NodeSource 仓库 (推荐)
```bash
# 添加 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs  # Ubuntu

# 或使用 yum (CentOS)
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node --version
npm --version
```

#### Windows
1. 下载 Node.js: https://nodejs.org/
2. 运行安装程序
3. 验证: `node --version`

### 4. Python 环境安装

#### Ubuntu/CentOS
```bash
# 安装 Python 3.8+
sudo apt install -y python3 python3-pip python3-venv  # Ubuntu
sudo yum install -y python3 python3-pip  # CentOS

# 创建虚拟环境
python3 -m venv traffic_analysis_env
source traffic_analysis_env/bin/activate

# 升级 pip
pip install --upgrade pip
```

#### Windows
1. 下载 Python: https://www.python.org/downloads/
2. 安装时勾选 "Add Python to PATH"
3. 创建虚拟环境:
```cmd
python -m venv traffic_analysis_env
traffic_analysis_env\Scripts\activate
```

### 5. MongoDB 安装

#### Ubuntu
```bash
# 导入 MongoDB 公钥
wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | sudo apt-key add -

# 添加 MongoDB 仓库
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list

# 安装 MongoDB
sudo apt-get update
sudo apt-get install -y mongodb-org

# 启动 MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### Windows
1. 下载 MongoDB Community Server: https://www.mongodb.com/try/download/community
2. 运行安装程序
3. 配置为 Windows 服务
4. 启动 MongoDB 服务

### 6. 项目部署

#### 克隆项目
```bash
git clone https://github.com/your-repo/traffic-analysis-system.git
cd traffic-analysis-system
```

#### 后端部署
```bash
cd trafficsystem

# 配置数据库连接
cp src/main/resources/application-example.yml src/main/resources/application.yml
# 编辑 application.yml 配置文件

# 构建项目
./mvnw clean package -DskipTests

# 启动后端服务
java -jar target/traffic-analysis-system-1.0.0.jar
```

#### 前端部署
```bash
cd nvm/trafficsystem

# 安装依赖
npm install

# 配置 API 地址
cp .env.example .env.local
# 编辑 .env.local 配置文件

# 构建生产版本
npm run build

# 启动前端服务
npm run serve
```

#### AI服务部署
```bash
cd python_service

# 激活虚拟环境
source ../traffic_analysis_env/bin/activate  # Linux/Mac
# 或 ..\traffic_analysis_env\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 下载 YOLOv12x 模型
python download_model.py

# 启动 AI 服务
python app.py
```

## 配置文件

### 后端配置 (application.yml)
```yaml
server:
  port: 8080

spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/traffic_analysis
      
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 2GB

traffic:
  analysis:
    video:
      storage-path: /data/videos
      temp-path: /tmp/traffic
    ai:
      service-url: http://localhost:5000
      timeout: 300000
    websocket:
      allowed-origins: http://localhost:3000
```

### 前端配置 (.env.local)
```env
VUE_APP_API_BASE_URL=http://localhost:8080/api
VUE_APP_WEBSOCKET_URL=ws://localhost:8080/ws
VUE_APP_UPLOAD_MAX_SIZE=524288000
VUE_APP_SUPPORTED_FORMATS=mp4,avi,mov,wmv,flv,webm,mkv
```

### AI服务配置 (config.py)
```python
# 服务配置
HOST = '0.0.0.0'
PORT = 5000
DEBUG = False

# 模型配置
MODEL_PATH = './models/yolov12x.pt'
CONFIDENCE_THRESHOLD = 0.5
IOU_THRESHOLD = 0.45

# 处理配置
MAX_CONCURRENT_TASKS = 4
FRAME_SKIP = 1
OUTPUT_FORMAT = 'jpg'
OUTPUT_QUALITY = 85

# 存储配置
TEMP_DIR = '/tmp/ai_processing'
RESULT_CACHE_SIZE = 1000
```

## 数据库初始化

### 创建数据库和用户
```javascript
// 连接 MongoDB
mongo

// 创建数据库
use traffic_analysis

// 创建用户
db.createUser({
  user: "trafficUser",
  pwd: "your_password",
  roles: [
    { role: "readWrite", db: "traffic_analysis" }
  ]
})

// 创建索引
db.users.createIndex({ "username": 1 }, { unique: true })
db.intersection_analysis_four_way.createIndex({ "taskId": 1 }, { unique: true })
db.intersection_analysis_four_way.createIndex({ "userId": 1, "createdAt": -1 })
```

### 运行索引创建脚本
```bash
mongo traffic_analysis < src/main/resources/mongodb/create-indexes.js
```

## 系统启动

### 使用脚本启动 (推荐)
```bash
# 创建启动脚本
cat > start_system.sh << 'EOF'
#!/bin/bash

echo "启动交通分析系统..."

# 启动 MongoDB
sudo systemctl start mongod

# 启动后端服务
cd trafficsystem
nohup java -jar target/traffic-analysis-system-1.0.0.jar > ../logs/backend.log 2>&1 &
echo $! > ../pids/backend.pid

# 启动 AI 服务
cd ../python_service
source ../traffic_analysis_env/bin/activate
nohup python app.py > ../logs/ai_service.log 2>&1 &
echo $! > ../pids/ai_service.pid

# 启动前端服务
cd ../nvm/trafficsystem
nohup npm run serve > ../../logs/frontend.log 2>&1 &
echo $! > ../../pids/frontend.pid

echo "系统启动完成！"
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:8080"
echo "AI服务地址: http://localhost:5000"
EOF

chmod +x start_system.sh
./start_system.sh
```

### 手动启动
```bash
# 1. 启动 MongoDB
sudo systemctl start mongod

# 2. 启动后端服务
cd trafficsystem
java -jar target/traffic-analysis-system-1.0.0.jar &

# 3. 启动 AI 服务
cd python_service
source ../traffic_analysis_env/bin/activate
python app.py &

# 4. 启动前端服务
cd nvm/trafficsystem
npm run serve &
```

## 系统停止

### 使用脚本停止
```bash
# 创建停止脚本
cat > stop_system.sh << 'EOF'
#!/bin/bash

echo "停止交通分析系统..."

# 停止前端服务
if [ -f pids/frontend.pid ]; then
    kill $(cat pids/frontend.pid)
    rm pids/frontend.pid
fi

# 停止 AI 服务
if [ -f pids/ai_service.pid ]; then
    kill $(cat pids/ai_service.pid)
    rm pids/ai_service.pid
fi

# 停止后端服务
if [ -f pids/backend.pid ]; then
    kill $(cat pids/backend.pid)
    rm pids/backend.pid
fi

echo "系统停止完成！"
EOF

chmod +x stop_system.sh
./stop_system.sh
```

## 生产环境部署

### 使用 Docker 部署 (推荐)
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 使用 Nginx 反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /var/www/traffic-analysis/dist;
        try_files $uri $uri/ /index.html;
    }

    # API 代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # WebSocket 代理
    location /ws/ {
        proxy_pass http://localhost:8080/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

## 监控和维护

### 系统监控
```bash
# 查看系统资源使用
htop
df -h
free -h

# 查看服务状态
systemctl status mongod
ps aux | grep java
ps aux | grep python
ps aux | grep node

# 查看日志
tail -f logs/backend.log
tail -f logs/ai_service.log
tail -f logs/frontend.log
```

### 数据备份
```bash
# MongoDB 备份
mongodump --db traffic_analysis --out /backup/$(date +%Y%m%d)

# 文件备份
tar -czf /backup/videos_$(date +%Y%m%d).tar.gz /data/videos
```

### 性能优化
1. **数据库优化**: 定期运行索引优化脚本
2. **缓存配置**: 启用 Redis 缓存（可选）
3. **负载均衡**: 使用 Nginx 进行负载均衡
4. **资源监控**: 配置 Prometheus + Grafana 监控

## 故障排除

### 常见问题

1. **端口冲突**
   - 检查端口占用: `netstat -tulpn | grep :8080`
   - 修改配置文件中的端口号

2. **内存不足**
   - 增加 JVM 堆内存: `-Xmx4g -Xms2g`
   - 优化 Python 进程数量

3. **数据库连接失败**
   - 检查 MongoDB 服务状态
   - 验证连接字符串和认证信息

4. **文件上传失败**
   - 检查磁盘空间
   - 验证文件权限和目录存在

### 日志分析
```bash
# 查看错误日志
grep -i error logs/*.log
grep -i exception logs/*.log

# 查看性能日志
grep -i "slow" logs/backend.log
grep -i "timeout" logs/*.log
```

## 更新升级

### 系统更新
```bash
# 1. 备份数据
./backup_system.sh

# 2. 停止服务
./stop_system.sh

# 3. 更新代码
git pull origin main

# 4. 重新构建
cd trafficsystem && ./mvnw clean package -DskipTests
cd ../nvm/trafficsystem && npm run build
cd ../python_service && pip install -r requirements.txt

# 5. 启动服务
./start_system.sh
```

### 数据库迁移
```bash
# 运行数据库迁移脚本
mongo traffic_analysis < migrations/v1.1.0_migration.js
```

## 安全配置

### 防火墙设置
```bash
# Ubuntu/CentOS
sudo ufw allow 22/tcp      # SSH
sudo ufw allow 80/tcp      # HTTP
sudo ufw allow 443/tcp     # HTTPS
sudo ufw allow 8080/tcp    # 后端服务（仅内网）
sudo ufw enable
```

### SSL 证书配置
```bash
# 使用 Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 数据库安全
```javascript
// 启用认证
// 编辑 /etc/mongod.conf
security:
  authorization: enabled
```

这样就完成了交通分析系统的完整部署指南。
