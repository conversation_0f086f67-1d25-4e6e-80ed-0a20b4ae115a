{"ast": null, "code": "import { ref, computed, watch } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { VideoCamera, VideoPlay, VideoPause, Download } from '@element-plus/icons-vue';\nexport default {\n  name: 'RealTimeFrameViewer',\n  components: {\n    VideoCamera,\n    VideoPlay,\n    VideoPause,\n    Download\n  },\n  props: {\n    direction: {\n      type: String,\n      required: true,\n      validator: value => ['east', 'south', 'west', 'north'].includes(value)\n    },\n    frameData: {\n      type: Object,\n      default: null\n    },\n    status: {\n      type: String,\n      default: 'waiting',\n      validator: value => ['waiting', 'processing', 'completed', 'error'].includes(value)\n    },\n    progress: {\n      type: Number,\n      default: 0\n    },\n    showControls: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['pause-toggled', 'frame-saved'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const currentFrame = ref(null);\n    const isPaused = ref(false);\n    const imageLoading = ref(false);\n\n    // 计算属性\n    const directionName = computed(() => {\n      const names = {\n        east: '东向',\n        south: '南向',\n        west: '西向',\n        north: '北向'\n      };\n      return names[props.direction] || props.direction;\n    });\n    const hasCurrentFrame = computed(() => {\n      return currentFrame.value && currentFrame.value.imageData;\n    });\n    const statusClass = computed(() => {\n      return {\n        'status-waiting': props.status === 'waiting',\n        'status-processing': props.status === 'processing',\n        'status-completed': props.status === 'completed',\n        'status-error': props.status === 'error'\n      };\n    });\n    const statusText = computed(() => {\n      const statusMap = {\n        waiting: '等待处理',\n        processing: '正在处理',\n        completed: '处理完成',\n        error: '处理失败'\n      };\n      return statusMap[props.status] || '未知状态';\n    });\n\n    // 监听帧数据变化\n    watch(() => props.frameData, newFrameData => {\n      if (newFrameData && !isPaused.value) {\n        updateFrame(newFrameData);\n      }\n    }, {\n      immediate: true\n    });\n\n    // 方法\n    const updateFrame = frameData => {\n      if (!frameData) return;\n\n      // 验证帧数据\n      if (!frameData.imageData || !frameData.imageData.startsWith('data:image/')) {\n        console.warn('无效的帧数据:', frameData);\n        return;\n      }\n      currentFrame.value = {\n        ...frameData,\n        timestamp: new Date().toISOString()\n      };\n    };\n    const getVehicleTypeName = type => {\n      const typeNames = {\n        car: '小汽车',\n        truck: '卡车',\n        bus: '公交车',\n        motorcycle: '摩托车'\n      };\n      return typeNames[type] || type;\n    };\n    const togglePause = () => {\n      isPaused.value = !isPaused.value;\n      emit('pause-toggled', {\n        direction: props.direction,\n        isPaused: isPaused.value\n      });\n    };\n    const saveFrame = () => {\n      if (!hasCurrentFrame.value) {\n        ElMessage.warning('没有可保存的帧数据');\n        return;\n      }\n      try {\n        // 创建下载链接\n        const link = document.createElement('a');\n        link.href = currentFrame.value.imageData;\n        link.download = `${props.direction}_frame_${currentFrame.value.frameNumber || 'unknown'}_${Date.now()}.jpg`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        ElMessage.success('帧图像已保存');\n        emit('frame-saved', {\n          direction: props.direction,\n          frameData: currentFrame.value\n        });\n      } catch (error) {\n        console.error('保存帧失败:', error);\n        ElMessage.error('保存帧失败');\n      }\n    };\n    const onImageLoad = () => {\n      imageLoading.value = false;\n    };\n    const onImageError = () => {\n      imageLoading.value = false;\n      console.error('帧图像加载失败');\n    };\n    const getWaitingMessage = () => {\n      if (props.status === 'processing') {\n        return '正在处理视频，请稍候...';\n      } else if (props.status === 'waiting') {\n        return '等待视频帧数据...';\n      } else if (props.status === 'error') {\n        return '视频处理失败';\n      }\n      return '等待视频帧数据...';\n    };\n    return {\n      currentFrame,\n      isPaused,\n      imageLoading,\n      directionName,\n      hasCurrentFrame,\n      statusClass,\n      statusText,\n      getVehicleTypeName,\n      getWaitingMessage,\n      togglePause,\n      saveFrame,\n      onImageLoad,\n      onImageError\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "watch", "ElMessage", "VideoCamera", "VideoPlay", "VideoPause", "Download", "name", "components", "props", "direction", "type", "String", "required", "validator", "value", "includes", "frameData", "Object", "default", "status", "progress", "Number", "showControls", "Boolean", "emits", "setup", "emit", "currentFrame", "isPaused", "imageLoading", "directionName", "names", "east", "south", "west", "north", "hasCurrentFrame", "imageData", "statusClass", "statusText", "statusMap", "waiting", "processing", "completed", "error", "newFrameData", "updateFrame", "immediate", "startsWith", "console", "warn", "timestamp", "Date", "toISOString", "getVehicleTypeName", "typeNames", "car", "truck", "bus", "motorcycle", "toggle<PERSON><PERSON>e", "saveFrame", "warning", "link", "document", "createElement", "href", "download", "frameNumber", "now", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "success", "onImageLoad", "onImageError", "getWaitingMessage"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\RealTimeFrameViewer.vue"], "sourcesContent": ["<template>\n  <div class=\"real-time-frame-viewer\">\n    <!-- 视频显示区域 -->\n    <div class=\"video-container\" :class=\"{ 'has-frame': hasCurrentFrame }\">\n      <div v-if=\"!hasCurrentFrame\" class=\"no-frame-placeholder\">\n        <el-icon size=\"48\"><VideoCamera /></el-icon>\n        <p>{{ getWaitingMessage() }}</p>\n        <el-progress \n          v-if=\"progress > 0\"\n          :percentage=\"progress\" \n          :stroke-width=\"6\"\n          :show-text=\"false\"\n        />\n      </div>\n      \n      <div v-else class=\"frame-display\">\n        <img \n          :src=\"currentFrame.imageData\" \n          :alt=\"`${directionName}方向检测结果`\"\n          class=\"frame-image\"\n          @load=\"onImageLoad\"\n          @error=\"onImageError\"\n        />\n        \n        <!-- 检测信息覆盖层 -->\n        <div class=\"detection-overlay\">\n          <div class=\"detection-info\">\n            <span class=\"vehicle-count\">{{ currentFrame.detectionCount || 0 }}</span>\n            <span class=\"vehicle-label\">辆车</span>\n          </div>\n          \n          <div class=\"frame-info\">\n            <span class=\"frame-number\">\n              {{ currentFrame.frameNumber || 0 }} / {{ currentFrame.totalFrames || 0 }}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 方向标识 -->\n    <div class=\"direction-header\">\n      <div class=\"direction-icon\" :class=\"direction\">\n        {{ directionName.substring(0, 1) }}\n      </div>\n      <div class=\"direction-info\">\n        <h4 class=\"direction-name\">{{ directionName }}</h4>\n        <p class=\"direction-status\" :class=\"statusClass\">{{ statusText }}</p>\n      </div>\n    </div>\n    \n    <!-- 统计信息 -->\n    <div class=\"stats-panel\" v-if=\"hasCurrentFrame && currentFrame.vehicleTypes\">\n      <h5>车辆类型统计</h5>\n      <div class=\"vehicle-types\">\n        <div \n          v-for=\"(count, type) in currentFrame.vehicleTypes\" \n          :key=\"type\"\n          class=\"vehicle-type-item\"\n        >\n          <span class=\"type-name\">{{ getVehicleTypeName(type) }}</span>\n          <span class=\"type-count\">{{ count }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 控制按钮 -->\n    <div class=\"controls\" v-if=\"showControls\">\n      <el-button \n        size=\"small\" \n        @click=\"togglePause\"\n        :icon=\"isPaused ? VideoPlay : VideoPause\"\n      >\n        {{ isPaused ? '播放' : '暂停' }}\n      </el-button>\n      \n      <el-button \n        size=\"small\" \n        @click=\"saveFrame\"\n        :disabled=\"!hasCurrentFrame\"\n        icon=\"Download\"\n      >\n        保存帧\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, watch } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport { \n  VideoCamera, VideoPlay, VideoPause, Download \n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'RealTimeFrameViewer',\n  components: {\n    VideoCamera, VideoPlay, VideoPause, Download\n  },\n  props: {\n    direction: {\n      type: String,\n      required: true,\n      validator: (value) => ['east', 'south', 'west', 'north'].includes(value)\n    },\n    frameData: {\n      type: Object,\n      default: null\n    },\n    status: {\n      type: String,\n      default: 'waiting',\n      validator: (value) => ['waiting', 'processing', 'completed', 'error'].includes(value)\n    },\n    progress: {\n      type: Number,\n      default: 0\n    },\n    showControls: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['pause-toggled', 'frame-saved'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const currentFrame = ref(null)\n    const isPaused = ref(false)\n    const imageLoading = ref(false)\n    \n    // 计算属性\n    const directionName = computed(() => {\n      const names = {\n        east: '东向',\n        south: '南向',\n        west: '西向',\n        north: '北向'\n      }\n      return names[props.direction] || props.direction\n    })\n    \n    const hasCurrentFrame = computed(() => {\n      return currentFrame.value && currentFrame.value.imageData\n    })\n    \n    const statusClass = computed(() => {\n      return {\n        'status-waiting': props.status === 'waiting',\n        'status-processing': props.status === 'processing',\n        'status-completed': props.status === 'completed',\n        'status-error': props.status === 'error'\n      }\n    })\n    \n    const statusText = computed(() => {\n      const statusMap = {\n        waiting: '等待处理',\n        processing: '正在处理',\n        completed: '处理完成',\n        error: '处理失败'\n      }\n      return statusMap[props.status] || '未知状态'\n    })\n    \n    // 监听帧数据变化\n    watch(() => props.frameData, (newFrameData) => {\n      if (newFrameData && !isPaused.value) {\n        updateFrame(newFrameData)\n      }\n    }, { immediate: true })\n    \n    // 方法\n    const updateFrame = (frameData) => {\n      if (!frameData) return\n      \n      // 验证帧数据\n      if (!frameData.imageData || !frameData.imageData.startsWith('data:image/')) {\n        console.warn('无效的帧数据:', frameData)\n        return\n      }\n      \n      currentFrame.value = {\n        ...frameData,\n        timestamp: new Date().toISOString()\n      }\n    }\n    \n    const getVehicleTypeName = (type) => {\n      const typeNames = {\n        car: '小汽车',\n        truck: '卡车',\n        bus: '公交车',\n        motorcycle: '摩托车'\n      }\n      return typeNames[type] || type\n    }\n    \n    const togglePause = () => {\n      isPaused.value = !isPaused.value\n      emit('pause-toggled', {\n        direction: props.direction,\n        isPaused: isPaused.value\n      })\n    }\n    \n    const saveFrame = () => {\n      if (!hasCurrentFrame.value) {\n        ElMessage.warning('没有可保存的帧数据')\n        return\n      }\n      \n      try {\n        // 创建下载链接\n        const link = document.createElement('a')\n        link.href = currentFrame.value.imageData\n        link.download = `${props.direction}_frame_${currentFrame.value.frameNumber || 'unknown'}_${Date.now()}.jpg`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        \n        ElMessage.success('帧图像已保存')\n        emit('frame-saved', {\n          direction: props.direction,\n          frameData: currentFrame.value\n        })\n        \n      } catch (error) {\n        console.error('保存帧失败:', error)\n        ElMessage.error('保存帧失败')\n      }\n    }\n    \n    const onImageLoad = () => {\n      imageLoading.value = false\n    }\n    \n    const onImageError = () => {\n      imageLoading.value = false\n      console.error('帧图像加载失败')\n    }\n\n    const getWaitingMessage = () => {\n      if (props.status === 'processing') {\n        return '正在处理视频，请稍候...'\n      } else if (props.status === 'waiting') {\n        return '等待视频帧数据...'\n      } else if (props.status === 'error') {\n        return '视频处理失败'\n      }\n      return '等待视频帧数据...'\n    }\n\n    return {\n      currentFrame,\n      isPaused,\n      imageLoading,\n      directionName,\n      hasCurrentFrame,\n      statusClass,\n      statusText,\n      getVehicleTypeName,\n      getWaitingMessage,\n      togglePause,\n      saveFrame,\n      onImageLoad,\n      onImageError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.real-time-frame-viewer {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  overflow: hidden;\n  background: #fff;\n}\n\n.video-container {\n  flex: 1;\n  position: relative;\n  background: #f5f7fa;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 200px;\n}\n\n.no-frame-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #c0c4cc;\n  text-align: center;\n  padding: 20px;\n}\n\n.no-frame-placeholder p {\n  margin: 12px 0 16px 0;\n  font-size: 14px;\n}\n\n.frame-display {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.frame-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 4px;\n}\n\n.detection-overlay {\n  position: absolute;\n  top: 8px;\n  left: 8px;\n  right: 8px;\n  display: flex;\n  justify-content: space-between;\n  pointer-events: none;\n}\n\n.detection-info {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 6px 12px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.vehicle-count {\n  font-size: 18px;\n  font-weight: bold;\n}\n\n.vehicle-label {\n  font-size: 12px;\n}\n\n.frame-info {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.direction-header {\n  display: flex;\n  align-items: center;\n  padding: 12px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #fafafa;\n}\n\n.direction-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  font-size: 14px;\n  margin-right: 12px;\n}\n\n.direction-icon.east {\n  background: #409eff;\n}\n\n.direction-icon.south {\n  background: #67c23a;\n}\n\n.direction-icon.west {\n  background: #e6a23c;\n}\n\n.direction-icon.north {\n  background: #f56c6c;\n}\n\n.direction-info {\n  flex: 1;\n}\n\n.direction-name {\n  margin: 0 0 4px 0;\n  font-size: 16px;\n  color: #2c3e50;\n}\n\n.direction-status {\n  margin: 0;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.status-waiting {\n  color: #909399;\n}\n\n.status-processing {\n  color: #e6a23c;\n}\n\n.status-completed {\n  color: #67c23a;\n}\n\n.status-error {\n  color: #f56c6c;\n}\n\n.stats-panel {\n  padding: 12px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #fafafa;\n}\n\n.stats-panel h5 {\n  margin: 0 0 8px 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.vehicle-types {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.vehicle-type-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n}\n\n.type-name {\n  color: #606266;\n}\n\n.type-count {\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.controls {\n  display: flex;\n  gap: 8px;\n  padding: 12px;\n  background: #fafafa;\n}\n\n.controls .el-button {\n  flex: 1;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .detection-overlay {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .direction-header {\n    padding: 8px;\n  }\n  \n  .direction-icon {\n    width: 28px;\n    height: 28px;\n    font-size: 12px;\n  }\n  \n  .direction-name {\n    font-size: 14px;\n  }\n}\n</style>\n"], "mappings": "AAyFA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,KAAI,QAAS,KAAI;AACzC,SAASC,SAAQ,QAAS,cAAa;AACvC,SACEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAO,QACtC,yBAAwB;AAE/B,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAE;IACVL,WAAW;IAAEC,SAAS;IAAEC,UAAU;IAAEC;EACtC,CAAC;EACDG,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAGC,KAAK,IAAK,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACzE,CAAC;IACDE,SAAS,EAAE;MACTN,IAAI,EAAEO,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE;MACNT,IAAI,EAAEC,MAAM;MACZO,OAAO,EAAE,SAAS;MAClBL,SAAS,EAAGC,KAAK,IAAK,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACtF,CAAC;IACDM,QAAQ,EAAE;MACRV,IAAI,EAAEW,MAAM;MACZH,OAAO,EAAE;IACX,CAAC;IACDI,YAAY,EAAE;MACZZ,IAAI,EAAEa,OAAO;MACbL,OAAO,EAAE;IACX;EACF,CAAC;EACDM,KAAK,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC;EACvCC,KAAKA,CAACjB,KAAK,EAAE;IAAEkB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,YAAW,GAAI7B,GAAG,CAAC,IAAI;IAC7B,MAAM8B,QAAO,GAAI9B,GAAG,CAAC,KAAK;IAC1B,MAAM+B,YAAW,GAAI/B,GAAG,CAAC,KAAK;;IAE9B;IACA,MAAMgC,aAAY,GAAI/B,QAAQ,CAAC,MAAM;MACnC,MAAMgC,KAAI,GAAI;QACZC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE;MACT;MACA,OAAOJ,KAAK,CAACvB,KAAK,CAACC,SAAS,KAAKD,KAAK,CAACC,SAAQ;IACjD,CAAC;IAED,MAAM2B,eAAc,GAAIrC,QAAQ,CAAC,MAAM;MACrC,OAAO4B,YAAY,CAACb,KAAI,IAAKa,YAAY,CAACb,KAAK,CAACuB,SAAQ;IAC1D,CAAC;IAED,MAAMC,WAAU,GAAIvC,QAAQ,CAAC,MAAM;MACjC,OAAO;QACL,gBAAgB,EAAES,KAAK,CAACW,MAAK,KAAM,SAAS;QAC5C,mBAAmB,EAAEX,KAAK,CAACW,MAAK,KAAM,YAAY;QAClD,kBAAkB,EAAEX,KAAK,CAACW,MAAK,KAAM,WAAW;QAChD,cAAc,EAAEX,KAAK,CAACW,MAAK,KAAM;MACnC;IACF,CAAC;IAED,MAAMoB,UAAS,GAAIxC,QAAQ,CAAC,MAAM;MAChC,MAAMyC,SAAQ,GAAI;QAChBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAE;MACT;MACA,OAAOJ,SAAS,CAAChC,KAAK,CAACW,MAAM,KAAK,MAAK;IACzC,CAAC;;IAED;IACAnB,KAAK,CAAC,MAAMQ,KAAK,CAACQ,SAAS,EAAG6B,YAAY,IAAK;MAC7C,IAAIA,YAAW,IAAK,CAACjB,QAAQ,CAACd,KAAK,EAAE;QACnCgC,WAAW,CAACD,YAAY;MAC1B;IACF,CAAC,EAAE;MAAEE,SAAS,EAAE;IAAK,CAAC;;IAEtB;IACA,MAAMD,WAAU,GAAK9B,SAAS,IAAK;MACjC,IAAI,CAACA,SAAS,EAAE;;MAEhB;MACA,IAAI,CAACA,SAAS,CAACqB,SAAQ,IAAK,CAACrB,SAAS,CAACqB,SAAS,CAACW,UAAU,CAAC,aAAa,CAAC,EAAE;QAC1EC,OAAO,CAACC,IAAI,CAAC,SAAS,EAAElC,SAAS;QACjC;MACF;MAEAW,YAAY,CAACb,KAAI,GAAI;QACnB,GAAGE,SAAS;QACZmC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC;IACF;IAEA,MAAMC,kBAAiB,GAAK5C,IAAI,IAAK;MACnC,MAAM6C,SAAQ,GAAI;QAChBC,GAAG,EAAE,KAAK;QACVC,KAAK,EAAE,IAAI;QACXC,GAAG,EAAE,KAAK;QACVC,UAAU,EAAE;MACd;MACA,OAAOJ,SAAS,CAAC7C,IAAI,KAAKA,IAAG;IAC/B;IAEA,MAAMkD,WAAU,GAAIA,CAAA,KAAM;MACxBhC,QAAQ,CAACd,KAAI,GAAI,CAACc,QAAQ,CAACd,KAAI;MAC/BY,IAAI,CAAC,eAAe,EAAE;QACpBjB,SAAS,EAAED,KAAK,CAACC,SAAS;QAC1BmB,QAAQ,EAAEA,QAAQ,CAACd;MACrB,CAAC;IACH;IAEA,MAAM+C,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAI,CAACzB,eAAe,CAACtB,KAAK,EAAE;QAC1Bb,SAAS,CAAC6D,OAAO,CAAC,WAAW;QAC7B;MACF;MAEA,IAAI;QACF;QACA,MAAMC,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG;QACvCF,IAAI,CAACG,IAAG,GAAIvC,YAAY,CAACb,KAAK,CAACuB,SAAQ;QACvC0B,IAAI,CAACI,QAAO,GAAI,GAAG3D,KAAK,CAACC,SAAS,UAAUkB,YAAY,CAACb,KAAK,CAACsD,WAAU,IAAK,SAAS,IAAIhB,IAAI,CAACiB,GAAG,CAAC,CAAC,MAAK;QAC1GL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI;QAC9BA,IAAI,CAACS,KAAK,CAAC;QACXR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI;QAE9B9D,SAAS,CAACyE,OAAO,CAAC,QAAQ;QAC1BhD,IAAI,CAAC,aAAa,EAAE;UAClBjB,SAAS,EAAED,KAAK,CAACC,SAAS;UAC1BO,SAAS,EAAEW,YAAY,CAACb;QAC1B,CAAC;MAEH,EAAE,OAAO8B,KAAK,EAAE;QACdK,OAAO,CAACL,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7B3C,SAAS,CAAC2C,KAAK,CAAC,OAAO;MACzB;IACF;IAEA,MAAM+B,WAAU,GAAIA,CAAA,KAAM;MACxB9C,YAAY,CAACf,KAAI,GAAI,KAAI;IAC3B;IAEA,MAAM8D,YAAW,GAAIA,CAAA,KAAM;MACzB/C,YAAY,CAACf,KAAI,GAAI,KAAI;MACzBmC,OAAO,CAACL,KAAK,CAAC,SAAS;IACzB;IAEA,MAAMiC,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,IAAIrE,KAAK,CAACW,MAAK,KAAM,YAAY,EAAE;QACjC,OAAO,eAAc;MACvB,OAAO,IAAIX,KAAK,CAACW,MAAK,KAAM,SAAS,EAAE;QACrC,OAAO,YAAW;MACpB,OAAO,IAAIX,KAAK,CAACW,MAAK,KAAM,OAAO,EAAE;QACnC,OAAO,QAAO;MAChB;MACA,OAAO,YAAW;IACpB;IAEA,OAAO;MACLQ,YAAY;MACZC,QAAQ;MACRC,YAAY;MACZC,aAAa;MACbM,eAAe;MACfE,WAAW;MACXC,UAAU;MACVe,kBAAkB;MAClBuB,iBAAiB;MACjBjB,WAAW;MACXC,SAAS;MACTc,WAAW;MACXC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}