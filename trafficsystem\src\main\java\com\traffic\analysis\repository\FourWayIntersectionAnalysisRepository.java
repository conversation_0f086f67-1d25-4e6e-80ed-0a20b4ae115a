package com.traffic.analysis.repository;

import com.traffic.analysis.model.FourWayIntersectionAnalysis;
import com.traffic.analysis.model.Direction;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 四方向交通分析数据访问接口
 */
@Repository
public interface FourWayIntersectionAnalysisRepository extends MongoRepository<FourWayIntersectionAnalysis, String> {
    
    /**
     * 根据任务ID查找分析记录
     */
    Optional<FourWayIntersectionAnalysis> findByTaskId(String taskId);
    
    /**
     * 根据用户ID查找分析记录
     */
    List<FourWayIntersectionAnalysis> findByUserId(String userId);
    
    /**
     * 根据用户ID分页查找分析记录
     */
    Page<FourWayIntersectionAnalysis> findByUserId(String userId, Pageable pageable);
    
    /**
     * 根据状态查找分析记录
     */
    List<FourWayIntersectionAnalysis> findByStatus(String status);
    
    /**
     * 根据用户ID和状态查找分析记录
     */
    List<FourWayIntersectionAnalysis> findByUserIdAndStatus(String userId, String status);
    
    /**
     * 根据创建时间范围查找分析记录
     */
    List<FourWayIntersectionAnalysis> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根据用户ID和创建时间范围查找分析记录
     */
    List<FourWayIntersectionAnalysis> findByUserIdAndCreatedAtBetween(String userId, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找正在处理中的任务
     */
    @Query("{'status': 'processing'}")
    List<FourWayIntersectionAnalysis> findProcessingTasks();
    
    /**
     * 查找已完成的任务
     */
    @Query("{'status': 'completed'}")
    List<FourWayIntersectionAnalysis> findCompletedTasks();
    
    /**
     * 查找失败的任务
     */
    @Query("{'status': 'failed'}")
    List<FourWayIntersectionAnalysis> findFailedTasks();
    
    /**
     * 根据用户名查找分析记录
     */
    List<FourWayIntersectionAnalysis> findByUsername(String username);
    
    /**
     * 根据角色查找分析记录
     */
    List<FourWayIntersectionAnalysis> findByRole(String role);
    
    /**
     * 查找指定时间之前创建的记录
     */
    List<FourWayIntersectionAnalysis> findByCreatedAtBefore(LocalDateTime dateTime);
    
    /**
     * 查找指定时间之后创建的记录
     */
    List<FourWayIntersectionAnalysis> findByCreatedAtAfter(LocalDateTime dateTime);
    
    /**
     * 查找处理时间超过指定时长的任务
     */
    @Query("{'processingStartTime': {$ne: null}, 'processingEndTime': null, 'processingStartTime': {$lt: ?0}}")
    List<FourWayIntersectionAnalysis> findLongRunningTasks(LocalDateTime cutoffTime);
    
    /**
     * 统计用户的分析任务数量
     */
    long countByUserId(String userId);
    
    /**
     * 统计指定状态的任务数量
     */
    long countByStatus(String status);
    
    /**
     * 统计用户指定状态的任务数量
     */
    long countByUserIdAndStatus(String userId, String status);
    
    /**
     * 统计指定时间范围内的任务数量
     */
    long countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查找最近的分析记录
     */
    @Query(value = "{}", sort = "{'createdAt': -1}")
    List<FourWayIntersectionAnalysis> findRecentAnalyses(Pageable pageable);
    
    /**
     * 查找用户最近的分析记录
     */
    @Query(value = "{'userId': ?0}", sort = "{'createdAt': -1}")
    List<FourWayIntersectionAnalysis> findRecentAnalysesByUser(String userId, Pageable pageable);
    
    /**
     * 查找包含特定方向数据的分析记录
     */
    @Query("{'directions.?0': {$exists: true}}")
    List<FourWayIntersectionAnalysis> findByDirectionExists(String direction);
    
    /**
     * 查找所有方向都已完成的分析记录
     */
    @Query("{'directions.east.status': 'completed', 'directions.south.status': 'completed', 'directions.west.status': 'completed', 'directions.north.status': 'completed'}")
    List<FourWayIntersectionAnalysis> findAllDirectionsCompleted();
    
    /**
     * 查找有任何方向失败的分析记录
     */
    @Query("{$or: [{'directions.east.status': 'failed'}, {'directions.south.status': 'failed'}, {'directions.west.status': 'failed'}, {'directions.north.status': 'failed'}]}")
    List<FourWayIntersectionAnalysis> findAnyDirectionFailed();
    
    /**
     * 查找车辆总数超过指定数量的分析记录
     */
    @Query("{'trafficAnalysis.totalVehicleCount': {$gt: ?0}}")
    List<FourWayIntersectionAnalysis> findByTotalVehicleCountGreaterThan(int vehicleCount);
    
    /**
     * 查找指定拥堵等级的分析记录
     */
    @Query("{'trafficAnalysis.congestionLevel': ?0}")
    List<FourWayIntersectionAnalysis> findByCongestionLevel(String congestionLevel);
    
    /**
     * 查找流量平衡度在指定范围内的分析记录
     */
    @Query("{'trafficAnalysis.trafficFlowBalance': {$gte: ?0, $lte: ?1}}")
    List<FourWayIntersectionAnalysis> findByTrafficFlowBalanceBetween(double minBalance, double maxBalance);
    
    /**
     * 删除指定时间之前的记录
     */
    void deleteByCreatedAtBefore(LocalDateTime dateTime);
    
    /**
     * 删除指定用户的记录
     */
    void deleteByUserId(String userId);
    
    /**
     * 删除指定状态的记录
     */
    void deleteByStatus(String status);
    
    /**
     * 查找需要清理的临时记录
     */
    @Query("{'status': {$in: ['queued', 'processing']}, 'createdAt': {$lt: ?0}}")
    List<FourWayIntersectionAnalysis> findStaleRecords(LocalDateTime cutoffTime);
    
    /**
     * 查找有报告数据的分析记录
     */
    @Query("{'reportData': {$ne: null}}")
    List<FourWayIntersectionAnalysis> findWithReportData();
    
    /**
     * 查找没有报告数据的已完成分析记录
     */
    @Query("{'status': 'completed', 'reportData': null}")
    List<FourWayIntersectionAnalysis> findCompletedWithoutReport();
    
    /**
     * 根据分析类型查找记录
     */
    List<FourWayIntersectionAnalysis> findByAnalysisType(String analysisType);
    
    /**
     * 查找处理时间最长的任务
     */
    @Query(value = "{'processingStartTime': {$ne: null}, 'processingEndTime': {$ne: null}}", sort = "{'processingDuration': -1}")
    List<FourWayIntersectionAnalysis> findLongestProcessingTasks(Pageable pageable);
    
    /**
     * 查找处理时间最短的任务
     */
    @Query(value = "{'processingStartTime': {$ne: null}, 'processingEndTime': {$ne: null}}", sort = "{'processingDuration': 1}")
    List<FourWayIntersectionAnalysis> findShortestProcessingTasks(Pageable pageable);
}
