<template>
  <div class="signal-optimization-panel">
    <div v-if="!hasOptimizationData" class="no-data-state">
      <el-icon size="48"><Setting /></el-icon>
      <p>暂无优化建议</p>
      <el-button type="primary" size="small" @click="generateOptimization">
        生成优化方案
      </el-button>
    </div>

    <div v-else class="optimization-content">
      <!-- 推荐信号周期 -->
      <div class="optimization-section">
        <h4 class="section-title">
          <el-icon><Timer /></el-icon>
          推荐信号周期
        </h4>
        <div class="cycle-display">
          <div class="cycle-value">
            <span class="value">{{ optimizationData.recommendedCycle }}</span>
            <span class="unit">秒</span>
          </div>
          <div class="cycle-comparison" v-if="currentCycle">
            <span class="current">当前: {{ currentCycle }}秒</span>
            <span class="change" :class="getCycleChangeClass()">
              {{ getCycleChangeText() }}
            </span>
          </div>
        </div>
      </div>

      <!-- 绿灯时间分配 -->
      <div class="optimization-section">
        <h4 class="section-title">
          <el-icon><Clock /></el-icon>
          绿灯时间分配
        </h4>
        <div class="time-allocation">
          <div 
            v-for="(time, direction) in optimizationData.greenTimeAllocation" 
            :key="direction"
            class="direction-time"
          >
            <div class="direction-info">
              <span class="direction-name">{{ getDirectionName(direction) }}</span>
              <span class="time-value">{{ time }}秒</span>
            </div>
            <el-progress 
              :percentage="getTimePercentage(time)"
              :color="getDirectionColor(direction)"
              :stroke-width="8"
              :show-text="false"
            />
          </div>
        </div>
      </div>

      <!-- 优化理由 -->
      <div class="optimization-section" v-if="optimizationData.reason">
        <h4 class="section-title">
          <el-icon><InfoFilled /></el-icon>
          优化理由
        </h4>
        <p class="reason-text">{{ optimizationData.reason }}</p>
      </div>

      <!-- 预期改善效果 -->
      <div class="optimization-section" v-if="optimizationData.expectedImprovement">
        <h4 class="section-title">
          <el-icon><TrendCharts /></el-icon>
          预期改善效果
        </h4>
        <div class="improvement-display">
          <el-tag type="success" size="large">
            {{ optimizationData.expectedImprovement }}
          </el-tag>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="optimization-actions">
        <el-button 
          type="primary" 
          @click="applyOptimization"
          :loading="applying"
          class="apply-btn"
        >
          <el-icon><Check /></el-icon>
          应用优化方案
        </el-button>
        
        <el-button 
          @click="previewOptimization"
          class="preview-btn"
        >
          <el-icon><View /></el-icon>
          预览效果
        </el-button>
        
        <el-button 
          @click="exportOptimization"
          class="export-btn"
        >
          <el-icon><Download /></el-icon>
          导出方案
        </el-button>
      </div>

      <!-- 优化历史 -->
      <div class="optimization-history" v-if="showHistory && optimizationHistory.length > 0">
        <h4 class="section-title">
          <el-icon><Clock /></el-icon>
          优化历史
        </h4>
        <el-timeline class="history-timeline">
          <el-timeline-item
            v-for="(record, index) in optimizationHistory"
            :key="index"
            :timestamp="record.timestamp"
            :type="record.type"
          >
            <div class="history-item">
              <p class="history-title">{{ record.title }}</p>
              <p class="history-description">{{ record.description }}</p>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="优化方案预览"
      width="600px"
      :before-close="closePreviewDialog"
    >
      <div class="preview-content">
        <div class="preview-chart">
          <h5>时间分配对比</h5>
          <div class="comparison-bars">
            <div 
              v-for="(time, direction) in optimizationData.greenTimeAllocation" 
              :key="direction"
              class="comparison-bar"
            >
              <span class="bar-label">{{ getDirectionName(direction) }}</span>
              <div class="bar-container">
                <div class="current-bar">
                  <span class="bar-text">当前</span>
                  <div class="bar" :style="{ width: getCurrentTimePercentage(direction) + '%' }"></div>
                  <span class="bar-value">{{ getCurrentTime(direction) }}s</span>
                </div>
                <div class="optimized-bar">
                  <span class="bar-text">优化</span>
                  <div 
                    class="bar optimized" 
                    :style="{ 
                      width: getTimePercentage(time) + '%',
                      backgroundColor: getDirectionColor(direction)
                    }"
                  ></div>
                  <span class="bar-value">{{ time }}s</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="preview-summary">
          <h5>优化摘要</h5>
          <ul>
            <li>信号周期: {{ currentCycle || 120 }}秒 → {{ optimizationData.recommendedCycle }}秒</li>
            <li>预期改善: {{ optimizationData.expectedImprovement }}</li>
            <li>优化理由: {{ optimizationData.reason }}</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closePreviewDialog">取消</el-button>
          <el-button type="primary" @click="confirmApplyOptimization">
            确认应用
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Setting, Timer, Clock, InfoFilled, TrendCharts, 
  Check, View, Download 
} from '@element-plus/icons-vue'

export default {
  name: 'SignalOptimizationPanel',
  components: {
    Setting, Timer, Clock, InfoFilled, TrendCharts,
    Check, View, Download
  },
  props: {
    optimizationData: {
      type: Object,
      default: null
    },
    currentCycle: {
      type: Number,
      default: 120
    },
    showHistory: {
      type: Boolean,
      default: false
    }
  },
  emits: ['apply-optimization', 'preview-optimization', 'export-optimization'],
  setup(props, { emit }) {
    // 响应式数据
    const applying = ref(false)
    const previewDialogVisible = ref(false)
    
    // 模拟优化历史数据
    const optimizationHistory = ref([
      {
        timestamp: '2024-01-15 14:30',
        type: 'success',
        title: '优化方案已应用',
        description: '信号周期调整为110秒，各方向绿灯时间重新分配'
      },
      {
        timestamp: '2024-01-15 10:15',
        type: 'warning',
        title: '优化方案生成',
        description: '基于当前交通流量生成新的信号优化方案'
      }
    ])
    
    // 计算属性
    const hasOptimizationData = computed(() => {
      return props.optimizationData && 
             props.optimizationData.recommendedCycle &&
             props.optimizationData.greenTimeAllocation
    })
    
    const maxGreenTime = computed(() => {
      if (!hasOptimizationData.value) return 60
      return Math.max(...Object.values(props.optimizationData.greenTimeAllocation))
    })
    
    // 方法
    const getDirectionName = (direction) => {
      const names = {
        east: '东向',
        south: '南向',
        west: '西向',
        north: '北向'
      }
      return names[direction] || direction
    }
    
    const getDirectionColor = (direction) => {
      const colors = {
        east: '#409eff',
        south: '#67c23a',
        west: '#e6a23c',
        north: '#f56c6c'
      }
      return colors[direction] || '#909399'
    }
    
    const getTimePercentage = (time) => {
      return maxGreenTime.value > 0 ? Math.round((time / maxGreenTime.value) * 100) : 0
    }
    
    const getCurrentTime = (direction) => {
      // 模拟当前时间分配
      const currentTimes = {
        east: 25,
        south: 20,
        west: 25,
        north: 20
      }
      return currentTimes[direction] || 20
    }
    
    const getCurrentTimePercentage = (direction) => {
      const currentTime = getCurrentTime(direction)
      return maxGreenTime.value > 0 ? Math.round((currentTime / maxGreenTime.value) * 100) : 0
    }
    
    const getCycleChangeClass = () => {
      if (!props.currentCycle) return ''
      const change = props.optimizationData.recommendedCycle - props.currentCycle
      return change > 0 ? 'increase' : change < 0 ? 'decrease' : 'same'
    }
    
    const getCycleChangeText = () => {
      if (!props.currentCycle) return ''
      const change = props.optimizationData.recommendedCycle - props.currentCycle
      if (change > 0) return `+${change}秒`
      if (change < 0) return `${change}秒`
      return '无变化'
    }
    
    const generateOptimization = () => {
      ElMessage.info('正在生成优化方案...')
      // 这里应该调用API生成优化方案
      setTimeout(() => {
        ElMessage.success('优化方案生成完成')
      }, 2000)
    }
    
    const applyOptimization = async () => {
      if (!hasOptimizationData.value) {
        ElMessage.warning('没有可应用的优化方案')
        return
      }
      
      try {
        await ElMessageBox.confirm(
          '确定要应用此优化方案吗？这将影响实际的交通信号控制。',
          '确认应用优化方案',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        applying.value = true
        
        // 模拟应用过程
        setTimeout(() => {
          applying.value = false
          ElMessage.success('优化方案已成功应用')
          
          // 添加到历史记录
          optimizationHistory.value.unshift({
            timestamp: new Date().toLocaleString(),
            type: 'success',
            title: '优化方案已应用',
            description: `信号周期调整为${props.optimizationData.recommendedCycle}秒`
          })
          
          emit('apply-optimization', props.optimizationData)
        }, 2000)
        
      } catch {
        ElMessage.info('已取消应用优化方案')
      }
    }
    
    const previewOptimization = () => {
      if (!hasOptimizationData.value) {
        ElMessage.warning('没有可预览的优化方案')
        return
      }
      
      previewDialogVisible.value = true
      emit('preview-optimization', props.optimizationData)
    }
    
    const exportOptimization = () => {
      if (!hasOptimizationData.value) {
        ElMessage.warning('没有可导出的优化方案')
        return
      }
      
      try {
        const exportData = {
          ...props.optimizationData,
          exportTime: new Date().toISOString(),
          version: '1.0'
        }
        
        const dataStr = JSON.stringify(exportData, null, 2)
        const blob = new Blob([dataStr], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        
        const link = document.createElement('a')
        link.href = url
        link.download = `信号优化方案_${new Date().toISOString().slice(0, 10)}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
        
        ElMessage.success('优化方案已导出')
        emit('export-optimization', exportData)
        
      } catch (error) {
        ElMessage.error('导出失败: ' + error.message)
      }
    }
    
    const closePreviewDialog = () => {
      previewDialogVisible.value = false
    }
    
    const confirmApplyOptimization = () => {
      previewDialogVisible.value = false
      applyOptimization()
    }
    
    return {
      // 响应式数据
      applying,
      previewDialogVisible,
      optimizationHistory,
      
      // 计算属性
      hasOptimizationData,
      maxGreenTime,
      
      // 方法
      getDirectionName,
      getDirectionColor,
      getTimePercentage,
      getCurrentTime,
      getCurrentTimePercentage,
      getCycleChangeClass,
      getCycleChangeText,
      generateOptimization,
      applyOptimization,
      previewOptimization,
      exportOptimization,
      closePreviewDialog,
      confirmApplyOptimization
    }
  }
}
</script>

<style scoped>
.signal-optimization-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.no-data-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  text-align: center;
}

.no-data-state p {
  margin: 12px 0 16px 0;
  font-size: 14px;
}

.optimization-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.optimization-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.optimization-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.cycle-display {
  text-align: center;
}

.cycle-value {
  margin-bottom: 8px;
}

.cycle-value .value {
  font-size: 28px;
  font-weight: 600;
  color: #409eff;
}

.cycle-value .unit {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

.cycle-comparison {
  display: flex;
  justify-content: center;
  gap: 12px;
  font-size: 12px;
}

.current {
  color: #909399;
}

.change {
  font-weight: 600;
}

.change.increase {
  color: #e6a23c;
}

.change.decrease {
  color: #67c23a;
}

.change.same {
  color: #909399;
}

.time-allocation {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.direction-time {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.direction-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.direction-name {
  color: #606266;
  font-weight: 500;
}

.time-value {
  color: #2c3e50;
  font-weight: 600;
}

.reason-text {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.improvement-display {
  text-align: center;
}

.optimization-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.apply-btn, .preview-btn, .export-btn {
  width: 100%;
}

.optimization-history {
  max-height: 200px;
  overflow-y: auto;
}

.history-timeline {
  margin-top: 12px;
}

.history-item {
  margin-bottom: 8px;
}

.history-title {
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
}

.history-description {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

/* 预览对话框样式 */
.preview-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-chart h5,
.preview-summary h5 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #2c3e50;
}

.comparison-bars {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.comparison-bar {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.bar-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.bar-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.current-bar,
.optimized-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bar-text {
  width: 40px;
  font-size: 12px;
  color: #909399;
}

.bar {
  height: 20px;
  border-radius: 4px;
  background: #e4e7ed;
  transition: width 0.3s ease;
}

.bar.optimized {
  background: #409eff;
}

.bar-value {
  font-size: 12px;
  color: #606266;
  min-width: 30px;
}

.preview-summary ul {
  margin: 0;
  padding-left: 20px;
}

.preview-summary li {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}
</style>
