/**
 * 系统管理API服务
 * 用于系统状态监控、服务控制和连接测试
 */

import apiClient from '@/utils/http-common';

const SystemService = {
  /**
   * 获取系统状态
   * @returns {Promise} 系统状态信息
   */
  getSystemStatus() {
    return apiClient.get('/api/system/status');
  },

  /**
   * 切换模型服务状态
   * @param {string} action - 'start' 或 'stop'
   * @returns {Promise} 操作结果
   */
  toggleModelService(action) {
    return apiClient.post(`/api/system/model/${action}`);
  },

  /**
   * 切换数据库服务状态
   * @param {string} action - 'start' 或 'stop'
   * @returns {Promise} 操作结果
   */
  toggleDatabaseService(action) {
    return apiClient.post(`/api/system/database/${action}`);
  },

  /**
   * 测试模型连接
   * @returns {Promise} 连接测试结果
   */
  testModelConnection() {
    return apiClient.post('/api/system/test/model');
  },

  /**
   * 测试数据库连接
   * @returns {Promise} 连接测试结果
   */
  testDatabaseConnection() {
    return apiClient.post('/api/system/test/database');
  },

  /**
   * 清除系统日志
   * @returns {Promise} 清除操作结果
   */
  clearSystemLogs() {
    return apiClient.delete('/api/system/logs');
  },

  /**
   * 获取系统日志
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页大小
   * @param {string} params.level - 日志级别
   * @returns {Promise} 日志数据
   */
  getSystemLogs(params = {}) {
    return apiClient.get('/api/system/logs', { params });
  },

  /**
   * 获取系统性能指标
   * @returns {Promise} 性能指标数据
   */
  getSystemMetrics() {
    return apiClient.get('/api/system/metrics');
  },

  /**
   * 重启系统服务
   * @param {string} serviceName - 服务名称
   * @returns {Promise} 重启操作结果
   */
  restartService(serviceName) {
    return apiClient.post(`/api/system/restart/${serviceName}`);
  },

  /**
   * 获取服务健康状态
   * @returns {Promise} 服务健康状态
   */
  getHealthCheck() {
    return apiClient.get('/api/system/health');
  },

  /**
   * 更新系统配置
   * @param {Object} config - 配置数据
   * @returns {Promise} 更新结果
   */
  updateSystemConfig(config) {
    return apiClient.put('/api/system/config', config);
  },

  /**
   * 获取系统配置
   * @returns {Promise} 系统配置数据
   */
  getSystemConfig() {
    return apiClient.get('/api/system/config');
  },

  /**
   * 导出系统日志
   * @param {Object} params - 导出参数
   * @param {string} params.startDate - 开始日期
   * @param {string} params.endDate - 结束日期
   * @param {string} params.format - 导出格式 (csv, json, txt)
   * @returns {Promise} 导出文件
   */
  exportSystemLogs(params = {}) {
    return apiClient.get('/api/system/logs/export', {
      params,
      responseType: 'blob'
    });
  },

  /**
   * 获取系统版本信息
   * @returns {Promise} 版本信息
   */
  getSystemVersion() {
    return apiClient.get('/api/system/version');
  },

  /**
   * 检查系统更新
   * @returns {Promise} 更新检查结果
   */
  checkSystemUpdate() {
    return apiClient.get('/api/system/update/check');
  },

  /**
   * 执行系统备份
   * @param {Object} options - 备份选项
   * @returns {Promise} 备份操作结果
   */
  createSystemBackup(options = {}) {
    return apiClient.post('/api/system/backup', options);
  },

  /**
   * 获取备份列表
   * @returns {Promise} 备份列表
   */
  getBackupList() {
    return apiClient.get('/api/system/backup/list');
  },

  /**
   * 恢复系统备份
   * @param {string} backupId - 备份ID
   * @returns {Promise} 恢复操作结果
   */
  restoreSystemBackup(backupId) {
    return apiClient.post(`/api/system/backup/restore/${backupId}`);
  }
};

export default SystemService;

// 导出具名函数以便按需导入
export const {
  getSystemStatus,
  toggleModelService,
  toggleDatabaseService,
  testModelConnection,
  testDatabaseConnection,
  clearSystemLogs,
  getSystemLogs,
  getSystemMetrics,
  restartService,
  getHealthCheck,
  updateSystemConfig,
  getSystemConfig,
  exportSystemLogs,
  getSystemVersion,
  checkSystemUpdate,
  createSystemBackup,
  getBackupList,
  restoreSystemBackup
} = SystemService;