<template>
  <div class="realtime-test-container">
    <div class="page-header">
      <h2>实时帧推送功能测试</h2>
      <p class="sub-title">测试和验证实时视频预览功能</p>
    </div>

    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>功能测试面板</span>
          <el-button type="primary" @click="runAllTests" :loading="testing">
            {{ testing ? '测试中...' : '运行所有测试' }}
          </el-button>
        </div>
      </template>

      <!-- 测试控制面板 -->
      <div class="test-controls">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-button @click="testWebSocketConnection" :loading="testStates.websocket">
              WebSocket连接测试
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button @click="testFrameReceiving" :loading="testStates.frames">
              帧接收测试
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button @click="testNetworkAdaptation" :loading="testStates.network">
              网络自适应测试
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button @click="testComponentIntegration" :loading="testStates.integration">
              组件集成测试
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 测试结果显示 -->
      <div class="test-results">
        <h3>测试结果</h3>
        <el-table :data="testResults" style="width: 100%">
          <el-table-column prop="testName" label="测试项目" width="200" />
          <el-table-column prop="status" label="状态" width="120">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="耗时(ms)" width="120" />
          <el-table-column prop="details" label="详情" />
          <el-table-column prop="timestamp" label="测试时间" width="180" />
        </el-table>
      </div>

      <!-- 实时预览测试区域 -->
      <div class="preview-test-area" v-if="showPreviewTest">
        <h3>实时预览测试</h3>
        <div class="test-task-input">
          <el-input 
            v-model="testTaskId" 
            placeholder="输入测试任务ID"
            style="width: 300px; margin-right: 10px;"
          />
          <el-button type="primary" @click="startPreviewTest" :disabled="!testTaskId">
            开始预览测试
          </el-button>
          <el-button @click="stopPreviewTest" :disabled="!previewTestRunning">
            停止测试
          </el-button>
        </div>
        
        <div v-if="previewTestRunning" class="preview-test-container">
          <RealTimeFrameViewer
            ref="testFrameViewer"
            :task-id="testTaskId"
            :auto-start="true"
            :max-buffer-frames="20"
            @frame-received="handleTestFrameReceived"
            @playback-state-change="handleTestPlaybackChange"
          />
        </div>
      </div>

      <!-- 网络状况监控 -->
      <div class="network-monitor">
        <h3>网络状况监控</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-statistic title="帧接收速率" :value="networkStats.frameRate" suffix="fps" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="平均帧大小" :value="networkStats.averageSize" suffix="KB" />
          </el-col>
          <el-col :span="8">
            <div class="connection-quality">
              <span>连接质量: </span>
              <el-tag :type="getQualityType(networkStats.quality)">
                {{ getQualityText(networkStats.quality) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 测试日志 -->
    <el-card class="log-card">
      <template #header>
        <div class="card-header">
          <span>测试日志</span>
          <el-button size="small" @click="clearLogs">清空日志</el-button>
        </div>
      </template>
      
      <div class="test-logs">
        <div 
          v-for="(log, index) in testLogs" 
          :key="index" 
          class="log-entry"
          :class="log.level"
        >
          <span class="log-time">{{ log.timestamp }}</span>
          <span class="log-level">[{{ log.level.toUpperCase() }}]</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import RealTimeFrameViewer from '@/components/video/RealTimeFrameViewer.vue'
import stompService from '@/utils/stomp-service'

export default {
  name: 'RealtimeTest',
  components: {
    RealTimeFrameViewer
  },
  setup() {
    // 测试状态
    const testing = ref(false)
    const testStates = reactive({
      websocket: false,
      frames: false,
      network: false,
      integration: false
    })
    
    // 测试结果
    const testResults = ref([])
    const testLogs = ref([])
    
    // 预览测试
    const showPreviewTest = ref(true)
    const testTaskId = ref('test_task_' + Date.now())
    const previewTestRunning = ref(false)
    const testFrameViewer = ref(null)
    
    // 网络状况
    const networkStats = reactive({
      frameRate: 0,
      averageSize: 0,
      quality: 'good'
    })
    
    // 监控定时器
    let networkMonitorTimer = null
    
    // 添加测试日志
    const addLog = (level, message) => {
      const timestamp = new Date().toLocaleTimeString()
      testLogs.value.unshift({
        timestamp,
        level,
        message
      })
      
      // 限制日志数量
      if (testLogs.value.length > 100) {
        testLogs.value = testLogs.value.slice(0, 100)
      }
    }
    
    // 添加测试结果
    const addTestResult = (testName, status, duration, details = '') => {
      testResults.value.unshift({
        testName,
        status,
        duration,
        details,
        timestamp: new Date().toLocaleTimeString()
      })
    }
    
    // WebSocket连接测试
    const testWebSocketConnection = async () => {
      testStates.websocket = true
      const startTime = Date.now()
      
      try {
        addLog('info', '开始WebSocket连接测试...')
        
        // 测试STOMP连接
        if (!stompService.connected) {
          await stompService.init()
        }
        
        const duration = Date.now() - startTime
        addTestResult('WebSocket连接', '成功', duration, 'STOMP连接正常')
        addLog('success', `WebSocket连接测试成功，耗时 ${duration}ms`)
        
      } catch (error) {
        const duration = Date.now() - startTime
        addTestResult('WebSocket连接', '失败', duration, error.message)
        addLog('error', `WebSocket连接测试失败: ${error.message}`)
      } finally {
        testStates.websocket = false
      }
    }
    
    // 帧接收测试
    const testFrameReceiving = async () => {
      testStates.frames = true
      const startTime = Date.now()
      
      try {
        addLog('info', '开始帧接收测试...')
        
        // 模拟订阅帧数据
        const testTaskId = 'frame_test_' + Date.now()
        let frameCount = 0
        
        const subscription = await stompService.subscribeFrameUpdates(testTaskId, (frameData) => {
          frameCount++
          addLog('info', `接收到测试帧: ${frameData.frameNumber}`)
        })
        
        // 等待一段时间
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        // 清理订阅
        stompService.clearFrameBuffer(testTaskId)
        
        const duration = Date.now() - startTime
        addTestResult('帧接收功能', '成功', duration, `接收到 ${frameCount} 帧`)
        addLog('success', `帧接收测试完成，接收到 ${frameCount} 帧`)
        
      } catch (error) {
        const duration = Date.now() - startTime
        addTestResult('帧接收功能', '失败', duration, error.message)
        addLog('error', `帧接收测试失败: ${error.message}`)
      } finally {
        testStates.frames = false
      }
    }
    
    // 网络自适应测试
    const testNetworkAdaptation = async () => {
      testStates.network = true
      const startTime = Date.now()
      
      try {
        addLog('info', '开始网络自适应测试...')
        
        // 获取当前网络统计
        const stats = stompService.getNetworkStats()
        
        const duration = Date.now() - startTime
        addTestResult('网络自适应', '成功', duration, `质量: ${stats.connectionQuality}`)
        addLog('success', `网络自适应测试完成，当前质量: ${stats.connectionQuality}`)
        
      } catch (error) {
        const duration = Date.now() - startTime
        addTestResult('网络自适应', '失败', duration, error.message)
        addLog('error', `网络自适应测试失败: ${error.message}`)
      } finally {
        testStates.network = false
      }
    }
    
    // 组件集成测试
    const testComponentIntegration = async () => {
      testStates.integration = true
      const startTime = Date.now()
      
      try {
        addLog('info', '开始组件集成测试...')
        
        // 测试RealTimeFrameViewer组件
        if (testFrameViewer.value) {
          const frameCount = testFrameViewer.value.getFrameCount()
          addLog('info', `RealTimeFrameViewer当前缓冲帧数: ${frameCount}`)
        }
        
        const duration = Date.now() - startTime
        addTestResult('组件集成', '成功', duration, '所有组件正常工作')
        addLog('success', '组件集成测试完成')
        
      } catch (error) {
        const duration = Date.now() - startTime
        addTestResult('组件集成', '失败', duration, error.message)
        addLog('error', `组件集成测试失败: ${error.message}`)
      } finally {
        testStates.integration = false
      }
    }
    
    // 运行所有测试
    const runAllTests = async () => {
      testing.value = true
      addLog('info', '开始运行所有测试...')
      
      try {
        await testWebSocketConnection()
        await testFrameReceiving()
        await testNetworkAdaptation()
        await testComponentIntegration()
        
        addLog('success', '所有测试完成')
        ElMessage.success('所有测试完成')
        
      } catch (error) {
        addLog('error', `测试过程中发生错误: ${error.message}`)
        ElMessage.error('测试过程中发生错误')
      } finally {
        testing.value = false
      }
    }
    
    // 开始预览测试
    const startPreviewTest = () => {
      previewTestRunning.value = true
      addLog('info', `开始预览测试，任务ID: ${testTaskId.value}`)
    }
    
    // 停止预览测试
    const stopPreviewTest = () => {
      previewTestRunning.value = false
      addLog('info', '停止预览测试')
    }
    
    // 处理测试帧接收
    const handleTestFrameReceived = (frameData) => {
      addLog('info', `测试帧接收: 帧${frameData.frameNumber}, 车辆${frameData.detectionCount}`)
    }
    
    // 处理测试播放状态变化
    const handleTestPlaybackChange = (state) => {
      addLog('info', `播放状态变化: ${JSON.stringify(state)}`)
    }
    
    // 更新网络状况
    const updateNetworkStats = () => {
      try {
        const stats = stompService.getNetworkStats()
        networkStats.frameRate = stats.frameReceiveRate || 0
        networkStats.averageSize = stats.averageFrameSize || 0
        networkStats.quality = stats.connectionQuality || 'good'
      } catch (error) {
        console.error('更新网络统计失败:', error)
      }
    }
    
    // 清空日志
    const clearLogs = () => {
      testLogs.value = []
      addLog('info', '日志已清空')
    }
    
    // 获取状态类型
    const getStatusType = (status) => {
      switch (status) {
        case '成功': return 'success'
        case '失败': return 'danger'
        case '警告': return 'warning'
        default: return 'info'
      }
    }
    
    // 获取质量类型
    const getQualityType = (quality) => {
      switch (quality) {
        case 'good': return 'success'
        case 'fair': return 'warning'
        case 'poor': return 'danger'
        default: return 'info'
      }
    }
    
    // 获取质量文本
    const getQualityText = (quality) => {
      switch (quality) {
        case 'good': return '良好'
        case 'fair': return '一般'
        case 'poor': return '较差'
        default: return '未知'
      }
    }
    
    // 组件挂载
    onMounted(() => {
      addLog('info', '实时帧推送测试页面已加载')
      
      // 启动网络监控
      networkMonitorTimer = setInterval(updateNetworkStats, 2000)
    })
    
    // 组件卸载
    onUnmounted(() => {
      if (networkMonitorTimer) {
        clearInterval(networkMonitorTimer)
      }
    })
    
    return {
      testing,
      testStates,
      testResults,
      testLogs,
      showPreviewTest,
      testTaskId,
      previewTestRunning,
      testFrameViewer,
      networkStats,
      runAllTests,
      testWebSocketConnection,
      testFrameReceiving,
      testNetworkAdaptation,
      testComponentIntegration,
      startPreviewTest,
      stopPreviewTest,
      handleTestFrameReceived,
      handleTestPlaybackChange,
      clearLogs,
      getStatusType,
      getQualityType,
      getQualityText
    }
  }
}
</script>

<style scoped>
.realtime-test-container {
  min-height: 100vh;
  background-color: #111827;
  color: #e5e7eb;
  padding: 2rem;
}

.page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.page-header h2 {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 0.5rem;
}

.sub-title {
  color: #d1d5db;
  font-size: 1.1rem;
}

.test-card, .log-card {
  background: rgba(255, 255, 255, 0.03) !important;
  border: 1px solid rgba(255, 255, 255, 0.06) !important;
  border-radius: 1rem !important;
  margin-bottom: 2rem;
}

:deep(.el-card__header) {
  background-color: rgba(26, 32, 50, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #e5e7eb;
  font-weight: 600;
}

.test-controls {
  margin-bottom: 2rem;
}

.test-controls .el-button {
  width: 100%;
  margin-bottom: 10px;
}

.test-results {
  margin-bottom: 2rem;
}

.test-results h3 {
  color: #e5e7eb;
  margin-bottom: 1rem;
}

.preview-test-area {
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
}

.preview-test-area h3 {
  color: #e5e7eb;
  margin-bottom: 1rem;
}

.test-task-input {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.preview-test-container {
  margin-top: 1rem;
}

.network-monitor {
  padding: 1rem;
  background: rgba(31, 41, 55, 0.5);
  border-radius: 8px;
}

.network-monitor h3 {
  color: #e5e7eb;
  margin-bottom: 1rem;
}

.connection-quality {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e5e7eb;
}

.test-logs {
  max-height: 400px;
  overflow-y: auto;
  background: rgba(17, 24, 39, 0.8);
  border-radius: 6px;
  padding: 1rem;
}

.log-entry {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #9ca3af;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-entry.info .log-level {
  color: #3b82f6;
}

.log-entry.success .log-level {
  color: #10b981;
}

.log-entry.error .log-level {
  color: #ef4444;
}

.log-entry.warning .log-level {
  color: #f59e0b;
}

.log-message {
  color: #e5e7eb;
  flex: 1;
}

/* Element Plus 样式覆盖 */
:deep(.el-table) {
  background: transparent !important;
}

:deep(.el-table th) {
  background: rgba(26, 32, 50, 0.8) !important;
  color: #e5e7eb !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-table td) {
  background: rgba(31, 41, 55, 0.5) !important;
  color: #e5e7eb !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

:deep(.el-statistic__content) {
  color: #e5e7eb !important;
}

:deep(.el-statistic__head) {
  color: #d1d5db !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .realtime-test-container {
    padding: 1rem;
  }

  .page-header h2 {
    font-size: 2rem;
  }

  .test-controls .el-col {
    margin-bottom: 10px;
  }

  .test-task-input {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .test-task-input .el-input {
    width: 100% !important;
  }
}
</style>
