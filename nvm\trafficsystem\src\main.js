// Vue特性标志
window.__VUE_PROD_DEVTOOLS__ = false;
window.__VUE_PROD_HYDRATION_MISMATCH_DETAILS__ = false;

import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// 导入自定义样式
import './assets/styles.css'

// 添加全局样式，防止导航栏字体变色
import './styles/prevent-color-change.css'

// 导入双视频预览样式
import './styles/dual-video-preview.css'

// 导入Bootstrap样式和图标
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap-icons/font/bootstrap-icons.css'
// 导入Bootstrap脚本
import 'bootstrap/dist/js/bootstrap.bundle.min.js'

// 导入并初始化STOMP服务
import stompService from './utils/stomp-service'

// 导入WebSocket重连工具
import wsReconnector from './utils/websocket-reconnect'

// 开发环境下加载调试工具
if (process.env.NODE_ENV === 'development') {
  import('./utils/websocket-debug.js').then(() => {
    console.log('🔧 WebSocket调试工具已加载')
  }).catch(err => {
    console.warn('调试工具加载失败:', err)
  })
}

// 创建Vue应用实例
const app = createApp(App)

// 全局错误处理器
app.config.errorHandler = (err, vm, info) => {
  if (err.name === 'ChunkLoadError' || (err.message && err.message.includes('Loading chunk'))) {
    sessionStorage.clear();
    setTimeout(() => window.location.reload(), 1000);
  }
};

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(store)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})

// 初始化STOMP服务连接
stompService.init().catch(() => {
  // STOMP服务初始化失败时静默处理
});

// 启动WebSocket重连监控
setTimeout(() => {
  wsReconnector.startPeriodicCheck()
}, 3000)

// 开发环境下将stompService挂载到全局
if (process.env.NODE_ENV === 'development') {
  window.stompService = stompService;
}

// 挂载应用
app.mount('#app')
