package com.traffic.analysis.config;

import com.traffic.analysis.service.PythonServiceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * Python服务启动器
 * 在Spring Boot应用启动完成后自动启动Python服务
 */
@Component
public class PythonServiceRunner implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(PythonServiceRunner.class);

    @Autowired
    private PythonServiceManager pythonServiceManager;

    @Value("${python.service.auto-start:true}")
    private boolean autoStart;

    @Value("${python.service.independent-control:true}")
    private boolean independentControl;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (!autoStart) {
            logger.info("Python服务自动启动已禁用，跳过启动");
            if (independentControl) {
                logger.info("独立控制模式已启用，可通过API手动控制服务");
            }
            return;
        }

        logger.info("Spring Boot应用启动完成，开始启动Python服务...");

        try {
            // 启动Python服务
            pythonServiceManager.startAllServices();

            // 等待一段时间让服务完全启动
            Thread.sleep(10000);

            // 检查服务状态
            checkServiceStatus();

        } catch (Exception e) {
            logger.error("启动Python服务时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查服务状态
     */
    private void checkServiceStatus() {
        logger.info("检查Python服务状态...");
        
        boolean modelApiRunning = pythonServiceManager.isModelApiRunning();
        boolean apiControllerRunning = pythonServiceManager.isApiControllerRunning();
        
        logger.info("模型API服务状态: {}", modelApiRunning ? "运行中" : "未运行");
        logger.info("API控制器服务状态: {}", apiControllerRunning ? "运行中" : "未运行");
        
        if (modelApiRunning && apiControllerRunning) {
            logger.info("所有Python服务启动成功！");
        } else {
            logger.warn("部分Python服务启动失败，请检查日志");
        }
    }
}
