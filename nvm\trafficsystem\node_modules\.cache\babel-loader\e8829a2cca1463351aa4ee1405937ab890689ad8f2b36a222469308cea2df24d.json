{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, with<PERSON><PERSON><PERSON> as _withKeys, renderList as _renderList, Fragment as _Fragment, createBlock as _createBlock, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"video-history-container\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"selection-controls\"\n};\nconst _hoisted_5 = {\n  class: \"filter-panel\"\n};\nconst _hoisted_6 = {\n  class: \"filter-row\"\n};\nconst _hoisted_7 = {\n  class: \"filter-item\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"filter-item\"\n};\nconst _hoisted_9 = {\n  class: \"filter-item\"\n};\nconst _hoisted_10 = {\n  class: \"filter-actions\"\n};\nconst _hoisted_11 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_12 = {\n  key: 1,\n  class: \"error-container\"\n};\nconst _hoisted_13 = {\n  class: \"error-actions\"\n};\nconst _hoisted_14 = {\n  key: 2,\n  class: \"empty-container\"\n};\nconst _hoisted_15 = {\n  key: 3\n};\nconst _hoisted_16 = {\n  key: 0\n};\nconst _hoisted_17 = {\n  key: 1,\n  class: \"video-name-container\"\n};\nconst _hoisted_18 = {\n  class: \"video-name\"\n};\nconst _hoisted_19 = {\n  key: 0\n};\nconst _hoisted_20 = {\n  key: 1\n};\nconst _hoisted_21 = {\n  key: 2,\n  class: \"text-muted\"\n};\nconst _hoisted_22 = {\n  class: \"table-actions\"\n};\nconst _hoisted_23 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_24 = {\n  class: \"batch-actions\"\n};\nconst _hoisted_25 = {\n  key: 1,\n  class: \"selected-info\"\n};\nconst _hoisted_26 = {\n  class: \"pagination-wrapper\"\n};\nconst _hoisted_27 = {\n  class: \"pagination-info\"\n};\nconst _hoisted_28 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_29 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_delete = _resolveComponent(\"delete\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_plus = _resolveComponent(\"plus\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_search = _resolveComponent(\"search\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  const _component_refresh = _resolveComponent(\"refresh\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_Edit = _resolveComponent(\"Edit\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"history-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[14] || (_cache[14] = _createElementVNode(\"h2\", null, \"视频分析历史\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [$setup.videoTasks.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_checkbox, {\n      modelValue: $setup.selectAll,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.selectAll = $event),\n      onChange: $setup.toggleSelectAll,\n      indeterminate: $setup.isIndeterminate,\n      class: \"wider-checkbox\"\n    }, {\n      default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"全选\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\", \"indeterminate\"])])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n      class: \"custom-btn-outline\",\n      disabled: !$setup.hasSelected,\n      onClick: $setup.handleBatchDelete\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_delete)]),\n        _: 1 /* STABLE */\n      }), _cache[12] || (_cache[12] = _createTextVNode(\" 批量删除 \"))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"disabled\", \"onClick\"]), _createVNode(_component_router_link, {\n      to: \"/video-upload\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        class: \"custom-btn-primary\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_plus)]),\n          _: 1 /* STABLE */\n        }), _cache[13] || (_cache[13] = _createTextVNode(\" 上传新视频 \"))]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_cache[15] || (_cache[15] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"搜索：\", -1 /* HOISTED */)), _createVNode(_component_el_input, {\n      modelValue: $setup.searchQuery,\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.searchQuery = $event),\n      placeholder: \"搜索视频名称\",\n      clearable: \"\",\n      onClear: $setup.handleSearch,\n      onKeyup: _withKeys($setup.handleSearch, [\"enter\"]),\n      size: \"small\",\n      class: \"search-input\"\n    }, {\n      prefix: _withCtx(() => [_createVNode(_component_el_icon, {\n        class: \"el-input__icon\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_search)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onClear\", \"onKeyup\"])]), _createCommentVNode(\" 管理员用户筛选功能 \"), $setup.isAdmin ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_cache[16] || (_cache[16] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"用户：\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n      modelValue: $setup.userFilter,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.userFilter = $event),\n      placeholder: \"用户筛选\",\n      clearable: \"\",\n      onChange: $setup.handleSearch,\n      size: \"small\",\n      \"popper-append-to-body\": true,\n      \"reserve-keyword\": false\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_option, {\n        label: \"全部用户\",\n        value: \"\"\n      }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.userList, user => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: user.id,\n          label: user.username,\n          value: user.id\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_9, [_cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n      class: \"filter-label\"\n    }, \"日期：\", -1 /* HOISTED */)), _createVNode(_component_el_date_picker, {\n      modelValue: $setup.dateRange,\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.dateRange = $event),\n      type: \"daterange\",\n      \"range-separator\": \"至\",\n      \"start-placeholder\": \"开始日期\",\n      \"end-placeholder\": \"结束日期\",\n      format: \"YYYY-MM-DD\",\n      \"value-format\": \"YYYY-MM-DD\",\n      shortcuts: $setup.dateShortcuts,\n      editable: false,\n      size: \"small\",\n      class: \"date-picker\"\n    }, null, 8 /* PROPS */, [\"modelValue\", \"shortcuts\"])]), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      size: \"small\",\n      onClick: $setup.handleSearch,\n      class: \"filter-btn\"\n    }, {\n      default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"应用\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.resetFilters,\n      class: \"reset-btn\"\n    }, {\n      default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"重置\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.fetchVideoTasks,\n      class: \"refresh-btn\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_refresh)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])])]), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createVNode(_component_el_skeleton, {\n      rows: 10,\n      animated: \"\"\n    })])) : $setup.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createVNode(_component_el_alert, {\n      title: \"获取历史记录失败\",\n      type: \"error\",\n      description: $setup.error,\n      \"show-icon\": \"\"\n    }, null, 8 /* PROPS */, [\"description\"]), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.fetchVideoTasks\n    }, {\n      default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"重试\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])])) : $setup.videoTasks.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createVNode(_component_el_empty, {\n      description: \"暂无视频分析记录\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        class: \"custom-btn-primary\",\n        onClick: _cache[4] || (_cache[4] = $event => _ctx.$router.push('/video-upload'))\n      }, {\n        default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"上传新视频\")])),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.videoTasks,\n      style: {\n        \"width\": \"100%\"\n      },\n      border: \"\",\n      stripe: \"\",\n      \"default-sort\": {\n        prop: $setup.sortProp,\n        order: $setup.sortOrder\n      },\n      onSortChange: $setup.handleSortChange,\n      onSelectionChange: $setup.handleSelectionChange,\n      \"max-height\": \"calc(100vh - 350px)\",\n      class: \"custom-table\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        width: \"55\"\n      }, {\n        header: _withCtx(() => [_createVNode(_component_el_checkbox, {\n          modelValue: $setup.selectAll,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.selectAll = $event),\n          onChange: $setup.toggleSelectAll,\n          indeterminate: $setup.isIndeterminate\n        }, null, 8 /* PROPS */, [\"modelValue\", \"onChange\", \"indeterminate\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"videoName\",\n        label: \"视频名称\",\n        \"min-width\": \"220\",\n        sortable: \"\"\n      }, {\n        default: _withCtx(scope => [scope.row.editing ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createVNode(_component_el_input, {\n          modelValue: scope.row.editName,\n          \"onUpdate:modelValue\": $event => scope.row.editName = $event,\n          size: \"small\",\n          placeholder: \"输入视频名称\",\n          onKeyup: _withKeys($event => $setup.confirmRename(scope.row), [\"enter\"])\n        }, {\n          append: _withCtx(() => [_createVNode(_component_el_button, {\n            onClick: $event => $setup.confirmRename(scope.row)\n          }, {\n            default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"确定\")])),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"onUpdate:modelValue\", \"onKeyup\"])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"span\", _hoisted_18, _toDisplayString($setup.getVideoDisplayName(scope.row)), 1 /* TEXT */), _createVNode(_component_el_button, {\n          type: \"primary\",\n          size: \"small\",\n          circle: \"\",\n          onClick: $event => $setup.startRename(scope.row),\n          class: \"rename-button\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Edit)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"completedAt\",\n        label: \"完成时间\",\n        width: \"180\",\n        sortable: \"\"\n      }, {\n        default: _withCtx(scope => [scope.row.status === 'completed' && scope.row.completedAt ? (_openBlock(), _createElementBlock(\"span\", _hoisted_19, _toDisplayString($setup.formatDate(scope.row.completedAt)), 1 /* TEXT */)) : scope.row.status === 'completed' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_20, _toDisplayString($setup.formatDate(scope.row.createdAt)), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_21, _toDisplayString(scope.row.status === 'processing' ? '分析中...' : scope.row.status === 'queued' ? '排队中...' : scope.row.status === 'failed' ? '分析失败' : '未完成'), 1 /* TEXT */))]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 添加分析人列 \"), _createVNode(_component_el_table_column, {\n        prop: \"username\",\n        label: \"分析人\",\n        width: \"120\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString(scope.row.username || '未知用户'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"status\",\n        label: \"状态\",\n        width: \"120\",\n        sortable: \"\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_tag, {\n          type: $setup.getStatusType(scope.row.status),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(scope.row.status)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"progress\",\n        label: \"进度\",\n        width: \"150\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_progress, {\n          percentage: scope.row.progress || 0,\n          status: $setup.getProgressStatus(scope.row.status),\n          \"stroke-width\": 10\n        }, null, 8 /* PROPS */, [\"percentage\", \"status\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"220\",\n        fixed: \"right\"\n      }, {\n        default: _withCtx(scope => [_createElementVNode(\"div\", _hoisted_22, [scope.row.status === 'completed' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          size: \"small\",\n          class: \"custom-btn-primary\",\n          onClick: $event => $setup.viewResult(scope.row)\n        }, {\n          default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\" 查看结果 \")])),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), ['queued', 'processing'].includes(scope.row.status) ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 1,\n          size: \"small\",\n          class: \"action-btn\",\n          onClick: $event => $setup.checkStatus(scope.row)\n        }, {\n          default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\" 查看进度 \")])),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), scope.row.status === 'failed' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 2,\n          size: \"small\",\n          class: \"action-btn-warning\",\n          onClick: $event => $setup.handleRetryAnalysis(scope.row)\n        }, {\n          default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\" 重试 \")])),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n          size: \"small\",\n          class: \"custom-btn-outline\",\n          onClick: $event => $setup.showDeleteConfirm(scope.row)\n        }, {\n          default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\" 删除 \")])),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\", \"default-sort\", \"onSortChange\", \"onSelectionChange\"])), [[_directive_loading, $setup.tableLoading]]), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [$setup.videoTasks.length > 0 ? (_openBlock(), _createBlock(_component_el_checkbox, {\n      key: 0,\n      modelValue: $setup.selectAll,\n      \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.selectAll = $event),\n      onChange: $setup.toggleSelectAll,\n      class: \"me-2\"\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"全选\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])) : _createCommentVNode(\"v-if\", true), $setup.selectedRows.length > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_25, \" 已选择 \" + _toDisplayString($setup.selectedRows.length) + \" 项 \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.selectedRows.length > 0 ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 2,\n      class: \"custom-btn-outline\",\n      size: \"small\",\n      onClick: $setup.handleBatchDelete\n    }, {\n      default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\" 批量删除 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, \"共 \" + _toDisplayString($setup.total) + \" 个记录\", 1 /* TEXT */), _createVNode(_component_el_pagination, {\n      background: \"\",\n      layout: \"prev, pager, next, sizes\",\n      total: $setup.total,\n      \"page-size\": $setup.pageSize,\n      \"current-page\": $setup.currentPage,\n      \"page-sizes\": [10, 20, 50, 100],\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange,\n      \"prev-text\": \"上一页\",\n      \"next-text\": \"下一页\"\n    }, null, 8 /* PROPS */, [\"total\", \"page-size\", \"current-page\", \"onSizeChange\", \"onCurrentChange\"])])])]))]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 批量删除确认对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"批量删除\",\n    modelValue: $setup.batchDeleteDialogVisible,\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.batchDeleteDialogVisible = $event),\n    width: \"400px\",\n    class: \"dark-theme-dialog\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_28, [_createVNode(_component_el_button, {\n      class: \"confirm-delete-btn\",\n      onClick: $setup.confirmBatchDelete,\n      loading: $setup.batchOperationLoading\n    }, {\n      default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\" 确认删除 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"]), _createVNode(_component_el_button, {\n      class: \"cancel-btn\",\n      onClick: _cache[7] || (_cache[7] = $event => $setup.batchDeleteDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */\n    })])]),\n    default: _withCtx(() => [_createElementVNode(\"span\", null, \"确定要删除选中的\" + _toDisplayString($setup.selectedRows.length) + \"条记录吗？此操作不可恢复！\", 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 单条记录删除确认对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"确认删除\",\n    modelValue: $setup.deleteDialogVisible,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.deleteDialogVisible = $event),\n    width: \"400px\",\n    class: \"dark-theme-dialog\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_29, [_createVNode(_component_el_button, {\n      class: \"confirm-delete-btn\",\n      onClick: $setup.confirmDelete\n    }, {\n      default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\" 确定删除 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      class: \"cancel-btn\",\n      onClick: _cache[9] || (_cache[9] = $event => $setup.deleteDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */\n    })])]),\n    default: _withCtx(() => [_cache[33] || (_cache[33] = _createElementVNode(\"span\", null, \"确定要删除这条分析记录吗？此操作无法恢复。\", -1 /* HOISTED */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "$setup", "videoTasks", "length", "_hoisted_4", "_component_el_checkbox", "modelValue", "selectAll", "_cache", "$event", "onChange", "toggleSelectAll", "indeterminate", "isIndeterminate", "default", "_createTextVNode", "_", "_createCommentVNode", "_component_el_button", "disabled", "hasSelected", "onClick", "handleBatchDelete", "_component_el_icon", "_component_delete", "_component_router_link", "to", "_component_plus", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_component_el_input", "searchQuery", "placeholder", "clearable", "onClear", "handleSearch", "onKeyup", "_with<PERSON><PERSON><PERSON>", "size", "prefix", "_component_search", "isAdmin", "_hoisted_8", "_component_el_select", "userFilter", "_component_el_option", "label", "value", "_Fragment", "_renderList", "userList", "user", "_createBlock", "id", "username", "_hoisted_9", "_component_el_date_picker", "date<PERSON><PERSON><PERSON>", "type", "format", "shortcuts", "dateShortcuts", "editable", "_hoisted_10", "resetFilters", "fetchVideoTasks", "_component_refresh", "loading", "_hoisted_11", "_component_el_skeleton", "rows", "animated", "error", "_hoisted_12", "_component_el_alert", "title", "description", "_hoisted_13", "_hoisted_14", "_component_el_empty", "_ctx", "$router", "push", "_hoisted_15", "_component_el_table", "data", "style", "border", "stripe", "prop", "sortProp", "order", "sortOrder", "onSortChange", "handleSortChange", "onSelectionChange", "handleSelectionChange", "_component_el_table_column", "width", "sortable", "scope", "row", "editing", "_hoisted_16", "editName", "<PERSON><PERSON><PERSON><PERSON>", "append", "_hoisted_17", "_hoisted_18", "_toDisplayString", "getVideoDisplayName", "circle", "startRename", "_component_Edit", "status", "completedAt", "_hoisted_19", "formatDate", "_hoisted_20", "createdAt", "_hoisted_21", "_component_el_tag", "getStatusType", "getStatusText", "_component_el_progress", "percentage", "progress", "getProgressStatus", "fixed", "_hoisted_22", "viewResult", "includes", "checkStatus", "handleRetryAnalysis", "showDeleteConfirm", "tableLoading", "_hoisted_23", "_hoisted_24", "selectedRows", "_hoisted_25", "_hoisted_26", "_hoisted_27", "total", "_component_el_pagination", "background", "layout", "pageSize", "currentPage", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "batchDeleteDialogVisible", "footer", "_hoisted_28", "confirmBatchDelete", "batchOperationLoading", "deleteDialogVisible", "_hoisted_29", "confirmDelete"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\VideoHistory.vue"], "sourcesContent": ["<template>\n  <div class=\"video-history-container\">\n    <el-card class=\"history-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h2>视频分析历史</h2>\n          <div class=\"header-actions\">\n            <div class=\"selection-controls\" v-if=\"videoTasks.length > 0\">\n              <el-checkbox \n                v-model=\"selectAll\" \n                @change=\"toggleSelectAll\"\n                :indeterminate=\"isIndeterminate\"\n                class=\"wider-checkbox\"\n              >全选</el-checkbox>\n            </div>\n            <el-button \n              class=\"custom-btn-outline\" \n              :disabled=\"!hasSelected\" \n              @click=\"handleBatchDelete\"\n            >\n              <el-icon><delete /></el-icon> 批量删除\n            </el-button>\n            <router-link to=\"/video-upload\">\n              <el-button class=\"custom-btn-primary\">\n                <el-icon><plus /></el-icon> 上传新视频\n              </el-button>\n            </router-link>\n          </div>\n        </div>\n      </template>\n      \n      <!-- 筛选面板 -->\n      <div class=\"filter-panel\">\n        <div class=\"filter-row\">\n          <div class=\"filter-item\">\n            <span class=\"filter-label\">搜索：</span>\n            <el-input\n              v-model=\"searchQuery\"\n              placeholder=\"搜索视频名称\"\n              clearable\n              @clear=\"handleSearch\"\n              @keyup.enter=\"handleSearch\"\n              size=\"small\"\n              class=\"search-input\"\n            >\n              <template #prefix>\n                <el-icon class=\"el-input__icon\"><search /></el-icon>\n              </template>\n            </el-input>\n          </div>\n          \n          <!-- 管理员用户筛选功能 -->\n          <div v-if=\"isAdmin\" class=\"filter-item\">\n            <span class=\"filter-label\">用户：</span>\n            <el-select\n              v-model=\"userFilter\"\n              placeholder=\"用户筛选\"\n              clearable\n              @change=\"handleSearch\"\n              size=\"small\"\n              :popper-append-to-body=\"true\"\n              :reserve-keyword=\"false\"\n            >\n              <el-option label=\"全部用户\" value=\"\"></el-option>\n              <el-option\n                v-for=\"user in userList\"\n                :key=\"user.id\"\n                :label=\"user.username\"\n                :value=\"user.id\"\n              ></el-option>\n            </el-select>\n          </div>\n          \n          <div class=\"filter-item\">\n            <span class=\"filter-label\">日期：</span>\n            <el-date-picker\n              v-model=\"dateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              format=\"YYYY-MM-DD\"\n              value-format=\"YYYY-MM-DD\"\n              :shortcuts=\"dateShortcuts\"\n              :editable=\"false\"\n              size=\"small\"\n              class=\"date-picker\"\n            >\n            </el-date-picker>\n          </div>\n          \n          <div class=\"filter-actions\">\n            <el-button type=\"primary\" size=\"small\" @click=\"handleSearch\" class=\"filter-btn\">应用</el-button>\n            <el-button size=\"small\" @click=\"resetFilters\" class=\"reset-btn\">重置</el-button>\n            <el-button size=\"small\" @click=\"fetchVideoTasks\" class=\"refresh-btn\">\n              <el-icon><refresh /></el-icon>\n            </el-button>\n          </div>\n        </div>\n      </div>\n      \n      <div v-if=\"loading\" class=\"loading-container\">\n        <el-skeleton :rows=\"10\" animated />\n      </div>\n      \n      <div v-else-if=\"error\" class=\"error-container\">\n        <el-alert\n          title=\"获取历史记录失败\"\n          type=\"error\"\n          :description=\"error\"\n          show-icon\n        />\n        <div class=\"error-actions\">\n          <el-button type=\"primary\" @click=\"fetchVideoTasks\">重试</el-button>\n        </div>\n      </div>\n      \n      <div v-else-if=\"videoTasks.length === 0\" class=\"empty-container\">\n        <el-empty description=\"暂无视频分析记录\">\n          <el-button class=\"custom-btn-primary\" @click=\"$router.push('/video-upload')\">上传新视频</el-button>\n        </el-empty>\n      </div>\n      \n      <div v-else>\n        <el-table\n          :data=\"videoTasks\"\n          style=\"width: 100%\"\n          border\n          stripe\n          :default-sort=\"{ prop: sortProp, order: sortOrder }\"\n          @sort-change=\"handleSortChange\"\n          @selection-change=\"handleSelectionChange\"\n          v-loading=\"tableLoading\"\n          max-height=\"calc(100vh - 350px)\"\n          class=\"custom-table\"\n        >\n          <el-table-column type=\"selection\" width=\"55\">\n            <template #header>\n              <el-checkbox \n                v-model=\"selectAll\" \n                @change=\"toggleSelectAll\"\n                :indeterminate=\"isIndeterminate\"\n              />\n            </template>\n          </el-table-column>\n          \n          <el-table-column prop=\"videoName\" label=\"视频名称\" min-width=\"220\" sortable>\n            <template #default=\"scope\">\n              <div v-if=\"scope.row.editing\">\n                <el-input \n                  v-model=\"scope.row.editName\" \n                  size=\"small\" \n                  placeholder=\"输入视频名称\"\n                  @keyup.enter=\"confirmRename(scope.row)\"\n                >\n                  <template #append>\n                    <el-button @click=\"confirmRename(scope.row)\">确定</el-button>\n                  </template>\n                </el-input>\n              </div>\n              <div v-else class=\"video-name-container\">\n                <span class=\"video-name\">{{ getVideoDisplayName(scope.row) }}</span>\n                <el-button \n                  type=\"primary\" \n                  size=\"small\" \n                  circle \n                  @click=\"startRename(scope.row)\"\n                  class=\"rename-button\"\n                >\n                  <el-icon><Edit /></el-icon>\n                </el-button>\n              </div>\n            </template>\n          </el-table-column>\n          \n          <el-table-column prop=\"completedAt\" label=\"完成时间\" width=\"180\" sortable>\n            <template #default=\"scope\">\n              <span v-if=\"scope.row.status === 'completed' && scope.row.completedAt\">\n                {{ formatDate(scope.row.completedAt) }}\n              </span>\n              <span v-else-if=\"scope.row.status === 'completed'\">\n                {{ formatDate(scope.row.createdAt) }}\n              </span>\n              <span v-else class=\"text-muted\">\n                {{ scope.row.status === 'processing' ? '分析中...' :\n                   scope.row.status === 'queued' ? '排队中...' :\n                   scope.row.status === 'failed' ? '分析失败' : '未完成' }}\n              </span>\n            </template>\n          </el-table-column>\n          \n          <!-- 添加分析人列 -->\n          <el-table-column prop=\"username\" label=\"分析人\" width=\"120\">\n            <template #default=\"scope\">\n              {{ scope.row.username || '未知用户' }}\n            </template>\n          </el-table-column>\n          \n          <el-table-column prop=\"status\" label=\"状态\" width=\"120\" sortable>\n            <template #default=\"scope\">\n              <el-tag :type=\"getStatusType(scope.row.status)\" size=\"small\">\n                {{ getStatusText(scope.row.status) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          \n          <el-table-column prop=\"progress\" label=\"进度\" width=\"150\">\n            <template #default=\"scope\">\n              <el-progress \n                :percentage=\"scope.row.progress || 0\" \n                :status=\"getProgressStatus(scope.row.status)\"\n                :stroke-width=\"10\"\n              />\n            </template>\n          </el-table-column>\n          \n          <el-table-column label=\"操作\" width=\"220\" fixed=\"right\">\n            <template #default=\"scope\">\n              <div class=\"table-actions\">\n                <el-button \n                  size=\"small\" \n                  class=\"custom-btn-primary\" \n                  v-if=\"scope.row.status === 'completed'\"\n                  @click=\"viewResult(scope.row)\"\n                >\n                  查看结果\n                </el-button>\n                \n                <el-button \n                  size=\"small\" \n                  class=\"action-btn\"\n                  v-if=\"['queued', 'processing'].includes(scope.row.status)\"\n                  @click=\"checkStatus(scope.row)\"\n                >\n                  查看进度\n                </el-button>\n                \n                <el-button \n                  size=\"small\" \n                  class=\"action-btn-warning\" \n                  v-if=\"scope.row.status === 'failed'\"\n                  @click=\"handleRetryAnalysis(scope.row)\"\n                >\n                  重试\n                </el-button>\n                \n                <el-button \n                  size=\"small\" \n                  class=\"custom-btn-outline\"\n                  @click=\"showDeleteConfirm(scope.row)\"\n                >\n                  删除\n                </el-button>\n              </div>\n            </template>\n          </el-table-column>\n        </el-table>\n        \n        <div class=\"pagination-container\">\n          <div class=\"batch-actions\">\n            <el-checkbox v-if=\"videoTasks.length > 0\" v-model=\"selectAll\" @change=\"toggleSelectAll\" class=\"me-2\">全选</el-checkbox>\n            <span v-if=\"selectedRows.length > 0\" class=\"selected-info\">\n              已选择 {{ selectedRows.length }} 项\n            </span>\n            <el-button \n              v-if=\"selectedRows.length > 0\" \n              class=\"custom-btn-outline\"\n              size=\"small\" \n              @click=\"handleBatchDelete\"\n            >\n              批量删除\n            </el-button>\n          </div>\n          <div class=\"pagination-wrapper\">\n            <div class=\"pagination-info\">共 {{ total }} 个记录</div>\n            <el-pagination\n              background\n              layout=\"prev, pager, next, sizes\"\n              :total=\"total\"\n              :page-size=\"pageSize\"\n              :current-page=\"currentPage\"\n              :page-sizes=\"[10, 20, 50, 100]\"\n              @size-change=\"handleSizeChange\"\n              @current-change=\"handleCurrentChange\"\n              prev-text=\"上一页\"\n              next-text=\"下一页\"\n            />\n          </div>\n        </div>\n      </div>\n    </el-card>\n    \n    <!-- 批量删除确认对话框 -->\n    <el-dialog\n      title=\"批量删除\"\n      v-model=\"batchDeleteDialogVisible\"\n      width=\"400px\"\n      class=\"dark-theme-dialog\"\n      :close-on-click-modal=\"false\"\n    >\n      <span>确定要删除选中的{{ selectedRows.length }}条记录吗？此操作不可恢复！</span>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button class=\"confirm-delete-btn\" @click=\"confirmBatchDelete\" :loading=\"batchOperationLoading\">\n            确认删除\n          </el-button>\n          <el-button class=\"cancel-btn\" @click=\"batchDeleteDialogVisible = false\">取消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 单条记录删除确认对话框 -->\n    <el-dialog\n      title=\"确认删除\"\n      v-model=\"deleteDialogVisible\"\n      width=\"400px\"\n      class=\"dark-theme-dialog\"\n      :close-on-click-modal=\"false\"\n    >\n      <span>确定要删除这条分析记录吗？此操作无法恢复。</span>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button class=\"confirm-delete-btn\" @click=\"confirmDelete\">\n            确定删除\n          </el-button>\n          <el-button class=\"cancel-btn\" @click=\"deleteDialogVisible = false\">取消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, computed, watch, watchEffect, nextTick } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { getVideoTaskList, retryVideoAnalysis, updateVideoName, getVideoResultId } from '@/api/video';\nimport { ElMessage } from 'element-plus';\nimport { Search, Edit, Delete, Plus, Refresh } from '@element-plus/icons-vue';\nimport { batchDeleteHistory } from '@/api/traffic';\nimport FileSaver from 'file-saver';\nimport * as XLSX from 'xlsx';\nimport { useStore } from 'vuex';\nimport apiClient from '@/utils/http-common'; // 导入apiClient\nimport { apiService } from '@/api'; // 导入apiService用于获取用户列表\n\nexport default {\n  name: 'VideoHistory',\n  components: {\n    Search,\n    Edit,\n    Delete,\n    Plus,\n    Refresh\n  },\n  setup() {\n    const router = useRouter();\n    const store = useStore();\n    const videoTasks = ref([]);\n    const loading = ref(true);\n    const tableLoading = ref(false);\n    const error = ref('');\n    \n    // 分页参数\n    const total = ref(0);\n    const pageSize = ref(10);\n    const currentPage = ref(1);\n    \n    // 排序参数\n    const sortProp = ref('createdAt');\n    const sortOrder = ref('descending');\n    \n    // 筛选参数\n    const searchQuery = ref('');\n    const userFilter = ref('');\n    const userList = ref([]);\n    const dateRange = ref([]);\n    \n    // 日期快捷选项\n    const dateShortcuts = [\n      {\n        text: '最近一周',\n        value: () => {\n          const end = new Date();\n          const start = new Date();\n          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n          return [formatToYYYYMMDD(start), formatToYYYYMMDD(end)];\n        },\n      },\n      {\n        text: '最近一个月',\n        value: () => {\n          const end = new Date();\n          const start = new Date();\n          start.setMonth(start.getMonth() - 1);\n          return [formatToYYYYMMDD(start), formatToYYYYMMDD(end)];\n        },\n      },\n      {\n        text: '最近三个月',\n        value: () => {\n          const end = new Date();\n          const start = new Date();\n          start.setMonth(start.getMonth() - 3);\n          return [formatToYYYYMMDD(start), formatToYYYYMMDD(end)];\n        },\n      }\n    ];\n\n    // 格式化日期为YYYY-MM-DD\n    const formatToYYYYMMDD = (date) => {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      return `${year}-${month}-${day}`;\n    };\n    \n    // 选择相关\n    const selectedRows = ref([]);\n    const hasSelected = computed(() => selectedRows.value.length > 0);\n    const selectAll = ref(false);\n    const isIndeterminate = ref(false);\n    \n    // 批量操作相关\n    const batchDeleteDialogVisible = ref(false);\n    const batchOperationLoading = ref(false);\n    \n    // 单条删除相关\n    const deleteDialogVisible = ref(false);\n    const currentDeleteTask = ref(null);\n    \n    // 全选/取消全选\n    const toggleSelectAll = (val) => {\n      if (val) {\n        // 获取当前页上的所有记录\n        selectedRows.value = [...videoTasks.value];\n      } else {\n        selectedRows.value = [];\n      }\n      isIndeterminate.value = false;\n    };\n    \n    // 用户权限相关计算属性\n    const isAdmin = computed(() => {\n      // 强制从localStorage获取最新用户信息\n      try {\n        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');\n        const role = storedUser?.role?.toLowerCase() || store.state.user?.role?.toLowerCase();\n        return role === 'admin' || role === 'administrator';\n      } catch (e) {\n        console.error('解析用户角色出错:', e);\n        const role = store.state.user?.role?.toLowerCase();\n        return role === 'admin' || role === 'administrator';\n      }\n    });\n    \n    const currentUser = computed(() => {\n      try {\n        // 优先使用localStorage中的最新用户信息\n        const storedUser = JSON.parse(localStorage.getItem('user') || '{}');\n        if (storedUser && storedUser.id) {\n          return storedUser;\n        }\n      } catch (e) {\n        console.error('解析当前用户信息出错:', e);\n      }\n      return store.state.user;\n    });\n    \n    // 监听筛选条件变化，重置页码并重新获取数据\n    watch([searchQuery, userFilter, dateRange], () => {\n      currentPage.value = 1;\n      fetchVideoTasks();  // 自动刷新数据以反映筛选结果\n    }, { deep: true });\n    \n    // 监听用户ID变化，立即重新获取数据\n    watch(() => store.state.user?.id, (newUserId, oldUserId) => {\n      if (newUserId !== oldUserId && newUserId) {\n        console.log(`检测到用户变化: ${oldUserId} -> ${newUserId}，立即刷新数据`);\n        // 重置分页\n        currentPage.value = 1;\n        // 重新获取数据\n        fetchVideoTasks();\n      }\n    });\n    \n    // 视频名称相关\n    const getVideoDisplayName = (task) => {\n      // 添加调试日志\n      console.log('获取显示名称，原始数据:', {\n        taskId: task.taskId,\n        task_id: task.task_id, \n        videoName: task.videoName,\n        _id: task._id,\n        hasVideoName: !!task.videoName\n      });\n\n      // 优先使用重命名后的videoName字段（如果存在且有内容）\n      if (task.videoName && typeof task.videoName === 'string' && task.videoName.trim() !== '') {\n        console.log('使用videoName:', task.videoName);\n        return String(task.videoName);\n      } \n      // 默认使用task_id作为显示名称\n      else if (task.task_id) {\n        console.log('使用task_id:', task.task_id);\n        return task.task_id;\n      }\n      // 备选方案：如果没有task_id字段，使用其他ID字段\n      else if (task.taskId) {\n        console.log('使用taskId:', task.taskId);\n        return task.taskId;\n      }\n      // 最后备选：使用其他ID字段\n      else {\n        const fallback = task._id || '';\n        console.log('使用备选ID:', fallback);\n        return fallback;\n      }\n    };\n    \n    // 开始重命名\n    const startRename = (task) => {\n      task.editing = true;\n      task.editName = task.videoName || '';\n    };\n    \n    // 确认重命名\n    const confirmRename = async (task) => {\n      try {\n        tableLoading.value = true;\n        \n        // 调用API保存到服务器\n        const response = await updateVideoName(task.taskId, task.editName);\n        \n        // 只要HTTP状态码是2xx，就认为成功\n        if (response && response.status >= 200 && response.status < 300) {\n          console.log('重命名成功，新名称:', task.editName);\n          \n          // 更新本地数据 - 只设置videoName字段\n          task.videoName = task.editName;\n          task.editing = false;\n          ElMessage.success('视频重命名成功');\n          \n          // 强制刷新列表，确保显示最新数据\n          setTimeout(() => {\n            fetchVideoTasks();  \n          }, 300);\n        } else {\n          throw new Error(response?.data?.message || '重命名失败');\n        }\n      } catch (err) {\n        ElMessage.error('视频重命名失败: ' + (err.message || '未知错误'));\n        console.error('重命名错误:', err);\n      } finally {\n        tableLoading.value = false;\n        // 确保无论成功失败，都退出编辑模式\n        if (task.editing) {\n          task.editing = false;\n        }\n      }\n    };\n    \n    // 获取视频任务列表\n    const fetchVideoTasks = async () => {\n      console.log('[获取数据] 开始获取视频任务列表');\n\n      // 检查组件是否已准备就绪（仅在组件初始化后检查）\n      if (isComponentReady.value === false) {\n        console.log('[获取数据] 组件尚未准备就绪，跳过数据获取');\n        return;\n      }\n\n      // 使用改进的用户认证检查\n      const authResult = await ensureUserAuthenticated();\n      if (!authResult) {\n        console.error('[获取数据] 用户认证失败');\n        error.value = '用户认证失败，请重新登录';\n        loading.value = false;\n        router.push('/login?redirect=' + encodeURIComponent(router.currentRoute.value.fullPath));\n        return;\n      }\n\n      // 再次确认用户信息完整性\n      if (!currentUser.value || !currentUser.value.id) {\n        console.error('[获取数据] 用户信息不完整，无法继续');\n        error.value = '无法获取当前用户信息，权限验证失败';\n        loading.value = false;\n        return;\n      }\n\n      console.log(`[获取数据] 用户认证成功: ${currentUser.value.username}(${currentUser.value.id}), 角色: ${currentUser.value.role}`);\n\n      loading.value = true;\n      error.value = '';\n      \n      try {\n        // 构建查询参数\n        const params = {\n          page: currentPage.value - 1, // 后端从0开始计算页码\n          pageSize: pageSize.value,\n          sortBy: sortProp.value,\n          sortOrder: sortOrder.value === 'ascending' ? 'asc' : 'desc',\n          type: 'video' // 明确指定类型为video\n        };\n        \n        if (searchQuery.value) {\n          params.query = searchQuery.value;\n        }\n        \n        // 添加用户筛选参数\n        if (userFilter.value && isAdmin.value) {\n          params.userId = userFilter.value;\n          console.log(`筛选用户ID: ${userFilter.value}`);\n        }\n        \n        // 改进日期范围筛选\n        if (dateRange.value && dateRange.value.length === 2) {\n          const startDate = dateRange.value[0];\n          const endDate = dateRange.value[1];\n          \n          // 格式化日期并添加时间部分\n          params.startDate = `${startDate} 00:00:00`;  // 开始日期的开始时刻\n          params.endDate = `${endDate} 23:59:59`;    // 结束日期的结束时刻\n          \n          console.log(`筛选日期范围: ${params.startDate} 至 ${params.endDate} (字段: createdAt)`);\n          ElMessage.info(`正在筛选日期范围: ${startDate} 至 ${endDate}`);\n        }\n        \n        // 获取最新的用户状态\n        const isCurrentAdmin = isAdmin.value;\n        const currentUserInfo = currentUser.value;\n        \n        // 严格控制权限：非管理员用户必须添加用户ID过滤\n        if (!isCurrentAdmin) {\n          if (!currentUserInfo || !currentUserInfo.id) {\n            throw new Error('无法获取当前用户信息，权限验证失败');\n          }\n          // 添加用户ID筛选条件，确保只查看自己的记录\n          params.userId = currentUserInfo.id;\n          params.user_id = currentUserInfo.id; // 添加这个兼容字段\n          console.log(`用户权限: 普通用户 ${currentUserInfo.username}(${currentUserInfo.id})，只显示自己的记录`);\n        } else {\n          console.log(`用户权限: 管理员 ${currentUserInfo?.username || 'unknown'}，显示所有记录`);\n        }\n        \n        // 添加更详细的日志\n        console.log('请求参数:', params);\n        console.log('当前用户信息:', currentUser.value);\n        console.log('认证令牌前20字符:', localStorage.getItem('auth_token')?.substring(0, 20));\n        \n        const headers = {};\n        const token = localStorage.getItem('auth_token');\n        if (token) {\n          headers['Authorization'] = `Bearer ${token}`;\n        }\n        \n        const response = await getVideoTaskList(params, headers);\n        \n        console.log('后端原始响应数据:', response.data); // 添加日志记录原始响应\n        \n        if (response && response.data) {\n          // 兼容多种返回格式\n          let rawTasks = response.data.results || response.data.tasks || [];\n          \n          if (rawTasks.length === 0 && response.data.data) {\n            // 处理可能的嵌套data对象\n            rawTasks = response.data.data.results || response.data.data.tasks || [];\n          }\n          \n          // 应用数据映射处理每一条记录\n          videoTasks.value = rawTasks.map(task => mapTaskData(task));\n          \n          // 客户端权限二次验证，确保数据安全\n          if (!isCurrentAdmin && currentUserInfo) {\n            const userId = currentUserInfo.id;\n            const beforeFilter = videoTasks.value.length;\n            \n            // 改进过滤逻辑，确保同时检查userId和user_id字段\n            videoTasks.value = videoTasks.value.filter(task => {\n              return task.userId === userId || task.user_id === userId;\n            });\n            \n            const afterFilter = videoTasks.value.length;\n            \n            if (beforeFilter !== afterFilter) {\n\n            }\n            \n            // 使用过滤后的实际记录数作为总数\n            total.value = afterFilter;\n          } else {\n            // 管理员用户使用服务器返回的总数或应用额外的客户端筛选\n            if (isCurrentAdmin && userFilter.value) {\n              // 确保客户端也应用用户筛选，防止后端筛选未生效\n              const beforeFilter = videoTasks.value.length;\n              videoTasks.value = videoTasks.value.filter(task => task.userId === userFilter.value);\n              const afterFilter = videoTasks.value.length;\n              \n              if (beforeFilter !== afterFilter) {\n                console.log(`客户端额外筛选: 筛选用户ID ${userFilter.value}，从 ${beforeFilter} 条记录过滤到 ${afterFilter} 条`);\n              }\n              // 更新总数为实际过滤后的记录数\n              total.value = afterFilter;\n            } else {\n              // 没有进行用户筛选或者服务端已正确处理\n              total.value = response.data.total || videoTasks.value.length;\n            }\n          }\n          \n        } else {\n          throw new Error('获取视频任务列表失败');\n        }\n      } catch (err) {\n        error.value = err.message || '获取视频任务列表失败';\n        console.error('获取视频任务列表错误:', err);\n      } finally {\n        loading.value = false;\n      }\n    };\n    \n    // 格式化日期\n    const formatDate = (dateString) => {\n      if (!dateString) return '未知';\n      const date = new Date(dateString);\n      return new Intl.DateTimeFormat('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit',\n        hour12: false\n      }).format(date);\n    };\n    \n    // 获取状态类型\n    const getStatusType = (status) => {\n      if (status === 'completed') return 'success';\n      if (status === 'processing') return 'primary';\n      if (status === 'queued') return 'info';\n      if (status === 'failed') return 'danger';\n      return 'info';\n    };\n    \n    // 获取状态文本\n    const getStatusText = (status) => {\n      if (status === 'completed') return '已完成';\n      if (status === 'processing') return '处理中';\n      if (status === 'queued') return '排队中';\n      if (status === 'failed') return '失败';\n      return '未知';\n    };\n    \n    // 获取进度条状态\n    const getProgressStatus = (status) => {\n      if (status === 'completed') return 'success';\n      if (status === 'failed') return 'exception';\n      return '';\n    };\n    \n    // 查看结果 - 使用后端智能ID转换接口\n    const viewResult = async (task) => {\n      try {\n\n        \n        // 获取任务ID，优先使用UUID格式（taskId, task_id）\n        const resultId = task.taskId || task.task_id || task.resultId || task.result_id || task.id || task._id;\n        \n        if (!resultId) {\n          ElMessage.warning('没有找到有效的结果ID，无法查看结果');\n          return;\n        }\n        \n\n        \n        // 使用通用ID查询路径，让后端处理ID转换\n        router.push(`/video-result/id/${resultId}`);\n      } catch (error) {\n\n        ElMessage.error('查看结果失败: ' + error.message);\n      }\n    };\n\n    // 辅助函数：确定ID的来源字段，用于调试\n    const determineIdSource = (task) => {\n      if (task.task_id) return 'task_id';\n      if (task.taskId) return 'taskId';\n      if (task.resultId) return 'resultId';\n      if (task._id) return '_id';\n      if (task.id) return 'id';\n      return '未知';\n    };\n    \n    // 查看状态\n    const checkStatus = (task) => {\n      router.push(`/video-status/${task.taskId}`);\n    };\n    \n    // 重试分析 (函数名已改为handleRetryAnalysis，避免与API函数冲突)\n    const handleRetryAnalysis = async (task) => {\n      try {\n        const response = await retryVideoAnalysis(task.taskId);\n        \n        if (response && response.data && response.data.success) {\n          ElMessage.success('已重新提交分析任务');\n          // 跳转到状态页面\n          router.push(`/video-status/${task.taskId}`);\n        } else {\n          throw new Error('重新分析失败');\n        }\n      } catch (err) {\n        ElMessage.error(err.message || '重新分析失败');\n      }\n    };\n    \n    // 显示删除确认对话框\n    const showDeleteConfirm = (task) => {\n      currentDeleteTask.value = task;\n      deleteDialogVisible.value = true;\n    };\n    \n    // 确认删除单条记录\n    const confirmDelete = async () => {\n      if (!currentDeleteTask.value) return;\n      \n      try {\n        tableLoading.value = true;\n        \n        // 获取任务ID，优先使用MongoDB ObjectId格式\n        const taskId = currentDeleteTask.value.taskId || \n                      currentDeleteTask.value.task_id || \n                      currentDeleteTask.value.id || \n                      currentDeleteTask.value._id;\n        \n        if (!taskId) {\n          throw new Error('无法获取记录的ID');\n        }\n        \n        console.log('正在删除任务:', taskId);\n        console.log('ID格式分析:', {\n          id: taskId, \n          is_uuid: taskId.includes('-'),\n          is_objectid: /^[0-9a-f]{24}$/i.test(taskId),\n          length: taskId.length\n        });\n        \n        // 使用批量删除API，传递单个ID\n        const response = await batchDeleteHistory([taskId], 'video');\n        \n        if (response && response.status >= 200 && response.status < 300) {\n          ElMessage.success('删除成功');\n          // 短暂延迟后刷新数据，确保后端处理完成\n          setTimeout(() => {\n            fetchVideoTasks();\n          }, 300);\n        } else {\n          throw new Error(response?.data?.message || '删除失败');\n        }\n      } catch (err) {\n        console.error('删除单条记录失败:', err);\n        ElMessage.error('删除失败: ' + (err.message || '未知错误'));\n      } finally {\n        tableLoading.value = false;\n        deleteDialogVisible.value = false;\n        currentDeleteTask.value = null;\n      }\n    };\n    \n    // 处理行选择变化\n    const handleSelectionChange = (rows) => {\n      selectedRows.value = rows;\n      \n      // 更新全选状态\n      const allSelected = rows.length === videoTasks.value.length && rows.length > 0;\n      const partiallySelected = rows.length > 0 && rows.length < videoTasks.value.length;\n      \n      selectAll.value = allSelected;\n      isIndeterminate.value = partiallySelected;\n    };\n    \n    // 显示批量删除对话框\n    const handleBatchDelete = () => {\n      if (selectedRows.value.length === 0) {\n        ElMessage.warning('请先选择要删除的记录');\n        return;\n      }\n      batchDeleteDialogVisible.value = true;\n    };\n    \n    // 确认批量删除\n    const confirmBatchDelete = async () => {\n      try {\n        batchOperationLoading.value = true;\n        \n        // 获取所有选中行的ID，优先使用MongoDB ObjectId格式\n        const taskIds = selectedRows.value.map(row => {\n          return row.taskId || row.task_id || row.id || row._id;\n        }).filter(id => id); // 确保没有undefined或null值\n        \n        if (taskIds.length === 0) {\n          throw new Error('无法获取选中记录的ID');\n        }\n        \n\n        \n        // 调用批量删除API，使用查询参数方式\n        const response = await batchDeleteHistory(taskIds, 'video');\n        console.log('批量删除响应:', response);\n        \n        // 基本的成功响应检查\n        if (response && response.status >= 200 && response.status < 300) {\n          ElMessage.success(`成功删除${taskIds.length}条记录`);\n          batchDeleteDialogVisible.value = false;\n          // 短暂延迟后刷新数据，确保后端处理完成\n          setTimeout(() => {\n            fetchVideoTasks();\n          }, 300);\n        } else {\n          console.warn('批量删除返回值不符合预期:', response);\n          throw new Error(response?.data?.message || '批量删除返回状态异常');\n        }\n      } catch (err) {\n        console.error('批量删除失败:', err);\n        ElMessage.error('批量删除失败: ' + (err.message || '未知错误'));\n      } finally {\n        batchOperationLoading.value = false;\n        batchDeleteDialogVisible.value = false;\n      }\n    };\n    \n    // 导出数据为Excel\n    const exportData = async () => {\n      try {\n        tableLoading.value = true;\n        ElMessage.info('正在准备导出数据...');\n        \n        // 导出前强制刷新一次用户信息\n        try {\n          const storedUser = localStorage.getItem('user');\n          if (storedUser) {\n            const userInfo = JSON.parse(storedUser);\n            // 每次请求前强制更新store中的用户信息\n            store.commit('SET_USER', userInfo);\n            console.log('导出前刷新用户信息：', userInfo.username, userInfo.id);\n          }\n        } catch (err) {\n          console.warn('刷新用户信息失败:', err);\n        }\n        \n        // 构建完整的导出数据，不受分页限制\n        const exportParams = {\n          page: 0,\n          pageSize: 1000, // 导出更多数据\n          sortBy: sortProp.value,\n          sortOrder: sortOrder.value === 'ascending' ? 'asc' : 'desc'\n        };\n        \n        if (searchQuery.value) {\n          exportParams.query = searchQuery.value;\n        }\n        \n        if (dateRange.value && dateRange.value.length === 2) {\n          exportParams.startDate = dateRange.value[0];\n          exportParams.endDate = dateRange.value[1];\n          \n          // 添加时间部分，确保完整的日期范围\n          exportParams.startDate += ' 00:00:00';  // 开始日期的开始时刻\n          exportParams.endDate += ' 23:59:59';    // 结束日期的结束时刻\n          \n          console.log('导出筛选日期范围:', exportParams.startDate, '至', exportParams.endDate);\n        }\n        \n        // 确保导出数据时也应用相同的权限规则\n        if (!isAdmin.value) {\n          if (!currentUser.value || !currentUser.value.id) {\n            throw new Error('无法获取当前用户信息，权限验证失败');\n          }\n          // 添加用户ID筛选条件，确保只导出自己的记录\n          exportParams.userId = currentUser.value.id;\n          exportParams.user_id = currentUser.value.id; // 添加兼容字段\n          console.log(`导出权限: 普通用户 ${currentUser.value.username}(${currentUser.value.id})，只导出自己的记录`);\n        } else {\n          console.log('导出权限: 管理员，导出所有记录');\n        }\n        \n        const response = await getVideoTaskList(exportParams);\n        \n        if (!response || !response.data || (!response.data.tasks && !response.data.results)) {\n          throw new Error('获取导出数据失败');\n        }\n        \n        // 修改获取导出数据的方式，适配后端返回的results字段\n        let rawExportTasks = response.data.results || response.data.tasks || [];\n        \n        if (rawExportTasks.length === 0 && response.data.data) {\n          // 处理可能的嵌套data对象\n          rawExportTasks = response.data.data.results || response.data.data.tasks || [];\n        }\n        \n        // 应用数据映射\n        const exportTasks = rawExportTasks.map(task => mapTaskData(task));\n        console.log(`处理后的导出任务: 共${exportTasks.length}条记录`);\n        \n        // 客户端权限二次验证，确保数据安全\n        let filteredExportTasks = exportTasks;\n        if (!isAdmin.value && currentUser.value) {\n          const userId = currentUser.value.id;\n          const beforeFilter = filteredExportTasks.length;\n          \n          // 改进过滤逻辑，同时检查userId和user_id\n          filteredExportTasks = filteredExportTasks.filter(task => {\n            return task.userId === userId || task.user_id === userId;\n          });\n          \n          const afterFilter = filteredExportTasks.length;\n          \n          if (beforeFilter !== afterFilter) {\n            console.warn(`导出权限问题: 服务器返回了未经授权的数据，已过滤 ${beforeFilter - afterFilter} 条记录`);\n          }\n          \n          // 更新用户界面显示的导出数量\n          ElMessage.success(`成功导出${afterFilter}条记录`);\n        } else {\n          // 管理员导出所有记录\n          ElMessage.success(`成功导出${filteredExportTasks.length}条记录`);\n        }\n        \n        // 将数据转换为Excel可用格式\n        const exportData = filteredExportTasks.map(task => ({\n          '视频名称': task.videoName || '未命名视频',\n          '上传时间': formatDate(task.createdAt),\n          '分析人': task.username || '未知用户',\n          '状态': getStatusText(task.status),\n          '进度': `${task.progress || 0}%`,\n          '任务ID': task.taskId,\n          '结果ID': task.resultId || ''\n        }));\n        \n        // 创建工作表\n        const worksheet = XLSX.utils.json_to_sheet(exportData);\n        const workbook = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(workbook, worksheet, '视频分析历史');\n        \n        // 生成Excel文件并下载\n        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });\n        const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\n        \n        const fileName = `视频分析历史_${new Date().toISOString().slice(0, 10)}.xlsx`;\n        FileSaver.saveAs(blob, fileName);\n        \n        // 这里不再需要通用提示，已在上方添加了个性化提示\n      } catch (err) {\n        ElMessage.error('导出失败: ' + (err.message || '未知错误'));\n        console.error('导出错误:', err);\n      } finally {\n        tableLoading.value = false;\n      }\n    };\n    \n    // 处理搜索\n    const handleSearch = () => {\n      currentPage.value = 1; // 重置为第一页\n      fetchVideoTasks();\n    };\n    \n    // 处理排序变化\n    const handleSortChange = (column) => {\n      if (column.prop && column.order) {\n        sortProp.value = column.prop;\n        sortOrder.value = column.order;\n        fetchVideoTasks();\n      }\n    };\n    \n    // 处理每页数量变化\n    const handleSizeChange = (size) => {\n      pageSize.value = size;\n      fetchVideoTasks();\n    };\n    \n    // 处理页码变化\n    const handleCurrentChange = (page) => {\n      currentPage.value = page;\n      fetchVideoTasks();\n    };\n    \n    // 重置筛选\n    const resetFilters = () => {\n      searchQuery.value = '';\n      userFilter.value = '';\n      dateRange.value = [];\n      currentPage.value = 1; // 重置为第一页\n      \n      ElMessage.success('筛选条件已重置');\n      fetchVideoTasks();\n    };\n    \n    // 改进用户列表加载逻辑，确保正确获取用户数据\n    const loadUserList = async () => {\n      // 获取最新的用户角色\n      const isCurrentAdmin = isAdmin.value;\n      \n      // 强化权限检查，只有管理员才尝试加载用户列表\n      if (!isCurrentAdmin) {\n        console.log('当前用户非管理员，跳过用户列表加载');\n        return;\n      }\n      \n      try {\n        console.log('开始加载用户列表，当前用户角色:', currentUser.value?.role);\n        \n        // 防止重复加载\n        if (userList.value.length > 0) {\n          console.log('用户列表已加载，跳过重复加载');\n          return;\n        }\n        \n        // 使用与UserManagement.vue中相同的方法获取用户列表\n        const response = await apiService.getUsers(1, 100); // 获取最多100个用户\n        \n        // 使用nextTick延迟处理数据，避免过快更新DOM\n        await nextTick();\n        \n        if (response.data && response.data.data) {\n          // 获取正确的嵌套数据结构\n          const responseData = response.data.data;\n          userList.value = responseData.items || [];\n          \n          // 添加用户列表加载验证\n          console.log(`用户列表加载完成：${userList.value.length} 个用户`); \n        } else if (response.data) {\n          // 兼容非嵌套结构\n          userList.value = response.data.items || [];\n          console.log(`用户列表加载完成(非嵌套结构)：${userList.value.length} 个用户`);\n        }\n      } catch (err) {\n        // 对于403权限错误，不向用户显示错误提示\n        if (err.response && err.response.status === 403) {\n          console.warn('用户无权加载用户列表（权限不足）');\n          return;\n        }\n        \n        console.error('加载用户列表失败:', err);\n        // 仅对管理员显示用户列表加载失败的提示\n        if (isCurrentAdmin) {\n          ElMessage.warning('无法加载用户列表');\n        }\n      }\n    };\n    \n    // 添加数据映射处理函数\n    const mapTaskData = (task) => {\n      // 确保任务数据有正确的字段映射\n      if (!task) return task;\n      \n      // 标准化字段\n      const mappedTask = { ...task };\n      \n      // 记录原始字段，方便调试\n      console.log('映射前原始字段:', Object.keys(task).join(', '));\n      \n      // MongoDB中，_id是ObjectId格式，task_id和result_id是UUID格式\n      \n      // 确保task_id字段存在 (UUID格式)\n      if (!mappedTask.task_id && task.taskId && task.taskId.includes('-')) {\n        mappedTask.task_id = task.taskId;\n        console.log(`映射task_id: ${mappedTask.task_id}`);\n      }\n      \n      // 确保前端组件可能仍然使用taskId和resultId的字段映射\n      // taskId 是指向任务的主键，始终应该是UUID格式\n      if (!mappedTask.taskId) {\n        mappedTask.taskId = mappedTask.task_id || mappedTask.id || '';\n        console.log(`ID映射: taskId=${mappedTask.taskId}`);\n      }\n      \n      // resultId 应该是指向结果的ID，优先使用result_id，如果没有则使用taskId（通常它们是相同的）\n      if (!mappedTask.resultId) {\n        mappedTask.resultId = mappedTask.result_id || mappedTask.task_id || \n                             mappedTask.taskId || '';\n        console.log(`ID映射: resultId=${mappedTask.resultId}`);\n      }\n      \n      // 视频名称映射 - 处理多种可能的名称字段\n      // 优先级：videoName > video_name > name > fileName > video_filename\n      if (task.videoName && task.videoName.trim() !== '') {\n        mappedTask.videoName = task.videoName;\n        console.log(`使用videoName字段: ${mappedTask.videoName}`);\n      } else if (task.video_name && task.video_name.trim() !== '') {\n        mappedTask.videoName = task.video_name;\n        console.log(`使用video_name字段: ${mappedTask.videoName}`);\n      } else if (task.name && task.name.trim() !== '') {\n        mappedTask.videoName = task.name;\n        console.log(`使用name字段: ${mappedTask.videoName}`);\n      } else if (task.fileName && task.fileName.trim() !== '') {\n        mappedTask.videoName = task.fileName;\n        console.log(`使用fileName字段: ${mappedTask.videoName}`);\n      } else if (task.video_filename && task.video_filename.trim() !== '') {\n        // 从文件名中提取有意义的部分\n        const filename = task.video_filename;\n        // 移除扩展名和前缀，保留可读部分\n        const cleanName = filename.replace(/^video_\\d+_/, '').replace(/\\.\\w+$/, '');\n        mappedTask.videoName = cleanName;\n        console.log(`从video_filename提取名称: ${mappedTask.videoName}`);\n      }\n      \n      // 将所有可能的视频名称相关字段添加到日志中\n      console.log('所有可能的名称字段:', {\n        videoName: task.videoName,\n        video_name: task.video_name,\n        name: task.name,\n        fileName: task.fileName,\n        video_filename: task.video_filename\n      });\n      \n      // 进度映射\n      if (mappedTask.progress === undefined || mappedTask.progress === null) {\n        mappedTask.progress = mappedTask.analysisProgress !== undefined ? \n                             mappedTask.analysisProgress : \n                             (mappedTask.status === 'completed' ? 100 : 0);\n      }\n      \n      // 日期映射\n      if (!mappedTask.createdAt) {\n        mappedTask.createdAt = mappedTask.created_at || \n                              mappedTask.createTime || \n                              mappedTask.create_time || \n                              mappedTask.uploadTime || \n                              new Date().toISOString();\n      }\n      \n      // 添加完成时间映射 - 优先使用实际完成时间\n      if (!mappedTask.completedAt) {\n        // 优先级：completed_at > completedAt > updateTime > updatedAt > updated_at\n        // 只有当状态为completed时才使用完成时间，否则使用创建时间\n        if (mappedTask.status === 'completed') {\n          mappedTask.completedAt = mappedTask.completed_at ||\n                                  mappedTask.completedAt ||\n                                  mappedTask.updateTime ||\n                                  mappedTask.updatedAt ||\n                                  mappedTask.updated_at;\n        }\n\n        // 如果没有找到完成时间或状态不是completed，使用创建时间作为备选\n        if (!mappedTask.completedAt) {\n          mappedTask.completedAt = mappedTask.createdAt ||\n                                  mappedTask.created_at;\n        }\n      }\n\n      // 记录完成时间映射结果，用于调试\n      console.log('完成时间映射结果:', {\n        status: mappedTask.status,\n        completed_at: task.completed_at,\n        updateTime: task.updateTime,\n        updatedAt: task.updatedAt,\n        updated_at: task.updated_at,\n        createdAt: task.createdAt,\n        created_at: task.created_at,\n        finalCompletedAt: mappedTask.completedAt\n      });\n      \n      // 状态映射\n      if (!mappedTask.status) {\n        mappedTask.status = mappedTask.analysisStatus || \n                           mappedTask.state || \n                           mappedTask.task_status || \n                           'unknown';\n      }\n      \n      // 确保用户ID字段 - 优先使用user_id\n      if (!mappedTask.userId) {\n        mappedTask.userId = mappedTask.user_id || \n                           mappedTask.uid || \n                           mappedTask.analyst_id || \n                           currentUser.value?.id || '';\n      }\n      \n      // 补充用户名\n      if (!mappedTask.username) {\n        mappedTask.username = mappedTask.analyst || \n                             mappedTask.userName || \n                             mappedTask.user_name || \n                             currentUser.value?.username || \n                             '未知用户';\n      }\n\n      // 记录ID映射结果，用于调试\n      console.log('ID映射结果:', {\n        _id: mappedTask._id,                  // MongoDB ObjectId\n        task_id: mappedTask.task_id,          // UUID\n        result_id: mappedTask.result_id,      // UUID\n        taskId: mappedTask.taskId,            // 前端使用\n        resultId: mappedTask.resultId,         // 前端使用\n        userId: mappedTask.userId,            // 用户ID\n        user_id: mappedTask.user_id           // 原始用户ID\n      });\n      \n      // 记录映射后的字段\n      console.log('映射后字段:', Object.keys(mappedTask).join(', '));\n      \n      // 添加关键字段日志\n      console.log('关键字段值:', {\n        taskId: mappedTask.taskId,\n        task_id: mappedTask.task_id, \n        videoName: mappedTask.videoName,\n        fileName: mappedTask.fileName,\n        status: mappedTask.status,\n        createdAt: mappedTask.createdAt\n      });\n      \n      return mappedTask;\n    };\n    \n    // 组件初始化状态标记\n    const isComponentReady = ref(false);\n\n    // 改进的用户认证管理函数\n    const ensureUserAuthenticated = async (retryCount = 0) => {\n      const maxRetries = 3;\n      const retryDelay = 200; // 200ms延迟\n\n      console.log(`[用户认证检查] 第${retryCount + 1}次尝试`);\n\n      // 检查用户状态是否已初始化\n      if (!currentUser.value || !currentUser.value.id) {\n        const storedUser = localStorage.getItem('user');\n        const storedToken = localStorage.getItem('auth_token');\n\n        console.log(`[用户认证检查] localStorage用户信息存在: ${!!storedUser}, token存在: ${!!storedToken}`);\n\n        if (storedUser && storedToken) {\n          try {\n            const userInfo = JSON.parse(storedUser);\n            if (userInfo && userInfo.id) {\n              // 确保更新store中的用户信息\n              store.commit('SET_USER', userInfo);\n              console.log(`[用户认证检查] 从localStorage恢复用户信息成功: ${userInfo.username}, 角色: ${userInfo.role}, ID: ${userInfo.id}`);\n\n              // 等待一个tick确保响应式更新完成\n              await nextTick();\n\n              // 再次检查currentUser是否已更新\n              if (currentUser.value && currentUser.value.id) {\n                return true;\n              } else {\n                console.warn('[用户认证检查] store更新后currentUser仍为空，可能存在响应式问题');\n              }\n            } else {\n              console.error('[用户认证检查] localStorage中的用户信息不完整:', userInfo);\n            }\n          } catch (err) {\n            console.error('[用户认证检查] 解析localStorage用户信息出错:', err);\n          }\n        }\n\n        // 如果还是没有用户信息且未达到最大重试次数，则重试\n        if (retryCount < maxRetries) {\n          console.log(`[用户认证检查] 用户信息不完整，${retryDelay}ms后进行第${retryCount + 2}次重试`);\n          await new Promise(resolve => setTimeout(resolve, retryDelay));\n          return await ensureUserAuthenticated(retryCount + 1);\n        }\n\n        console.error('[用户认证检查] 达到最大重试次数，用户认证失败');\n        return false;\n      }\n\n      // 即使当前有用户信息，也强制从localStorage刷新一次以确保数据一致性\n      try {\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          const userInfo = JSON.parse(storedUser);\n          // 仅当localStorage中的用户ID与当前不同时才更新\n          if (userInfo.id !== currentUser.value.id) {\n            console.log(`[用户认证检查] 检测到用户变化: ${currentUser.value.username}(${currentUser.value.id}) -> ${userInfo.username}(${userInfo.id})`);\n            store.commit('SET_USER', userInfo);\n            await nextTick(); // 等待响应式更新\n          }\n        }\n      } catch (err) {\n        console.warn('[用户认证检查] 刷新用户信息时出错:', err);\n      }\n\n      console.log(`[用户认证检查] 认证成功: ${currentUser.value?.username}(${currentUser.value?.id})`);\n      return true;\n    };\n    \n    // 改进的组件初始化函数\n    const initializeComponent = async () => {\n      console.log('[组件初始化] 开始初始化VideoHistory组件');\n\n      try {\n        // 第一步：强制同步用户状态\n        const storedUser = localStorage.getItem('user');\n        const storedToken = localStorage.getItem('auth_token');\n\n        if (storedUser && storedToken) {\n          try {\n            const userInfo = JSON.parse(storedUser);\n            if (userInfo && userInfo.id) {\n              store.commit('SET_USER', userInfo);\n              console.log(`[组件初始化] 同步用户信息: ${userInfo.username}, 角色: ${userInfo.role}, ID: ${userInfo.id}`);\n            }\n          } catch (e) {\n            console.error('[组件初始化] 解析用户信息出错:', e);\n          }\n        }\n\n        // 第二步：等待用户认证完成\n        const authResult = await ensureUserAuthenticated();\n        if (!authResult) {\n          console.error('[组件初始化] 用户认证失败，无法继续初始化');\n          error.value = '用户认证失败，请重新登录';\n          router.push('/login?redirect=' + encodeURIComponent(router.currentRoute.value.fullPath));\n          return;\n        }\n\n        // 第三步：标记组件准备就绪\n        isComponentReady.value = true;\n        console.log('[组件初始化] 组件准备就绪，开始获取数据');\n\n        // 第四步：获取视频任务数据\n        await fetchVideoTasks();\n\n        // 第五步：根据用户角色加载用户列表\n        setTimeout(async () => {\n          const isCurrentAdmin = isAdmin.value;\n          console.log(`[组件初始化] 用户角色检查: ${currentUser.value?.role || '未知'}, 是否管理员: ${isCurrentAdmin}`);\n\n          if (isCurrentAdmin) {\n            console.log('[组件初始化] 用户角色: admin，加载用户列表');\n            await loadUserList();\n          } else {\n            console.log(`[组件初始化] 用户角色: ${currentUser.value?.role || '未知'}，无需加载用户列表`);\n          }\n        }, 50);\n\n        console.log('[组件初始化] 组件初始化完成');\n      } catch (error) {\n        console.error('[组件初始化] 初始化过程中发生错误:', error);\n        error.value = '组件初始化失败: ' + error.message;\n      }\n    };\n\n    onMounted(() => {\n      console.log('[生命周期] VideoHistory组件已挂载，开始初始化');\n      initializeComponent();\n    });\n    \n    return {\n      videoTasks,\n      loading,\n      tableLoading,\n      error,\n      total,\n      pageSize,\n      currentPage,\n      sortProp,\n      sortOrder,\n      searchQuery,\n      userFilter,\n      userList,\n      dateRange,\n      selectedRows,\n      hasSelected,\n      selectAll,\n      isIndeterminate,\n      batchDeleteDialogVisible,\n      batchOperationLoading,\n      isAdmin,\n      currentUser,\n      isComponentReady,\n      fetchVideoTasks,\n      formatDate,\n      getStatusType,\n      getStatusText,\n      getProgressStatus,\n      viewResult,\n      checkStatus,\n      handleRetryAnalysis,\n      handleSearch,\n      handleSortChange,\n      handleSelectionChange,\n      handleSizeChange,\n      handleCurrentChange,\n      handleBatchDelete,\n      toggleSelectAll,\n      confirmBatchDelete,\n      exportData,\n      getVideoDisplayName,\n      startRename,\n      confirmRename,\n      loadUserList,\n      resetFilters,\n      dateShortcuts,\n      formatToYYYYMMDD,\n      deleteDialogVisible,\n      showDeleteConfirm,\n      confirmDelete,\n      ensureUserAuthenticated,\n      initializeComponent\n    };\n  }\n};\n</script>\n\n<style scoped>\n.video-history-container {\n  min-height: calc(100vh - 60px);\n  padding: 0;\n  background-color: #0e1525;\n  color: #e5e7eb;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  overflow: hidden;\n}\n\n.history-card {\n  max-width: 1400px;\n  margin: 20px auto;\n  width: calc(100% - 40px);\n  flex: 1;\n  background: rgba(13, 18, 32, 0.97);\n  border: none;\n  border-radius: 0;\n  overflow: hidden;\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: rgba(22, 30, 49, 0.95);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.12);\n  padding: 0.8rem 1rem;\n}\n\n.card-header h2 {\n  font-weight: 700;\n  background: linear-gradient(90deg, #3b82f6, #8b5cf6);\n  -webkit-background-clip: text;\n  background-clip: text;\n  color: transparent;\n  margin: 0;\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n  letter-spacing: 0.5px;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.selection-controls {\n  display: flex;\n  align-items: center;\n  margin-right: 5px;\n}\n\n/* 筛选面板样式 */\n.filter-panel {\n  margin-bottom: 15px;\n  padding: 10px 15px;\n  background: rgba(22, 30, 49, 0.95);\n  border-radius: 0;\n  border: none;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.08);\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);\n}\n\n.filter-row {\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  gap: 10px;\n}\n\n.filter-item {\n  display: flex;\n  align-items: center;\n  white-space: nowrap;\n}\n\n.filter-label {\n  margin-right: 4px;\n  font-weight: 600;\n  color: rgba(229, 231, 235, 0.95);\n  white-space: nowrap;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n  font-size: 0.85rem;\n}\n\n.search-input {\n  width: 200px !important;\n}\n\n/* 控制日期选择器宽度 */\n.date-picker {\n  width: 250px !important;\n}\n\n/* 修改过的filter-actions样式 */\n.filter-actions {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-left: auto; /* 将按钮推到右侧 */\n}\n\n/* 应用筛选按钮样式 */\n.filter-btn {\n  height: 28px;\n  padding: 0 12px;\n  font-size: 0.85rem;\n}\n\n/* 重置按钮样式 */\n.reset-btn {\n  background: rgba(255, 255, 255, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  color: rgba(255, 255, 255, 0.9);\n  height: 28px;\n  padding: 0 12px;\n  font-size: 0.85rem;\n}\n\n.reset-btn:hover {\n  background: rgba(255, 255, 255, 0.15);\n}\n\n.refresh-btn {\n  background: rgba(255, 255, 255, 0.08);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  color: rgba(255, 255, 255, 0.9);\n  height: 28px;\n  padding: 0 10px;\n  font-size: 0.85rem;\n}\n\n.refresh-btn:hover {\n  background: rgba(255, 255, 255, 0.15);\n}\n\n/* 优化Element Plus日期时间选择器样式 */\n:deep(.el-input__wrapper) {\n  padding: 0 8px !important;\n}\n\n:deep(.el-input__inner) {\n  font-size: 0.85rem !important;\n}\n\n:deep(.el-range-separator) {\n  padding: 0 2px !important;\n}\n\n:deep(.el-range-input) {\n  width: 30% !important;\n}\n\n.loading-container,\n.error-container,\n.empty-container {\n  padding: 20px;\n  text-align: center;\n  background: rgba(18, 25, 46, 0.95);\n  border-radius: 4px;\n  margin: 10px 0;\n}\n\n.error-actions {\n  margin-top: 20px;\n}\n\n/* 表格样式 */\n.custom-table {\n  background: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n:deep(.el-table) {\n  background-color: transparent !important;\n  color: #e5e7eb !important;\n}\n\n:deep(.el-table__header-wrapper) {\n  background-color: rgba(22, 30, 49, 0.95) !important;\n}\n\n:deep(.el-table__header) {\n  background-color: rgba(22, 30, 49, 0.95) !important;\n  color: #ffffff !important;\n}\n\n:deep(.el-table__row) {\n  background-color: rgba(25, 32, 50, 0.95) !important;\n}\n\n:deep(.el-table__row:hover),\n:deep(.el-table__row.hover-row),\n:deep(.el-table__row.current-row),\n:deep(.el-table__row--striped.hover-row),\n:deep(.el-table__row--striped.current-row),\n:deep(.el-table tr.hover-row > td.el-table__cell) {\n  background-color: rgba(25, 32, 50, 0.95) !important;\n}\n\n:deep(.el-table__row--striped) {\n  background-color: rgba(22, 30, 49, 0.95) !important;\n}\n\n:deep(.el-table__row--striped:hover) {\n  background-color: rgba(22, 30, 49, 0.95) !important;\n}\n\n/* 修复点击行后变白色的问题 */\n:deep(.el-table__row:active),\n:deep(.el-table__row--striped:active),\n:deep(.el-table__row.current-row:active),\n:deep(.el-table__row--striped.current-row:active) {\n  background-color: rgba(22, 30, 49, 0.95) !important;\n}\n\n/* 增强单元格的背景色保持一致性 */\n:deep(.el-table__cell) {\n  background-color: inherit !important;\n}\n\n:deep(.el-table__cell:hover) {\n  background-color: inherit !important;\n}\n\n/* 表格单元格样式 */\n:deep(.el-table th) {\n  background-color: rgba(22, 30, 49, 0.95) !important;\n  color: rgba(255, 255, 255, 0.95) !important;\n  font-weight: 600 !important;\n  padding: 8px 0 !important;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;\n}\n\n:deep(.el-table td) {\n  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;\n  color: rgba(229, 231, 235, 0.9) !important;\n  padding: 10px 0 !important;\n}\n\n/* Disable highlight on table cells when row is hovered */\n:deep(.el-table__body tr.hover-row > td.el-table__cell) {\n  background-color: inherit !important;\n}\n\n/* 确保按钮点击后恢复正确的颜色 */\n:deep(.custom-btn-outline:active),\n:deep(.custom-btn-outline:focus) {\n  background: rgba(239, 68, 68, 0.15) !important;\n  color: #ffffff !important;\n}\n\n:deep(.custom-btn-outline:hover) {\n  background: rgba(239, 68, 68, 0.25) !important;\n  border-color: #ef4444 !important;\n  color: #ffffff !important;\n}\n\n/* 完成时间列的状态文本样式 */\n.text-muted {\n  color: rgba(156, 163, 175, 0.8) !important;\n  font-style: italic;\n  font-size: 0.9em;\n}\n\n.video-name-container {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.video-name {\n  margin-right: 10px;\n  flex: 1;\n  color: rgba(255, 255, 255, 0.95);\n  font-weight: 500;\n}\n\n.rename-button {\n  opacity: 0.6;\n}\n\n.video-name-container:hover .rename-button {\n  opacity: 1;\n}\n\n.table-actions {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex-wrap: wrap;\n  justify-content: flex-end;\n}\n\n/* 分页容器样式 */\n.pagination-container {\n  margin-top: 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 18px;\n  flex-wrap: wrap;\n  gap: 10px;\n  background: rgba(22, 30, 49, 0.95);\n  border-top: none;\n  border-radius: 0;\n  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.15);\n}\n\n.pagination-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.pagination-info {\n  color: rgba(209, 213, 219, 0.95);\n  font-size: 14px;\n  font-weight: 600;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.batch-actions {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.selected-info {\n  color: rgba(209, 213, 219, 0.95);\n  font-size: 14px;\n  font-weight: 600;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  width: 100%;\n}\n\n.me-2 {\n  margin-right: 8px;\n}\n\n/* 自定义删除按钮样式 */\n.custom-btn-outline {\n  background: rgba(239, 68, 68, 0.15);\n  border: 1px solid #ef4444;\n  color: #ffffff;\n  border-radius: 0.25rem;\n  padding: 0.3rem 0.6rem;\n  font-weight: 600;\n  font-size: 0.85rem;\n  transition: all 0.2s ease;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.custom-btn-outline:hover {\n  background: rgba(239, 68, 68, 0.25);\n  border-color: #ef4444;\n  color: #ffffff;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 5px rgba(239, 68, 68, 0.3);\n}\n\n.custom-btn-outline i {\n  color: #ef4444;\n  margin-right: 4px;\n}\n\n/* 自定义主要按钮样式 */\n.custom-btn-primary {\n  border-radius: 0.25rem;\n  transition: all 0.2s;\n  font-weight: 600;\n  padding: 0.3rem 0.6rem;\n  height: 32px;\n  font-size: 0.85rem;\n  background-color: rgba(79, 70, 229, 0.9) !important;\n  border-color: rgba(79, 70, 229, 0.9) !important;\n  color: #ffffff !important;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.custom-btn-primary:hover {\n  background-color: rgba(99, 90, 249, 0.9) !important;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 5px rgba(79, 70, 229, 0.3) !important;\n}\n\n.action-btn {\n  background: rgba(45, 55, 72, 0.5);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.9);\n  border-radius: 0.25rem;\n  padding: 0.3rem 0.6rem;\n  font-size: 0.85rem;\n  transition: all 0.2s;\n  height: 32px;\n}\n\n.action-btn:hover {\n  background: rgba(55, 65, 82, 0.5);\n  color: white;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\n}\n\n.action-btn-warning {\n  background: rgba(245, 158, 11, 0.2);\n  border: 1px solid rgba(245, 158, 11, 0.4);\n  color: #fbbf24;\n  border-radius: 0.25rem;\n  padding: 0.3rem 0.6rem;\n  font-size: 0.85rem;\n  transition: all 0.2s;\n  height: 32px;\n}\n\n.action-btn-warning:hover {\n  background: rgba(245, 158, 11, 0.3);\n  transform: translateY(-1px);\n  box-shadow: 0 2px 5px rgba(245, 158, 11, 0.3);\n}\n\n/* 标签样式 */\n:deep(.el-tag) {\n  border: none;\n  padding: 0.25rem 0.75rem;\n  font-weight: 500;\n  border-radius: 1rem;\n}\n\n:deep(.el-tag--info) {\n  background-color: rgba(51, 65, 85, 0.7) !important;\n  color: #cbd5e1 !important;\n  border: none !important;\n}\n\n:deep(.el-tag--success) {\n  background-color: rgba(16, 185, 129, 0.2) !important;\n  color: #34d399 !important;\n  border: 1px solid rgba(16, 185, 129, 0.3) !important;\n}\n\n:deep(.el-tag--danger) {\n  background-color: rgba(239, 68, 68, 0.2) !important;\n  color: #f87171 !important;\n  border: 1px solid rgba(239, 68, 68, 0.3) !important;\n}\n\n:deep(.el-tag--warning) {\n  background-color: rgba(245, 158, 11, 0.2) !important;\n  color: #fbbf24 !important;\n  border: 1px solid rgba(245, 158, 11, 0.3) !important;\n}\n\n:deep(.el-tag--primary) {\n  background-color: rgba(99, 102, 241, 0.2) !important;\n  color: #818cf8 !important;\n  border: 1px solid rgba(99, 102, 241, 0.3) !important;\n}\n\n/* 页面特定的下拉框样式调整 */\n:deep(.el-select) {\n  width: auto !important;\n  min-width: 130px !important;\n}\n\n/* 分页样式 */\n:deep(.el-pagination) {\n  --el-pagination-bg-color: transparent;\n  --el-pagination-button-bg-color: rgba(79, 70, 229, 0.1);\n  --el-pagination-button-color: #fff;\n  --el-pagination-button-disabled-color: #606266;\n  --el-pagination-button-disabled-bg-color: rgba(255, 255, 255, 0.1);\n  --el-pagination-hover-color: #4f46e5;\n}\n\n:deep(.el-pagination .el-pager li) {\n  background: rgba(255, 255, 255, 0.1);\n  color: #fff;\n}\n\n/* 输入框样式 */\n:deep(.el-input .el-input__wrapper) {\n  background-color: rgba(17, 24, 39, 0.3) !important;\n  box-shadow: none !important;\n  border: 1px solid rgba(255, 255, 255, 0.1) !important;\n}\n\n:deep(.el-input .el-input__inner) {\n  color: rgba(255, 255, 255, 0.9) !important;\n}\n\n/* 进度条样式 */\n:deep(.el-progress-bar__outer) {\n  background-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n:deep(.el-progress-bar__inner) {\n  background-color: rgba(79, 70, 229, 0.9) !important;\n}\n\n:deep(.el-progress--success .el-progress-bar__inner) {\n  background-color: rgba(16, 185, 129, 0.9) !important;\n}\n\n:deep(.el-progress--exception .el-progress-bar__inner) {\n  background-color: rgba(239, 68, 68, 0.9) !important;\n}\n\n/* 响应式优化 */\n@media (max-width: 1200px) {\n  .filter-row {\n    flex-wrap: wrap;\n  }\n  \n  .filter-actions {\n    margin-left: 0;\n    margin-top: 10px;\n    width: 100%;\n    justify-content: flex-end;\n  }\n}\n\n@media (max-width: 768px) {\n  .history-card {\n    width: calc(100% - 20px);\n    margin: 10px auto;\n  }\n  \n  .card-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .header-actions {\n    margin-top: 10px;\n    width: 100%;\n  }\n  \n  .filter-row {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .filter-item {\n    width: 100%;\n    margin-bottom: 8px;\n  }\n  \n  .filter-label {\n    min-width: 60px;\n  }\n  \n  .date-picker,\n  .search-input {\n    width: 100% !important;\n  }\n  \n  .pagination-container {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .pagination-wrapper {\n    margin-top: 10px;\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n/* 装饰元素 */\n.video-history-container::before {\n  content: '';\n  position: absolute;\n  width: 300px;\n  height: 300px;\n  border-radius: 50%;\n  background: radial-gradient(#3b82f6, transparent);\n  opacity: 0.15;\n  top: 10%;\n  right: 5%;\n  animation: float 8s ease-in-out infinite;\n}\n\n.video-history-container::after {\n  content: '';\n  position: absolute;\n  width: 200px;\n  height: 200px;\n  border-radius: 50%;\n  background: radial-gradient(#8b5cf6, transparent);\n  opacity: 0.15;\n  bottom: 15%;\n  left: 10%;\n  animation: float 6s ease-in-out infinite;\n  animation-delay: 1s;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0); }\n  50% { transform: translateY(-20px); }\n}\n\n.wider-checkbox {\n  margin-right: 10px;\n}\n\n/* 自定义对话框样式 */\n:deep(.dark-theme-dialog) {\n  background: rgba(13, 18, 38, 0.98) !important;\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5) !important;\n  border-radius: 0.75rem !important;\n  border: 1px solid rgba(99, 102, 241, 0.2) !important;\n  overflow: hidden !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__header) {\n  background: rgba(22, 30, 55, 0.98) !important;\n  color: #e5e7eb !important;\n  border-bottom: 1px solid rgba(99, 102, 241, 0.15) !important;\n  padding: 15px 20px !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__title) {\n  color: #e5e7eb !important;\n  font-weight: 600 !important;\n  font-size: 1.1rem !important;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__body) {\n  background: rgba(13, 18, 38, 0.98) !important;\n  color: #e5e7eb !important;\n  padding: 24px 20px !important;\n  font-size: 1rem !important;\n  line-height: 1.5 !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__footer) {\n  background: rgba(22, 30, 55, 0.98) !important;\n  border-top: 1px solid rgba(99, 102, 241, 0.15) !important;\n  padding: 15px 20px !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__headerbtn:hover .el-dialog__close) {\n  color: #818cf8 !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__headerbtn .el-dialog__close) {\n  color: #e5e7eb !important;\n}\n\n/* 修复按钮样式和比例问题 */\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  width: 100%;\n}\n\n:deep(.cancel-btn) {\n  background: rgba(31, 41, 65, 0.8) !important;\n  border: 1px solid rgba(255, 255, 255, 0.15) !important;\n  color: #e5e7eb !important;\n  min-width: 80px !important;\n  flex: 0 0 auto !important;\n}\n\n:deep(.cancel-btn:hover) {\n  background: rgba(55, 65, 95, 0.8) !important;\n  border-color: rgba(255, 255, 255, 0.25) !important;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) !important;\n}\n\n:deep(.confirm-delete-btn) {\n  background: rgba(220, 38, 38, 0.15) !important;\n  border: 1px solid rgba(239, 68, 68, 0.5) !important;\n  color: #f87171 !important;\n  min-width: 100px !important;\n  flex: 0 0 auto !important;\n  font-weight: 600 !important;\n}\n\n:deep(.confirm-delete-btn:hover) {\n  background: rgba(220, 38, 38, 0.25) !important;\n  color: #fca5a5 !important;\n  border-color: rgba(239, 68, 68, 0.7) !important;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3) !important;\n}\n</style> "], "mappings": ";;;EACOA,KAAK,EAAC;AAAyB;;EAGzBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;EANrCC,GAAA;EAOiBD,KAAK,EAAC;;;EAyBZA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAa;;EAlClCC,GAAA;EAoD8BD,KAAK,EAAC;;;EAqBrBA,KAAK,EAAC;AAAa;;EAkBnBA,KAAK,EAAC;AAAgB;;EA3FrCC,GAAA;EAqG0BD,KAAK,EAAC;;;EArGhCC,GAAA;EAyG6BD,KAAK,EAAC;;;EAOtBA,KAAK,EAAC;AAAe;;EAhHlCC,GAAA;EAqH+CD,KAAK,EAAC;;;EArHrDC,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;EAgK0BD,KAAK,EAAC;;;EACVA,KAAK,EAAC;AAAY;;EAjKxCC,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;EAuL2BD,KAAK,EAAC;;;EAmCdA,KAAK,EAAC;AAAe;;EAwC3BA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAe;;EAnQpCC,GAAA;EAqQiDD,KAAK,EAAC;;;EAYxCA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiB;;EA4B3BA,KAAK,EAAC;AAAe;;EAmBrBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;;;;;;uBAhUhCE,mBAAA,CAwUM,OAxUNC,UAwUM,GAvUJC,YAAA,CAgSUC,kBAAA;IAhSDL,KAAK,EAAC;EAAc;IAChBM,MAAM,EAAAC,QAAA,CACf,MAwBM,CAxBNC,mBAAA,CAwBM,OAxBNC,UAwBM,G,4BAvBJD,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CAqBM,OArBNE,UAqBM,GApBkCC,MAAA,CAAAC,UAAU,CAACC,MAAM,Q,cAAvDX,mBAAA,CAOM,OAPNY,UAOM,GANJV,YAAA,CAKiBW,sBAAA;MAb/BC,UAAA,EASyBL,MAAA,CAAAM,SAAS;MATlC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IASyBR,MAAA,CAAAM,SAAS,GAAAE,MAAA;MACjBC,QAAM,EAAET,MAAA,CAAAU,eAAe;MACvBC,aAAa,EAAEX,MAAA,CAAAY,eAAe;MAC/BvB,KAAK,EAAC;;MAZtBwB,OAAA,EAAAjB,QAAA,CAae,MAAEW,MAAA,SAAAA,MAAA,QAbjBO,gBAAA,CAae,IAAE,E;MAbjBC,CAAA;wEAAAC,mBAAA,gBAeYvB,YAAA,CAMYwB,oBAAA;MALV5B,KAAK,EAAC,oBAAoB;MACzB6B,QAAQ,GAAGlB,MAAA,CAAAmB,WAAW;MACtBC,OAAK,EAAEpB,MAAA,CAAAqB;;MAlBtBR,OAAA,EAAAjB,QAAA,CAoBc,MAA6B,CAA7BH,YAAA,CAA6B6B,kBAAA;QApB3CT,OAAA,EAAAjB,QAAA,CAoBuB,MAAU,CAAVH,YAAA,CAAU8B,iBAAA,E;QApBjCR,CAAA;sCAAAD,gBAAA,CAoB2C,QAC/B,G;MArBZC,CAAA;gDAsBYtB,YAAA,CAIc+B,sBAAA;MAJDC,EAAE,EAAC;IAAe;MAtB3CZ,OAAA,EAAAjB,QAAA,CAuBc,MAEY,CAFZH,YAAA,CAEYwB,oBAAA;QAFD5B,KAAK,EAAC;MAAoB;QAvBnDwB,OAAA,EAAAjB,QAAA,CAwBgB,MAA2B,CAA3BH,YAAA,CAA2B6B,kBAAA;UAxB3CT,OAAA,EAAAjB,QAAA,CAwByB,MAAQ,CAARH,YAAA,CAAQiC,eAAA,E;UAxBjCX,CAAA;wCAAAD,gBAAA,CAwB2C,SAC7B,G;QAzBdC,CAAA;;MAAAA,CAAA;;IAAAF,OAAA,EAAAjB,QAAA,CAgCM,MAmEM,CAnENC,mBAAA,CAmEM,OAnEN8B,UAmEM,GAlEJ9B,mBAAA,CAiEM,OAjEN+B,UAiEM,GAhEJ/B,mBAAA,CAeM,OAfNgC,UAeM,G,4BAdJhC,mBAAA,CAAqC;MAA/BR,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BI,YAAA,CAYWqC,mBAAA;MAhDvBzB,UAAA,EAqCuBL,MAAA,CAAA+B,WAAW;MArClC,uBAAAxB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAqCuBR,MAAA,CAAA+B,WAAW,GAAAvB,MAAA;MACpBwB,WAAW,EAAC,QAAQ;MACpBC,SAAS,EAAT,EAAS;MACRC,OAAK,EAAElC,MAAA,CAAAmC,YAAY;MACnBC,OAAK,EAzCpBC,SAAA,CAyC4BrC,MAAA,CAAAmC,YAAY;MAC1BG,IAAI,EAAC,OAAO;MACZjD,KAAK,EAAC;;MAEKkD,MAAM,EAAA3C,QAAA,CACf,MAAoD,CAApDH,YAAA,CAAoD6B,kBAAA;QAA3CjC,KAAK,EAAC;MAAgB;QA9C/CwB,OAAA,EAAAjB,QAAA,CA8CgD,MAAU,CAAVH,YAAA,CAAU+C,iBAAA,E;QA9C1DzB,CAAA;;MAAAA,CAAA;+DAmDUC,mBAAA,eAAkB,EACPhB,MAAA,CAAAyC,OAAO,I,cAAlBlD,mBAAA,CAmBM,OAnBNmD,UAmBM,G,4BAlBJ7C,mBAAA,CAAqC;MAA/BR,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BI,YAAA,CAgBYkD,oBAAA;MAtExBtC,UAAA,EAuDuBL,MAAA,CAAA4C,UAAU;MAvDjC,uBAAArC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuDuBR,MAAA,CAAA4C,UAAU,GAAApC,MAAA;MACnBwB,WAAW,EAAC,MAAM;MAClBC,SAAS,EAAT,EAAS;MACRxB,QAAM,EAAET,MAAA,CAAAmC,YAAY;MACrBG,IAAI,EAAC,OAAO;MACX,uBAAqB,EAAE,IAAI;MAC3B,iBAAe,EAAE;;MA7DhCzB,OAAA,EAAAjB,QAAA,CA+Dc,MAA6C,CAA7CH,YAAA,CAA6CoD,oBAAA;QAAlCC,KAAK,EAAC,MAAM;QAACC,KAAK,EAAC;6BAC9BxD,mBAAA,CAKayD,SAAA,QArE3BC,WAAA,CAiE+BjD,MAAA,CAAAkD,QAAQ,EAAhBC,IAAI;6BADbC,YAAA,CAKaP,oBAAA;UAHVvD,GAAG,EAAE6D,IAAI,CAACE,EAAE;UACZP,KAAK,EAAEK,IAAI,CAACG,QAAQ;UACpBP,KAAK,EAAEI,IAAI,CAACE;;;MApE7BtC,CAAA;uDAAAC,mBAAA,gBAyEUnB,mBAAA,CAgBM,OAhBN0D,UAgBM,G,4BAfJ1D,mBAAA,CAAqC;MAA/BR,KAAK,EAAC;IAAc,GAAC,KAAG,sBAC9BI,YAAA,CAaiB+D,yBAAA;MAxF7BnD,UAAA,EA4EuBL,MAAA,CAAAyD,SAAS;MA5EhC,uBAAAlD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA4EuBR,MAAA,CAAAyD,SAAS,GAAAjD,MAAA;MAClBkD,IAAI,EAAC,WAAW;MAChB,iBAAe,EAAC,GAAG;MACnB,mBAAiB,EAAC,MAAM;MACxB,iBAAe,EAAC,MAAM;MACtBC,MAAM,EAAC,YAAY;MACnB,cAAY,EAAC,YAAY;MACxBC,SAAS,EAAE5D,MAAA,CAAA6D,aAAa;MACxBC,QAAQ,EAAE,KAAK;MAChBxB,IAAI,EAAC,OAAO;MACZjD,KAAK,EAAC;4DAKVQ,mBAAA,CAMM,OANNkE,WAMM,GALJtE,YAAA,CAA8FwB,oBAAA;MAAnFyC,IAAI,EAAC,SAAS;MAACpB,IAAI,EAAC,OAAO;MAAElB,OAAK,EAAEpB,MAAA,CAAAmC,YAAY;MAAE9C,KAAK,EAAC;;MA5F/EwB,OAAA,EAAAjB,QAAA,CA4F4F,MAAEW,MAAA,SAAAA,MAAA,QA5F9FO,gBAAA,CA4F4F,IAAE,E;MA5F9FC,CAAA;oCA6FYtB,YAAA,CAA8EwB,oBAAA;MAAnEqB,IAAI,EAAC,OAAO;MAAElB,OAAK,EAAEpB,MAAA,CAAAgE,YAAY;MAAE3E,KAAK,EAAC;;MA7FhEwB,OAAA,EAAAjB,QAAA,CA6F4E,MAAEW,MAAA,SAAAA,MAAA,QA7F9EO,gBAAA,CA6F4E,IAAE,E;MA7F9EC,CAAA;oCA8FYtB,YAAA,CAEYwB,oBAAA;MAFDqB,IAAI,EAAC,OAAO;MAAElB,OAAK,EAAEpB,MAAA,CAAAiE,eAAe;MAAE5E,KAAK,EAAC;;MA9FnEwB,OAAA,EAAAjB,QAAA,CA+Fc,MAA8B,CAA9BH,YAAA,CAA8B6B,kBAAA;QA/F5CT,OAAA,EAAAjB,QAAA,CA+FuB,MAAW,CAAXH,YAAA,CAAWyE,kBAAA,E;QA/FlCnD,CAAA;;MAAAA,CAAA;0CAqGiBf,MAAA,CAAAmE,OAAO,I,cAAlB5E,mBAAA,CAEM,OAFN6E,WAEM,GADJ3E,YAAA,CAAmC4E,sBAAA;MAArBC,IAAI,EAAE,EAAE;MAAEC,QAAQ,EAAR;YAGVvE,MAAA,CAAAwE,KAAK,I,cAArBjF,mBAAA,CAUM,OAVNkF,WAUM,GATJhF,YAAA,CAKEiF,mBAAA;MAJAC,KAAK,EAAC,UAAU;MAChBjB,IAAI,EAAC,OAAO;MACXkB,WAAW,EAAE5E,MAAA,CAAAwE,KAAK;MACnB,WAAS,EAAT;8CAEF3E,mBAAA,CAEM,OAFNgF,WAEM,GADJpF,YAAA,CAAiEwB,oBAAA;MAAtDyC,IAAI,EAAC,SAAS;MAAEtC,OAAK,EAAEpB,MAAA,CAAAiE;;MAjH5CpD,OAAA,EAAAjB,QAAA,CAiH6D,MAAEW,MAAA,SAAAA,MAAA,QAjH/DO,gBAAA,CAiH6D,IAAE,E;MAjH/DC,CAAA;0CAqHsBf,MAAA,CAAAC,UAAU,CAACC,MAAM,U,cAAjCX,mBAAA,CAIM,OAJNuF,WAIM,GAHJrF,YAAA,CAEWsF,mBAAA;MAFDH,WAAW,EAAC;IAAU;MAtHxC/D,OAAA,EAAAjB,QAAA,CAuHU,MAA8F,CAA9FH,YAAA,CAA8FwB,oBAAA;QAAnF5B,KAAK,EAAC,oBAAoB;QAAE+B,OAAK,EAAAb,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEwE,IAAA,CAAAC,OAAO,CAACC,IAAI;;QAvHpErE,OAAA,EAAAjB,QAAA,CAuHuF,MAAKW,MAAA,SAAAA,MAAA,QAvH5FO,gBAAA,CAuHuF,OAAK,E;QAvH5FC,CAAA;;MAAAA,CAAA;2BA2HMxB,mBAAA,CAsKM,OAjSZ4F,WAAA,G,+BA4HQ/B,YAAA,CAoIWgC,mBAAA;MAnIRC,IAAI,EAAErF,MAAA,CAAAC,UAAU;MACjBqF,KAAmB,EAAnB;QAAA;MAAA,CAAmB;MACnBC,MAAM,EAAN,EAAM;MACNC,MAAM,EAAN,EAAM;MACL,cAAY;QAAAC,IAAA,EAAUzF,MAAA,CAAA0F,QAAQ;QAAAC,KAAA,EAAS3F,MAAA,CAAA4F;MAAS;MAChDC,YAAW,EAAE7F,MAAA,CAAA8F,gBAAgB;MAC7BC,iBAAgB,EAAE/F,MAAA,CAAAgG,qBAAqB;MAExC,YAAU,EAAC,qBAAqB;MAChC3G,KAAK,EAAC;;MAtIhBwB,OAAA,EAAAjB,QAAA,CAwIU,MAQkB,CARlBH,YAAA,CAQkBwG,0BAAA;QARDvC,IAAI,EAAC,WAAW;QAACwC,KAAK,EAAC;;QAC3BvG,MAAM,EAAAC,QAAA,CACf,MAIE,CAJFH,YAAA,CAIEW,sBAAA;UA9IhBC,UAAA,EA2IyBL,MAAA,CAAAM,SAAS;UA3IlC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2IyBR,MAAA,CAAAM,SAAS,GAAAE,MAAA;UACjBC,QAAM,EAAET,MAAA,CAAAU,eAAe;UACvBC,aAAa,EAAEX,MAAA,CAAAY;;QA7IhCG,CAAA;UAkJUtB,YAAA,CA2BkBwG,0BAAA;QA3BDR,IAAI,EAAC,WAAW;QAAC3C,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAACqD,QAAQ,EAAR;;QAClDtF,OAAO,EAAAjB,QAAA,CA0BuBwG,KA1BhB,KACZA,KAAK,CAACC,GAAG,CAACC,OAAO,I,cAA5B/G,mBAAA,CAWM,OA/JpBgH,WAAA,GAqJgB9G,YAAA,CASWqC,mBAAA;UA9J3BzB,UAAA,EAsJ2B+F,KAAK,CAACC,GAAG,CAACG,QAAQ;UAtJ7C,uBAAAhG,MAAA,IAsJ2B4F,KAAK,CAACC,GAAG,CAACG,QAAQ,GAAAhG,MAAA;UAC3B8B,IAAI,EAAC,OAAO;UACZN,WAAW,EAAC,QAAQ;UACnBI,OAAK,EAzJxBC,SAAA,CAAA7B,MAAA,IAyJgCR,MAAA,CAAAyG,aAAa,CAACL,KAAK,CAACC,GAAG;;UAE1BK,MAAM,EAAA9G,QAAA,CACf,MAA2D,CAA3DH,YAAA,CAA2DwB,oBAAA;YAA/CG,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAyG,aAAa,CAACL,KAAK,CAACC,GAAG;;YA5J9DxF,OAAA,EAAAjB,QAAA,CA4JiE,MAAEW,MAAA,SAAAA,MAAA,QA5JnEO,gBAAA,CA4JiE,IAAE,E;YA5JnEC,CAAA;;UAAAA,CAAA;kHAgKcxB,mBAAA,CAWM,OAXNoH,WAWM,GAVJ9G,mBAAA,CAAoE,QAApE+G,WAAoE,EAAAC,gBAAA,CAAxC7G,MAAA,CAAA8G,mBAAmB,CAACV,KAAK,CAACC,GAAG,mBACzD5G,YAAA,CAQYwB,oBAAA;UAPVyC,IAAI,EAAC,SAAS;UACdpB,IAAI,EAAC,OAAO;UACZyE,MAAM,EAAN,EAAM;UACL3F,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAgH,WAAW,CAACZ,KAAK,CAACC,GAAG;UAC7BhH,KAAK,EAAC;;UAvKxBwB,OAAA,EAAAjB,QAAA,CAyKkB,MAA2B,CAA3BH,YAAA,CAA2B6B,kBAAA;YAzK7CT,OAAA,EAAAjB,QAAA,CAyK2B,MAAQ,CAARH,YAAA,CAAQwH,eAAA,E;YAzKnClG,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UA+KUtB,YAAA,CAckBwG,0BAAA;QAdDR,IAAI,EAAC,aAAa;QAAC3C,KAAK,EAAC,MAAM;QAACoD,KAAK,EAAC,KAAK;QAACC,QAAQ,EAAR;;QAChDtF,OAAO,EAAAjB,QAAA,CAsBnBwG,KAtB0B,KACXA,KAAK,CAACC,GAAG,CAACa,MAAM,oBAAoBd,KAAK,CAACC,GAAG,CAACc,WAAW,I,cAArE5H,mBAAA,CAEO,QAnLrB6H,WAAA,EAAAP,gBAAA,CAkLmB7G,MAAA,CAAAqH,UAAU,CAACjB,KAAK,CAACC,GAAG,CAACc,WAAW,qBAEpBf,KAAK,CAACC,GAAG,CAACa,MAAM,oB,cAAjC3H,mBAAA,CAEO,QAtLrB+H,WAAA,EAAAT,gBAAA,CAqLmB7G,MAAA,CAAAqH,UAAU,CAACjB,KAAK,CAACC,GAAG,CAACkB,SAAS,sB,cAEnChI,mBAAA,CAIO,QAJPiI,WAIO,EAAAX,gBAAA,CAHFT,KAAK,CAACC,GAAG,CAACa,MAAM,+BAAkDd,KAAK,CAACC,GAAG,CAACa,MAAM,2BAA8Cd,KAAK,CAACC,GAAG,CAACa,MAAM,gD;QAxLnKnG,CAAA;UA+LUC,mBAAA,YAAe,EACfvB,YAAA,CAIkBwG,0BAAA;QAJDR,IAAI,EAAC,UAAU;QAAC3C,KAAK,EAAC,KAAK;QAACoD,KAAK,EAAC;;QACtCrF,OAAO,EAAAjB,QAAA,CACkBwG,KADX,KAjMrCtF,gBAAA,CAAA+F,gBAAA,CAkMiBT,KAAK,CAACC,GAAG,CAAC/C,QAAQ,2B;QAlMnCvC,CAAA;UAsMUtB,YAAA,CAMkBwG,0BAAA;QANDR,IAAI,EAAC,QAAQ;QAAC3C,KAAK,EAAC,IAAI;QAACoD,KAAK,EAAC,KAAK;QAACC,QAAQ,EAAR;;QACzCtF,OAAO,EAAAjB,QAAA,CAGPwG,KAHc,KACvB3G,YAAA,CAESgI,iBAAA;UAFA/D,IAAI,EAAE1D,MAAA,CAAA0H,aAAa,CAACtB,KAAK,CAACC,GAAG,CAACa,MAAM;UAAG5E,IAAI,EAAC;;UAxMnEzB,OAAA,EAAAjB,QAAA,CAyMgB,MAAqC,CAzMrDkB,gBAAA,CAAA+F,gBAAA,CAyMmB7G,MAAA,CAAA2H,aAAa,CAACvB,KAAK,CAACC,GAAG,CAACa,MAAM,kB;UAzMjDnG,CAAA;;QAAAA,CAAA;UA8MUtB,YAAA,CAQkBwG,0BAAA;QARDR,IAAI,EAAC,UAAU;QAAC3C,KAAK,EAAC,IAAI;QAACoD,KAAK,EAAC;;QACrCrF,OAAO,EAAAjB,QAAA,CAKdwG,KALqB,KACvB3G,YAAA,CAIEmI,sBAAA;UAHCC,UAAU,EAAEzB,KAAK,CAACC,GAAG,CAACyB,QAAQ;UAC9BZ,MAAM,EAAElH,MAAA,CAAA+H,iBAAiB,CAAC3B,KAAK,CAACC,GAAG,CAACa,MAAM;UAC1C,cAAY,EAAE;;QAnN/BnG,CAAA;UAwNUtB,YAAA,CAuCkBwG,0BAAA;QAvCDnD,KAAK,EAAC,IAAI;QAACoD,KAAK,EAAC,KAAK;QAAC8B,KAAK,EAAC;;QACjCnH,OAAO,EAAAjB,QAAA,CAoCVwG,KApCiB,KACvBvG,mBAAA,CAmCM,OAnCNoI,WAmCM,GA/BI7B,KAAK,CAACC,GAAG,CAACa,MAAM,oB,cAHxB9D,YAAA,CAOYnC,oBAAA;UAlO5B3B,GAAA;UA4NkBgD,IAAI,EAAC,OAAO;UACZjD,KAAK,EAAC,oBAAoB;UAEzB+B,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAkI,UAAU,CAAC9B,KAAK,CAACC,GAAG;;UA/N9CxF,OAAA,EAAAjB,QAAA,CAgOiB,MAEDW,MAAA,SAAAA,MAAA,QAlOhBO,gBAAA,CAgOiB,QAED,E;UAlOhBC,CAAA;4DAAAC,mBAAA,gB,yBAuOiDmH,QAAQ,CAAC/B,KAAK,CAACC,GAAG,CAACa,MAAM,K,cAH1D9D,YAAA,CAOYnC,oBAAA;UA3O5B3B,GAAA;UAqOkBgD,IAAI,EAAC,OAAO;UACZjD,KAAK,EAAC,YAAY;UAEjB+B,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAoI,WAAW,CAAChC,KAAK,CAACC,GAAG;;UAxO/CxF,OAAA,EAAAjB,QAAA,CAyOiB,MAEDW,MAAA,SAAAA,MAAA,QA3OhBO,gBAAA,CAyOiB,QAED,E;UA3OhBC,CAAA;4DAAAC,mBAAA,gBAgPwBoF,KAAK,CAACC,GAAG,CAACa,MAAM,iB,cAHxB9D,YAAA,CAOYnC,oBAAA;UApP5B3B,GAAA;UA8OkBgD,IAAI,EAAC,OAAO;UACZjD,KAAK,EAAC,oBAAoB;UAEzB+B,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAqI,mBAAmB,CAACjC,KAAK,CAACC,GAAG;;UAjPvDxF,OAAA,EAAAjB,QAAA,CAkPiB,MAEDW,MAAA,SAAAA,MAAA,QApPhBO,gBAAA,CAkPiB,MAED,E;UApPhBC,CAAA;4DAAAC,mBAAA,gBAsPgBvB,YAAA,CAMYwB,oBAAA;UALVqB,IAAI,EAAC,OAAO;UACZjD,KAAK,EAAC,oBAAoB;UACzB+B,OAAK,EAAAZ,MAAA,IAAER,MAAA,CAAAsI,iBAAiB,CAAClC,KAAK,CAACC,GAAG;;UAzPrDxF,OAAA,EAAAjB,QAAA,CA0PiB,MAEDW,MAAA,SAAAA,MAAA,QA5PhBO,gBAAA,CA0PiB,MAED,E;UA5PhBC,CAAA;;QAAAA,CAAA;;MAAAA,CAAA;6GAoIqBf,MAAA,CAAAuI,YAAY,E,GA8HzB1I,mBAAA,CA8BM,OA9BN2I,WA8BM,GA7BJ3I,mBAAA,CAaM,OAbN4I,WAaM,GAZezI,MAAA,CAAAC,UAAU,CAACC,MAAM,Q,cAApCkD,YAAA,CAAqHhD,sBAAA;MApQjId,GAAA;MAAAe,UAAA,EAoQ+DL,MAAA,CAAAM,SAAS;MApQxE,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAoQ+DR,MAAA,CAAAM,SAAS,GAAAE,MAAA;MAAGC,QAAM,EAAET,MAAA,CAAAU,eAAe;MAAErB,KAAK,EAAC;;MApQ1GwB,OAAA,EAAAjB,QAAA,CAoQiH,MAAEW,MAAA,SAAAA,MAAA,QApQnHO,gBAAA,CAoQiH,IAAE,E;MApQnHC,CAAA;qDAAAC,mBAAA,gBAqQwBhB,MAAA,CAAA0I,YAAY,CAACxI,MAAM,Q,cAA/BX,mBAAA,CAEO,QAFPoJ,WAEO,EAFoD,OACrD,GAAA9B,gBAAA,CAAG7G,MAAA,CAAA0I,YAAY,CAACxI,MAAM,IAAG,KAC/B,mBAvQZc,mBAAA,gBAyQoBhB,MAAA,CAAA0I,YAAY,CAACxI,MAAM,Q,cAD3BkD,YAAA,CAOYnC,oBAAA;MA/QxB3B,GAAA;MA0QcD,KAAK,EAAC,oBAAoB;MAC1BiD,IAAI,EAAC,OAAO;MACXlB,OAAK,EAAEpB,MAAA,CAAAqB;;MA5QtBR,OAAA,EAAAjB,QAAA,CA6Qa,MAEDW,MAAA,SAAAA,MAAA,QA/QZO,gBAAA,CA6Qa,QAED,E;MA/QZC,CAAA;sCAAAC,mBAAA,e,GAiRUnB,mBAAA,CAcM,OAdN+I,WAcM,GAbJ/I,mBAAA,CAAoD,OAApDgJ,WAAoD,EAAvB,IAAE,GAAAhC,gBAAA,CAAG7G,MAAA,CAAA8I,KAAK,IAAG,MAAI,iBAC9CrJ,YAAA,CAWEsJ,wBAAA;MAVAC,UAAU,EAAV,EAAU;MACVC,MAAM,EAAC,0BAA0B;MAChCH,KAAK,EAAE9I,MAAA,CAAA8I,KAAK;MACZ,WAAS,EAAE9I,MAAA,CAAAkJ,QAAQ;MACnB,cAAY,EAAElJ,MAAA,CAAAmJ,WAAW;MACzB,YAAU,EAAE,iBAAiB;MAC7BC,YAAW,EAAEpJ,MAAA,CAAAqJ,gBAAgB;MAC7BC,eAAc,EAAEtJ,MAAA,CAAAuJ,mBAAmB;MACpC,WAAS,EAAC,KAAK;MACf,WAAS,EAAC;;IA7RxBxI,CAAA;MAoSIC,mBAAA,eAAkB,EAClBvB,YAAA,CAgBY+J,oBAAA;IAfV7E,KAAK,EAAC,MAAM;IAtSlBtE,UAAA,EAuSeL,MAAA,CAAAyJ,wBAAwB;IAvSvC,uBAAAlJ,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAuSeR,MAAA,CAAAyJ,wBAAwB,GAAAjJ,MAAA;IACjC0F,KAAK,EAAC,OAAO;IACb7G,KAAK,EAAC,mBAAmB;IACxB,sBAAoB,EAAE;;IAGZqK,MAAM,EAAA9J,QAAA,CACf,MAKM,CALNC,mBAAA,CAKM,OALN8J,WAKM,GAJJlK,YAAA,CAEYwB,oBAAA;MAFD5B,KAAK,EAAC,oBAAoB;MAAE+B,OAAK,EAAEpB,MAAA,CAAA4J,kBAAkB;MAAGzF,OAAO,EAAEnE,MAAA,CAAA6J;;MA/StFhJ,OAAA,EAAAjB,QAAA,CA+S6G,MAEnGW,MAAA,SAAAA,MAAA,QAjTVO,gBAAA,CA+S6G,QAEnG,E;MAjTVC,CAAA;+CAkTUtB,YAAA,CAAsFwB,oBAAA;MAA3E5B,KAAK,EAAC,YAAY;MAAE+B,OAAK,EAAAb,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAER,MAAA,CAAAyJ,wBAAwB;;MAlTxE5I,OAAA,EAAAjB,QAAA,CAkTkF,MAAEW,MAAA,SAAAA,MAAA,QAlTpFO,gBAAA,CAkTkF,IAAE,E;MAlTpFC,CAAA;;IAAAF,OAAA,EAAAjB,QAAA,CA4SM,MAA2D,CAA3DC,mBAAA,CAA2D,cAArD,UAAQ,GAAAgH,gBAAA,CAAG7G,MAAA,CAAA0I,YAAY,CAACxI,MAAM,IAAG,eAAa,gB;IA5S1Da,CAAA;qCAuTIC,mBAAA,iBAAoB,EACpBvB,YAAA,CAgBY+J,oBAAA;IAfV7E,KAAK,EAAC,MAAM;IAzTlBtE,UAAA,EA0TeL,MAAA,CAAA8J,mBAAmB;IA1TlC,uBAAAvJ,MAAA,SAAAA,MAAA,OAAAC,MAAA,IA0TeR,MAAA,CAAA8J,mBAAmB,GAAAtJ,MAAA;IAC5B0F,KAAK,EAAC,OAAO;IACb7G,KAAK,EAAC,mBAAmB;IACxB,sBAAoB,EAAE;;IAGZqK,MAAM,EAAA9J,QAAA,CACf,MAKM,CALNC,mBAAA,CAKM,OALNkK,WAKM,GAJJtK,YAAA,CAEYwB,oBAAA;MAFD5B,KAAK,EAAC,oBAAoB;MAAE+B,OAAK,EAAEpB,MAAA,CAAAgK;;MAlUxDnJ,OAAA,EAAAjB,QAAA,CAkUuE,MAE7DW,MAAA,SAAAA,MAAA,QApUVO,gBAAA,CAkUuE,QAE7D,E;MApUVC,CAAA;oCAqUUtB,YAAA,CAAiFwB,oBAAA;MAAtE5B,KAAK,EAAC,YAAY;MAAE+B,OAAK,EAAAb,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAER,MAAA,CAAA8J,mBAAmB;;MArUnEjJ,OAAA,EAAAjB,QAAA,CAqU6E,MAAEW,MAAA,SAAAA,MAAA,QArU/EO,gBAAA,CAqU6E,IAAE,E;MArU/EC,CAAA;;IAAAF,OAAA,EAAAjB,QAAA,CA+TM,MAAkC,C,4BAAlCC,mBAAA,CAAkC,cAA5B,uBAAqB,qB;IA/TjCkB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}