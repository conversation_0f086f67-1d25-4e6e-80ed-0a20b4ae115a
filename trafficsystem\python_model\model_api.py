#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
YOLOv12x模型API服务
提供REST API用于图片和视频分析
"""

import os
import time
import base64
import json
import logging
import traceback
import numpy as np
import cv2
from datetime import datetime
from pathlib import Path
from io import BytesIO
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from ultralytics import YOLO
from bson import ObjectId
from pymongo import MongoClient
from gridfs import GridFS
import tempfile
import uuid
import subprocess
import shutil
import requests
import threading

# 导入配置
from config import MODEL_CONFIG, VIDEO_CONFIG, MONGODB_CONFIG
from db_config import DatabaseConfig

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("模型API服务")

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 设置最大请求体积为500MB (视频文件较大)
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024

# 初始化数据库
db_config = DatabaseConfig()
fs = db_config.fs  # GridFS引用

# 连接MongoDB
def get_mongodb_client():
    try:
        client = MongoClient(MONGODB_CONFIG["uri"])
        return client
    except Exception as e:
        logger.error(f"MongoDB连接失败: {str(e)}")
        return None

# 获取MongoDB客户端
mongodb_client = get_mongodb_client()

# 加载YOLOv11x模型
def load_model():
    try:
        model_path = MODEL_CONFIG["model_path"]
        logger.info(f"正在加载YOLOv11x模型: {model_path}")

        if not os.path.exists(model_path):
            logger.error(f"模型文件不存在: {model_path}")
            return None

        # 修复PyTorch 2.6版本的weights_only问题
        import torch
        # 临时设置torch.load的安全模式为False以支持YOLO模型
        original_load = torch.load
        def safe_load(*args, **kwargs):
            kwargs.setdefault('weights_only', False)
            return original_load(*args, **kwargs)
        torch.load = safe_load

        try:
            model = YOLO(model_path)
            logger.info(f"YOLOv11x模型加载成功")
            return model
        finally:
            # 恢复原始的torch.load函数
            torch.load = original_load

    except Exception as e:
        logger.error(f"模型加载失败: {str(e)}")
        logger.error(traceback.format_exc())
        return None

# 全局模型变量
model = load_model()

# 车辆类别映射函数
def get_vehicle_class_name(class_id):
    """
    根据COCO数据集的类别ID获取车辆类别名称

    Args:
        class_id: COCO数据集的类别ID

    Returns:
        str: 车辆类别名称
    """
    vehicle_class_mapping = {
        2: "car",        # 汽车
        3: "motorcycle", # 摩托车
        5: "bus",        # 公交车
        7: "truck"       # 卡车
    }

    return vehicle_class_mapping.get(class_id, f"vehicle_{class_id}")

# WebSocket推送配置
JAVA_BACKEND_URL = "http://localhost:8080"  # Java后端地址
FRAME_PUSH_INTERVAL = 1  # 每处理N帧推送一次关键帧（设置为1，每帧都推送）
FRAME_PREVIEW_SIZE = (480, 360)  # 预览帧尺寸（降低分辨率提高性能）
FRAME_QUALITY = 60  # JPEG压缩质量（降低质量提高速度）

# 智能帧选择配置（优化实时预览性能）
SMART_FRAME_SELECTION = True   # 启用智能帧选择
MIN_DETECTION_CHANGE = 0  # 最小检测变化阈值（降低以提高响应性）
MAX_FRAME_SIZE_KB = 100  # 最大帧大小（KB）- 降低以提高传输速度
ADAPTIVE_QUALITY = True  # 启用自适应质量调整
NETWORK_TIMEOUT = 3  # 网络超时时间（秒）
FORCE_PUSH_WITH_DETECTION = True  # 强制推送有检测结果的帧
PUSH_EVERY_FRAME = False  # 使用智能选择，但频率更高
LONG_VIDEO_THRESHOLD = 30  # 长视频阈值（秒）
LONG_VIDEO_FRAME_INTERVAL = 2  # 长视频帧推送间隔（从5降到2，提高帧率）
REALTIME_PREVIEW_MODE = True  # 启用实时预览模式
REALTIME_FRAME_INTERVAL = 1  # 实时预览模式下的帧间隔（每帧推送）

def send_four_way_frame_to_backend(task_id, direction, frame_data, frame_number, total_frames, detection_count):
    """
    向Java后端发送四方向帧数据

    Args:
        task_id: 任务ID
        direction: 方向（east, south, west, north）
        frame_data: Base64编码的帧图像数据
        frame_number: 当前帧号
        total_frames: 总帧数
        detection_count: 检测到的车辆数量
    """
    try:
        # 清理任务ID，移除可能的前缀（如 h_, v_）
        clean_task_id = task_id
        if '_' in task_id and len(task_id.split('_')[0]) <= 2:
            # 如果任务ID以短前缀开头（如 h_, v_），则移除前缀
            parts = task_id.split('_', 1)
            if len(parts) > 1:
                clean_task_id = parts[1]
                logger.debug(f"清理任务ID: {task_id} -> {clean_task_id}")

        # 构建推送数据
        payload = {
            "type": "four_way_frame_update",
            "taskId": clean_task_id,  # 使用清理后的任务ID
            "direction": direction,
            "frameNumber": frame_number,
            "totalFrames": total_frames,
            "imageData": frame_data,
            "detectionCount": detection_count,
            "timestamp": datetime.now().isoformat(),
            "quality": get_adaptive_quality(),
            "networkStats": network_performance
        }

        # 发送POST请求到Java后端
        url = "http://localhost:8080/api/video-progress/four-way-frame-update"
        headers = {"Content-Type": "application/json"}

        response = requests.post(url, json=payload, headers=headers, timeout=2)

        if response.status_code == 200:
            logger.info(f"✓ 成功推送四方向{direction}方向帧数据: 任务{clean_task_id}, 帧{frame_number}")
        else:
            logger.warning(f"✗ 推送四方向{direction}方向帧数据失败: HTTP {response.status_code}")

    except requests.exceptions.Timeout:
        logger.warning(f"推送四方向{direction}方向帧数据超时: 任务{task_id}, 帧{frame_number}")
    except Exception as e:
        logger.error(f"推送四方向{direction}方向帧数据时出错: {str(e)}")

def push_four_way_frame_data(task_id, direction, frame_data, frame_number, total_frames, detection_count):
    """
    推送四方向帧数据到Java后端的WebSocket（简化版本）

    Args:
        task_id: 任务ID
        direction: 方向（east, south, west, north）
        frame_data: Base64编码的帧图像数据
        frame_number: 当前帧号
        total_frames: 总帧数
        detection_count: 检测到的车辆数量
    """
    send_four_way_frame_to_backend(task_id, direction, frame_data, frame_number, total_frames, detection_count)

def send_frame_to_backend(task_id, frame_data, frame_number, total_frames, detection_count):
    """
    向Java后端发送帧数据（增强版，包含性能监控）

    Args:
        task_id: 任务ID
        frame_data: Base64编码的帧图像数据
        frame_number: 当前帧号
        total_frames: 总帧数
        detection_count: 检测到的车辆数量
    """
    try:
        # 清理任务ID，移除可能的前缀（如 h_, v_）
        clean_task_id = task_id
        if '_' in task_id and len(task_id.split('_')[0]) <= 2:
            # 如果任务ID以短前缀开头（如 h_, v_），则移除前缀
            parts = task_id.split('_', 1)
            if len(parts) > 1:
                clean_task_id = parts[1]
                logger.debug(f"清理任务ID: {task_id} -> {clean_task_id}")

        # 构建推送数据
        payload = {
            "type": "frame_update",
            "taskId": clean_task_id,  # 使用清理后的任务ID
            "frameNumber": frame_number,
            "totalFrames": total_frames,
            "imageData": frame_data,
            "detectionCount": detection_count,
            "timestamp": datetime.now().isoformat(),
            "quality": get_adaptive_quality(),
            "networkStats": network_performance
        }

        # 发送到Java后端的WebSocket推送接口
        url = f"{JAVA_BACKEND_URL}/api/video-progress/frame-update"
        headers = {
            'Content-Type': 'application/json'
        }

        # 异步发送，避免阻塞视频处理
        def send_async():
            start_time = time.time()
            success = False

            try:
                logger.info(f"发送帧数据到: {url}")
                logger.debug(f"帧数据大小: {len(str(payload))} 字符")

                response = requests.post(url, json=payload, headers=headers, timeout=NETWORK_TIMEOUT)
                response_time = time.time() - start_time

                if response.status_code == 200:
                    success = True
                    logger.info(f"✓ 成功推送帧数据: 任务{task_id}, 帧{frame_number}, 耗时{response_time:.2f}s")
                    logger.debug(f"响应内容: {response.text}")
                else:
                    logger.warning(f"✗ 推送帧数据失败: HTTP {response.status_code}, 耗时{response_time:.2f}s")
                    logger.warning(f"响应内容: {response.text}")

                # 更新网络性能统计
                update_network_performance(response_time, success)

            except requests.exceptions.Timeout:
                response_time = time.time() - start_time
                logger.warning(f"⏰ 推送帧数据超时: 任务{task_id}, 帧{frame_number}, 耗时{response_time:.2f}s")
                update_network_performance(response_time, False)

            except requests.exceptions.ConnectionError as e:
                response_time = time.time() - start_time
                logger.error(f"🔌 连接错误: 无法连接到Java后端 {url}, 错误: {str(e)}")
                update_network_performance(response_time, False)

            except Exception as e:
                response_time = time.time() - start_time
                logger.error(f"❌ 推送帧数据异常: {str(e)}, 耗时{response_time:.2f}s")
                import traceback
                logger.error(f"错误堆栈: {traceback.format_exc()}")
                update_network_performance(response_time, False)

        # 在新线程中发送，避免阻塞
        thread = threading.Thread(target=send_async)
        thread.daemon = True
        thread.start()

    except Exception as e:
        logger.warning(f"准备推送帧数据时出错: {str(e)}")

# 全局变量用于智能帧选择
last_detection_count = 0
last_pushed_frame = 0
network_performance = {"avg_response_time": 1.0, "success_rate": 1.0}
current_quality = FRAME_QUALITY

def update_network_performance(response_time, success):
    """
    更新网络性能统计

    Args:
        response_time: 响应时间（秒）
        success: 是否成功
    """
    global network_performance

    # 使用指数移动平均更新响应时间
    alpha = 0.3
    network_performance["avg_response_time"] = (
        alpha * response_time + (1 - alpha) * network_performance["avg_response_time"]
    )

    # 更新成功率
    network_performance["success_rate"] = (
        alpha * (1.0 if success else 0.0) + (1 - alpha) * network_performance["success_rate"]
    )

def should_push_frame(frame_number, detection_count, total_frames, video_duration=None, realtime_mode=True):
    """
    判断是否应该推送当前帧（优化实时预览性能）

    Args:
        frame_number: 当前帧号
        detection_count: 当前检测数量
        total_frames: 总帧数
        video_duration: 视频时长（秒）
        realtime_mode: 是否为实时预览模式

    Returns:
        bool: 是否应该推送
    """
    global last_detection_count, last_pushed_frame

    # 实时预览模式：大幅提高推送频率
    if realtime_mode and REALTIME_PREVIEW_MODE:
        # 实时模式下每帧都推送，或者使用很小的间隔
        if REALTIME_FRAME_INTERVAL == 1:
            return True
        else:
            return frame_number % REALTIME_FRAME_INTERVAL == 0

    # 如果启用了每帧推送模式，直接返回True（仅用于短视频）
    if PUSH_EVERY_FRAME and (video_duration is None or video_duration <= LONG_VIDEO_THRESHOLD):
        return True

    # 判断是否为长视频
    is_long_video = video_duration is not None and video_duration > LONG_VIDEO_THRESHOLD

    # 根据视频长度调整推送间隔（已优化）
    if is_long_video:
        # 长视频使用优化后的推送间隔
        push_interval = LONG_VIDEO_FRAME_INTERVAL  # 现在是2而不是5
        min_interval = 2  # 最小间隔降低到2帧
    else:
        # 短视频使用原有间隔
        push_interval = FRAME_PUSH_INTERVAL
        min_interval = 1  # 短视频最小间隔降低到1帧

    # 如果未启用智能选择，使用固定间隔
    if not SMART_FRAME_SELECTION:
        return frame_number % push_interval == 0

    # 智能选择逻辑（优化版）
    # 强制推送有检测结果的帧（降低最小间隔要求）
    if FORCE_PUSH_WITH_DETECTION and detection_count > 0:
        if frame_number - last_pushed_frame >= min_interval:
            return True

    # 检查是否达到最小推送间隔
    if frame_number - last_pushed_frame < min_interval:
        return False

    # 检查是否达到最大推送间隔（缩短最大间隔）
    max_interval = push_interval * 2 if is_long_video else push_interval * 1.5
    if frame_number - last_pushed_frame >= max_interval:
        return True

    # 检查检测数量变化（降低变化阈值）
    detection_change = abs(detection_count - last_detection_count)
    if detection_change >= MIN_DETECTION_CHANGE:
        return True

    # 在视频开始和结束时推送
    if frame_number <= 10 or frame_number >= total_frames - 10:
        return True

    # 定期推送
    if frame_number % push_interval == 0:
        return True

    # 如果检测到车辆且间隔足够，推送
    if detection_count > 0 and frame_number - last_pushed_frame >= min_interval:
        return True

    return False

def get_adaptive_quality():
    """
    根据网络性能自适应调整图像质量

    Returns:
        int: 调整后的JPEG质量
    """
    global current_quality

    if not ADAPTIVE_QUALITY:
        return FRAME_QUALITY

    # 根据网络性能调整质量
    avg_time = network_performance["avg_response_time"]
    success_rate = network_performance["success_rate"]

    if avg_time > 2.0 or success_rate < 0.8:
        # 网络较差，降低质量
        current_quality = max(50, current_quality - 5)
    elif avg_time < 0.5 and success_rate > 0.95:
        # 网络良好，提高质量
        current_quality = min(85, current_quality + 2)

    return current_quality

def prepare_frame_for_push(frame, detections_count, frame_number=0):
    """
    准备帧数据用于推送（增强版）

    Args:
        frame: OpenCV帧图像
        detections_count: 检测数量
        frame_number: 帧号

    Returns:
        str: Base64编码的图像数据
    """
    try:
        # 调整帧大小以减少传输数据量
        resized_frame = cv2.resize(frame, FRAME_PREVIEW_SIZE)

        # 添加检测信息文本
        cv2.putText(resized_frame, f"Vehicles: {detections_count}",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

        # 添加帧号信息
        cv2.putText(resized_frame, f"Frame: {frame_number}",
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)

        # 使用自适应质量
        quality = get_adaptive_quality()
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
        _, buffer = cv2.imencode('.jpg', resized_frame, encode_param)

        # 检查文件大小
        size_kb = len(buffer) / 1024
        if size_kb > MAX_FRAME_SIZE_KB:
            # 如果太大，降低质量重新编码
            quality = max(30, quality - 20)
            encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
            _, buffer = cv2.imencode('.jpg', resized_frame, encode_param)
            logger.debug(f"帧大小过大，降低质量到 {quality}")

        # 转换为Base64
        frame_base64 = base64.b64encode(buffer).decode('utf-8')

        logger.debug(f"帧数据准备完成: 质量={quality}, 大小={len(buffer)/1024:.1f}KB")
        return frame_base64

    except Exception as e:
        logger.error(f"准备推送帧数据时出错: {str(e)}")
        return None

# 四方向视频分析函数
def analyze_four_way_direction_video_file(video_path, task_id, direction, save_video=False, enable_realtime_push=False):
    """
    分析四方向单个方向的视频文件并返回结果

    Args:
        video_path: 视频文件路径
        task_id: 任务ID
        direction: 方向（east, south, west, north）
        save_video: 是否保存处理后的视频
        enable_realtime_push: 是否启用实时帧推送
    """
    try:
        logger.info(f"开始分析四方向{direction}方向视频: {video_path}")

        # 打开视频文件
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise Exception(f"无法打开视频文件: {video_path}")

        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = total_frames / fps if fps > 0 else 0

        logger.info(f"四方向{direction}方向视频信息: {width}x{height}, {fps}fps, {total_frames}帧, {duration:.2f}秒")

        # 初始化结果统计
        total_detections = 0
        vehicle_types = {}
        detection_results = []
        frame_count = 0

        # 设置输出视频（如果需要保存）
        output_video_path = None
        out = None
        if save_video:
            output_video_path = os.path.join(tempfile.gettempdir(), f"four_way_{direction}_analyzed_{task_id}.mp4")
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                frame_count += 1

                # 使用YOLO模型进行检测
                results = model(frame, verbose=False)

                # 处理检测结果
                frame_detections = 0
                frame_vehicles = {}
                detected_objects = []

                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        for box in boxes:
                            # 获取类别ID和置信度
                            class_id = int(box.cls[0])
                            confidence = float(box.conf[0])

                            # 只处理车辆类别（根据COCO数据集）
                            if class_id in [2, 3, 5, 7]:  # car, motorcycle, bus, truck
                                class_name = get_vehicle_class_name(class_id)

                                if confidence >= 0.5:  # 置信度阈值
                                    frame_detections += 1
                                    frame_vehicles[class_name] = frame_vehicles.get(class_name, 0) + 1

                                    # 获取边界框坐标
                                    x1, y1, x2, y2 = box.xyxy[0].tolist()

                                    detected_objects.append({
                                        'class_id': class_id,
                                        'class_name': class_name,
                                        'confidence': confidence,
                                        'bbox': [x1, y1, x2, y2]
                                    })

                                    # 在帧上绘制检测框
                                    cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                                    cv2.putText(frame, f'{class_name}: {confidence:.2f}',
                                              (int(x1), int(y1) - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                # 更新总体统计
                total_detections += frame_detections
                for vehicle_type, count in frame_vehicles.items():
                    vehicle_types[vehicle_type] = vehicle_types.get(vehicle_type, 0) + count

                # 记录检测结果
                detection_results.append({
                    'frame_number': frame_count,
                    'timestamp': frame_count / fps,
                    'vehicle_count': frame_detections,
                    'detected_objects': detected_objects
                })

                # 保存处理后的帧
                if out is not None:
                    out.write(frame)

                # 实时推送帧数据（如果启用）- 使用智能选择
                if enable_realtime_push:
                    try:
                        # 使用智能帧选择判断是否推送（启用实时模式）
                        should_push = should_push_frame(frame_count, frame_detections, total_frames, duration, realtime_mode=True)

                        if should_push:
                            # 使用优化的帧准备函数
                            frame_base64 = prepare_frame_for_push(frame, frame_detections, frame_count)

                            if frame_base64:
                                # 推送四方向帧数据
                                push_four_way_frame_data(task_id, direction, frame_base64, frame_count, total_frames, frame_detections)

                            # 更新推送状态
                            global last_detection_count, last_pushed_frame
                            last_detection_count = frame_detections
                            last_pushed_frame = frame_count

                    except Exception as e:
                        logger.warning(f"推送四方向{direction}方向帧数据失败: {str(e)}")

                # 进度日志
                if frame_count % 30 == 0:
                    progress = (frame_count / total_frames) * 100
                    logger.info(f"四方向{direction}方向分析进度: {progress:.1f}% ({frame_count}/{total_frames})")

        finally:
            cap.release()
            if out is not None:
                out.release()

        # 构建分析结果
        result = {
            "status": "success",
            "task_id": task_id,
            "direction": direction,
            "result": {
                "video_info": {
                    "width": width,
                    "height": height,
                    "fps": fps,
                    "total_frames": total_frames,
                    "duration": duration
                },
                "vehicle_count": total_detections,
                "vehicle_types": vehicle_types,
                "detection_results": detection_results[:50],  # 只返回前50帧的详细结果
                "analysis_summary": {
                    "total_frames_analyzed": frame_count,
                    "average_vehicles_per_frame": total_detections / frame_count if frame_count > 0 else 0,
                    "peak_vehicles_in_frame": max([r['vehicle_count'] for r in detection_results]) if detection_results else 0
                }
            }
        }

        if output_video_path and os.path.exists(output_video_path):
            result["result"]["output_video_path"] = output_video_path

        logger.info(f"四方向{direction}方向视频分析完成: 总检测数={total_detections}, 车辆类型={vehicle_types}")
        return result

    except Exception as e:
        logger.error(f"四方向{direction}方向视频分析失败: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "status": "error",
            "message": f"四方向{direction}方向视频分析失败: {str(e)}",
            "task_id": task_id,
            "direction": direction
        }

# 视频分析函数
def analyze_video_file(video_path, task_id=None, save_video=True, enable_realtime_push=True):
    """
    分析视频文件并生成带有检测框的结果视频

    Args:
        video_path: 视频文件路径
        task_id: 任务ID
        save_video: 是否保存结果视频
        enable_realtime_push: 是否启用实时帧推送

    Returns:
        dict: 包含分析结果的字典
    """
    if model is None:
        logger.error("模型未加载，无法分析视频")
        return {"status": "error", "message": "模型未加载"}
    
    try:
        logger.info(f"开始分析视频: {video_path}")
        start_time = time.time()
        
        # 打开视频文件
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频文件: {video_path}")
            return {"status": "error", "message": "无法打开视频文件"}
        
        # 获取视频属性
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps if fps > 0 else 0
        
        logger.info(f"视频属性: fps={fps}, width={width}, height={height}, frames={total_frames}, duration={duration:.2f}秒")
        
        # 创建临时输出视频文件
        output_path = None
        video_writer = None
        
        if save_video:
            temp_dir = tempfile.gettempdir()
            file_id = task_id or str(uuid.uuid4())[:8]
            
            # 直接创建MP4文件，尝试多种浏览器兼容编码器
            output_path = os.path.join(temp_dir, f"analyzed_video_{file_id}.mp4")
            
            # 尝试各种常见的编码器，按浏览器兼容性排序
            encoders = [
                ('avc1', 'AVC1/H.264编码器'),  # 常用H.264实现
                ('H264', 'H.264编码器'),       # 直接H.264编码器
                ('X264', 'X264编码器'),        # 另一种H.264实现
                ('mp4v', 'MP4V编码器'),        # MP4V编码器，大多数平台支持
                ('XVID', 'XVID编码器'),        # 备选编码器
                ('DIVX', 'DIVX编码器'),        # 备选编码器
            ]
            
            # 尝试所有编码器直到找到一个可用的
            video_writer = None
            for code, name in encoders:
                try:
                    logger.info(f"尝试使用{name}({code})创建视频")
                    fourcc = cv2.VideoWriter_fourcc(*code)
                    temp_writer = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
                    
                    if temp_writer.isOpened():
                        logger.info(f"成功使用{name}({code})编码器")
                        video_writer = temp_writer
                        break
                    else:
                        logger.warning(f"{name}({code})编码器不可用")
                        # 确保关闭失败的writer
                        temp_writer.release()
                except Exception as e:
                    logger.warning(f"尝试{name}({code})编码器时出错: {str(e)}")
            
            # 如果所有编码器都失败，尝试使用系统默认编码器
            if video_writer is None or not video_writer.isOpened():
                logger.warning("所有指定编码器都失败，尝试使用系统默认编码器")
                # 使用-1表示系统默认编码器
                try:
                    video_writer = cv2.VideoWriter(output_path, -1, fps, (width, height))
                    if video_writer.isOpened():
                        logger.info("成功使用系统默认编码器")
                    else:
                        logger.error("所有编码器都失败，无法创建视频")
                        save_video = False
                except Exception as e:
                    logger.error(f"使用系统默认编码器时出错: {str(e)}")
                    save_video = False
            
            if video_writer and video_writer.isOpened():
                logger.info(f"创建输出视频: {output_path}")
            else:
                logger.error("无法创建视频写入器，放弃视频保存")
                save_video = False
        
        # 初始化统计信息
        frame_count = 0
        vehicle_counts = []
        vehicle_types = {}
        all_detections = []
        
        # 处理进度报告频率
        progress_interval = max(1, total_frames // 20)  # 每5%报告一次

        # 开始逐帧处理
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # 增加帧计数
            frame_count += 1

            # 计算进度
            progress = (frame_count / total_frames) * 100 if total_frames > 0 else 0

            # 定期输出进度
            if frame_count % progress_interval == 0:
                logger.info(f"视频处理进度: {progress:.1f}% ({frame_count}/{total_frames})")

            # 使用模型进行检测
            results = model(frame, conf=MODEL_CONFIG["conf_threshold"], iou=MODEL_CONFIG["iou_threshold"])

            # 提取当前帧的检测结果
            frame_detections = []
            frame_vehicle_count = 0

            if results and len(results) > 0:
                result = results[0]

                # 获取检测框和类别
                if hasattr(result, 'boxes') and result.boxes is not None:
                    for box in result.boxes:
                        cls_id = int(box.cls.item())
                        confidence = float(box.conf.item())
                        xyxy = box.xyxy.cpu().numpy()[0]

                        # 获取类别名称
                        class_name = MODEL_CONFIG["class_names"].get(cls_id, result.names[cls_id])

                        # 计算车辆数量
                        is_vehicle = False
                        if cls_id in MODEL_CONFIG["vehicle_classes"]:
                            frame_vehicle_count += 1
                            is_vehicle = True

                            # 更新车辆类型统计
                            if class_name in vehicle_types:
                                vehicle_types[class_name] += 1
                            else:
                                vehicle_types[class_name] = 1

                        # 添加到检测结果
                        detection = {
                            "frame": frame_count,
                            "class_id": cls_id,
                            "class_name": class_name,
                            "confidence": confidence,
                            "bbox": xyxy.tolist(),
                            "is_vehicle": is_vehicle
                        }

                        frame_detections.append(detection)
                        all_detections.append(detection)

            # 存储当前帧车辆数量
            vehicle_counts.append(frame_vehicle_count)

            # 智能实时帧推送逻辑
            if enable_realtime_push and task_id:
                try:
                    # 使用智能帧选择算法判断是否推送，传递视频时长
                    should_push = should_push_frame(frame_count, frame_vehicle_count, total_frames, duration)

                    # 添加详细日志（每10帧记录一次，避免日志过多）
                    if frame_count <= 5 or frame_count % 10 == 0:
                        logger.info(f"帧推送检查: 任务{task_id}, 帧{frame_count}/{total_frames}, 车辆{frame_vehicle_count}, 是否推送: {should_push}")

                    if should_push:
                        # 获取带有检测框的帧用于推送
                        push_frame = results[0].plot() if results and len(results) > 0 else frame

                        # 准备推送数据
                        frame_data = prepare_frame_for_push(push_frame, frame_vehicle_count, frame_count)

                        if frame_data:
                            # 发送到后端
                            send_frame_to_backend(task_id, frame_data, frame_count, total_frames, frame_vehicle_count)

                            # 更新全局状态
                            global last_detection_count, last_pushed_frame
                            last_detection_count = frame_vehicle_count
                            last_pushed_frame = frame_count

                            # 只在有检测结果或每20帧时记录详细日志
                            if frame_vehicle_count > 0 or frame_count % 20 == 0:
                                logger.info(f"✓ 推送帧: 任务{task_id}, 帧{frame_count}, 车辆数{frame_vehicle_count}, 质量{get_adaptive_quality()}")
                        else:
                            logger.warning(f"✗ 帧数据准备失败: 任务{task_id}, 帧{frame_count}")

                except Exception as e:
                    logger.error(f"推送帧数据时出错: 任务{task_id}, 帧{frame_count}, 错误: {str(e)}")
                    import traceback
                    logger.error(f"错误堆栈: {traceback.format_exc()}")
                    # 推送失败不影响视频处理继续进行
            
            # 如果需要保存视频
            if save_video and video_writer is not None:
                # 获取带有检测框的帧
                result_frame = results[0].plot() if results and len(results) > 0 else frame
                
                # 添加帧号和统计信息
                timestamp = frame_count / fps
                cv2.putText(result_frame, f"Frame: {frame_count}/{total_frames} Time: {timestamp:.2f}s", 
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                cv2.putText(result_frame, f"Vehicles: {frame_vehicle_count}", 
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                
                # 写入视频帧
                video_writer.write(result_frame)
        
        # 释放资源
        cap.release()
        
        # 处理视频文件
        result_file_id = None
        if save_video and video_writer is not None:
            # 确保正确关闭视频写入器
            video_writer.release()
            logger.info(f"视频编辑完成，已保存到: {output_path}")
            
            # 检查输出文件是否有效
            if os.path.exists(output_path) and os.path.getsize(output_path) > 100000:  # 至少100KB
                logger.info(f"视频文件生成成功: {output_path}, 大小: {os.path.getsize(output_path)/1024:.2f}KB")
                
                # 验证生成的视频文件是否可以打开
                try:
                    test_cap = cv2.VideoCapture(output_path)
                    if test_cap.isOpened():
                        # 获取视频的编解码器信息
                        fourcc_int = int(test_cap.get(cv2.CAP_PROP_FOURCC))
                        fourcc_str = "".join([chr((fourcc_int >> 8 * i) & 0xFF) for i in range(4)])
                        logger.info(f"视频编码器: {fourcc_str}")
                        
                        test_cap.release()
                        # 文件有效，准备保存到GridFS
                        with open(output_path, 'rb') as f:
                            file_data = f.read()
                            # 再次检查数据大小
                            if len(file_data) > 0:
                                file_size = os.path.getsize(output_path)
                                result_file_id = fs.put(
                                    file_data,
                                    filename=f"analyzed_video_{file_id}.mp4",
                                    content_type="video/mp4",  # 使用标准MIME类型
                                    file_size=file_size,
                                    codec=fourcc_str,  # 记录编码器信息
                                    task_id=task_id,
                                    analysis_date=datetime.now(),
                                    vehicle_count=max(vehicle_counts) if vehicle_counts else 0,
                                    vehicle_types=json.dumps(vehicle_types),
                                    processing_time=time.time() - start_time,
                                    total_frames=total_frames,
                                    duration=duration
                                )
                                logger.info(f"分析结果视频保存到GridFS: {result_file_id}, 大小: {file_size/1024:.2f}KB")
                            else:
                                logger.error(f"读取到的文件数据为空，不保存到GridFS")
                    else:
                        logger.error(f"无法打开生成的视频文件，不保存到GridFS")
                except Exception as e:
                    logger.error(f"验证视频文件时出错: {str(e)}")
            else:
                logger.error(f"视频文件无效或为空: {output_path}")
                if os.path.exists(output_path):
                    logger.error(f"文件大小: {os.path.getsize(output_path)} 字节")
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 计算总车辆数量 (取每帧检测到的最大值)
        max_vehicles = max(vehicle_counts) if vehicle_counts else 0
        avg_vehicles = sum(vehicle_counts) / len(vehicle_counts) if vehicle_counts else 0
        
        logger.info(f"视频分析完成: 处理时间={processing_time:.2f}秒, 最大车辆数={max_vehicles}, 平均车辆数={avg_vehicles:.2f}")
        
        # 构建结果
        result = {
            "status": "success",
            "task_id": task_id,
            "processing_time": processing_time,
            "total_frames": total_frames,
            "fps": fps,
            "duration": duration,
            "width": width,
            "height": height,
            "vehicle_count": max_vehicles,
            "vehicle_types": vehicle_types,
            "result_file_id": str(result_file_id) if result_file_id else None,
            "output_path": output_path if output_path and os.path.exists(output_path) else None
        }
        
        return result
    
    except Exception as e:
        logger.error(f"视频分析出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {"status": "error", "message": f"视频分析出错: {str(e)}"}

@app.route('/analyze_video', methods=['POST'])
def analyze_video_api():
    """视频分析API"""
    if model is None:
        return jsonify({"status": "error", "message": "模型未加载"}), 500
    
    try:
        # 检查请求
        if 'video' not in request.files:
            return jsonify({"status": "error", "message": "未提供视频文件"}), 400
        
        # 获取视频文件
        video_file = request.files['video']
        
        # 获取任务ID (如果有)
        task_id = request.form.get('task_id', str(uuid.uuid4()))
        
        # 保存视频到临时文件
        temp_video_path = os.path.join(tempfile.gettempdir(), f"temp_video_{task_id}.mp4")
        video_file.save(temp_video_path)
        
        logger.info(f"收到视频分析请求: task_id={task_id}, 视频已保存到 {temp_video_path}")
        
        # 分析视频
        result = analyze_video_file(temp_video_path, task_id)
        
        # 清理临时文件
        try:
            os.remove(temp_video_path)
        except:
            pass
        
        return jsonify(result)
    
    except Exception as e:
        logger.error(f"处理视频分析请求出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"处理视频分析请求出错: {str(e)}"}), 500

@app.route('/analyze_video_from_gridfs', methods=['POST'])
def analyze_video_from_gridfs():
    """从GridFS中获取视频并分析"""
    if model is None:
        return jsonify({"status": "error", "message": "模型未加载"}), 500
    
    try:
        # 检查请求
        data = request.json
        if not data or 'video_id' not in data:
            return jsonify({"status": "error", "message": "未提供视频ID"}), 400
        
        video_id = data['video_id']
        task_id = data.get('task_id', str(uuid.uuid4()))
        
        logger.info(f"收到GridFS视频分析请求: video_id={video_id}, task_id={task_id}")
        
        # 从GridFS获取视频
        try:
            obj_id = ObjectId(video_id)
            if not fs.exists(obj_id):
                return jsonify({"status": "error", "message": f"GridFS中未找到视频: {video_id}"}), 404
            
            # 获取视频文件
            video_file = fs.get(obj_id)
            
            # 保存到临时文件
            temp_video_path = os.path.join(tempfile.gettempdir(), f"gridfs_video_{task_id}.mp4")
            with open(temp_video_path, 'wb') as f:
                f.write(video_file.read())
            
            logger.info(f"已从GridFS获取视频并保存到 {temp_video_path}")
            
            # 分析视频，启用实时推送
            result = analyze_video_file(temp_video_path, task_id, save_video=True, enable_realtime_push=True)
            
            # 清理临时文件
            try:
                os.remove(temp_video_path)
            except:
                pass
            
            return jsonify(result)
            
        except Exception as e:
            logger.error(f"从GridFS获取视频失败: {str(e)}")
            return jsonify({"status": "error", "message": f"从GridFS获取视频失败: {str(e)}"}), 500
        
    except Exception as e:
        logger.error(f"处理GridFS视频分析请求出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"处理GridFS视频分析请求出错: {str(e)}"}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    if model is not None:
        return jsonify({"status": "ok", "message": "服务正常运行"})
    else:
        return jsonify({"status": "error", "message": "模型加载失败"}), 500

@app.route('/analyze', methods=['POST'])
def analyze_image():
    """分析图片接口"""
    if model is None:
        return jsonify({"status": "error", "message": "模型未加载"}), 500
    
    try:
        # 检查请求中是否包含图片
        if 'image' not in request.files and 'image_base64' not in request.json:
            return jsonify({"status": "error", "message": "未提供图片"}), 400
        
        # 从请求中获取图片
        if 'image' in request.files:
            # 文件上传方式
            image_file = request.files['image']
            image_data = image_file.read()
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        else:
            # Base64编码方式
            image_base64 = request.json['image_base64']
            # 去除可能存在的Base64头
            if ',' in image_base64:
                image_base64 = image_base64.split(',')[1]
            image_data = base64.b64decode(image_base64)
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            return jsonify({"status": "error", "message": "图片解码失败"}), 400
        
        # 调整图像大小
        image = cv2.resize(image, (VIDEO_CONFIG["resize_width"], VIDEO_CONFIG["resize_height"]))
        
        # 记录开始时间
        start_time = time.time()
        
        # 使用模型预测
        results = model(image, conf=MODEL_CONFIG["conf_threshold"], iou=MODEL_CONFIG["iou_threshold"])
        
        # 计算预测时间
        inference_time = time.time() - start_time
        
        # 提取检测结果
        detections = []
        vehicle_count = 0
        
        if results and len(results) > 0:
            result = results[0]
            
            # 获取检测框和类别
            if hasattr(result, 'boxes') and result.boxes is not None:
                for box in result.boxes:
                    cls_id = int(box.cls.item())
                    confidence = float(box.conf.item())
                    xyxy = box.xyxy.cpu().numpy()[0]
                    
                    # 获取类别名称
                    class_name = MODEL_CONFIG["class_names"].get(cls_id, result.names[cls_id])
                    
                    # 输出调试信息
                    logger.info(f"检测到对象: class_id={cls_id}, class_name={class_name}, confidence={confidence:.4f}")
                    
                    # 车辆类别统计 - 使用ID判断
                    if cls_id in MODEL_CONFIG["vehicle_classes"]:
                        vehicle_count += 1
                        logger.info(f"计入车辆: class_id={cls_id}, class_name={class_name}")
                    # 备用判断 - 使用类别名称判断
                    elif class_name.lower() in ["car", "truck", "bus", "motorcycle", "汽车", "卡车", "公交车", "摩托车"]:
                        vehicle_count += 1
                        logger.info(f"通过名称计入车辆: class_id={cls_id}, class_name={class_name}")
                    
                    # 添加到检测结果
                    detections.append({
                        "class_id": cls_id,
                        "class_name": class_name,
                        "confidence": confidence,
                        "bbox": xyxy.tolist()
                    })
        
        # 记录最终计算的车辆数量
        logger.info(f"检测到对象总数: {len(detections)}, 车辆数量: {vehicle_count}")
        
        # 创建结果图像
        result_image = results[0].plot() if results and len(results) > 0 else image
        
        # 将结果图像转换为Base64
        _, buffer = cv2.imencode('.jpg', result_image)
        result_image_base64 = base64.b64encode(buffer).decode('utf-8')
        
        # 保存结果图像到GridFS
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        image_filename = f"result_{timestamp}.jpg"
        
        # 将图像数据保存到GridFS
        file_id = fs.put(
            buffer.tobytes(), 
            filename=image_filename,
            content_type="image/jpeg",
            timestamp=datetime.now(),
            vehicle_count=vehicle_count,
            inference_time=inference_time
        )
        
        logger.info(f"保存结果图像到GridFS, 文件ID: {file_id}")
        
        # 构建图像URL (使用GridFS ID)
        image_url = str(file_id)  # 直接使用GridFS ID作为URL标识
        
        # 构建响应数据
        response_data = {
            "status": "success",
            "vehicleCount": vehicle_count,
            "detections": detections,
            "inferenceTime": inference_time,
            "result_image_base64": result_image_base64,
            "imageUrl": image_url,  # 这里是GridFS ID
            "timestamp": datetime.now().isoformat()
        }
        
        return jsonify(response_data)
    
    except Exception as e:
        logger.error(f"图像分析出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"处理图像时出错: {str(e)}"}), 500

@app.route('/status', methods=['GET'])
def status():
    """状态检查接口"""
    return jsonify({
        "status": "online",
        "model_status": "online" if model is not None else "offline",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    })

@app.route('/api/history', methods=['GET'])
def get_history():
    """获取历史记录接口"""
    try:
        # 获取查询参数
        limit = request.args.get('limit', default=10, type=int)
        skip = request.args.get('skip', default=0, type=int)
        
        # 获取数据库集合
        db = mongodb_client[MONGODB_CONFIG["database"]]
        collection = db["analysis_history"]
        
        # 查询历史记录
        cursor = collection.find().sort("timestamp", -1).skip(skip).limit(limit)
        
        # 转换结果
        history = []
        for doc in cursor:
            doc['_id'] = str(doc['_id'])  # 转换ObjectId为字符串
            history.append(doc)
        
        return jsonify({
            "status": "success",
            "data": history,
            "total": collection.count_documents({}),
            "limit": limit,
            "skip": skip
        })
        
    except Exception as e:
        logger.error(f"获取历史记录失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"获取历史记录失败: {str(e)}"}), 500

@app.route('/api/history/<history_id>', methods=['DELETE'])
def delete_history(history_id):
    """删除历史记录接口"""
    try:
        # 获取数据库集合
        db = mongodb_client[MONGODB_CONFIG["database"]]
        collection = db["analysis_history"]
        
        # 删除记录
        result = collection.delete_one({"_id": ObjectId(history_id)})
        
        if result.deleted_count > 0:
            return jsonify({"status": "success", "message": "历史记录删除成功"})
        else:
            return jsonify({"status": "error", "message": "历史记录不存在"}), 404
            
    except Exception as e:
        logger.error(f"删除历史记录失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"删除历史记录失败: {str(e)}"}), 500

@app.route('/api/media/image/<image_id>', methods=['GET'])
def get_image(image_id):
    """从GridFS获取图像接口"""
    try:
        # 将字符串ID转换为ObjectId
        obj_id = ObjectId(image_id)
        
        # 检查文件是否存在
        if not fs.exists(obj_id):
            logger.error(f"GridFS中没有找到图像: {image_id}")
            return jsonify({"error": "图像不存在"}), 404
        
        # 从GridFS获取文件
        file_data = fs.get(obj_id)
        
        # 获取文件类型
        content_type = file_data.content_type or "image/jpeg"
        
        # 返回图像数据
        response = app.response_class(
            response=file_data.read(),
            status=200,
            mimetype=content_type
        )
        
        # 添加缓存控制头
        response.headers["Cache-Control"] = "public, max-age=31536000"
        
        return response
        
    except Exception as e:
        logger.error(f"获取图像时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@app.route('/api/media/video/<video_id>', methods=['GET'])
def get_video(video_id):
    """从GridFS获取视频接口"""
    try:
        # 将字符串ID转换为ObjectId
        obj_id = ObjectId(video_id)
        
        # 检查文件是否存在
        if not fs.exists(obj_id):
            logger.error(f"GridFS中没有找到视频: {video_id}")
            return jsonify({"error": "视频不存在"}), 404
        
        # 从GridFS获取文件
        file_data = fs.get(obj_id)
        
        # 获取文件类型
        content_type = file_data.content_type or "video/mp4"
        
        # 返回视频数据
        response = app.response_class(
            response=file_data.read(),
            status=200,
            mimetype=content_type
        )
        
        # 添加缓存控制头
        response.headers["Cache-Control"] = "public, max-age=31536000"
        
        return response
        
    except Exception as e:
        logger.error(f"获取视频时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

# 为了测试，添加直接下载分析结果视频的接口
@app.route('/download_analyzed_video/<video_id>', methods=['GET'])
def download_analyzed_video(video_id):
    """下载分析后的视频"""
    try:
        # 将字符串ID转换为ObjectId
        obj_id = ObjectId(video_id)
        
        # 检查文件是否存在
        if not fs.exists(obj_id):
            logger.error(f"GridFS中没有找到视频: {video_id}")
            return jsonify({"error": "视频不存在"}), 404
        
        # 从GridFS获取文件
        file_data = fs.get(obj_id)
        
        # 获取文件名和内容类型
        filename = file_data.filename or f"analyzed_video_{video_id}.mp4"
        content_type = file_data.content_type or "video/mp4"
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp4')
        temp_file.write(file_data.read())
        temp_file.close()
        
        # 返回视频文件下载
        return send_file(
            temp_file.name,
            mimetype=content_type,
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        logger.error(f"下载分析视频时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

# 添加新的接口，用于将视频转换为MP4格式并下载
@app.route('/convert_to_mp4/<video_id>', methods=['GET'])
def convert_to_mp4(video_id):
    """转换视频为MP4格式并下载"""
    try:
        # 将字符串ID转换为ObjectId
        obj_id = ObjectId(video_id)
        
        # 检查文件是否存在
        if not fs.exists(obj_id):
            logger.error(f"GridFS中没有找到视频: {video_id}")
            return jsonify({"error": "视频不存在"}), 404
        
        # 从GridFS获取文件
        file_data = fs.get(obj_id)
        
        # 创建临时输入文件
        temp_input = tempfile.NamedTemporaryFile(delete=False, suffix='.mp4')
        temp_input.write(file_data.read())
        temp_input.close()
        
        # 构建文件名
        filename = file_data.filename
        if not filename:
            filename = f"converted_video_{video_id}.mp4"
        else:
            # 确保文件扩展名为.mp4
            name_parts = filename.rsplit('.', 1)
            filename = f"{name_parts[0]}.mp4"
        
        # 检查视频格式和编码器
        is_already_compatible = False
        try:
            cap = cv2.VideoCapture(temp_input.name)
            if cap.isOpened():
                # 获取视频编码器
                fourcc_int = int(cap.get(cv2.CAP_PROP_FOURCC))
                fourcc_str = "".join([chr((fourcc_int >> 8 * i) & 0xFF) for i in range(4)])
                
                # 获取视频属性
                fps = cap.get(cv2.CAP_PROP_FPS)
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                cap.release()
                
                # 检查是否已经是MP4格式且使用了Web兼容编码器
                is_already_compatible = fourcc_str.lower() in ['avc1', 'h264', 'x264', 'mp4v']
                logger.info(f"视频格式检查: 编码器={fourcc_str}, 是否兼容={is_already_compatible}")
        except Exception as e:
            logger.error(f"检查视频编码器时出错: {str(e)}")
        
        # 如果已经是兼容格式，直接使用原文件
        if is_already_compatible:
            logger.info(f"视频已经是Web兼容的MP4格式 (编码器: {fourcc_str})，无需转码")
            return send_file(
                temp_input.name,
                mimetype="video/mp4",
                as_attachment=True,
                download_name=filename
            )
            
        # 创建临时输出文件
        temp_output = tempfile.NamedTemporaryFile(delete=False, suffix='.mp4')
        temp_output.close()
        
        # 使用OpenCV进行视频转换
        logger.info("使用OpenCV转换视频格式")
        try:
            # 打开源视频
            cap = cv2.VideoCapture(temp_input.name)
            if not cap.isOpened():
                logger.error("无法打开源视频文件进行转换")
                return send_file(
                    temp_input.name,
                    mimetype="video/mp4",
                    as_attachment=True,
                    download_name=filename
                )
            
            # 获取视频属性
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 尝试创建MP4编码器
            success = False
            for codec in ['avc1', 'mp4v', 'H264', 'X264', 'XVID']:
                try:
                    fourcc = cv2.VideoWriter_fourcc(*codec)
                    out = cv2.VideoWriter(temp_output.name, fourcc, fps, (width, height))
                    if out.isOpened():
                        logger.info(f"成功使用{codec}编码器创建MP4文件")
                        success = True
                        break
                except Exception as e:
                    logger.warning(f"尝试{codec}编码器失败: {str(e)}")
            
            if not success:
                logger.error("所有编码器都失败，返回原始文件")
                return send_file(
                    temp_input.name,
                    mimetype="video/mp4",
                    as_attachment=True,
                    download_name=filename
                )
            
            # 逐帧转换
            frame_count = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                out.write(frame)
                frame_count += 1
                if frame_count % 100 == 0:
                    logger.info(f"已转换 {frame_count} 帧")
            
            # 释放资源
            cap.release()
            out.release()
            logger.info(f"视频转换完成，共处理 {frame_count} 帧")
            
            # 检查输出文件
            if os.path.exists(temp_output.name) and os.path.getsize(temp_output.name) > 100000:
                logger.info(f"转换后的视频大小: {os.path.getsize(temp_output.name)/1024:.2f}KB")
                return send_file(
                    temp_output.name,
                    mimetype="video/mp4",
                    as_attachment=True,
                    download_name=filename
                )
            else:
                logger.error("转换后的视频无效或太小")
                return send_file(
                    temp_input.name,
                    mimetype="video/mp4",
                    as_attachment=True,
                    download_name=filename
                )
        except Exception as e:
            logger.error(f"使用OpenCV转换视频失败: {str(e)}")
            return send_file(
                temp_input.name,
                mimetype="video/mp4",
                as_attachment=True,
                download_name=filename
            )
        
    except Exception as e:
        logger.error(f"转换视频为MP4格式失败: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

# ==================== 四方向交通分析API ====================

@app.route('/analyze_four_way_direction_video', methods=['POST'])
def analyze_four_way_direction_video():
    """四方向单个方向视频分析API"""
    if model is None:
        return jsonify({"status": "error", "message": "模型未加载"}), 500

    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "请求数据为空"}), 400

        video_id = data.get('video_id')
        task_id = data.get('task_id')
        direction = data.get('direction')

        if not video_id:
            return jsonify({"status": "error", "message": "缺少视频ID"}), 400

        if not task_id:
            return jsonify({"status": "error", "message": "缺少任务ID"}), 400

        if not direction:
            return jsonify({"status": "error", "message": "缺少方向信息"}), 400

        logger.info(f"收到四方向{direction}方向视频分析请求: video_id={video_id}, task_id={task_id}")

        # 从GridFS获取视频
        try:
            obj_id = ObjectId(video_id)
            if not fs.exists(obj_id):
                return jsonify({"status": "error", "message": f"GridFS中未找到视频: {video_id}"}), 404

            # 获取视频文件
            video_file = fs.get(obj_id)

            # 保存到临时文件
            temp_video_path = os.path.join(tempfile.gettempdir(), f"four_way_{direction}_{task_id}.mp4")
            with open(temp_video_path, 'wb') as f:
                f.write(video_file.read())

            logger.info(f"已从GridFS获取{direction}方向视频并保存到 {temp_video_path}")

            # 分析视频，启用四方向实时推送
            result = analyze_four_way_direction_video_file(temp_video_path, task_id, direction, save_video=True, enable_realtime_push=True)

            # 清理临时文件
            try:
                os.remove(temp_video_path)
            except:
                pass

            return jsonify(result)

        except Exception as e:
            logger.error(f"从GridFS获取视频失败: {str(e)}")
            return jsonify({"status": "error", "message": f"获取视频失败: {str(e)}"}), 500

    except Exception as e:
        logger.error(f"四方向{direction}方向视频分析出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"四方向视频分析出错: {str(e)}"}), 500

@app.route('/analyze_four_way_intersection', methods=['POST'])
def analyze_four_way_intersection():
    """四方向十字路口视频分析API"""
    if model is None:
        return jsonify({"status": "error", "message": "模型未加载"}), 500

    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "请求数据为空"}), 400

        task_id = data.get('task_id')
        directions_data = data.get('directions', {})

        if not task_id:
            return jsonify({"status": "error", "message": "缺少任务ID"}), 400

        if len(directions_data) != 4:
            return jsonify({"status": "error", "message": "需要提供四个方向的视频数据"}), 400

        logger.info(f"开始四方向交通分析，任务ID: {task_id}")

        # 并行处理四个方向的视频
        results = {}
        direction_names = ['east', 'south', 'west', 'north']

        for direction in direction_names:
            if direction not in directions_data:
                logger.warning(f"缺少{direction}方向的视频数据")
                continue

            direction_info = directions_data[direction]
            video_id = direction_info.get('video_id')

            if not video_id:
                logger.warning(f"{direction}方向缺少视频ID")
                continue

            logger.info(f"开始处理{direction}方向视频: {video_id}")

            # 分析单个方向的视频
            direction_result = analyze_direction_video(
                video_id,
                f"{direction}_{task_id}",
                direction
            )

            results[direction] = direction_result
            logger.info(f"{direction}方向分析完成")

        # 生成智能交通分析结果
        traffic_analysis = generate_traffic_analysis(results)

        # 保存分析结果到数据库
        save_four_way_analysis_result(task_id, results, traffic_analysis)

        response = {
            "status": "success",
            "task_id": task_id,
            "message": "四方向交通分析完成",
            "results": results,
            "traffic_analysis": traffic_analysis
        }

        logger.info(f"四方向交通分析完成，任务ID: {task_id}")
        return jsonify(response)

    except Exception as e:
        logger.error(f"四方向交通分析出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"status": "error", "message": f"四方向交通分析出错: {str(e)}"}), 500

def analyze_direction_video(video_id, task_id, direction):
    """分析单个方向的视频"""
    try:
        logger.info(f"开始分析{direction}方向视频: {video_id}")

        # 从GridFS获取视频文件
        obj_id = ObjectId(video_id)
        grid_file = fs.get(obj_id)

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
            temp_file.write(grid_file.read())
            temp_video_path = temp_file.name

        try:
            # 分析视频
            result = analyze_video_file(
                temp_video_path,
                task_id=task_id,
                save_video=False,  # 四方向分析不保存单独的视频
                enable_realtime_push=True
            )

            # 添加方向信息
            result['direction'] = direction
            result['video_id'] = video_id

            return result

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_video_path)
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")

    except Exception as e:
        logger.error(f"分析{direction}方向视频出错: {str(e)}")
        return {
            "status": "error",
            "direction": direction,
            "video_id": video_id,
            "message": f"分析{direction}方向视频出错: {str(e)}"
        }

def generate_traffic_analysis(direction_results):
    """生成智能交通分析结果"""
    try:
        logger.info("开始生成智能交通分析结果")

        # 统计各方向车辆数量
        direction_counts = {}
        total_vehicles = 0

        for direction, result in direction_results.items():
            if result.get('status') == 'success':
                vehicle_count = result.get('vehicle_count', 0)
                direction_counts[direction] = vehicle_count
                total_vehicles += vehicle_count
            else:
                direction_counts[direction] = 0

        # 找出车流量最大的方向
        peak_direction = max(direction_counts, key=direction_counts.get) if direction_counts else None
        peak_count = direction_counts.get(peak_direction, 0) if peak_direction else 0

        # 计算交通流量平衡度
        if total_vehicles > 0:
            ideal_ratio = 1.0 / 4  # 理想情况下每个方向25%
            variance = 0.0
            for count in direction_counts.values():
                actual_ratio = count / total_vehicles
                variance += (actual_ratio - ideal_ratio) ** 2
            traffic_flow_balance = max(0.0, 1.0 - (variance / ideal_ratio))
        else:
            traffic_flow_balance = 1.0

        # 确定拥堵等级
        avg_vehicles = total_vehicles / 4
        if avg_vehicles < 10:
            congestion_level = "畅通"
        elif avg_vehicles < 20:
            congestion_level = "轻度拥堵"
        elif avg_vehicles < 35:
            congestion_level = "中度拥堵"
        else:
            congestion_level = "严重拥堵"

        # 生成信号灯优化建议
        signal_optimization = generate_signal_optimization(direction_counts, total_vehicles)

        # 生成改进建议
        recommendations = generate_traffic_recommendations(
            direction_counts, congestion_level, traffic_flow_balance, peak_direction
        )

        analysis_result = {
            "total_vehicle_count": total_vehicles,
            "direction_counts": direction_counts,
            "peak_direction": peak_direction,
            "peak_count": peak_count,
            "traffic_flow_balance": round(traffic_flow_balance, 3),
            "congestion_level": congestion_level,
            "average_vehicles_per_direction": round(avg_vehicles, 1),
            "signal_optimization": signal_optimization,
            "recommendations": recommendations,
            "analysis_timestamp": datetime.now().isoformat()
        }

        logger.info("智能交通分析结果生成完成")
        return analysis_result

    except Exception as e:
        logger.error(f"生成智能交通分析结果出错: {str(e)}")
        return {
            "error": f"生成智能交通分析结果出错: {str(e)}",
            "analysis_timestamp": datetime.now().isoformat()
        }

def generate_signal_optimization(direction_counts, total_vehicles):
    """生成信号灯优化建议"""
    try:
        # 推荐信号周期（秒）
        recommended_cycle = 120

        # 总绿灯时间（扣除黄灯和红灯时间）
        total_green_time = 80

        # 根据各方向车流量分配绿灯时间
        green_time_allocation = {}

        if total_vehicles > 0:
            for direction, count in direction_counts.items():
                # 按比例分配，但确保最小15秒，最大35秒
                ratio = count / total_vehicles
                green_time = int(ratio * total_green_time)
                green_time = max(15, min(35, green_time))
                green_time_allocation[direction] = green_time
        else:
            # 如果没有车辆，平均分配
            for direction in ['east', 'south', 'west', 'north']:
                green_time_allocation[direction] = 20

        # 计算预期改善效果
        max_count = max(direction_counts.values()) if direction_counts else 0
        min_count = min(direction_counts.values()) if direction_counts else 0
        imbalance = max_count - min_count

        if imbalance > 20:
            expected_improvement = "预计可减少平均等待时间25-35%"
        elif imbalance > 10:
            expected_improvement = "预计可减少平均等待时间15-25%"
        else:
            expected_improvement = "预计可减少平均等待时间5-15%"

        return {
            "recommended_cycle": recommended_cycle,
            "green_time_allocation": green_time_allocation,
            "reason": "根据各方向车流量动态分配绿灯时间，优化通行效率",
            "expected_improvement": expected_improvement
        }

    except Exception as e:
        logger.error(f"生成信号灯优化建议出错: {str(e)}")
        return {
            "recommended_cycle": 120,
            "green_time_allocation": {"east": 20, "south": 20, "west": 20, "north": 20},
            "reason": "使用默认配置",
            "expected_improvement": "需要更多数据进行优化"
        }

def generate_traffic_recommendations(direction_counts, congestion_level, traffic_flow_balance, peak_direction):
    """生成交通改进建议"""
    recommendations = []

    try:
        # 根据拥堵等级给出建议
        if congestion_level == "严重拥堵":
            recommendations.extend([
                "建议增加交通疏导人员，特别是在高峰时段",
                "考虑实施分时段限行措施，减少车流压力",
                "优化信号灯配时，延长主要方向绿灯时间",
                "建议增设临时交通标志，引导车辆分流"
            ])
        elif congestion_level == "中度拥堵":
            recommendations.extend([
                "调整信号灯配时，提高通行效率",
                "加强交通监控，及时发现异常情况",
                "在高峰时段增加交通引导"
            ])
        elif congestion_level == "轻度拥堵":
            recommendations.extend([
                "保持现有交通管理措施",
                "定期监测交通流量变化",
                "适当优化信号配时"
            ])
        else:
            recommendations.append("交通状况良好，维持现状")

        # 根据流量平衡度给出建议
        if traffic_flow_balance < 0.6:
            recommendations.extend([
                "各方向车流量不均衡，建议优化信号配时",
                "考虑在车流量大的方向增设车道或优化路线",
                "建议实施智能交通信号控制系统"
            ])
        elif traffic_flow_balance < 0.8:
            recommendations.append("车流量分布相对均衡，可进行微调优化")

        # 根据峰值方向给出建议
        if peak_direction:
            direction_names = {
                'east': '东向', 'south': '南向',
                'west': '西向', 'north': '北向'
            }
            direction_name = direction_names.get(peak_direction, peak_direction)
            recommendations.append(f"重点关注{direction_name}的交通状况，适当延长该方向绿灯时间")

        # 通用建议
        recommendations.extend([
            "建议定期进行交通流量分析，持续优化交通管理",
            "考虑引入智能交通管理系统，实现动态调控"
        ])

        return recommendations

    except Exception as e:
        logger.error(f"生成交通建议出错: {str(e)}")
        return ["建议联系交通管理部门进行专业评估"]

def save_four_way_analysis_result(task_id, direction_results, traffic_analysis):
    """保存四方向分析结果到数据库"""
    try:
        if mongodb_client is None:
            logger.warning("MongoDB客户端未连接，无法保存分析结果")
            return

        db = mongodb_client[MONGODB_CONFIG["database"]]
        collection = db["intersection_analysis_four_way"]

        # 构建保存的数据
        result_data = {
            "task_id": task_id,
            "analysis_type": "four_way_intersection",
            "direction_results": direction_results,
            "traffic_analysis": traffic_analysis,
            "created_at": datetime.now(),
            "status": "completed"
        }

        # 更新或插入结果
        collection.update_one(
            {"task_id": task_id},
            {"$set": result_data},
            upsert=True
        )

        logger.info(f"四方向分析结果已保存到数据库，任务ID: {task_id}")

    except Exception as e:
        logger.error(f"保存四方向分析结果出错: {str(e)}")

@app.route('/get_four_way_analysis_result/<task_id>', methods=['GET'])
def get_four_way_analysis_result(task_id):
    """获取四方向分析结果"""
    try:
        if mongodb_client is None:
            return jsonify({"status": "error", "message": "数据库连接失败"}), 500

        db = mongodb_client[MONGODB_CONFIG["database"]]
        collection = db["intersection_analysis_four_way"]

        # 查找分析结果
        result = collection.find_one({"task_id": task_id})

        if not result:
            return jsonify({"status": "error", "message": "未找到分析结果"}), 404

        # 转换ObjectId为字符串
        if '_id' in result:
            result['_id'] = str(result['_id'])

        # 转换datetime为字符串
        if 'created_at' in result:
            result['created_at'] = result['created_at'].isoformat()

        return jsonify({
            "status": "success",
            "data": result
        })

    except Exception as e:
        logger.error(f"获取四方向分析结果出错: {str(e)}")
        return jsonify({"status": "error", "message": f"获取分析结果出错: {str(e)}"}), 500

if __name__ == '__main__':
    # 启动Flask应用
    port = int(os.environ.get('PORT', 5001))
    app.run(host='0.0.0.0', port=port, debug=False)
    logger.info(f"服务已启动在 http://localhost:{port}") 