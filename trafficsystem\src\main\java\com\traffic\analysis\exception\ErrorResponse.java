package com.traffic.analysis.exception;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一错误响应格式
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorResponse {
    
    /**
     * 错误发生时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    /**
     * HTTP状态码
     */
    private Integer status;
    
    /**
     * 错误类型
     */
    private String error;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 请求路径
     */
    private String path;
    
    /**
     * 错误代码
     */
    private String code;
    
    /**
     * 详细信息
     */
    private Object details;
    
    /**
     * 请求ID（用于追踪）
     */
    private String requestId;
    
    /**
     * 建议操作
     */
    private String suggestion;
    
    /**
     * 创建简单错误响应
     */
    public static ErrorResponse of(String message) {
        return ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .message(message)
                .build();
    }
    
    /**
     * 创建带状态码的错误响应
     */
    public static ErrorResponse of(Integer status, String message) {
        return ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(status)
                .message(message)
                .build();
    }
    
    /**
     * 创建完整的错误响应
     */
    public static ErrorResponse of(Integer status, String error, String message, String path) {
        return ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(status)
                .error(error)
                .message(message)
                .path(path)
                .build();
    }
    
    /**
     * 添加详细信息
     */
    public ErrorResponse withDetails(Object details) {
        this.details = details;
        return this;
    }
    
    /**
     * 添加错误代码
     */
    public ErrorResponse withCode(String code) {
        this.code = code;
        return this;
    }
    
    /**
     * 添加建议操作
     */
    public ErrorResponse withSuggestion(String suggestion) {
        this.suggestion = suggestion;
        return this;
    }
    
    /**
     * 添加请求ID
     */
    public ErrorResponse withRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }
}
