<template>
  <div class="traffic-analysis-dashboard">
    <!-- 增强的仪表板标题 -->
    <div class="dashboard-header">
      <div class="header-content">
        <h2 class="dashboard-title">
          <el-icon><DataAnalysis /></el-icon>
          智能交通分析仪表板
        </h2>
        <p class="dashboard-subtitle">实时数据分析与智能优化建议 - 任务ID: {{ taskId }}</p>
      </div>

      <div class="header-controls">
        <el-button-group>
          <el-button
            :type="analysisMode === 'realtime' ? 'primary' : ''"
            @click="setAnalysisMode('realtime')"
            size="small"
          >
            实时分析
          </el-button>
          <el-button
            :type="analysisMode === 'historical' ? 'primary' : ''"
            @click="setAnalysisMode('historical')"
            size="small"
          >
            历史分析
          </el-button>
          <el-button
            :type="analysisMode === 'predictive' ? 'primary' : ''"
            @click="setAnalysisMode('predictive')"
            size="small"
          >
            预测分析
          </el-button>
        </el-button-group>

        <div class="header-actions">
          <el-button type="primary" size="small" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button size="small" @click="exportReport">
            <el-icon><Download /></el-icon>
            导出报告
          </el-button>
          <el-button type="success" size="small" @click="generateIntelligentReport" :loading="generatingReport">
            <el-icon><Document /></el-icon>
            智能报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="metrics-cards">
      <el-card class="metric-card">
        <div class="metric-content">
          <div class="metric-icon total-vehicles">
            <el-icon size="24"><Car /></el-icon>
          </div>
          <div class="metric-info">
            <h3>{{ analysisData.totalVehicleCount || 0 }}</h3>
            <p>总车辆数</p>
          </div>
        </div>
      </el-card>

      <el-card class="metric-card">
        <div class="metric-content">
          <div class="metric-icon peak-direction">
            <el-icon size="24"><TrendCharts /></el-icon>
          </div>
          <div class="metric-info">
            <h3>{{ getPeakDirectionName() }}</h3>
            <p>车流量最大方向</p>
          </div>
        </div>
      </el-card>

      <el-card class="metric-card">
        <div class="metric-content">
          <div class="metric-icon flow-balance">
            <el-icon size="24"><ScaleToOriginal /></el-icon>
          </div>
          <div class="metric-info">
            <h3>{{ getFlowBalancePercent() }}%</h3>
            <p>交通流量平衡度</p>
          </div>
        </div>
      </el-card>

      <el-card class="metric-card">
        <div class="metric-content">
          <div class="metric-icon congestion-level" :class="getCongestionLevelClass()">
            <el-icon size="24"><Warning /></el-icon>
          </div>
          <div class="metric-info">
            <h3>{{ analysisData.congestionLevel || '未知' }}</h3>
            <p>拥堵等级</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 左侧图表 -->
      <div class="charts-left">
        <!-- 四方向车流量对比图 -->
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>四方向车流量对比</span>
              <el-button-group size="small">
                <el-button 
                  :type="chartType === 'bar' ? 'primary' : ''" 
                  @click="chartType = 'bar'"
                >
                  柱状图
                </el-button>
                <el-button 
                  :type="chartType === 'pie' ? 'primary' : ''" 
                  @click="chartType = 'pie'"
                >
                  饼图
                </el-button>
              </el-button-group>
            </div>
          </template>
          <div class="chart-container">
            <DirectionComparisonChart
              :data="analysisData.directions"
              :chart-type="chartType"
              :height="300"
            />
          </div>
        </el-card>

        <!-- 交通流向热力图 -->
        <el-card class="chart-card">
          <template #header>
            <span>交通流向热力图</span>
          </template>
          <div class="chart-container">
            <TrafficFlowHeatmap
              :flow-data="analysisData.trafficFlows"
              :height="300"
            />
          </div>
        </el-card>
      </div>

      <!-- 右侧面板 -->
      <div class="charts-right">
        <!-- 信号灯优化建议 -->
        <el-card class="optimization-card">
          <template #header>
            <span>信号灯优化建议</span>
          </template>
          <SignalOptimizationPanel
            :optimization-data="analysisData.signalOptimization"
            @apply-optimization="onApplyOptimization"
          />
        </el-card>

        <!-- 实时统计 -->
        <el-card class="stats-card">
          <template #header>
            <span>实时统计</span>
          </template>
          <div class="stats-content">
            <div class="stat-item" v-for="direction in directions" :key="direction">
              <div class="stat-label">{{ getDirectionName(direction) }}</div>
              <div class="stat-value">
                <el-progress 
                  :percentage="getDirectionPercentage(direction)"
                  :color="getDirectionColor(direction)"
                  :stroke-width="8"
                />
                <span class="stat-number">
                  {{ getDirectionVehicleCount(direction) }} 辆
                </span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 拥堵预测 -->
        <el-card class="prediction-card" v-if="analysisData.congestionPrediction">
          <template #header>
            <span>拥堵预测</span>
          </template>
          <div class="prediction-content">
            <div class="prediction-probability">
              <el-progress 
                type="circle" 
                :percentage="Math.round(analysisData.congestionPrediction.congestionProbability * 100)"
                :color="getPredictionColor(analysisData.congestionPrediction.congestionProbability)"
                :width="80"
              />
              <p>拥堵概率</p>
            </div>
            <div class="prediction-details">
              <p><strong>预测时间范围:</strong> {{ analysisData.congestionPrediction.predictionTimeRange }} 分钟</p>
              <div class="predicted-directions" v-if="analysisData.congestionPrediction.predictedCongestionDirections">
                <p><strong>可能拥堵方向:</strong></p>
                <el-tag 
                  v-for="direction in analysisData.congestionPrediction.predictedCongestionDirections" 
                  :key="direction"
                  type="warning"
                  size="small"
                  class="direction-tag"
                >
                  {{ getDirectionName(direction) }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 改进建议 -->
    <el-card class="recommendations-card" v-if="analysisData.recommendations && analysisData.recommendations.length > 0">
      <template #header>
        <span>智能改进建议</span>
      </template>
      <div class="recommendations-content">
        <el-timeline>
          <el-timeline-item
            v-for="(recommendation, index) in analysisData.recommendations"
            :key="index"
            :icon="getRecommendationIcon(recommendation)"
            :type="getRecommendationType(recommendation)"
          >
            {{ recommendation }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DataAnalysis, Refresh, Download, Van as Car, TrendCharts,
  ScaleToOriginal, Warning, Setting, InfoFilled,
  SuccessFilled, WarnTriangleFilled, Document
} from '@element-plus/icons-vue'
import DirectionComparisonChart from './charts/DirectionComparisonChart.vue'
import TrafficFlowHeatmap from './charts/TrafficFlowHeatmap.vue'
import SignalOptimizationPanel from './SignalOptimizationPanel.vue'
import { getFourWayAnalysisResult, generateFourWayTrafficReport } from '@/api/video'

export default {
  name: 'TrafficAnalysisDashboard',
  components: {
    DataAnalysis, Refresh, Download, Car, TrendCharts,
    ScaleToOriginal, Warning, Setting, InfoFilled,
    SuccessFilled, WarnTriangleFilled, Document,
    DirectionComparisonChart,
    TrafficFlowHeatmap,
    SignalOptimizationPanel
  },
  props: {
    taskId: {
      type: String,
      required: true
    },
    autoRefresh: {
      type: Boolean,
      default: false
    },
    refreshInterval: {
      type: Number,
      default: 30000 // 30秒
    }
  },
  emits: ['data-updated', 'optimization-applied', 'error'],
  setup(props, { emit }) {
    // 响应式数据
    const loading = ref(false)
    const chartType = ref('bar')
    const refreshTimer = ref(null)
    const analysisMode = ref('realtime')
    const generatingReport = ref(false)
    
    const analysisData = reactive({
      totalVehicleCount: 0,
      peakDirection: null,
      trafficFlowBalance: 0,
      congestionLevel: '未知',
      directions: {},
      trafficFlows: [],
      signalOptimization: null,
      congestionPrediction: null,
      recommendations: []
    })
    
    const directions = ['east', 'south', 'west', 'north']
    
    // 计算属性
    const maxVehicleCount = computed(() => {
      if (!analysisData.directions) return 0
      return Math.max(...Object.values(analysisData.directions).map(d => d.vehicleCount || 0))
    })
    
    // 方法
    const getDirectionName = (direction) => {
      const names = {
        east: '东向',
        south: '南向',
        west: '西向',
        north: '北向'
      }
      return names[direction] || direction
    }
    
    const getPeakDirectionName = () => {
      if (!analysisData.peakDirection) return '无'
      return getDirectionName(analysisData.peakDirection)
    }
    
    const getFlowBalancePercent = () => {
      return Math.round((analysisData.trafficFlowBalance || 0) * 100)
    }
    
    const getCongestionLevelClass = () => {
      const level = analysisData.congestionLevel
      if (level === '严重拥堵') return 'severe'
      if (level === '中度拥堵') return 'moderate'
      if (level === '轻度拥堵') return 'light'
      return 'normal'
    }
    
    const getDirectionVehicleCount = (direction) => {
      return analysisData.directions[direction]?.vehicleCount || 0
    }
    
    const getDirectionPercentage = (direction) => {
      const count = getDirectionVehicleCount(direction)
      return maxVehicleCount.value > 0 ? Math.round((count / maxVehicleCount.value) * 100) : 0
    }
    
    const getDirectionColor = (direction) => {
      const colors = {
        east: '#409eff',
        south: '#67c23a',
        west: '#e6a23c',
        north: '#f56c6c'
      }
      return colors[direction] || '#909399'
    }
    
    const getPredictionColor = (probability) => {
      if (probability > 0.7) return '#f56c6c'
      if (probability > 0.4) return '#e6a23c'
      return '#67c23a'
    }
    
    const getRecommendationIcon = (recommendation) => {
      if (recommendation.includes('严重') || recommendation.includes('紧急')) {
        return WarnTriangleFilled
      }
      if (recommendation.includes('建议') || recommendation.includes('优化')) {
        return InfoFilled
      }
      return SuccessFilled
    }
    
    const getRecommendationType = (recommendation) => {
      if (recommendation.includes('严重') || recommendation.includes('紧急')) {
        return 'danger'
      }
      if (recommendation.includes('建议') || recommendation.includes('优化')) {
        return 'warning'
      }
      return 'success'
    }
    
    // 新增方法
    const setAnalysisMode = (mode) => {
      analysisMode.value = mode
      ElMessage.info(`切换到${mode === 'realtime' ? '实时' : mode === 'historical' ? '历史' : '预测'}分析模式`)
      refreshData() // 切换模式后刷新数据
    }

    const generateIntelligentReport = async () => {
      generatingReport.value = true
      try {
        ElMessage.info('正在生成智能分析报告...')

        // 模拟智能报告生成
        await new Promise(resolve => setTimeout(resolve, 2000))

        const reportData = {
          ...analysisData,
          analysisMode: analysisMode.value,
          generatedAt: new Date(),
          intelligentInsights: [
            '东向车流量较高，建议延长绿灯时间',
            '南北向流量不均衡，建议优化信号配时',
            '预测下一小时拥堵概率为65%'
          ]
        }

        emit('data-updated', reportData)
        ElMessage.success('智能分析报告生成成功')
      } catch (error) {
        ElMessage.error('智能报告生成失败: ' + error.message)
      } finally {
        generatingReport.value = false
      }
    }

    const refreshData = async () => {
      if (!props.taskId) {
        ElMessage.warning('缺少任务ID，无法刷新数据')
        return
      }

      loading.value = true

      try {
        console.log(`刷新四方向分析数据，任务ID: ${props.taskId}, 分析模式: ${analysisMode.value}`)

        const response = await getFourWayAnalysisResult(props.taskId)
        const data = response.data

        if (data) {
          // 更新分析数据
          Object.assign(analysisData, {
            totalVehicleCount: data.totalVehicleCount || 0,
            peakDirection: data.peakDirection,
            trafficFlowBalance: data.trafficFlowBalance || 0,
            congestionLevel: data.congestionLevel || '未知',
            directions: data.directions || {},
            trafficFlows: data.trafficFlows || [],
            signalOptimization: data.signalOptimization,
            congestionPrediction: data.congestionPrediction,
            recommendations: data.recommendations || []
          })

          emit('data-updated', data)
          console.log('四方向分析数据已更新')
        }

      } catch (error) {
        console.error('刷新四方向分析数据失败:', error)
        ElMessage.error('刷新数据失败: ' + (error.message || '未知错误'))
        emit('error', error)
      } finally {
        loading.value = false
      }
    }
    
    const exportReport = async () => {
      if (!props.taskId) {
        ElMessage.warning('缺少任务ID，无法导出报告')
        return
      }
      
      try {
        ElMessage.info('正在生成报告...')
        
        const response = await generateFourWayTrafficReport(props.taskId)
        const reportData = response.data
        
        if (reportData) {
          // 创建并下载报告文件
          const reportContent = JSON.stringify(reportData, null, 2)
          const blob = new Blob([reportContent], { type: 'application/json' })
          const url = URL.createObjectURL(blob)
          
          const link = document.createElement('a')
          link.href = url
          link.download = `四方向交通分析报告_${props.taskId}_${new Date().toISOString().slice(0, 10)}.json`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          URL.revokeObjectURL(url)
          
          ElMessage.success('报告导出成功')
        }
        
      } catch (error) {
        console.error('导出报告失败:', error)
        ElMessage.error('导出报告失败: ' + (error.message || '未知错误'))
      }
    }
    
    const onApplyOptimization = (optimizationData) => {
      ElMessageBox.confirm(
        '确定要应用此信号灯优化方案吗？这将影响实际的交通信号控制。',
        '确认应用优化方案',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 这里应该调用实际的信号灯控制API
        ElMessage.success('优化方案已应用（模拟）')
        emit('optimization-applied', optimizationData)
      }).catch(() => {
        ElMessage.info('已取消应用优化方案')
      })
    }
    
    const startAutoRefresh = () => {
      if (props.autoRefresh && props.refreshInterval > 0) {
        refreshTimer.value = setInterval(() => {
          refreshData()
        }, props.refreshInterval)
      }
    }
    
    const stopAutoRefresh = () => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value)
        refreshTimer.value = null
      }
    }
    
    // 生命周期
    onMounted(() => {
      refreshData()
      startAutoRefresh()
    })
    
    // 监听器
    watch(() => props.taskId, (newTaskId) => {
      if (newTaskId) {
        refreshData()
      }
    })
    
    watch(() => props.autoRefresh, (newValue) => {
      if (newValue) {
        startAutoRefresh()
      } else {
        stopAutoRefresh()
      }
    })
    
    // 清理
    const cleanup = () => {
      stopAutoRefresh()
    }
    
    return {
      // 响应式数据
      loading,
      chartType,
      analysisData,
      directions,
      analysisMode,
      generatingReport,

      // 计算属性
      maxVehicleCount,

      // 方法
      setAnalysisMode,
      generateIntelligentReport,
      getDirectionName,
      getPeakDirectionName,
      getFlowBalancePercent,
      getCongestionLevelClass,
      getDirectionVehicleCount,
      getDirectionPercentage,
      getDirectionColor,
      getPredictionColor,
      getRecommendationIcon,
      getRecommendationType,
      refreshData,
      exportReport,
      onApplyOptimization,

      // 清理函数
      cleanup
    }
  },
  beforeUnmount() {
    this.cleanup()
  }
}
</script>

<style scoped>
.traffic-analysis-dashboard {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.header-content {
  flex: 1;
}

.dashboard-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  color: white;
}

.dashboard-subtitle {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.5;
}

.header-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-end;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.metrics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.metric-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.metric-icon.total-vehicles {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.metric-icon.peak-direction {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.metric-icon.flow-balance {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.metric-icon.congestion-level {
  background: linear-gradient(135deg, #909399, #a6a9ad);
}

.metric-icon.congestion-level.severe {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.metric-icon.congestion-level.moderate {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.metric-icon.congestion-level.light {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.metric-info h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
}

.metric-info p {
  margin: 0;
  font-size: 14px;
  color: #909399;
}

.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.charts-left {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.charts-right {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-card, .optimization-card, .stats-card, .prediction-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.stat-value {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-number {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 600;
  min-width: 50px;
}

.prediction-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.prediction-probability {
  text-align: center;
}

.prediction-probability p {
  margin: 8px 0 0 0;
  font-size: 12px;
  color: #909399;
}

.prediction-details {
  flex: 1;
}

.prediction-details p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.predicted-directions {
  margin-top: 12px;
}

.direction-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.recommendations-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recommendations-content {
  max-height: 300px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .charts-right {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    display: grid;
  }
}

@media (max-width: 768px) {
  .traffic-analysis-dashboard {
    padding: 16px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .metrics-cards {
    grid-template-columns: 1fr;
  }
  
  .prediction-content {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
