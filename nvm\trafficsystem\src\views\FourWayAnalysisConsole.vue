<template>
  <div class="four-way-analysis-console">
    <!-- 页面头部 -->
    <div class="console-header">
      <div class="header-content">
        <h1 class="console-title">
          <el-icon><Grid /></el-icon>
          四方向智能交通分析控制台
        </h1>
        <p class="console-description">
          集成视频上传、实时检测、智能分析和报告生成的一站式交通分析平台
        </p>
      </div>
      
      <div class="header-stats">
        <div class="stat-item">
          <div class="stat-value">{{ totalTasks }}</div>
          <div class="stat-label">总任务数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ activeTasks }}</div>
          <div class="stat-label">活跃任务</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ completedTasks }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
    </div>

    <!-- 工作流程导航 -->
    <div class="workflow-navigation">
      <el-steps :active="currentStep" align-center>
        <el-step 
          title="视频上传" 
          description="上传四方向视频文件"
          icon="Upload"
        />
        <el-step 
          title="实时检测" 
          description="AI模型实时分析"
          icon="VideoCamera"
        />
        <el-step 
          title="智能分析" 
          description="生成分析结果"
          icon="DataAnalysis"
        />
        <el-step 
          title="报告生成" 
          description="导出分析报告"
          icon="Document"
        />
      </el-steps>
    </div>

    <!-- 主要内容区域 -->
    <div class="console-content">
      <!-- 左侧面板 -->
      <div class="left-panel">


        <!-- 快速操作 -->
        <el-card class="quick-actions-card">
          <template #header>
            <span>快速操作</span>
          </template>
          
          <div class="quick-actions">
            <el-button 
              type="primary" 
              :icon="Upload" 
              @click="goToUpload"
              :disabled="!canUpload"
            >
              上传视频
            </el-button>
            <el-button 
              type="success" 
              :icon="VideoCamera" 
              @click="startDetection"
              :disabled="!canDetect"
            >
              开始检测
            </el-button>
            <el-button 
              type="warning" 
              :icon="DataAnalysis" 
              @click="generateAnalysis"
              :disabled="!canAnalyze"
            >
              智能分析
            </el-button>
            <el-button 
              type="info" 
              :icon="Document" 
              @click="exportReport"
              :disabled="!canExport"
            >
              导出报告
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 右侧主内容 -->
      <div class="main-content">
        <!-- 当前任务信息 -->
        <div v-if="currentTask" class="current-task-info">
          <el-card>
            <template #header>
              <div class="task-header">
                <div class="task-title-section">
                  <h3>{{ currentTask.name }}</h3>
                  <el-tag :type="getTaskStatusType(currentTask.status)">
                    {{ getTaskStatusText(currentTask.status) }}
                  </el-tag>
                </div>
                <div class="task-progress-section">
                  <el-progress 
                    :percentage="currentTask.progress" 
                    :status="getProgressStatus(currentTask.status)"
                    :stroke-width="8"
                  />
                  <span class="progress-text">{{ currentTask.progress }}%</span>
                </div>
              </div>
            </template>
            
            <div class="task-details">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="detail-item">
                    <span class="detail-label">任务ID:</span>
                    <span class="detail-value">{{ currentTask.id }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="detail-item">
                    <span class="detail-label">创建时间:</span>
                    <span class="detail-value">{{ formatTime(currentTask.createdAt) }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="detail-item">
                    <span class="detail-label">处理时长:</span>
                    <span class="detail-value">{{ getProcessingDuration(currentTask) }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>

        <!-- 动态内容区域 -->
        <div class="dynamic-content">
          <!-- 步骤1: 视频上传 -->
          <div v-if="currentStep === 0" class="step-content">
            <FourWayVideoUpload
              @upload-success="handleUploadSuccess"
              @upload-error="handleUploadError"
              @upload-progress="handleUploadProgress"
              @status-change="handleUploadStatusChange"
            />
          </div>

          <!-- 步骤2: 实时检测 -->
          <div v-if="currentStep === 1" class="step-content">
            <FourWayRealtimeViewer
              v-if="currentTaskId"
              :task-id="currentTaskId"
              :auto-start="true"
              @detection-update="handleDetectionUpdate"
              @status-change="handleDetectionStatusChange"
              @analysis-complete="handleAnalysisComplete"
            />
            <el-empty v-else description="请先上传视频文件">
              <el-button type="primary" @click="currentStep = 0">
                返回上传
              </el-button>
            </el-empty>
          </div>

          <!-- 步骤3: 智能分析 -->
          <div v-if="currentStep === 2" class="step-content">
            <TrafficAnalysisDashboard
              v-if="currentTaskId"
              :task-id="currentTaskId"
              @data-updated="handleAnalysisDataUpdate"
            />
            <el-empty v-else description="请先完成视频检测">
              <el-button type="primary" @click="currentStep = 1">
                返回检测
              </el-button>
            </el-empty>
          </div>

          <!-- 步骤4: 报告生成 -->
          <div v-if="currentStep === 3" class="step-content">
            <IntelligentTrafficReport
              v-if="currentTaskId && reportData"
              :task-id="currentTaskId"
              :report-data="reportData"
              @export-report="handleExportReport"
              @refresh-data="handleRefreshReportData"
            />
            <el-empty v-else description="请先完成智能分析">
              <el-button type="primary" @click="currentStep = 2">
                返回分析
              </el-button>
            </el-empty>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="console-footer">
      <div class="footer-info">
        <span>系统状态: </span>
        <el-tag :type="systemStatus.type" size="small">{{ systemStatus.text }}</el-tag>
        <span class="separator">|</span>
        <span>活跃连接: {{ activeConnections }}</span>
        <span class="separator">|</span>
        <span>最后更新: {{ lastUpdateTime }}</span>
      </div>
      
      <div class="footer-actions">
        <el-button size="small" @click="refreshSystem">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button size="small" @click="showSystemInfo">
          <el-icon><InfoFilled /></el-icon>
          系统信息
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Grid, Upload, VideoCamera, DataAnalysis, Document, Plus, 
  MoreFilled, Refresh, InfoFilled
} from '@element-plus/icons-vue'

// 导入组件
import FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue'
import FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue'
import TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue'
import IntelligentTrafficReport from '@/components/analysis/IntelligentTrafficReport.vue'

export default {
  name: 'FourWayAnalysisConsole',
  components: {
    Grid, Upload, VideoCamera, DataAnalysis, Document, Plus, 
    MoreFilled, Refresh, InfoFilled,
    FourWayVideoUpload,
    FourWayRealtimeViewer,
    TrafficAnalysisDashboard,
    IntelligentTrafficReport
  },
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const currentStep = ref(0)
    const currentTaskId = ref('')
    const currentTask = ref({
      id: '',
      name: '四方向交通分析任务',
      status: 'waiting',
      progress: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    const reportData = ref(null)
    const activeConnections = ref(0)
    const lastUpdateTime = ref('')

    // 系统状态
    const systemStatus = reactive({
      type: 'success',
      text: '正常运行'
    })

    // 计算属性
    const totalTasks = computed(() => 1)
    const activeTasks = computed(() => currentStep.value > 0 && currentStep.value < 4 ? 1 : 0)
    const completedTasks = computed(() => currentStep.value === 4 ? 1 : 0)

    const canUpload = computed(() => true)
    const canDetect = computed(() => currentStep.value >= 1)
    const canAnalyze = computed(() => currentStep.value >= 2)
    const canExport = computed(() => currentStep.value >= 3)
    
    // 方法
    
    // 事件处理
    const handleUploadSuccess = (response) => {
      const taskId = response.data?.taskId || `task_${Date.now()}`
      currentTaskId.value = taskId

      // 更新当前任务信息
      currentTask.value = {
        id: taskId,
        name: '四方向交通分析任务',
        status: 'processing',
        progress: 10,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      currentStep.value = 1
      ElMessage.success('视频上传成功，开始实时检测')
    }

    const handleUploadError = (error) => {
      ElMessage.error('视频上传失败: ' + error.message)
    }

    const handleUploadProgress = (progress) => {
      console.log('上传进度:', progress)
    }

    const handleUploadStatusChange = (status) => {
      console.log('上传状态变化:', status)
    }

    const handleDetectionUpdate = (data) => {
      console.log('检测更新:', data)

      // 更新任务进度
      if (currentTask.value && data.progress) {
        currentTask.value.progress = Math.min(data.progress, 90) // 检测阶段最多90%
        currentTask.value.updatedAt = new Date()
      }
    }

    const handleDetectionStatusChange = (status) => {
      if (status === 'completed') {
        // 更新任务状态
        if (currentTask.value) {
          currentTask.value.status = 'completed'
          currentTask.value.progress = 100
          currentTask.value.updatedAt = new Date()
        }

        currentStep.value = 2
        ElMessage.success('实时检测完成，开始智能分析')
      }
    }

    // 处理四方向分析完成事件
    const handleAnalysisComplete = (completeData) => {
      try {
        console.log('🎉 收到四方向分析完成事件:', completeData)

        // 更新任务状态
        if (currentTask.value) {
          currentTask.value.status = 'completed'
          currentTask.value.progress = 100
          currentTask.value.updatedAt = new Date()
        }

        // 显示完成提示并自动跳转
        ElMessage({
          message: `四方向分析完成！检测到 ${completeData.summary?.totalVehicles || 0} 辆车辆，正在跳转到智能分析模块...`,
          type: 'success',
          duration: 4000
        })

        // 延迟跳转到智能分析模块，给用户时间看到完成消息
        setTimeout(() => {
          currentStep.value = 2
          ElMessage.info('已自动跳转到智能分析模块')
        }, 2000)

      } catch (error) {
        console.error('处理分析完成事件失败:', error)
        ElMessage.error('处理完成事件失败，请手动跳转到智能分析')
      }
    }

    const handleAnalysisDataUpdate = (data) => {
      reportData.value = data
      currentStep.value = 3
      ElMessage.success('智能分析完成，可以生成报告')
    }

    const handleExportReport = (taskId) => {
      ElMessage.success('报告导出成功')
    }

    const handleRefreshReportData = (taskId) => {
      ElMessage.success('报告数据刷新成功')
    }
    
    // 快速操作
    const goToUpload = () => {
      currentStep.value = 0
    }
    
    const startDetection = () => {
      if (canDetect.value) {
        currentStep.value = 1
      } else {
        ElMessage.warning('请先上传视频文件')
      }
    }
    
    const generateAnalysis = () => {
      if (canAnalyze.value) {
        currentStep.value = 2
      } else {
        ElMessage.warning('请先完成视频检测')
      }
    }
    
    const exportReport = () => {
      if (canExport.value) {
        currentStep.value = 3
      } else {
        ElMessage.warning('请先完成智能分析')
      }
    }

    const refreshSystem = () => {
      lastUpdateTime.value = new Date().toLocaleTimeString()
      ElMessage.success('系统状态已刷新')
    }

    const showSystemInfo = () => {
      ElMessageBox.alert('四方向智能交通分析系统 v1.0.0', '系统信息', {
        confirmButtonText: '确定'
      })
    }

    // 任务状态辅助方法
    const getTaskStatusType = (status) => {
      const statusMap = {
        'waiting': 'info',
        'processing': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'paused': 'info'
      }
      return statusMap[status] || 'info'
    }

    const getTaskStatusText = (status) => {
      const statusMap = {
        'waiting': '等待中',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败',
        'paused': '已暂停'
      }
      return statusMap[status] || '未知'
    }

    const getProgressStatus = (status) => {
      if (status === 'completed') return 'success'
      if (status === 'failed') return 'exception'
      return null
    }

    const formatTime = (time) => {
      if (!time) return '-'
      return new Date(time).toLocaleString()
    }

    const getProcessingDuration = (task) => {
      if (!task || !task.createdAt) return '-'
      const start = new Date(task.createdAt)
      const end = task.updatedAt ? new Date(task.updatedAt) : new Date()
      const duration = Math.floor((end - start) / 1000)

      if (duration < 60) return `${duration}秒`
      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`
      return `${Math.floor(duration / 3600)}小时`
    }

    // 生命周期
    onMounted(() => {
      // 初始化系统状态
      lastUpdateTime.value = new Date().toLocaleTimeString()
      activeConnections.value = 1
    })
    
    return {
      // 图标组件
      Upload,
      VideoCamera,
      DataAnalysis,
      Document,
      Plus,
      MoreFilled,
      Refresh,
      InfoFilled,

      // 响应式数据
      currentStep,
      currentTaskId,
      currentTask,
      reportData,
      activeConnections,
      lastUpdateTime,
      systemStatus,

      // 计算属性
      totalTasks,
      activeTasks,
      completedTasks,
      canUpload,
      canDetect,
      canAnalyze,
      canExport,

      // 方法
      handleUploadSuccess,
      handleUploadError,
      handleUploadProgress,
      handleUploadStatusChange,
      handleDetectionUpdate,
      handleDetectionStatusChange,
      handleAnalysisComplete,
      handleAnalysisDataUpdate,
      handleExportReport,
      handleRefreshReportData,
      goToUpload,
      startDetection,
      generateAnalysis,
      exportReport,
      refreshSystem,
      showSystemInfo,

      // 任务状态辅助方法
      getTaskStatusType,
      getTaskStatusText,
      getProgressStatus,
      formatTime,
      getProcessingDuration
    }
  }
}
</script>

<style scoped>
.four-way-analysis-console {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

/* 控制台头部 */
.console-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  flex: 1;
}

.console-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.console-description {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  line-height: 1.5;
}

.header-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
  margin-top: 4px;
}

/* 工作流程导航 */
.workflow-navigation {
  background: white;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
}

/* 主要内容区域 */
.console-content {
  flex: 1;
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 24px;
  padding: 24px 32px;
  min-height: 0;
}

/* 左侧面板 */
.left-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.quick-actions-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-actions .el-button {
  justify-content: flex-start;
}

/* 主内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-height: 0;
}

.current-task-info {
  flex-shrink: 0;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.task-title-section h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.task-progress-section {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
}

.progress-text {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  min-width: 40px;
}

.task-details {
  margin-top: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

.dynamic-content {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: auto;
}

.step-content {
  height: 100%;
}

/* 底部状态栏 */
.console-footer {
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 12px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
}

.separator {
  color: #d1d5db;
}

.footer-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .console-content {
    grid-template-columns: 300px 1fr;
  }

  .header-stats {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .console-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-stats {
    justify-content: center;
  }

  .console-content {
    grid-template-columns: 1fr;
    padding: 16px;
  }

  .workflow-navigation {
    padding: 16px;
  }

  .console-footer {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .task-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .task-progress-section {
    width: 100%;
    min-width: auto;
  }
}

/* 滚动条样式 */
.task-list::-webkit-scrollbar {
  width: 6px;
}

.task-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.task-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.task-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.task-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-content {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
