{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createBlock as _createBlock, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"four-way-analysis-console\"\n};\nconst _hoisted_2 = {\n  class: \"console-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-content\"\n};\nconst _hoisted_4 = {\n  class: \"console-title\"\n};\nconst _hoisted_5 = {\n  class: \"header-stats\"\n};\nconst _hoisted_6 = {\n  class: \"stat-item\"\n};\nconst _hoisted_7 = {\n  class: \"stat-value\"\n};\nconst _hoisted_8 = {\n  class: \"stat-item\"\n};\nconst _hoisted_9 = {\n  class: \"stat-value\"\n};\nconst _hoisted_10 = {\n  class: \"stat-item\"\n};\nconst _hoisted_11 = {\n  class: \"stat-value\"\n};\nconst _hoisted_12 = {\n  class: \"workflow-navigation\"\n};\nconst _hoisted_13 = {\n  class: \"console-content\"\n};\nconst _hoisted_14 = {\n  class: \"left-panel\"\n};\nconst _hoisted_15 = {\n  class: \"quick-actions\"\n};\nconst _hoisted_16 = {\n  class: \"test-mode-controls\",\n  style: {\n    \"margin-top\": \"16px\"\n  }\n};\nconst _hoisted_17 = {\n  class: \"main-content\"\n};\nconst _hoisted_18 = {\n  key: 0,\n  class: \"current-task-info\"\n};\nconst _hoisted_19 = {\n  class: \"task-header\"\n};\nconst _hoisted_20 = {\n  class: \"task-title-section\"\n};\nconst _hoisted_21 = {\n  class: \"task-progress-section\"\n};\nconst _hoisted_22 = {\n  class: \"progress-text\"\n};\nconst _hoisted_23 = {\n  class: \"task-details\"\n};\nconst _hoisted_24 = {\n  class: \"detail-item\"\n};\nconst _hoisted_25 = {\n  class: \"detail-value\"\n};\nconst _hoisted_26 = {\n  class: \"detail-item\"\n};\nconst _hoisted_27 = {\n  class: \"detail-value\"\n};\nconst _hoisted_28 = {\n  class: \"detail-item\"\n};\nconst _hoisted_29 = {\n  class: \"detail-value\"\n};\nconst _hoisted_30 = {\n  class: \"dynamic-content\"\n};\nconst _hoisted_31 = {\n  key: 0,\n  class: \"step-content\"\n};\nconst _hoisted_32 = {\n  key: 1,\n  class: \"step-content\"\n};\nconst _hoisted_33 = {\n  key: 2,\n  class: \"step-content\"\n};\nconst _hoisted_34 = {\n  key: 3,\n  class: \"step-content\"\n};\nconst _hoisted_35 = {\n  class: \"console-footer\"\n};\nconst _hoisted_36 = {\n  class: \"footer-info\"\n};\nconst _hoisted_37 = {\n  class: \"footer-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Grid = _resolveComponent(\"Grid\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_step = _resolveComponent(\"el-step\");\n  const _component_el_steps = _resolveComponent(\"el-steps\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_switch = _resolveComponent(\"el-switch\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_FourWayVideoUpload = _resolveComponent(\"FourWayVideoUpload\");\n  const _component_FourWayRealtimeViewer = _resolveComponent(\"FourWayRealtimeViewer\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_TrafficAnalysisDashboard = _resolveComponent(\"TrafficAnalysisDashboard\");\n  const _component_IntelligentTrafficReport = _resolveComponent(\"IntelligentTrafficReport\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_InfoFilled = _resolveComponent(\"InfoFilled\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面头部 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h1\", _hoisted_4, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Grid)]),\n    _: 1 /* STABLE */\n  }), _cache[4] || (_cache[4] = _createTextVNode(\" 四方向智能交通分析控制台 \"))]), _cache[5] || (_cache[5] = _createElementVNode(\"p\", {\n    class: \"console-description\"\n  }, \" 集成视频上传、实时检测、智能分析和报告生成的一站式交通分析平台 \", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, _toDisplayString($setup.totalTasks), 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"总任务数\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.activeTasks), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"活跃任务\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString($setup.completedTasks), 1 /* TEXT */), _cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"已完成\", -1 /* HOISTED */))])])]), _createCommentVNode(\" 工作流程导航 \"), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_steps, {\n    active: $setup.currentStep,\n    \"align-center\": \"\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_step, {\n      title: \"视频上传\",\n      description: \"上传四方向视频文件\",\n      icon: \"Upload\"\n    }), _createVNode(_component_el_step, {\n      title: \"实时检测\",\n      description: \"AI模型实时分析\",\n      icon: \"VideoCamera\"\n    }), _createVNode(_component_el_step, {\n      title: \"智能分析\",\n      description: \"生成分析结果\",\n      icon: \"DataAnalysis\"\n    }), _createVNode(_component_el_step, {\n      title: \"报告生成\",\n      description: \"导出分析报告\",\n      icon: \"Document\"\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"active\"])]), _createCommentVNode(\" 主要内容区域 \"), _createElementVNode(\"div\", _hoisted_13, [_createCommentVNode(\" 左侧面板 \"), _createElementVNode(\"div\", _hoisted_14, [_createCommentVNode(\" 快速操作 \"), _createVNode(_component_el_card, {\n    class: \"quick-actions-card\"\n  }, {\n    header: _withCtx(() => _cache[9] || (_cache[9] = [_createElementVNode(\"span\", null, \"快速操作\", -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      icon: $setup.Upload,\n      onClick: $setup.goToUpload,\n      disabled: !$setup.canUpload\n    }, {\n      default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\" 上传视频 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"icon\", \"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n      type: \"success\",\n      icon: $setup.VideoCamera,\n      onClick: $setup.startDetection,\n      disabled: !$setup.canDetect && !_ctx.isTestMode\n    }, {\n      default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\" 开始检测 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"icon\", \"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n      type: \"warning\",\n      icon: $setup.DataAnalysis,\n      onClick: $setup.generateAnalysis,\n      disabled: !$setup.canAnalyze\n    }, {\n      default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\" 智能分析 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"icon\", \"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n      type: \"info\",\n      icon: $setup.Document,\n      onClick: $setup.exportReport,\n      disabled: !$setup.canExport\n    }, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\" 导出报告 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"icon\", \"onClick\", \"disabled\"])]), _createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_switch, {\n      modelValue: _ctx.isTestMode,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => _ctx.isTestMode = $event),\n      \"active-text\": \"测试模式\",\n      \"inactive-text\": \"正常模式\",\n      onChange: _ctx.handleTestModeChange\n    }, null, 8 /* PROPS */, [\"modelValue\", \"onChange\"]), _ctx.isTestMode ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"primary\",\n      size: \"small\",\n      onClick: _ctx.startTestDetection,\n      style: {\n        \"margin-left\": \"8px\"\n      }\n    }, {\n      default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\" 直接开始检测测试 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  })]), _createCommentVNode(\" 右侧主内容 \"), _createElementVNode(\"div\", _hoisted_17, [_createCommentVNode(\" 当前任务信息 \"), $setup.currentTask ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"h3\", null, _toDisplayString($setup.currentTask.name), 1 /* TEXT */), _createVNode(_component_el_tag, {\n      type: $setup.getTaskStatusType($setup.currentTask.status)\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getTaskStatusText($setup.currentTask.status)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_progress, {\n      percentage: $setup.currentTask.progress,\n      status: $setup.getProgressStatus($setup.currentTask.status),\n      \"stroke-width\": 8\n    }, null, 8 /* PROPS */, [\"percentage\", \"status\"]), _createElementVNode(\"span\", _hoisted_22, _toDisplayString($setup.currentTask.progress) + \"%\", 1 /* TEXT */)])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_24, [_cache[15] || (_cache[15] = _createElementVNode(\"span\", {\n          class: \"detail-label\"\n        }, \"任务ID:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_25, _toDisplayString($setup.currentTask.id), 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_26, [_cache[16] || (_cache[16] = _createElementVNode(\"span\", {\n          class: \"detail-label\"\n        }, \"创建时间:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_27, _toDisplayString($setup.formatTime($setup.currentTask.createdAt)), 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 8\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_28, [_cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n          class: \"detail-label\"\n        }, \"处理时长:\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_29, _toDisplayString($setup.getProcessingDuration($setup.currentTask)), 1 /* TEXT */)])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 动态内容区域 \"), _createElementVNode(\"div\", _hoisted_30, [_createCommentVNode(\" 步骤1: 视频上传 \"), $setup.currentStep === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [_createVNode(_component_FourWayVideoUpload, {\n    onUploadSuccess: $setup.handleUploadSuccess,\n    onUploadError: $setup.handleUploadError,\n    onUploadProgress: $setup.handleUploadProgress,\n    onStatusChange: $setup.handleUploadStatusChange\n  }, null, 8 /* PROPS */, [\"onUploadSuccess\", \"onUploadError\", \"onUploadProgress\", \"onStatusChange\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 步骤2: 实时检测 \"), $setup.currentStep === 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [_ctx.effectiveTaskId ? (_openBlock(), _createBlock(_component_FourWayRealtimeViewer, {\n    key: 0,\n    \"task-id\": _ctx.effectiveTaskId,\n    \"auto-start\": true,\n    onDetectionUpdate: $setup.handleDetectionUpdate,\n    onStatusChange: $setup.handleDetectionStatusChange\n  }, null, 8 /* PROPS */, [\"task-id\", \"onDetectionUpdate\", \"onStatusChange\"])) : (_openBlock(), _createBlock(_component_el_empty, {\n    key: 1,\n    description: \"请先上传视频文件或启用测试模式\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[1] || (_cache[1] = $event => $setup.currentStep = 0)\n    }, {\n      default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\" 返回上传 \")])),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 步骤3: 智能分析 \"), $setup.currentStep === 2 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_33, [$setup.currentTaskId ? (_openBlock(), _createBlock(_component_TrafficAnalysisDashboard, {\n    key: 0,\n    \"task-id\": $setup.currentTaskId,\n    onDataUpdated: $setup.handleAnalysisDataUpdate\n  }, null, 8 /* PROPS */, [\"task-id\", \"onDataUpdated\"])) : (_openBlock(), _createBlock(_component_el_empty, {\n    key: 1,\n    description: \"请先完成视频检测\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[2] || (_cache[2] = $event => $setup.currentStep = 1)\n    }, {\n      default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\" 返回检测 \")])),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 步骤4: 报告生成 \"), $setup.currentStep === 3 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_34, [$setup.currentTaskId && $setup.reportData ? (_openBlock(), _createBlock(_component_IntelligentTrafficReport, {\n    key: 0,\n    \"task-id\": $setup.currentTaskId,\n    \"report-data\": $setup.reportData,\n    onExportReport: $setup.handleExportReport,\n    onRefreshData: $setup.handleRefreshReportData\n  }, null, 8 /* PROPS */, [\"task-id\", \"report-data\", \"onExportReport\", \"onRefreshData\"])) : (_openBlock(), _createBlock(_component_el_empty, {\n    key: 1,\n    description: \"请先完成智能分析\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[3] || (_cache[3] = $event => $setup.currentStep = 2)\n    }, {\n      default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\" 返回分析 \")])),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }))])) : _createCommentVNode(\"v-if\", true)])])]), _createCommentVNode(\" 底部状态栏 \"), _createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, [_cache[21] || (_cache[21] = _createElementVNode(\"span\", null, \"系统状态: \", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.systemStatus.type,\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.systemStatus.text), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"]), _cache[22] || (_cache[22] = _createElementVNode(\"span\", {\n    class: \"separator\"\n  }, \"|\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, \"活跃连接: \" + _toDisplayString($setup.activeConnections), 1 /* TEXT */), _cache[23] || (_cache[23] = _createElementVNode(\"span\", {\n    class: \"separator\"\n  }, \"|\", -1 /* HOISTED */)), _createElementVNode(\"span\", null, \"最后更新: \" + _toDisplayString($setup.lastUpdateTime), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_37, [_createVNode(_component_el_button, {\n    size: \"small\",\n    onClick: $setup.refreshSystem\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Refresh)]),\n      _: 1 /* STABLE */\n    }), _cache[24] || (_cache[24] = _createTextVNode(\" 刷新 \"))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    size: \"small\",\n    onClick: $setup.showSystemInfo\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_InfoFilled)]),\n      _: 1 /* STABLE */\n    }), _cache[25] || (_cache[25] = _createTextVNode(\" 系统信息 \"))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])])]);\n}", "map": {"version": 3, "names": ["class", "style", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_Grid", "_", "_createTextVNode", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_toDisplayString", "$setup", "totalTasks", "_hoisted_8", "_hoisted_9", "activeTasks", "_hoisted_10", "_hoisted_11", "completedTasks", "_hoisted_12", "_component_el_steps", "active", "currentStep", "_component_el_step", "title", "description", "icon", "_hoisted_13", "_hoisted_14", "_component_el_card", "header", "_cache", "_hoisted_15", "_component_el_button", "type", "Upload", "onClick", "goToUpload", "disabled", "canUpload", "VideoCamera", "startDetection", "canDetect", "_ctx", "isTestMode", "DataAnalysis", "generateAnalysis", "canAnalyze", "Document", "exportReport", "canExport", "_hoisted_16", "_component_el_switch", "modelValue", "$event", "onChange", "handleTestModeChange", "_createBlock", "size", "startTestDetection", "_hoisted_17", "currentTask", "_hoisted_18", "_hoisted_19", "_hoisted_20", "name", "_component_el_tag", "getTaskStatusType", "status", "getTaskStatusText", "_hoisted_21", "_component_el_progress", "percentage", "progress", "getProgressStatus", "_hoisted_22", "_hoisted_23", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_24", "_hoisted_25", "id", "_hoisted_26", "_hoisted_27", "formatTime", "createdAt", "_hoisted_28", "_hoisted_29", "getProcessingDuration", "_hoisted_30", "_hoisted_31", "_component_FourWayVideoUpload", "onUploadSuccess", "handleUploadSuccess", "onUploadError", "handleUploadError", "onUploadProgress", "handleUploadProgress", "onStatusChange", "handleUploadStatusChange", "_hoisted_32", "effectiveTaskId", "_component_FourWayRealtimeViewer", "onDetectionUpdate", "handleDetectionUpdate", "handleDetectionStatusChange", "_component_el_empty", "_hoisted_33", "currentTaskId", "_component_TrafficAnalysisDashboard", "onDataUpdated", "handleAnalysisDataUpdate", "_hoisted_34", "reportData", "_component_IntelligentTrafficReport", "onExportReport", "handleExportReport", "onRefreshData", "handleRefreshReportData", "_hoisted_35", "_hoisted_36", "systemStatus", "text", "activeConnections", "lastUpdateTime", "_hoisted_37", "refreshSystem", "_component_Refresh", "showSystemInfo", "_component_InfoFilled"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\FourWayAnalysisConsole.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-analysis-console\">\n    <!-- 页面头部 -->\n    <div class=\"console-header\">\n      <div class=\"header-content\">\n        <h1 class=\"console-title\">\n          <el-icon><Grid /></el-icon>\n          四方向智能交通分析控制台\n        </h1>\n        <p class=\"console-description\">\n          集成视频上传、实时检测、智能分析和报告生成的一站式交通分析平台\n        </p>\n      </div>\n      \n      <div class=\"header-stats\">\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ totalTasks }}</div>\n          <div class=\"stat-label\">总任务数</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ activeTasks }}</div>\n          <div class=\"stat-label\">活跃任务</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ completedTasks }}</div>\n          <div class=\"stat-label\">已完成</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 工作流程导航 -->\n    <div class=\"workflow-navigation\">\n      <el-steps :active=\"currentStep\" align-center>\n        <el-step \n          title=\"视频上传\" \n          description=\"上传四方向视频文件\"\n          icon=\"Upload\"\n        />\n        <el-step \n          title=\"实时检测\" \n          description=\"AI模型实时分析\"\n          icon=\"VideoCamera\"\n        />\n        <el-step \n          title=\"智能分析\" \n          description=\"生成分析结果\"\n          icon=\"DataAnalysis\"\n        />\n        <el-step \n          title=\"报告生成\" \n          description=\"导出分析报告\"\n          icon=\"Document\"\n        />\n      </el-steps>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"console-content\">\n      <!-- 左侧面板 -->\n      <div class=\"left-panel\">\n\n\n        <!-- 快速操作 -->\n        <el-card class=\"quick-actions-card\">\n          <template #header>\n            <span>快速操作</span>\n          </template>\n          \n          <div class=\"quick-actions\">\n            <el-button\n              type=\"primary\"\n              :icon=\"Upload\"\n              @click=\"goToUpload\"\n              :disabled=\"!canUpload\"\n            >\n              上传视频\n            </el-button>\n            <el-button\n              type=\"success\"\n              :icon=\"VideoCamera\"\n              @click=\"startDetection\"\n              :disabled=\"!canDetect && !isTestMode\"\n            >\n              开始检测\n            </el-button>\n            <el-button\n              type=\"warning\"\n              :icon=\"DataAnalysis\"\n              @click=\"generateAnalysis\"\n              :disabled=\"!canAnalyze\"\n            >\n              智能分析\n            </el-button>\n            <el-button\n              type=\"info\"\n              :icon=\"Document\"\n              @click=\"exportReport\"\n              :disabled=\"!canExport\"\n            >\n              导出报告\n            </el-button>\n          </div>\n\n          <!-- 测试模式控制 -->\n          <div class=\"test-mode-controls\" style=\"margin-top: 16px;\">\n            <el-switch\n              v-model=\"isTestMode\"\n              active-text=\"测试模式\"\n              inactive-text=\"正常模式\"\n              @change=\"handleTestModeChange\"\n            />\n            <el-button\n              v-if=\"isTestMode\"\n              type=\"primary\"\n              size=\"small\"\n              @click=\"startTestDetection\"\n              style=\"margin-left: 8px;\"\n            >\n              直接开始检测测试\n            </el-button>\n          </div>\n        </el-card>\n      </div>\n\n      <!-- 右侧主内容 -->\n      <div class=\"main-content\">\n        <!-- 当前任务信息 -->\n        <div v-if=\"currentTask\" class=\"current-task-info\">\n          <el-card>\n            <template #header>\n              <div class=\"task-header\">\n                <div class=\"task-title-section\">\n                  <h3>{{ currentTask.name }}</h3>\n                  <el-tag :type=\"getTaskStatusType(currentTask.status)\">\n                    {{ getTaskStatusText(currentTask.status) }}\n                  </el-tag>\n                </div>\n                <div class=\"task-progress-section\">\n                  <el-progress \n                    :percentage=\"currentTask.progress\" \n                    :status=\"getProgressStatus(currentTask.status)\"\n                    :stroke-width=\"8\"\n                  />\n                  <span class=\"progress-text\">{{ currentTask.progress }}%</span>\n                </div>\n              </div>\n            </template>\n            \n            <div class=\"task-details\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">任务ID:</span>\n                    <span class=\"detail-value\">{{ currentTask.id }}</span>\n                  </div>\n                </el-col>\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">创建时间:</span>\n                    <span class=\"detail-value\">{{ formatTime(currentTask.createdAt) }}</span>\n                  </div>\n                </el-col>\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">处理时长:</span>\n                    <span class=\"detail-value\">{{ getProcessingDuration(currentTask) }}</span>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 动态内容区域 -->\n        <div class=\"dynamic-content\">\n          <!-- 步骤1: 视频上传 -->\n          <div v-if=\"currentStep === 0\" class=\"step-content\">\n            <FourWayVideoUpload\n              @upload-success=\"handleUploadSuccess\"\n              @upload-error=\"handleUploadError\"\n              @upload-progress=\"handleUploadProgress\"\n              @status-change=\"handleUploadStatusChange\"\n            />\n          </div>\n\n          <!-- 步骤2: 实时检测 -->\n          <div v-if=\"currentStep === 1\" class=\"step-content\">\n            <FourWayRealtimeViewer\n              v-if=\"effectiveTaskId\"\n              :task-id=\"effectiveTaskId\"\n              :auto-start=\"true\"\n              @detection-update=\"handleDetectionUpdate\"\n              @status-change=\"handleDetectionStatusChange\"\n            />\n            <el-empty v-else description=\"请先上传视频文件或启用测试模式\">\n              <el-button type=\"primary\" @click=\"currentStep = 0\">\n                返回上传\n              </el-button>\n            </el-empty>\n          </div>\n\n          <!-- 步骤3: 智能分析 -->\n          <div v-if=\"currentStep === 2\" class=\"step-content\">\n            <TrafficAnalysisDashboard\n              v-if=\"currentTaskId\"\n              :task-id=\"currentTaskId\"\n              @data-updated=\"handleAnalysisDataUpdate\"\n            />\n            <el-empty v-else description=\"请先完成视频检测\">\n              <el-button type=\"primary\" @click=\"currentStep = 1\">\n                返回检测\n              </el-button>\n            </el-empty>\n          </div>\n\n          <!-- 步骤4: 报告生成 -->\n          <div v-if=\"currentStep === 3\" class=\"step-content\">\n            <IntelligentTrafficReport\n              v-if=\"currentTaskId && reportData\"\n              :task-id=\"currentTaskId\"\n              :report-data=\"reportData\"\n              @export-report=\"handleExportReport\"\n              @refresh-data=\"handleRefreshReportData\"\n            />\n            <el-empty v-else description=\"请先完成智能分析\">\n              <el-button type=\"primary\" @click=\"currentStep = 2\">\n                返回分析\n              </el-button>\n            </el-empty>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 底部状态栏 -->\n    <div class=\"console-footer\">\n      <div class=\"footer-info\">\n        <span>系统状态: </span>\n        <el-tag :type=\"systemStatus.type\" size=\"small\">{{ systemStatus.text }}</el-tag>\n        <span class=\"separator\">|</span>\n        <span>活跃连接: {{ activeConnections }}</span>\n        <span class=\"separator\">|</span>\n        <span>最后更新: {{ lastUpdateTime }}</span>\n      </div>\n      \n      <div class=\"footer-actions\">\n        <el-button size=\"small\" @click=\"refreshSystem\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n        <el-button size=\"small\" @click=\"showSystemInfo\">\n          <el-icon><InfoFilled /></el-icon>\n          系统信息\n        </el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  Grid, Upload, VideoCamera, DataAnalysis, Document, Plus, \n  MoreFilled, Refresh, InfoFilled\n} from '@element-plus/icons-vue'\n\n// 导入组件\nimport FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue'\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue'\nimport TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue'\nimport IntelligentTrafficReport from '@/components/analysis/IntelligentTrafficReport.vue'\n\nexport default {\n  name: 'FourWayAnalysisConsole',\n  components: {\n    Grid, Upload, VideoCamera, DataAnalysis, Document, Plus, \n    MoreFilled, Refresh, InfoFilled,\n    FourWayVideoUpload,\n    FourWayRealtimeViewer,\n    TrafficAnalysisDashboard,\n    IntelligentTrafficReport\n  },\n  setup() {\n    const router = useRouter()\n    \n    // 响应式数据\n    const currentStep = ref(0)\n    const currentTaskId = ref('')\n    const currentTask = ref({\n      id: '',\n      name: '四方向交通分析任务',\n      status: 'waiting',\n      progress: 0,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    })\n\n    // 测试模式支持\n    const isTestMode = ref(false)\n    const testTaskId = ref('8689ed66-1063-4d52-a83e-6c0cd54ea37d') // 使用您之前的测试任务ID\n    const reportData = ref(null)\n    const activeConnections = ref(0)\n    const lastUpdateTime = ref('')\n\n    // 系统状态\n    const systemStatus = reactive({\n      type: 'success',\n      text: '正常运行'\n    })\n\n    // 计算属性\n    const totalTasks = computed(() => 1)\n    const activeTasks = computed(() => currentStep.value > 0 && currentStep.value < 4 ? 1 : 0)\n    const completedTasks = computed(() => currentStep.value === 4 ? 1 : 0)\n\n    const canUpload = computed(() => true)\n    const canDetect = computed(() => currentStep.value >= 1 || (isTestMode.value && !!testTaskId.value))\n    const canAnalyze = computed(() => currentStep.value >= 2)\n    const canExport = computed(() => currentStep.value >= 3)\n\n    // 获取当前有效的任务ID（优先使用实际任务ID，测试模式下使用测试ID）\n    const effectiveTaskId = computed(() => {\n      return currentTaskId.value || (isTestMode.value ? testTaskId.value : '')\n    })\n    \n    // 方法\n    \n    // 事件处理\n    const handleUploadSuccess = (response) => {\n      const taskId = response.data?.taskId || `task_${Date.now()}`\n      currentTaskId.value = taskId\n\n      // 更新当前任务信息\n      currentTask.value = {\n        id: taskId,\n        name: '四方向交通分析任务',\n        status: 'processing',\n        progress: 10,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      }\n\n      currentStep.value = 1\n      ElMessage.success('视频上传成功，开始实时检测')\n    }\n\n    const handleUploadError = (error) => {\n      ElMessage.error('视频上传失败: ' + error.message)\n    }\n\n    const handleUploadProgress = (progress) => {\n      console.log('上传进度:', progress)\n    }\n\n    const handleUploadStatusChange = (status) => {\n      console.log('上传状态变化:', status)\n    }\n\n    const handleDetectionUpdate = (data) => {\n      console.log('检测更新:', data)\n\n      // 更新任务进度\n      if (currentTask.value && data.progress) {\n        currentTask.value.progress = Math.min(data.progress, 90) // 检测阶段最多90%\n        currentTask.value.updatedAt = new Date()\n      }\n    }\n\n    const handleDetectionStatusChange = (status) => {\n      if (status === 'completed') {\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed'\n          currentTask.value.progress = 100\n          currentTask.value.updatedAt = new Date()\n        }\n\n        currentStep.value = 2\n        ElMessage.success('实时检测完成，开始智能分析')\n      }\n    }\n\n    const handleAnalysisDataUpdate = (data) => {\n      reportData.value = data\n      currentStep.value = 3\n      ElMessage.success('智能分析完成，可以生成报告')\n    }\n\n    const handleExportReport = (taskId) => {\n      ElMessage.success('报告导出成功')\n    }\n\n    const handleRefreshReportData = (taskId) => {\n      ElMessage.success('报告数据刷新成功')\n    }\n    \n    // 快速操作\n    const goToUpload = () => {\n      currentStep.value = 0\n    }\n    \n    const startDetection = () => {\n      if (canDetect.value) {\n        currentStep.value = 1\n      } else {\n        ElMessage.warning('请先上传视频文件')\n      }\n    }\n    \n    const generateAnalysis = () => {\n      if (canAnalyze.value) {\n        currentStep.value = 2\n      } else {\n        ElMessage.warning('请先完成视频检测')\n      }\n    }\n    \n    const exportReport = () => {\n      if (canExport.value) {\n        currentStep.value = 3\n      } else {\n        ElMessage.warning('请先完成智能分析')\n      }\n    }\n\n    const refreshSystem = () => {\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      ElMessage.success('系统状态已刷新')\n    }\n\n    const showSystemInfo = () => {\n      ElMessageBox.alert('四方向智能交通分析系统 v1.0.0', '系统信息', {\n        confirmButtonText: '确定'\n      })\n    }\n\n    // 任务状态辅助方法\n    const getTaskStatusType = (status) => {\n      const statusMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'failed': 'danger',\n        'paused': 'info'\n      }\n      return statusMap[status] || 'info'\n    }\n\n    const getTaskStatusText = (status) => {\n      const statusMap = {\n        'waiting': '等待中',\n        'processing': '处理中',\n        'completed': '已完成',\n        'failed': '失败',\n        'paused': '已暂停'\n      }\n      return statusMap[status] || '未知'\n    }\n\n    const getProgressStatus = (status) => {\n      if (status === 'completed') return 'success'\n      if (status === 'failed') return 'exception'\n      return null\n    }\n\n    const formatTime = (time) => {\n      if (!time) return '-'\n      return new Date(time).toLocaleString()\n    }\n\n    const getProcessingDuration = (task) => {\n      if (!task || !task.createdAt) return '-'\n      const start = new Date(task.createdAt)\n      const end = task.updatedAt ? new Date(task.updatedAt) : new Date()\n      const duration = Math.floor((end - start) / 1000)\n\n      if (duration < 60) return `${duration}秒`\n      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`\n      return `${Math.floor(duration / 3600)}小时`\n    }\n\n    // 生命周期\n    onMounted(() => {\n      // 初始化系统状态\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      activeConnections.value = 1\n    })\n    \n    return {\n      // 图标组件\n      Upload,\n      VideoCamera,\n      DataAnalysis,\n      Document,\n      Plus,\n      MoreFilled,\n      Refresh,\n      InfoFilled,\n\n      // 响应式数据\n      currentStep,\n      currentTaskId,\n      currentTask,\n      reportData,\n      activeConnections,\n      lastUpdateTime,\n      systemStatus,\n\n      // 计算属性\n      totalTasks,\n      activeTasks,\n      completedTasks,\n      canUpload,\n      canDetect,\n      canAnalyze,\n      canExport,\n\n      // 方法\n      handleUploadSuccess,\n      handleUploadError,\n      handleUploadProgress,\n      handleUploadStatusChange,\n      handleDetectionUpdate,\n      handleDetectionStatusChange,\n      handleAnalysisDataUpdate,\n      handleExportReport,\n      handleRefreshReportData,\n      goToUpload,\n      startDetection,\n      generateAnalysis,\n      exportReport,\n      refreshSystem,\n      showSystemInfo,\n\n      // 任务状态辅助方法\n      getTaskStatusType,\n      getTaskStatusText,\n      getProgressStatus,\n      formatTime,\n      getProcessingDuration\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-analysis-console {\n  min-height: 100vh;\n  background: #f5f7fa;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 控制台头部 */\n.console-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px 32px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.console-title {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 28px;\n  font-weight: 700;\n  margin: 0 0 8px 0;\n}\n\n.console-description {\n  font-size: 16px;\n  opacity: 0.9;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.header-stats {\n  display: flex;\n  gap: 32px;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: 700;\n  line-height: 1.2;\n}\n\n.stat-label {\n  font-size: 14px;\n  opacity: 0.8;\n  margin-top: 4px;\n}\n\n/* 工作流程导航 */\n.workflow-navigation {\n  background: white;\n  padding: 24px 32px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n/* 主要内容区域 */\n.console-content {\n  flex: 1;\n  display: grid;\n  grid-template-columns: 280px 1fr;\n  gap: 24px;\n  padding: 24px 32px;\n  min-height: 0;\n}\n\n/* 左侧面板 */\n.left-panel {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.quick-actions-card {\n  height: fit-content;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.quick-actions .el-button {\n  justify-content: flex-start;\n}\n\n/* 主内容区域 */\n.main-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  min-height: 0;\n}\n\n.current-task-info {\n  flex-shrink: 0;\n}\n\n.task-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.task-title-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.task-title-section h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.task-progress-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  min-width: 200px;\n}\n\n.progress-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: #6b7280;\n  min-width: 40px;\n}\n\n.task-details {\n  margin-top: 16px;\n}\n\n.detail-item {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.detail-label {\n  font-size: 12px;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.detail-value {\n  font-size: 14px;\n  color: #1f2937;\n  font-weight: 500;\n}\n\n.dynamic-content {\n  flex: 1;\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  overflow: auto;\n}\n\n.step-content {\n  height: 100%;\n}\n\n/* 底部状态栏 */\n.console-footer {\n  background: white;\n  border-top: 1px solid #e5e7eb;\n  padding: 12px 32px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 14px;\n}\n\n.footer-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #6b7280;\n}\n\n.separator {\n  color: #d1d5db;\n}\n\n.footer-actions {\n  display: flex;\n  gap: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .console-content {\n    grid-template-columns: 300px 1fr;\n  }\n\n  .header-stats {\n    gap: 16px;\n  }\n}\n\n@media (max-width: 768px) {\n  .console-header {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .header-stats {\n    justify-content: center;\n  }\n\n  .console-content {\n    grid-template-columns: 1fr;\n    padding: 16px;\n  }\n\n  .workflow-navigation {\n    padding: 16px;\n  }\n\n  .console-footer {\n    flex-direction: column;\n    gap: 12px;\n    padding: 16px;\n  }\n\n  .task-header {\n    flex-direction: column;\n    gap: 12px;\n    align-items: flex-start;\n  }\n\n  .task-progress-section {\n    width: 100%;\n    min-width: auto;\n  }\n}\n\n/* 滚动条样式 */\n.task-list::-webkit-scrollbar {\n  width: 6px;\n}\n\n.task-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.task-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.task-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 动画效果 */\n.task-item {\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.step-content {\n  animation: fadeIn 0.5s ease-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA2B;;EAE/BA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAgB;;EACrBA,KAAK,EAAC;AAAe;;EAStBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAOxBA,KAAK,EAAC;AAAqB;;EA0B3BA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAY;;EASdA,KAAK,EAAC;AAAe;;EAoCrBA,KAAK,EAAC,oBAAoB;EAACC,KAAyB,EAAzB;IAAA;EAAA;;;EAqB/BD,KAAK,EAAC;AAAc;;EA7H/BE,GAAA;EA+HgCF,KAAK,EAAC;;;EAGnBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAoB;;EAM1BA,KAAK,EAAC;AAAuB;;EAM1BA,KAAK,EAAC;AAAe;;EAK5BA,KAAK,EAAC;AAAc;;EAGdA,KAAK,EAAC;AAAa;;EAEhBA,KAAK,EAAC;AAAc;;EAIvBA,KAAK,EAAC;AAAa;;EAEhBA,KAAK,EAAC;AAAc;;EAIvBA,KAAK,EAAC;AAAa;;EAEhBA,KAAK,EAAC;AAAc;;EASjCA,KAAK,EAAC;AAAiB;;EA9KpCE,GAAA;EAgLwCF,KAAK,EAAC;;;EAhL9CE,GAAA;EA0LwCF,KAAK,EAAC;;;EA1L9CE,GAAA;EA0MwCF,KAAK,EAAC;;;EA1M9CE,GAAA;EAwNwCF,KAAK,EAAC;;;EAmBrCA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;EASnBA,KAAK,EAAC;AAAgB;;;;;;;;;;;;;;;;;;;;uBApP/BG,mBAAA,CA+PM,OA/PNC,UA+PM,GA9PJC,mBAAA,UAAa,EACbC,mBAAA,CAyBM,OAzBNC,UAyBM,GAxBJD,mBAAA,CAQM,OARNE,UAQM,GAPJF,mBAAA,CAGK,MAHLG,UAGK,GAFHC,YAAA,CAA2BC,kBAAA;IANrCC,OAAA,EAAAC,QAAA,CAMmB,MAAQ,CAARH,YAAA,CAAQI,eAAA,E;IAN3BC,CAAA;gCAAAC,gBAAA,CAMqC,gBAE7B,G,6BACAV,mBAAA,CAEI;IAFDN,KAAK,EAAC;EAAqB,GAAC,mCAE/B,qB,GAGFM,mBAAA,CAaM,OAbNW,UAaM,GAZJX,mBAAA,CAGM,OAHNY,UAGM,GAFJZ,mBAAA,CAA8C,OAA9Ca,UAA8C,EAAAC,gBAAA,CAAnBC,MAAA,CAAAC,UAAU,kB,0BACrChB,mBAAA,CAAkC;IAA7BN,KAAK,EAAC;EAAY,GAAC,MAAI,qB,GAE9BM,mBAAA,CAGM,OAHNiB,UAGM,GAFJjB,mBAAA,CAA+C,OAA/CkB,UAA+C,EAAAJ,gBAAA,CAApBC,MAAA,CAAAI,WAAW,kB,0BACtCnB,mBAAA,CAAkC;IAA7BN,KAAK,EAAC;EAAY,GAAC,MAAI,qB,GAE9BM,mBAAA,CAGM,OAHNoB,WAGM,GAFJpB,mBAAA,CAAkD,OAAlDqB,WAAkD,EAAAP,gBAAA,CAAvBC,MAAA,CAAAO,cAAc,kB,0BACzCtB,mBAAA,CAAiC;IAA5BN,KAAK,EAAC;EAAY,GAAC,KAAG,qB,OAKjCK,mBAAA,YAAe,EACfC,mBAAA,CAuBM,OAvBNuB,WAuBM,GAtBJnB,YAAA,CAqBWoB,mBAAA;IArBAC,MAAM,EAAEV,MAAA,CAAAW,WAAW;IAAE,cAAY,EAAZ;;IAhCtCpB,OAAA,EAAAC,QAAA,CAiCQ,MAIE,CAJFH,YAAA,CAIEuB,kBAAA;MAHAC,KAAK,EAAC,MAAM;MACZC,WAAW,EAAC,WAAW;MACvBC,IAAI,EAAC;QAEP1B,YAAA,CAIEuB,kBAAA;MAHAC,KAAK,EAAC,MAAM;MACZC,WAAW,EAAC,UAAU;MACtBC,IAAI,EAAC;QAEP1B,YAAA,CAIEuB,kBAAA;MAHAC,KAAK,EAAC,MAAM;MACZC,WAAW,EAAC,QAAQ;MACpBC,IAAI,EAAC;QAEP1B,YAAA,CAIEuB,kBAAA;MAHAC,KAAK,EAAC,MAAM;MACZC,WAAW,EAAC,QAAQ;MACpBC,IAAI,EAAC;;IAnDfrB,CAAA;mCAwDIV,mBAAA,YAAe,EACfC,mBAAA,CA+KM,OA/KN+B,WA+KM,GA9KJhC,mBAAA,UAAa,EACbC,mBAAA,CA+DM,OA/DNgC,WA+DM,GA5DJjC,mBAAA,UAAa,EACbK,YAAA,CA0DU6B,kBAAA;IA1DDvC,KAAK,EAAC;EAAoB;IACtBwC,MAAM,EAAA3B,QAAA,CACf,MAAiB4B,MAAA,QAAAA,MAAA,OAAjBnC,mBAAA,CAAiB,cAAX,MAAI,oB;IAjEtBM,OAAA,EAAAC,QAAA,CAoEU,MAiCM,CAjCNP,mBAAA,CAiCM,OAjCNoC,WAiCM,GAhCJhC,YAAA,CAOYiC,oBAAA;MANVC,IAAI,EAAC,SAAS;MACbR,IAAI,EAAEf,MAAA,CAAAwB,MAAM;MACZC,OAAK,EAAEzB,MAAA,CAAA0B,UAAU;MACjBC,QAAQ,GAAG3B,MAAA,CAAA4B;;MAzE1BrC,OAAA,EAAAC,QAAA,CA0Ea,MAED4B,MAAA,SAAAA,MAAA,QA5EZzB,gBAAA,CA0Ea,QAED,E;MA5EZD,CAAA;wDA6EYL,YAAA,CAOYiC,oBAAA;MANVC,IAAI,EAAC,SAAS;MACbR,IAAI,EAAEf,MAAA,CAAA6B,WAAW;MACjBJ,OAAK,EAAEzB,MAAA,CAAA8B,cAAc;MACrBH,QAAQ,GAAG3B,MAAA,CAAA+B,SAAS,KAAKC,IAAA,CAAAC;;MAjFxC1C,OAAA,EAAAC,QAAA,CAkFa,MAED4B,MAAA,SAAAA,MAAA,QApFZzB,gBAAA,CAkFa,QAED,E;MApFZD,CAAA;wDAqFYL,YAAA,CAOYiC,oBAAA;MANVC,IAAI,EAAC,SAAS;MACbR,IAAI,EAAEf,MAAA,CAAAkC,YAAY;MAClBT,OAAK,EAAEzB,MAAA,CAAAmC,gBAAgB;MACvBR,QAAQ,GAAG3B,MAAA,CAAAoC;;MAzF1B7C,OAAA,EAAAC,QAAA,CA0Fa,MAED4B,MAAA,SAAAA,MAAA,QA5FZzB,gBAAA,CA0Fa,QAED,E;MA5FZD,CAAA;wDA6FYL,YAAA,CAOYiC,oBAAA;MANVC,IAAI,EAAC,MAAM;MACVR,IAAI,EAAEf,MAAA,CAAAqC,QAAQ;MACdZ,OAAK,EAAEzB,MAAA,CAAAsC,YAAY;MACnBX,QAAQ,GAAG3B,MAAA,CAAAuC;;MAjG1BhD,OAAA,EAAAC,QAAA,CAkGa,MAED4B,MAAA,SAAAA,MAAA,QApGZzB,gBAAA,CAkGa,QAED,E;MApGZD,CAAA;0DAwGUT,mBAAA,CAgBM,OAhBNuD,WAgBM,GAfJnD,YAAA,CAKEoD,oBAAA;MA9GdC,UAAA,EA0GuBV,IAAA,CAAAC,UAAU;MA1GjC,uBAAAb,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IA0GuBX,IAAA,CAAAC,UAAU,GAAAU,MAAA;MACnB,aAAW,EAAC,MAAM;MAClB,eAAa,EAAC,MAAM;MACnBC,QAAM,EAAEZ,IAAA,CAAAa;yDAGHb,IAAA,CAAAC,UAAU,I,cADlBa,YAAA,CAQYxB,oBAAA;MAvHxBzC,GAAA;MAiHc0C,IAAI,EAAC,SAAS;MACdwB,IAAI,EAAC,OAAO;MACXtB,OAAK,EAAEO,IAAA,CAAAgB,kBAAkB;MAC1BpE,KAAyB,EAAzB;QAAA;MAAA;;MApHdW,OAAA,EAAAC,QAAA,CAqHa,MAED4B,MAAA,SAAAA,MAAA,QAvHZzB,gBAAA,CAqHa,YAED,E;MAvHZD,CAAA;sCAAAV,mBAAA,e;IAAAU,CAAA;QA4HMV,mBAAA,WAAc,EACdC,mBAAA,CA0GM,OA1GNgE,WA0GM,GAzGJjE,mBAAA,YAAe,EACJgB,MAAA,CAAAkD,WAAW,I,cAAtBpE,mBAAA,CA4CM,OA5CNqE,WA4CM,GA3CJ9D,YAAA,CA0CU6B,kBAAA;IAzCGC,MAAM,EAAA3B,QAAA,CACf,MAeM,CAfNP,mBAAA,CAeM,OAfNmE,WAeM,GAdJnE,mBAAA,CAKM,OALNoE,WAKM,GAJJpE,mBAAA,CAA+B,YAAAc,gBAAA,CAAxBC,MAAA,CAAAkD,WAAW,CAACI,IAAI,kBACvBjE,YAAA,CAESkE,iBAAA;MAFAhC,IAAI,EAAEvB,MAAA,CAAAwD,iBAAiB,CAACxD,MAAA,CAAAkD,WAAW,CAACO,MAAM;;MArIrElE,OAAA,EAAAC,QAAA,CAsIoB,MAA2C,CAtI/DG,gBAAA,CAAAI,gBAAA,CAsIuBC,MAAA,CAAA0D,iBAAiB,CAAC1D,MAAA,CAAAkD,WAAW,CAACO,MAAM,kB;MAtI3D/D,CAAA;mCAyIgBT,mBAAA,CAOM,OAPN0E,WAOM,GANJtE,YAAA,CAIEuE,sBAAA;MAHCC,UAAU,EAAE7D,MAAA,CAAAkD,WAAW,CAACY,QAAQ;MAChCL,MAAM,EAAEzD,MAAA,CAAA+D,iBAAiB,CAAC/D,MAAA,CAAAkD,WAAW,CAACO,MAAM;MAC5C,cAAY,EAAE;uDAEjBxE,mBAAA,CAA8D,QAA9D+E,WAA8D,EAAAjE,gBAAA,CAA/BC,MAAA,CAAAkD,WAAW,CAACY,QAAQ,IAAG,GAAC,gB;IA/IzEvE,OAAA,EAAAC,QAAA,CAoJY,MAqBM,CArBNP,mBAAA,CAqBM,OArBNgF,WAqBM,GApBJ5E,YAAA,CAmBS6E,iBAAA;MAnBAC,MAAM,EAAE;IAAE;MArJjC5E,OAAA,EAAAC,QAAA,CAsJgB,MAKS,CALTH,YAAA,CAKS+E,iBAAA;QALAC,IAAI,EAAE;MAAC;QAtJhC9E,OAAA,EAAAC,QAAA,CAuJkB,MAGM,CAHNP,mBAAA,CAGM,OAHNqF,WAGM,G,4BAFJrF,mBAAA,CAAuC;UAAjCN,KAAK,EAAC;QAAc,GAAC,OAAK,sBAChCM,mBAAA,CAAsD,QAAtDsF,WAAsD,EAAAxE,gBAAA,CAAxBC,MAAA,CAAAkD,WAAW,CAACsB,EAAE,iB;QAzJhE9E,CAAA;UA4JgBL,YAAA,CAKS+E,iBAAA;QALAC,IAAI,EAAE;MAAC;QA5JhC9E,OAAA,EAAAC,QAAA,CA6JkB,MAGM,CAHNP,mBAAA,CAGM,OAHNwF,WAGM,G,4BAFJxF,mBAAA,CAAuC;UAAjCN,KAAK,EAAC;QAAc,GAAC,OAAK,sBAChCM,mBAAA,CAAyE,QAAzEyF,WAAyE,EAAA3E,gBAAA,CAA3CC,MAAA,CAAA2E,UAAU,CAAC3E,MAAA,CAAAkD,WAAW,CAAC0B,SAAS,kB;QA/JlFlF,CAAA;UAkKgBL,YAAA,CAKS+E,iBAAA;QALAC,IAAI,EAAE;MAAC;QAlKhC9E,OAAA,EAAAC,QAAA,CAmKkB,MAGM,CAHNP,mBAAA,CAGM,OAHN4F,WAGM,G,4BAFJ5F,mBAAA,CAAuC;UAAjCN,KAAK,EAAC;QAAc,GAAC,OAAK,sBAChCM,mBAAA,CAA0E,QAA1E6F,WAA0E,EAAA/E,gBAAA,CAA5CC,MAAA,CAAA+E,qBAAqB,CAAC/E,MAAA,CAAAkD,WAAW,kB;QArKnFxD,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;UAAAV,mBAAA,gBA6KQA,mBAAA,YAAe,EACfC,mBAAA,CAwDM,OAxDN+F,WAwDM,GAvDJhG,mBAAA,eAAkB,EACPgB,MAAA,CAAAW,WAAW,U,cAAtB7B,mBAAA,CAOM,OAPNmG,WAOM,GANJ5F,YAAA,CAKE6F,6BAAA;IAJCC,eAAc,EAAEnF,MAAA,CAAAoF,mBAAmB;IACnCC,aAAY,EAAErF,MAAA,CAAAsF,iBAAiB;IAC/BC,gBAAe,EAAEvF,MAAA,CAAAwF,oBAAoB;IACrCC,cAAa,EAAEzF,MAAA,CAAA0F;2GArL9B1G,mBAAA,gBAyLUA,mBAAA,eAAkB,EACPgB,MAAA,CAAAW,WAAW,U,cAAtB7B,mBAAA,CAaM,OAbN6G,WAaM,GAXI3D,IAAA,CAAA4D,eAAe,I,cADvB9C,YAAA,CAME+C,gCAAA;IAjMdhH,GAAA;IA6Le,SAAO,EAAEmD,IAAA,CAAA4D,eAAe;IACxB,YAAU,EAAE,IAAI;IAChBE,iBAAgB,EAAE9F,MAAA,CAAA+F,qBAAqB;IACvCN,cAAa,EAAEzF,MAAA,CAAAgG;gGAElBlD,YAAA,CAIWmD,mBAAA;IAtMvBpH,GAAA;IAkM6BiC,WAAW,EAAC;;IAlMzCvB,OAAA,EAAAC,QAAA,CAmMc,MAEY,CAFZH,YAAA,CAEYiC,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAAL,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAAE3C,MAAA,CAAAW,WAAW;;MAnM3DpB,OAAA,EAAAC,QAAA,CAmMiE,MAEnD4B,MAAA,SAAAA,MAAA,QArMdzB,gBAAA,CAmMiE,QAEnD,E;MArMdD,CAAA;;IAAAA,CAAA;WAAAV,mBAAA,gBAyMUA,mBAAA,eAAkB,EACPgB,MAAA,CAAAW,WAAW,U,cAAtB7B,mBAAA,CAWM,OAXNoH,WAWM,GATIlG,MAAA,CAAAmG,aAAa,I,cADrBrD,YAAA,CAIEsD,mCAAA;IA/MdvH,GAAA;IA6Me,SAAO,EAAEmB,MAAA,CAAAmG,aAAa;IACtBE,aAAY,EAAErG,MAAA,CAAAsG;0EAEjBxD,YAAA,CAIWmD,mBAAA;IApNvBpH,GAAA;IAgN6BiC,WAAW,EAAC;;IAhNzCvB,OAAA,EAAAC,QAAA,CAiNc,MAEY,CAFZH,YAAA,CAEYiC,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAAL,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAAE3C,MAAA,CAAAW,WAAW;;MAjN3DpB,OAAA,EAAAC,QAAA,CAiNiE,MAEnD4B,MAAA,SAAAA,MAAA,QAnNdzB,gBAAA,CAiNiE,QAEnD,E;MAnNdD,CAAA;;IAAAA,CAAA;WAAAV,mBAAA,gBAuNUA,mBAAA,eAAkB,EACPgB,MAAA,CAAAW,WAAW,U,cAAtB7B,mBAAA,CAaM,OAbNyH,WAaM,GAXIvG,MAAA,CAAAmG,aAAa,IAAInG,MAAA,CAAAwG,UAAU,I,cADnC1D,YAAA,CAME2D,mCAAA;IA/Nd5H,GAAA;IA2Ne,SAAO,EAAEmB,MAAA,CAAAmG,aAAa;IACtB,aAAW,EAAEnG,MAAA,CAAAwG,UAAU;IACvBE,cAAa,EAAE1G,MAAA,CAAA2G,kBAAkB;IACjCC,aAAY,EAAE5G,MAAA,CAAA6G;2GAEjB/D,YAAA,CAIWmD,mBAAA;IApOvBpH,GAAA;IAgO6BiC,WAAW,EAAC;;IAhOzCvB,OAAA,EAAAC,QAAA,CAiOc,MAEY,CAFZH,YAAA,CAEYiC,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAAL,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAAE3C,MAAA,CAAAW,WAAW;;MAjO3DpB,OAAA,EAAAC,QAAA,CAiOiE,MAEnD4B,MAAA,SAAAA,MAAA,QAnOdzB,gBAAA,CAiOiE,QAEnD,E;MAnOdD,CAAA;;IAAAA,CAAA;WAAAV,mBAAA,e,OA0OIA,mBAAA,WAAc,EACdC,mBAAA,CAoBM,OApBN6H,WAoBM,GAnBJ7H,mBAAA,CAOM,OAPN8H,WAOM,G,4BANJ9H,mBAAA,CAAmB,cAAb,QAAM,sBACZI,YAAA,CAA+EkE,iBAAA;IAAtEhC,IAAI,EAAEvB,MAAA,CAAAgH,YAAY,CAACzF,IAAI;IAAEwB,IAAI,EAAC;;IA9O/CxD,OAAA,EAAAC,QAAA,CA8OuD,MAAuB,CA9O9EG,gBAAA,CAAAI,gBAAA,CA8O0DC,MAAA,CAAAgH,YAAY,CAACC,IAAI,iB;IA9O3EvH,CAAA;2DA+OQT,mBAAA,CAAgC;IAA1BN,KAAK,EAAC;EAAW,GAAC,GAAC,sBACzBM,mBAAA,CAA0C,cAApC,QAAM,GAAAc,gBAAA,CAAGC,MAAA,CAAAkH,iBAAiB,kB,4BAChCjI,mBAAA,CAAgC;IAA1BN,KAAK,EAAC;EAAW,GAAC,GAAC,sBACzBM,mBAAA,CAAuC,cAAjC,QAAM,GAAAc,gBAAA,CAAGC,MAAA,CAAAmH,cAAc,iB,GAG/BlI,mBAAA,CASM,OATNmI,WASM,GARJ/H,YAAA,CAGYiC,oBAAA;IAHDyB,IAAI,EAAC,OAAO;IAAEtB,OAAK,EAAEzB,MAAA,CAAAqH;;IAtPxC9H,OAAA,EAAAC,QAAA,CAuPU,MAA8B,CAA9BH,YAAA,CAA8BC,kBAAA;MAvPxCC,OAAA,EAAAC,QAAA,CAuPmB,MAAW,CAAXH,YAAA,CAAWiI,kBAAA,E;MAvP9B5H,CAAA;oCAAAC,gBAAA,CAuPwC,MAEhC,G;IAzPRD,CAAA;kCA0PQL,YAAA,CAGYiC,oBAAA;IAHDyB,IAAI,EAAC,OAAO;IAAEtB,OAAK,EAAEzB,MAAA,CAAAuH;;IA1PxChI,OAAA,EAAAC,QAAA,CA2PU,MAAiC,CAAjCH,YAAA,CAAiCC,kBAAA;MA3P3CC,OAAA,EAAAC,QAAA,CA2PmB,MAAc,CAAdH,YAAA,CAAcmI,qBAAA,E;MA3PjC9H,CAAA;oCAAAC,gBAAA,CA2P2C,QAEnC,G;IA7PRD,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}