<template>
  <div class="direction-comparison-chart" :style="{ height: height + 'px' }">
    <div ref="chartContainer" class="chart-container"></div>
    <div v-if="!hasData" class="no-data-message">
      <el-icon size="48"><DataLine /></el-icon>
      <p>暂无数据</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { DataLine } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

export default {
  name: 'DirectionComparisonChart',
  components: {
    DataLine
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    chartType: {
      type: String,
      default: 'bar', // 'bar' 或 'pie'
      validator: (value) => ['bar', 'pie'].includes(value)
    },
    height: {
      type: Number,
      default: 300
    }
  },
  setup(props) {
    const chartContainer = ref(null)
    let chartInstance = null
    
    const hasData = computed(() => {
      return props.data && Object.keys(props.data).length > 0
    })
    
    const getDirectionName = (direction) => {
      const names = {
        east: '东向',
        south: '南向',
        west: '西向',
        north: '北向'
      }
      return names[direction] || direction
    }
    
    const getDirectionColor = (direction) => {
      const colors = {
        east: '#409eff',
        south: '#67c23a',
        west: '#e6a23c',
        north: '#f56c6c'
      }
      return colors[direction] || '#909399'
    }
    
    const createBarChart = () => {
      if (!props.data || Object.keys(props.data).length === 0) return
      
      const directions = Object.keys(props.data)
      const chartData = directions.map(direction => ({
        name: getDirectionName(direction),
        value: props.data[direction]?.vehicleCount || 0,
        itemStyle: {
          color: getDirectionColor(direction)
        }
      }))
      
      const option = {
        title: {
          text: '各方向车辆数量对比',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#2c3e50'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            const data = params[0]
            return `${data.name}<br/>车辆数量: ${data.value} 辆`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: chartData.map(item => item.name),
          axisLabel: {
            color: '#606266'
          },
          axisLine: {
            lineStyle: {
              color: '#e4e7ed'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '车辆数量',
          nameTextStyle: {
            color: '#909399'
          },
          axisLabel: {
            color: '#606266'
          },
          axisLine: {
            lineStyle: {
              color: '#e4e7ed'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f5f7fa'
            }
          }
        },
        series: [{
          name: '车辆数量',
          type: 'bar',
          data: chartData,
          barWidth: '60%',
          label: {
            show: true,
            position: 'top',
            color: '#2c3e50',
            fontSize: 12
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      
      chartInstance.setOption(option)
    }
    
    const createPieChart = () => {
      if (!props.data || Object.keys(props.data).length === 0) return
      
      const directions = Object.keys(props.data)
      const chartData = directions.map(direction => ({
        name: getDirectionName(direction),
        value: props.data[direction]?.vehicleCount || 0,
        itemStyle: {
          color: getDirectionColor(direction)
        }
      }))
      
      const total = chartData.reduce((sum, item) => sum + item.value, 0)
      
      const option = {
        title: {
          text: '各方向车辆分布',
          subtext: `总计: ${total} 辆`,
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#2c3e50'
          },
          subtextStyle: {
            color: '#909399'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            const percent = total > 0 ? ((params.value / total) * 100).toFixed(1) : 0
            return `${params.name}<br/>车辆数量: ${params.value} 辆<br/>占比: ${percent}%`
          }
        },
        legend: {
          orient: 'horizontal',
          bottom: '5%',
          textStyle: {
            color: '#606266'
          }
        },
        series: [{
          name: '车辆分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '45%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'outside',
            formatter: function(params) {
              const percent = total > 0 ? ((params.value / total) * 100).toFixed(1) : 0
              return `${params.name}\n${params.value}辆\n${percent}%`
            },
            color: '#2c3e50',
            fontSize: 11
          },
          labelLine: {
            show: true,
            lineStyle: {
              color: '#c0c4cc'
            }
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 12,
              fontWeight: 'bold'
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          data: chartData
        }]
      }
      
      chartInstance.setOption(option)
    }
    
    const initChart = () => {
      if (!chartContainer.value) return
      
      // 销毁现有图表实例
      if (chartInstance) {
        chartInstance.dispose()
      }
      
      // 创建新的图表实例
      chartInstance = echarts.init(chartContainer.value)
      
      // 根据图表类型渲染
      if (props.chartType === 'pie') {
        createPieChart()
      } else {
        createBarChart()
      }
      
      // 响应式调整
      window.addEventListener('resize', () => {
        if (chartInstance) {
          chartInstance.resize()
        }
      })
    }
    
    const updateChart = () => {
      if (!chartInstance) {
        initChart()
        return
      }
      
      if (props.chartType === 'pie') {
        createPieChart()
      } else {
        createBarChart()
      }
    }
    
    // 生命周期
    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })
    
    // 监听器
    watch(() => props.data, () => {
      updateChart()
    }, { deep: true })
    
    watch(() => props.chartType, () => {
      updateChart()
    })
    
    watch(() => props.height, () => {
      if (chartInstance) {
        nextTick(() => {
          chartInstance.resize()
        })
      }
    })
    
    // 清理
    const cleanup = () => {
      if (chartInstance) {
        chartInstance.dispose()
        chartInstance = null
      }
      window.removeEventListener('resize', () => {
        if (chartInstance) {
          chartInstance.resize()
        }
      })
    }
    
    return {
      chartContainer,
      hasData,
      cleanup
    }
  },
  beforeUnmount() {
    this.cleanup()
  }
}
</script>

<style scoped>
.direction-comparison-chart {
  width: 100%;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.no-data-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #c0c4cc;
}

.no-data-message p {
  margin: 8px 0 0 0;
  font-size: 14px;
}
</style>
