package com.traffic.analysis.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.core.task.AsyncTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 性能优化配置
 */
@Configuration
@EnableCaching
@EnableAsync
@EnableScheduling
public class PerformanceConfig implements WebMvcConfigurer {

    /**
     * 缓存管理器配置
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();
        
        // 配置缓存名称
        cacheManager.setCacheNames(java.util.Arrays.asList(
            "userCache",           // 用户信息缓存
            "taskCache",           // 任务信息缓存
            "analysisResultCache", // 分析结果缓存
            "systemStatusCache",   // 系统状态缓存
            "statisticsCache",     // 统计数据缓存
            "configCache"          // 配置信息缓存
        ));
        
        // 允许空值缓存
        cacheManager.setAllowNullValues(true);
        
        return cacheManager;
    }

    /**
     * 异步任务执行器配置
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(4);
        
        // 最大线程数
        executor.setMaxPoolSize(16);
        
        // 队列容量
        executor.setQueueCapacity(100);
        
        // 线程名前缀
        executor.setThreadNamePrefix("VideoAnalysis-");
        
        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }

    /**
     * 视频处理专用执行器
     */
    @Bean(name = "videoProcessingExecutor")
    public Executor videoProcessingExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 视频处理是CPU密集型任务，线程数不宜过多
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(4);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("VideoProcessing-");
        
        // 拒绝策略：丢弃最老的任务
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.DiscardOldestPolicy());
        
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(120);
        
        executor.initialize();
        return executor;
    }

    /**
     * 四方向分析专用执行器
     */
    @Bean(name = "fourWayAnalysisExecutor")
    public Executor fourWayAnalysisExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 四方向分析需要确保4个方向能同时执行
        executor.setCorePoolSize(4);  // 核心线程数设为4，确保四个方向同时执行
        executor.setMaxPoolSize(8);   // 最大线程数设为8，允许额外的并发
        executor.setQueueCapacity(10); // 减少队列容量，优先创建新线程
        executor.setThreadNamePrefix("FourWayAnalysis-");

        // 拒绝策略：调用者运行，确保任务不会被丢弃
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());

        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(300); // 增加等待时间

        // 设置线程保活时间
        executor.setKeepAliveSeconds(60);
        executor.setAllowCoreThreadTimeOut(false); // 核心线程不超时

        executor.initialize();
        return executor;
    }

    /**
     * 数据库操作优化执行器
     */
    @Bean(name = "databaseExecutor")
    public Executor databaseExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 数据库操作通常是I/O密集型
        executor.setCorePoolSize(8);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("Database-");
        
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        return executor;
    }

    /**
     * Web异步支持配置
     */
    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 设置异步请求超时时间
        configurer.setDefaultTimeout(30000);
        
        // 设置异步请求执行器
        configurer.setTaskExecutor((AsyncTaskExecutor) taskExecutor());
    }

    /**
     * MongoDB查询优化配置
     */
    @Bean
    public MongoQueryOptimizer mongoQueryOptimizer(MongoTemplate mongoTemplate) {
        return new MongoQueryOptimizer(mongoTemplate);
    }

    /**
     * 线程池监控器
     */
    @Bean
    public ThreadPoolMonitor threadPoolMonitor() {
        return new ThreadPoolMonitor();
    }

    /**
     * MongoDB查询优化器
     */
    public static class MongoQueryOptimizer {
        
        private final MongoTemplate mongoTemplate;
        
        public MongoQueryOptimizer(MongoTemplate mongoTemplate) {
            this.mongoTemplate = mongoTemplate;
        }
        
        /**
         * 优化分页查询
         */
        public Query optimizePaginationQuery(Query query, int page, int size) {
            // 限制最大页面大小
            int maxSize = Math.min(size, 100);
            
            // 计算跳过的记录数
            int skip = page * maxSize;
            
            // 设置分页参数
            query.skip(skip).limit(maxSize);
            
            return query;
        }
        
        /**
         * 优化排序查询
         */
        public Query optimizeSortQuery(Query query, String sortField, boolean ascending) {
            // 添加排序
            org.springframework.data.domain.Sort.Direction direction = 
                ascending ? org.springframework.data.domain.Sort.Direction.ASC : 
                           org.springframework.data.domain.Sort.Direction.DESC;
            
            query.with(org.springframework.data.domain.Sort.by(direction, sortField));
            
            return query;
        }
        
        /**
         * 优化字段投影
         */
        public Query optimizeFieldProjection(Query query, String... fields) {
            // 只查询需要的字段
            for (String field : fields) {
                query.fields().include(field);
            }
            
            return query;
        }
        
        /**
         * 批量更新优化
         */
        public void optimizedBatchUpdate(String collectionName, 
                                       java.util.List<org.springframework.data.util.Pair<Query, Update>> updates) {
            // 使用批量操作
            org.springframework.data.mongodb.core.BulkOperations bulkOps = 
                mongoTemplate.bulkOps(org.springframework.data.mongodb.core.BulkOperations.BulkMode.UNORDERED, collectionName);
            
            for (org.springframework.data.util.Pair<Query, Update> update : updates) {
                bulkOps.updateOne(update.getFirst(), update.getSecond());
            }
            
            bulkOps.execute();
        }
    }

    /**
     * 性能监控配置
     */
    @Bean
    public PerformanceMonitor performanceMonitor() {
        return new PerformanceMonitor();
    }

    /**
     * 性能监控器
     */
    public static class PerformanceMonitor {
        
        private final java.util.concurrent.ConcurrentHashMap<String, Long> requestTimes = 
            new java.util.concurrent.ConcurrentHashMap<>();
        
        private final java.util.concurrent.ConcurrentHashMap<String, Integer> requestCounts = 
            new java.util.concurrent.ConcurrentHashMap<>();
        
        /**
         * 记录请求开始时间
         */
        public void recordRequestStart(String requestId) {
            requestTimes.put(requestId, System.currentTimeMillis());
        }
        
        /**
         * 记录请求结束时间并计算耗时
         */
        public long recordRequestEnd(String requestId) {
            Long startTime = requestTimes.remove(requestId);
            if (startTime != null) {
                long duration = System.currentTimeMillis() - startTime;
                
                // 统计请求次数
                String endpoint = extractEndpoint(requestId);
                requestCounts.merge(endpoint, 1, Integer::sum);
                
                return duration;
            }
            return 0;
        }
        
        /**
         * 获取性能统计信息
         */
        public java.util.Map<String, Object> getPerformanceStats() {
            java.util.Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("activeRequests", requestTimes.size());
            stats.put("requestCounts", new java.util.HashMap<>(requestCounts));
            stats.put("timestamp", System.currentTimeMillis());
            return stats;
        }
        
        private String extractEndpoint(String requestId) {
            // 从请求ID中提取端点信息
            if (requestId.contains("-")) {
                return requestId.substring(0, requestId.lastIndexOf("-"));
            }
            return "unknown";
        }
    }

    /**
     * 缓存预热配置
     */
    @Bean
    public CacheWarmupService cacheWarmupService() {
        return new CacheWarmupService();
    }

    /**
     * 缓存预热服务
     */
    public static class CacheWarmupService {
        
        /**
         * 预热系统缓存
         */
        @org.springframework.scheduling.annotation.Scheduled(fixedRate = 300000) // 5分钟
        public void warmupCache() {
            // 这里可以添加缓存预热逻辑
            // 例如：预加载热点数据、系统配置等
        }
        
        /**
         * 清理过期缓存
         */
        @org.springframework.scheduling.annotation.Scheduled(fixedRate = 600000) // 10分钟
        public void cleanupExpiredCache() {
            // 这里可以添加缓存清理逻辑
        }
    }
}
