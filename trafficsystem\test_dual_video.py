#!/usr/bin/env python3
"""
测试双视频并行处理功能
"""

import requests
import json
import time
import base64
from datetime import datetime

# 配置
JAVA_BACKEND_URL = "http://localhost:8080"
TEST_TASK_ID = "test_dual_" + str(int(time.time()))

def create_test_frame_data(task_prefix, frame_number, detection_count):
    """创建测试帧数据"""
    # 创建一个简单的测试图像（1x1像素的透明PNG）
    test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    return {
        "type": "frame_update",
        "taskId": TEST_TASK_ID,  # 使用清理后的任务ID
        "frameNumber": frame_number,
        "totalFrames": 100,
        "imageData": test_image_base64,
        "detectionCount": detection_count,
        "timestamp": datetime.now().isoformat(),
        "quality": 75,
        "direction": task_prefix  # 添加方向标识
    }

def test_dual_frame_push():
    """测试双视频帧推送"""
    print(f"🚀 开始测试双视频帧推送功能")
    print(f"📡 Java后端地址: {JAVA_BACKEND_URL}")
    print(f"🎯 测试任务ID: {TEST_TASK_ID}")
    print()
    
    # 测试端点URL
    url = f"{JAVA_BACKEND_URL}/api/video-progress/frame-update"
    
    # 模拟水平和垂直方向的帧数据
    directions = [
        {"prefix": "h", "name": "水平方向", "detection_pattern": lambda f: f % 3 + 1},
        {"prefix": "v", "name": "垂直方向", "detection_pattern": lambda f: f % 4 + 2}
    ]
    
    print(f"📦 开始发送测试帧数据...")
    
    for frame_num in range(1, 11):  # 发送10帧测试数据
        for direction in directions:
            detection_count = direction["detection_pattern"](frame_num)
            frame_data = create_test_frame_data(direction["prefix"], frame_num, detection_count)
            
            try:
                headers = {'Content-Type': 'application/json'}
                response = requests.post(url, json=frame_data, headers=headers, timeout=5)
                
                if response.status_code == 200:
                    print(f"✅ {direction['name']} 帧 {frame_num} 推送成功 (车辆数: {detection_count})")
                else:
                    print(f"❌ {direction['name']} 帧 {frame_num} 推送失败: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {direction['name']} 帧 {frame_num} 推送异常: {e}")
        
        # 间隔1秒
        time.sleep(1)
    
    print()
    print(f"📊 测试完成，共发送了 {10 * len(directions)} 帧数据")

def test_websocket_cache():
    """测试WebSocket缓存功能"""
    print(f"🔍 检查WebSocket缓存...")
    
    try:
        # 检查缓存的帧数据
        url = f"{JAVA_BACKEND_URL}/api/video-progress/frames/{TEST_TASK_ID}"
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            frame_count = data.get('count', 0)
            print(f"📦 缓存帧数量: {frame_count}")
            
            if frame_count > 0:
                frames = data.get('frames', [])
                print(f"📋 缓存帧详情:")
                for frame in frames[:5]:  # 只显示前5帧
                    print(f"  - 帧{frame.get('frameNumber', '?')}: 车辆{frame.get('detectionCount', '?')}, 方向{frame.get('direction', '?')}")
                
                if len(frames) > 5:
                    print(f"  ... 还有 {len(frames) - 5} 帧")
            else:
                print(f"⚠️  没有缓存的帧数据")
        else:
            print(f"❌ 获取缓存帧失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 检查WebSocket缓存时出错: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 双视频并行处理功能测试")
    print("=" * 60)
    print()
    
    # 1. 测试双视频帧推送
    test_dual_frame_push()
    print()
    
    # 2. 检查WebSocket缓存
    test_websocket_cache()
    print()
    
    print("=" * 60)
    print("🏁 测试完成")
    print("=" * 60)
    print()
    print("💡 前端使用说明:")
    print(f"   1. 在浏览器控制台运行: debugWebSocket('{TEST_TASK_ID}')")
    print(f"   2. 检查是否能收到水平和垂直方向的帧数据")
    print(f"   3. 验证双视频预览窗口是否正常显示")

if __name__ == "__main__":
    main()
