{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElement<PERSON><PERSON> as _createElementBlock, normalizeClass as _normalizeClass, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"four-way-realtime-viewer\"\n};\nconst _hoisted_2 = {\n  class: \"connection-status\"\n};\nconst _hoisted_3 = {\n  class: \"last-update\"\n};\nconst _hoisted_4 = {\n  class: \"detection-grid\"\n};\nconst _hoisted_5 = {\n  class: \"detection-item north\"\n};\nconst _hoisted_6 = {\n  class: \"direction-header\"\n};\nconst _hoisted_7 = {\n  class: \"detection-content\"\n};\nconst _hoisted_8 = {\n  class: \"direction-stats\"\n};\nconst _hoisted_9 = {\n  class: \"stat-item\"\n};\nconst _hoisted_10 = {\n  class: \"stat-value\"\n};\nconst _hoisted_11 = {\n  class: \"stat-item\"\n};\nconst _hoisted_12 = {\n  class: \"stat-value\"\n};\nconst _hoisted_13 = {\n  class: \"detection-item west\"\n};\nconst _hoisted_14 = {\n  class: \"direction-header\"\n};\nconst _hoisted_15 = {\n  class: \"detection-content\"\n};\nconst _hoisted_16 = {\n  class: \"direction-stats\"\n};\nconst _hoisted_17 = {\n  class: \"stat-item\"\n};\nconst _hoisted_18 = {\n  class: \"stat-value\"\n};\nconst _hoisted_19 = {\n  class: \"stat-item\"\n};\nconst _hoisted_20 = {\n  class: \"stat-value\"\n};\nconst _hoisted_21 = {\n  class: \"detection-item east\"\n};\nconst _hoisted_22 = {\n  class: \"direction-header\"\n};\nconst _hoisted_23 = {\n  class: \"detection-content\"\n};\nconst _hoisted_24 = {\n  class: \"direction-stats\"\n};\nconst _hoisted_25 = {\n  class: \"stat-item\"\n};\nconst _hoisted_26 = {\n  class: \"stat-value\"\n};\nconst _hoisted_27 = {\n  class: \"stat-item\"\n};\nconst _hoisted_28 = {\n  class: \"stat-value\"\n};\nconst _hoisted_29 = {\n  class: \"detection-item south\"\n};\nconst _hoisted_30 = {\n  class: \"direction-header\"\n};\nconst _hoisted_31 = {\n  class: \"detection-content\"\n};\nconst _hoisted_32 = {\n  class: \"direction-stats\"\n};\nconst _hoisted_33 = {\n  class: \"stat-item\"\n};\nconst _hoisted_34 = {\n  class: \"stat-value\"\n};\nconst _hoisted_35 = {\n  class: \"stat-item\"\n};\nconst _hoisted_36 = {\n  class: \"stat-value\"\n};\nconst _hoisted_37 = {\n  class: \"overview-stats\"\n};\nconst _hoisted_38 = {\n  class: \"total-vehicles-summary\"\n};\nconst _hoisted_39 = {\n  class: \"total-count\"\n};\nconst _hoisted_40 = {\n  class: \"peak-direction-info\"\n};\nconst _hoisted_41 = {\n  class: \"peak-text\"\n};\nconst _hoisted_42 = {\n  key: 0,\n  class: \"detection-progress\"\n};\nconst _hoisted_43 = {\n  class: \"progress-text\"\n};\nconst _hoisted_44 = {\n  class: \"enhanced-global-stats\"\n};\nconst _hoisted_45 = {\n  class: \"stats-header\"\n};\nconst _hoisted_46 = {\n  class: \"connection-status-info\"\n};\nconst _hoisted_47 = {\n  class: \"stats-grid\"\n};\nconst _hoisted_48 = {\n  class: \"stat-card primary\"\n};\nconst _hoisted_49 = {\n  class: \"stat-content\"\n};\nconst _hoisted_50 = {\n  class: \"stat-value\"\n};\nconst _hoisted_51 = {\n  class: \"stat-trend positive\"\n};\nconst _hoisted_52 = {\n  class: \"stat-card success\"\n};\nconst _hoisted_53 = {\n  class: \"stat-content\"\n};\nconst _hoisted_54 = {\n  class: \"stat-value\"\n};\nconst _hoisted_55 = {\n  class: \"stat-trend\"\n};\nconst _hoisted_56 = {\n  class: \"stat-card warning\"\n};\nconst _hoisted_57 = {\n  class: \"stat-content\"\n};\nconst _hoisted_58 = {\n  class: \"stat-value\"\n};\nconst _hoisted_59 = {\n  class: \"stat-card info\"\n};\nconst _hoisted_60 = {\n  class: \"stat-content\"\n};\nconst _hoisted_61 = {\n  class: \"stat-value\"\n};\nconst _hoisted_62 = {\n  class: \"stat-trend\"\n};\nconst _hoisted_63 = {\n  class: \"stat-card danger\"\n};\nconst _hoisted_64 = {\n  class: \"stat-content\"\n};\nconst _hoisted_65 = {\n  class: \"stat-value\"\n};\nconst _hoisted_66 = {\n  class: \"stat-trend\"\n};\nconst _hoisted_67 = {\n  class: \"stat-card purple\"\n};\nconst _hoisted_68 = {\n  class: \"stat-content\"\n};\nconst _hoisted_69 = {\n  class: \"stat-value\"\n};\nconst _hoisted_70 = {\n  class: \"stat-trend\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_Connection = _resolveComponent(\"Connection\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_Top = _resolveComponent(\"Top\");\n  const _component_RealTimeFrameViewer = _resolveComponent(\"RealTimeFrameViewer\");\n  const _component_Back = _resolveComponent(\"Back\");\n  const _component_Right = _resolveComponent(\"Right\");\n  const _component_Bottom = _resolveComponent(\"Bottom\");\n  const _component_Grid = _resolveComponent(\"Grid\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 连接状态指示器 \"), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_tag, {\n    type: $setup.connectionStatusType,\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_Connection)]),\n      _: 1 /* STABLE */\n    }), _createTextVNode(\" \" + _toDisplayString($setup.connectionStatusText), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"]), _createElementVNode(\"span\", _hoisted_3, \" 最后更新: \" + _toDisplayString($setup.lastUpdateTime || '未连接'), 1 /* TEXT */)]), _createCommentVNode(\" 四方向检测网格 - 2x2布局 \"), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 上排：北向和东向 \"), _createCommentVNode(\" 北向检测 - 左上 \"), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Top)]),\n    _: 1 /* STABLE */\n  }), _cache[8] || (_cache[8] = _createElementVNode(\"span\", {\n    class: \"direction-label\"\n  }, \"北向 (North)\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.getDirectionStatusType('north'),\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getDirectionStatusText('north')), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_RealTimeFrameViewer, {\n    ref: \"northViewer\",\n    direction: \"north\",\n    \"frame-data\": $setup.directionFrameData.north,\n    status: $setup.directionStats.north.status,\n    progress: 0,\n    \"show-controls\": false,\n    onPauseToggled: _cache[0] || (_cache[0] = data => console.log('North pause toggled:', data)),\n    onFrameSaved: _cache[1] || (_cache[1] = data => console.log('North frame saved:', data)),\n    class: \"direction-viewer\"\n  }, null, 8 /* PROPS */, [\"frame-data\", \"status\"]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", _hoisted_10, _toDisplayString($setup.directionStats.north.vehicleCount), 1 /* TEXT */), _cache[9] || (_cache[9] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"车辆数\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"span\", _hoisted_12, _toDisplayString($setup.directionStats.north.frameRate), 1 /* TEXT */), _cache[10] || (_cache[10] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"帧率\", -1 /* HOISTED */))])])])]), _createCommentVNode(\" 下排：西向和南向 \"), _createCommentVNode(\" 西向检测 - 左下 \"), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Back)]),\n    _: 1 /* STABLE */\n  }), _cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n    class: \"direction-label\"\n  }, \"西向 (West)\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.getDirectionStatusType('west'),\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getDirectionStatusText('west')), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_RealTimeFrameViewer, {\n    ref: \"westViewer\",\n    direction: \"west\",\n    \"frame-data\": $setup.directionFrameData.west,\n    status: $setup.directionStats.west.status,\n    progress: 0,\n    \"show-controls\": false,\n    onPauseToggled: _cache[2] || (_cache[2] = data => console.log('West pause toggled:', data)),\n    onFrameSaved: _cache[3] || (_cache[3] = data => console.log('West frame saved:', data)),\n    class: \"direction-viewer\"\n  }, null, 8 /* PROPS */, [\"frame-data\", \"status\"]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"span\", _hoisted_18, _toDisplayString($setup.directionStats.west.vehicleCount), 1 /* TEXT */), _cache[12] || (_cache[12] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"车辆数\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"span\", _hoisted_20, _toDisplayString($setup.directionStats.west.frameRate), 1 /* TEXT */), _cache[13] || (_cache[13] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"帧率\", -1 /* HOISTED */))])])])]), _createCommentVNode(\" 东向检测 - 右上 \"), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Right)]),\n    _: 1 /* STABLE */\n  }), _cache[14] || (_cache[14] = _createElementVNode(\"span\", {\n    class: \"direction-label\"\n  }, \"东向 (East)\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.getDirectionStatusType('east'),\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getDirectionStatusText('east')), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_RealTimeFrameViewer, {\n    ref: \"eastViewer\",\n    direction: \"east\",\n    \"frame-data\": $setup.directionFrameData.east,\n    status: $setup.directionStats.east.status,\n    progress: 0,\n    \"show-controls\": false,\n    onPauseToggled: _cache[4] || (_cache[4] = data => console.log('East pause toggled:', data)),\n    onFrameSaved: _cache[5] || (_cache[5] = data => console.log('East frame saved:', data)),\n    class: \"direction-viewer\"\n  }, null, 8 /* PROPS */, [\"frame-data\", \"status\"]), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"span\", _hoisted_26, _toDisplayString($setup.directionStats.east.vehicleCount), 1 /* TEXT */), _cache[15] || (_cache[15] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"车辆数\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"span\", _hoisted_28, _toDisplayString($setup.directionStats.east.frameRate), 1 /* TEXT */), _cache[16] || (_cache[16] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"帧率\", -1 /* HOISTED */))])])])]), _createCommentVNode(\" 南向检测 - 右下 \"), _createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Bottom)]),\n    _: 1 /* STABLE */\n  }), _cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n    class: \"direction-label\"\n  }, \"南向 (South)\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: $setup.getDirectionStatusType('south'),\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getDirectionStatusText('south')), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"])]), _createElementVNode(\"div\", _hoisted_31, [_createVNode(_component_RealTimeFrameViewer, {\n    ref: \"southViewer\",\n    direction: \"south\",\n    \"frame-data\": $setup.directionFrameData.south,\n    status: $setup.directionStats.south.status,\n    progress: 0,\n    \"show-controls\": false,\n    onPauseToggled: _cache[6] || (_cache[6] = data => console.log('South pause toggled:', data)),\n    onFrameSaved: _cache[7] || (_cache[7] = data => console.log('South frame saved:', data)),\n    class: \"direction-viewer\"\n  }, null, 8 /* PROPS */, [\"frame-data\", \"status\"]), _createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"span\", _hoisted_34, _toDisplayString($setup.directionStats.south.vehicleCount), 1 /* TEXT */), _cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"车辆数\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"span\", _hoisted_36, _toDisplayString($setup.directionStats.south.frameRate), 1 /* TEXT */), _cache[19] || (_cache[19] = _createElementVNode(\"span\", {\n    class: \"stat-label\"\n  }, \"帧率\", -1 /* HOISTED */))])])])])]), _createCommentVNode(\" 总览统计 \"), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Grid)]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"span\", _hoisted_39, _toDisplayString($setup.totalVehicleCount), 1 /* TEXT */), _cache[20] || (_cache[20] = _createElementVNode(\"span\", {\n    class: \"total-label\"\n  }, \"总车辆数\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"span\", _hoisted_41, \"高峰方向: \" + _toDisplayString($setup.getPeakDirection()), 1 /* TEXT */)])]), _createCommentVNode(\" 检测进度条 \"), $setup.showProgress ? (_openBlock(), _createElementBlock(\"div\", _hoisted_42, [_createVNode(_component_el_progress, {\n    percentage: $setup.overallProgress,\n    status: $setup.progressStatus,\n    \"stroke-width\": 8,\n    \"show-text\": true\n  }, {\n    default: _withCtx(({\n      percentage\n    }) => [_createElementVNode(\"span\", _hoisted_43, _toDisplayString(percentage) + \"% 检测进度\", 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"percentage\", \"status\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 增强的全局统计信息 \"), _createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"div\", _hoisted_45, [_cache[21] || (_cache[21] = _createElementVNode(\"h3\", null, \"实时交通分析\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_46, [_createVNode(_component_el_icon, {\n    class: _normalizeClass({\n      'connected': $setup.isConnected,\n      'disconnected': !$setup.isConnected\n    })\n  }, {\n    default: _withCtx(() => [_createVNode(_component_Connection)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"class\"]), _createElementVNode(\"span\", null, _toDisplayString($setup.isConnected ? '已连接' : '未连接'), 1 /* TEXT */), $setup.lastUpdateTime ? (_openBlock(), _createBlock(_component_el_tag, {\n    key: 0,\n    size: \"small\",\n    type: \"info\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.lastUpdateTime), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"div\", _hoisted_48, [_cache[23] || (_cache[23] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, \"🚗\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"div\", _hoisted_50, _toDisplayString($setup.totalVehicleCount), 1 /* TEXT */), _cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"总车辆数\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_51, \" +\" + _toDisplayString($setup.getRecentIncrease()), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_52, [_cache[25] || (_cache[25] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, \"📍\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_53, [_createElementVNode(\"div\", _hoisted_54, _toDisplayString($setup.getPeakDirection()), 1 /* TEXT */), _cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"最繁忙方向\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_55, _toDisplayString($setup.getPeakDirectionPercentage()) + \"% \", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_56, [_cache[28] || (_cache[28] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, \"⚡\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"div\", _hoisted_58, _toDisplayString($setup.getAverageFrameRate()), 1 /* TEXT */), _cache[26] || (_cache[26] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"平均帧率\", -1 /* HOISTED */)), _cache[27] || (_cache[27] = _createElementVNode(\"div\", {\n    class: \"stat-trend\"\n  }, \"实时\", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_59, [_cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, \"⏱️\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_60, [_createElementVNode(\"div\", _hoisted_61, _toDisplayString($setup.getProcessingTime()) + \"s\", 1 /* TEXT */), _cache[29] || (_cache[29] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"处理时间\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_62, _toDisplayString($setup.getEfficiencyLevel()), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_63, [_cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, \"🚨\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_64, [_createElementVNode(\"div\", _hoisted_65, _toDisplayString($setup.getCongestionLevel()), 1 /* TEXT */), _cache[31] || (_cache[31] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"拥堵等级\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_66, _toDisplayString($setup.getCongestionTrend()), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_67, [_cache[34] || (_cache[34] = _createElementVNode(\"div\", {\n    class: \"stat-icon\"\n  }, \"📊\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_68, [_createElementVNode(\"div\", _hoisted_69, _toDisplayString($setup.getTrafficFlowBalance()) + \"%\", 1 /* TEXT */), _cache[33] || (_cache[33] = _createElementVNode(\"div\", {\n    class: \"stat-label\"\n  }, \"流量平衡度\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_70, _toDisplayString($setup.getBalanceTrend()), 1 /* TEXT */)])])])])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_tag", "type", "$setup", "connectionStatusType", "size", "default", "_withCtx", "_component_el_icon", "_component_Connection", "_", "_createTextVNode", "_toDisplayString", "connectionStatusText", "_hoisted_3", "lastUpdateTime", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_component_Top", "getDirectionStatusType", "getDirectionStatusText", "_hoisted_7", "_component_RealTimeFrameViewer", "ref", "direction", "directionFrameData", "north", "status", "directionStats", "progress", "onPauseToggled", "_cache", "data", "console", "log", "onFrameSaved", "_hoisted_8", "_hoisted_9", "_hoisted_10", "vehicleCount", "_hoisted_11", "_hoisted_12", "frameRate", "_hoisted_13", "_hoisted_14", "_component_Back", "_hoisted_15", "west", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_component_Right", "_hoisted_23", "east", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_component_Bottom", "_hoisted_31", "south", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_component_Grid", "_hoisted_39", "totalVehicleCount", "_hoisted_40", "_hoisted_41", "getPeakDirection", "showProgress", "_hoisted_42", "_component_el_progress", "percentage", "overallProgress", "progressStatus", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "_normalizeClass", "isConnected", "_createBlock", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "getRecentIncrease", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "getPeakDirectionPercentage", "_hoisted_56", "_hoisted_57", "_hoisted_58", "getAverageFrameRate", "_hoisted_59", "_hoisted_60", "_hoisted_61", "getProcessingTime", "_hoisted_62", "getEfficiencyLevel", "_hoisted_63", "_hoisted_64", "_hoisted_65", "getCongestionLevel", "_hoisted_66", "getCongestionTrend", "_hoisted_67", "_hoisted_68", "_hoisted_69", "getTrafficFlowBalance", "_hoisted_70", "getBalanceTrend"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\FourWayRealtimeViewer.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-realtime-viewer\">\n    <!-- 连接状态指示器 -->\n    <div class=\"connection-status\">\n      <el-tag :type=\"connectionStatusType\" size=\"small\">\n        <el-icon><Connection /></el-icon>\n        {{ connectionStatusText }}\n      </el-tag>\n      <span class=\"last-update\">\n        最后更新: {{ lastUpdateTime || '未连接' }}\n      </span>\n    </div>\n\n    <!-- 四方向检测网格 - 2x2布局 -->\n    <div class=\"detection-grid\">\n      <!-- 上排：北向和东向 -->\n      <!-- 北向检测 - 左上 -->\n      <div class=\"detection-item north\">\n        <div class=\"direction-header\">\n          <el-icon><Top /></el-icon>\n          <span class=\"direction-label\">北向 (North)</span>\n          <el-tag :type=\"getDirectionStatusType('north')\" size=\"small\">\n            {{ getDirectionStatusText('north') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"northViewer\"\n            direction=\"north\"\n            :frame-data=\"directionFrameData.north\"\n            :status=\"directionStats.north.status\"\n            :progress=\"0\"\n            :show-controls=\"false\"\n            @pause-toggled=\"(data) => console.log('North pause toggled:', data)\"\n            @frame-saved=\"(data) => console.log('North frame saved:', data)\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.north.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.north.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 下排：西向和南向 -->\n      <!-- 西向检测 - 左下 -->\n      <div class=\"detection-item west\">\n        <div class=\"direction-header\">\n          <el-icon><Back /></el-icon>\n          <span class=\"direction-label\">西向 (West)</span>\n          <el-tag :type=\"getDirectionStatusType('west')\" size=\"small\">\n            {{ getDirectionStatusText('west') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"westViewer\"\n            direction=\"west\"\n            :frame-data=\"directionFrameData.west\"\n            :status=\"directionStats.west.status\"\n            :progress=\"0\"\n            :show-controls=\"false\"\n            @pause-toggled=\"(data) => console.log('West pause toggled:', data)\"\n            @frame-saved=\"(data) => console.log('West frame saved:', data)\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.west.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.west.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n\n      <!-- 东向检测 - 右上 -->\n      <div class=\"detection-item east\">\n        <div class=\"direction-header\">\n          <el-icon><Right /></el-icon>\n          <span class=\"direction-label\">东向 (East)</span>\n          <el-tag :type=\"getDirectionStatusType('east')\" size=\"small\">\n            {{ getDirectionStatusText('east') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"eastViewer\"\n            direction=\"east\"\n            :frame-data=\"directionFrameData.east\"\n            :status=\"directionStats.east.status\"\n            :progress=\"0\"\n            :show-controls=\"false\"\n            @pause-toggled=\"(data) => console.log('East pause toggled:', data)\"\n            @frame-saved=\"(data) => console.log('East frame saved:', data)\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.east.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.east.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 南向检测 - 右下 -->\n      <div class=\"detection-item south\">\n        <div class=\"direction-header\">\n          <el-icon><Bottom /></el-icon>\n          <span class=\"direction-label\">南向 (South)</span>\n          <el-tag :type=\"getDirectionStatusType('south')\" size=\"small\">\n            {{ getDirectionStatusText('south') }}\n          </el-tag>\n        </div>\n        <div class=\"detection-content\">\n          <RealTimeFrameViewer\n            ref=\"southViewer\"\n            direction=\"south\"\n            :frame-data=\"directionFrameData.south\"\n            :status=\"directionStats.south.status\"\n            :progress=\"0\"\n            :show-controls=\"false\"\n            @pause-toggled=\"(data) => console.log('South pause toggled:', data)\"\n            @frame-saved=\"(data) => console.log('South frame saved:', data)\"\n            class=\"direction-viewer\"\n          />\n          <div class=\"direction-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.south.vehicleCount }}</span>\n              <span class=\"stat-label\">车辆数</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-value\">{{ directionStats.south.frameRate }}</span>\n              <span class=\"stat-label\">帧率</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 总览统计 -->\n    <div class=\"overview-stats\">\n      <div class=\"total-vehicles-summary\">\n        <el-icon><Grid /></el-icon>\n        <span class=\"total-count\">{{ totalVehicleCount }}</span>\n        <span class=\"total-label\">总车辆数</span>\n      </div>\n      <div class=\"peak-direction-info\">\n        <span class=\"peak-text\">高峰方向: {{ getPeakDirection() }}</span>\n      </div>\n    </div>\n\n    <!-- 检测进度条 -->\n    <div v-if=\"showProgress\" class=\"detection-progress\">\n      <el-progress\n        :percentage=\"overallProgress\"\n        :status=\"progressStatus\"\n        :stroke-width=\"8\"\n        :show-text=\"true\"\n      >\n        <template #default=\"{ percentage }\">\n          <span class=\"progress-text\">{{ percentage }}% 检测进度</span>\n        </template>\n      </el-progress>\n    </div>\n\n    <!-- 增强的全局统计信息 -->\n    <div class=\"enhanced-global-stats\">\n      <div class=\"stats-header\">\n        <h3>实时交通分析</h3>\n        <div class=\"connection-status-info\">\n          <el-icon :class=\"{ 'connected': isConnected, 'disconnected': !isConnected }\">\n            <Connection />\n          </el-icon>\n          <span>{{ isConnected ? '已连接' : '未连接' }}</span>\n          <el-tag v-if=\"lastUpdateTime\" size=\"small\" type=\"info\">\n            {{ lastUpdateTime }}\n          </el-tag>\n        </div>\n      </div>\n\n      <div class=\"stats-grid\">\n        <div class=\"stat-card primary\">\n          <div class=\"stat-icon\">🚗</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ totalVehicleCount }}</div>\n            <div class=\"stat-label\">总车辆数</div>\n            <div class=\"stat-trend positive\">\n              +{{ getRecentIncrease() }}\n            </div>\n          </div>\n        </div>\n\n        <div class=\"stat-card success\">\n          <div class=\"stat-icon\">📍</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getPeakDirection() }}</div>\n            <div class=\"stat-label\">最繁忙方向</div>\n            <div class=\"stat-trend\">\n              {{ getPeakDirectionPercentage() }}%\n            </div>\n          </div>\n        </div>\n\n        <div class=\"stat-card warning\">\n          <div class=\"stat-icon\">⚡</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getAverageFrameRate() }}</div>\n            <div class=\"stat-label\">平均帧率</div>\n            <div class=\"stat-trend\">实时</div>\n          </div>\n        </div>\n\n        <div class=\"stat-card info\">\n          <div class=\"stat-icon\">⏱️</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getProcessingTime() }}s</div>\n            <div class=\"stat-label\">处理时间</div>\n            <div class=\"stat-trend\">{{ getEfficiencyLevel() }}</div>\n          </div>\n        </div>\n\n        <div class=\"stat-card danger\">\n          <div class=\"stat-icon\">🚨</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getCongestionLevel() }}</div>\n            <div class=\"stat-label\">拥堵等级</div>\n            <div class=\"stat-trend\">{{ getCongestionTrend() }}</div>\n          </div>\n        </div>\n\n        <div class=\"stat-card purple\">\n          <div class=\"stat-icon\">📊</div>\n          <div class=\"stat-content\">\n            <div class=\"stat-value\">{{ getTrafficFlowBalance() }}%</div>\n            <div class=\"stat-label\">流量平衡度</div>\n            <div class=\"stat-trend\">{{ getBalanceTrend() }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport {\n  Connection, Grid, Top, Bottom, ArrowLeft as Back, Right\n} from '@element-plus/icons-vue'\nimport RealTimeFrameViewer from '@/components/analysis/RealTimeFrameViewer.vue'\nimport stompService from '@/utils/stomp-service'\n\nexport default {\n  name: 'FourWayRealtimeViewer',\n  components: {\n    Connection, Grid, Top, Bottom, Back, Right,\n    RealTimeFrameViewer\n  },\n  props: {\n    taskId: {\n      type: String,\n      required: true\n    },\n    autoStart: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['detection-update', 'status-change', 'analysis-complete'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const isConnected = ref(false)\n    const lastUpdateTime = ref('')\n    const frameSubscription = ref(null)\n    const showProgress = ref(true)\n    \n    // 方向查看器引用\n    const northViewer = ref(null)\n    const southViewer = ref(null)\n    const eastViewer = ref(null)\n    const westViewer = ref(null)\n\n    // 方向帧数据状态\n    const directionFrameData = reactive({\n      north: null,\n      south: null,\n      east: null,\n      west: null\n    })\n\n    // 方向统计数据\n    const directionStats = reactive({\n      north: { vehicleCount: 0, frameRate: '0 fps', status: 'waiting', lastUpdate: null, lastFrameTime: null },\n      south: { vehicleCount: 0, frameRate: '0 fps', status: 'waiting', lastUpdate: null, lastFrameTime: null },\n      east: { vehicleCount: 0, frameRate: '0 fps', status: 'waiting', lastUpdate: null, lastFrameTime: null },\n      west: { vehicleCount: 0, frameRate: '0 fps', status: 'waiting', lastUpdate: null, lastFrameTime: null }\n    })\n    \n    // 计算属性\n    const connectionStatusType = computed(() => {\n      return isConnected.value ? 'success' : 'danger'\n    })\n    \n    const connectionStatusText = computed(() => {\n      return isConnected.value ? '已连接' : '未连接'\n    })\n    \n    const totalVehicleCount = computed(() => {\n      return Object.values(directionStats).reduce((total, stats) => total + stats.vehicleCount, 0)\n    })\n    \n    const overallProgress = computed(() => {\n      const directions = Object.values(directionStats)\n      const activeDirections = directions.filter(d => d.status !== 'waiting')\n      if (activeDirections.length === 0) return 0\n      \n      const completedDirections = directions.filter(d => d.status === 'completed')\n      return Math.round((completedDirections.length / 4) * 100)\n    })\n    \n    const progressStatus = computed(() => {\n      if (overallProgress.value === 100) return 'success'\n      if (overallProgress.value > 0) return ''\n      return 'exception'\n    })\n    \n    // 方法\n    const getDirectionStatusType = (direction) => {\n      const status = directionStats[direction].status\n      const typeMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'error': 'danger'\n      }\n      return typeMap[status] || 'info'\n    }\n\n    const getDirectionStatusText = (direction) => {\n      const status = directionStats[direction].status\n      const textMap = {\n        'waiting': '等待中',\n        'processing': '检测中',\n        'completed': '已完成',\n        'error': '检测失败'\n      }\n      return textMap[status] || '未知'\n    }\n    \n    const formatImageData = (imageData) => {\n      if (!imageData) {\n        console.warn('🖼️ 图像数据为空')\n        return null\n      }\n\n      // 如果已经是完整的 data URL，直接返回\n      if (imageData.startsWith('data:image/')) {\n        return imageData\n      }\n\n      // 如果是 Base64 字符串，添加 data URL 前缀\n      if (typeof imageData === 'string' && imageData.length > 0) {\n        const formattedData = `data:image/jpeg;base64,${imageData}`\n        console.log('🖼️ 格式化图像数据:', {\n          originalLength: imageData.length,\n          formattedLength: formattedData.length,\n          prefix: formattedData.substring(0, 50) + '...'\n        })\n        return formattedData\n      }\n\n      console.warn('🖼️ 无效的图像数据格式:', typeof imageData, imageData ? imageData.substring(0, 50) : 'null')\n      return null\n    }\n\n    const handleFrameReceived = (direction, frameData) => {\n      try {\n        console.log(`🎬 处理${direction}方向帧数据:`, {\n          frameNumber: frameData.frameNumber,\n          detectionCount: frameData.detectionCount,\n          hasImageData: !!frameData.imageData,\n          imageDataLength: frameData.imageData ? frameData.imageData.length : 0\n        })\n\n        // 格式化图像数据\n        const formattedImageData = formatImageData(frameData.imageData)\n        if (!formattedImageData) {\n          console.error(`❌ ${direction}方向图像数据格式化失败`)\n          return\n        }\n\n        // 创建格式化的帧数据\n        const formattedFrameData = {\n          ...frameData,\n          imageData: formattedImageData,\n          direction: direction,\n          timestamp: new Date().toISOString()\n        }\n\n        // 更新方向帧数据\n        directionFrameData[direction] = formattedFrameData\n        console.log(`✅ ${direction}方向帧数据已更新`)\n\n        // 更新方向统计\n        if (frameData.detectionCount !== undefined) {\n          directionStats[direction].vehicleCount += frameData.detectionCount\n        }\n\n        directionStats[direction].status = 'processing'\n        directionStats[direction].lastUpdate = new Date()\n\n        // 更新最后更新时间\n        lastUpdateTime.value = new Date().toLocaleTimeString()\n\n        // 计算帧率（简化版本）\n        const now = Date.now()\n        if (directionStats[direction].lastFrameTime) {\n          const interval = now - directionStats[direction].lastFrameTime\n          const fps = Math.round(1000 / interval * 10) / 10\n          directionStats[direction].frameRate = `${fps} fps`\n        }\n        directionStats[direction].lastFrameTime = now\n\n        // 发出检测更新事件\n        emit('detection-update', {\n          direction,\n          frameData: formattedFrameData,\n          directionStats: directionStats[direction],\n          globalStats: {\n            totalVehicles: totalVehicleCount.value,\n            peakDirection: getPeakDirection(),\n            averageSpeed: getAverageFrameRate(),\n            processingTime: getProcessingTime()\n          }\n        })\n\n      } catch (error) {\n        console.error(`❌ 处理${direction}方向帧数据失败:`, error)\n        directionStats[direction].status = 'error'\n      }\n    }\n    \n    const getPeakDirection = () => {\n      let maxCount = 0\n      let peakDir = '-'\n      \n      Object.entries(directionStats).forEach(([dir, stats]) => {\n        if (stats.vehicleCount > maxCount) {\n          maxCount = stats.vehicleCount\n          peakDir = getDirectionName(dir)\n        }\n      })\n      \n      return peakDir\n    }\n    \n    const getDirectionName = (direction) => {\n      const names = {\n        north: '北向',\n        south: '南向',\n        east: '东向',\n        west: '西向'\n      }\n      return names[direction] || direction\n    }\n    \n    const getAverageFrameRate = () => {\n      const rates = Object.values(directionStats)\n        .map(stats => parseFloat(stats.frameRate))\n        .filter(rate => !isNaN(rate))\n\n      if (rates.length === 0) return 0\n\n      const average = rates.reduce((sum, rate) => sum + rate, 0) / rates.length\n      return Math.round(average * 10) / 10\n    }\n\n    // 新增的智能分析方法\n    const getRecentIncrease = () => {\n      // 模拟最近增长数据\n      return Math.floor(Math.random() * 5) + 1\n    }\n\n    const getPeakDirectionPercentage = () => {\n      const total = totalVehicleCount.value\n      if (total === 0) return 0\n\n      const maxCount = Math.max(...Object.values(directionStats).map(s => s.vehicleCount))\n      return Math.round((maxCount / total) * 100)\n    }\n\n    const getEfficiencyLevel = () => {\n      const avgRate = getAverageFrameRate()\n      if (avgRate >= 25) return '高效'\n      if (avgRate >= 15) return '正常'\n      if (avgRate >= 10) return '较慢'\n      return '低效'\n    }\n\n    const getCongestionLevel = () => {\n      const total = totalVehicleCount.value\n      if (total >= 50) return '严重拥堵'\n      if (total >= 30) return '中度拥堵'\n      if (total >= 15) return '轻度拥堵'\n      return '畅通'\n    }\n\n    const getCongestionTrend = () => {\n      const level = getCongestionLevel()\n      if (level === '严重拥堵') return '↗️ 恶化'\n      if (level === '中度拥堵') return '→ 稳定'\n      if (level === '轻度拥堵') return '↘️ 改善'\n      return '✅ 良好'\n    }\n\n    const getTrafficFlowBalance = () => {\n      const counts = Object.values(directionStats).map(s => s.vehicleCount)\n      const max = Math.max(...counts)\n      const min = Math.min(...counts)\n\n      if (max === 0) return 100\n\n      const balance = ((max - min) / max) * 100\n      return Math.round(100 - balance)\n    }\n\n    const getBalanceTrend = () => {\n      const balance = getTrafficFlowBalance()\n      if (balance >= 80) return '均衡'\n      if (balance >= 60) return '较均衡'\n      if (balance >= 40) return '不均衡'\n      return '严重不均衡'\n    }\n    \n    const getProcessingTime = () => {\n      const startTimes = Object.values(directionStats)\n        .map(stats => stats.lastUpdate)\n        .filter(time => time)\n      \n      if (startTimes.length === 0) return 0\n      \n      const earliest = Math.min(...startTimes.map(time => time.getTime()))\n      return Math.round((Date.now() - earliest) / 1000)\n    }\n    \n    const initializeWebSocketConnection = async () => {\n      try {\n        console.log(`初始化四方向WebSocket连接: ${props.taskId}`)\n        \n        // 订阅四方向帧数据\n        frameSubscription.value = await stompService.subscribeFourWayFrameUpdates(\n          props.taskId,\n          (frameData) => {\n            const direction = frameData.direction\n            if (direction && directionStats[direction] !== undefined) {\n              handleFrameReceived(direction, frameData)\n            }\n          }\n        )\n        \n        isConnected.value = true\n        ElMessage.success('四方向实时检测连接成功')\n        \n      } catch (error) {\n        console.error('WebSocket连接失败:', error)\n        ElMessage.error('连接失败: ' + error.message)\n        isConnected.value = false\n      }\n    }\n    \n    const cleanup = () => {\n      // 清理WebSocket订阅\n      if (frameSubscription.value) {\n        stompService.unsubscribe(frameSubscription.value)\n        frameSubscription.value = null\n      }\n      \n      // 清理各方向的帧缓冲\n      const directions = ['north', 'south', 'east', 'west']\n      directions.forEach(direction => {\n        const taskId = getDirectionTaskId(direction)\n        stompService.clearFrameBuffer(taskId)\n      })\n      \n      isConnected.value = false\n    }\n    \n    // 监听taskId变化\n    watch(() => props.taskId, (newTaskId) => {\n      if (newTaskId) {\n        cleanup()\n        initializeWebSocketConnection()\n      }\n    })\n    \n    // 生命周期\n    onMounted(() => {\n      if (props.taskId && props.autoStart) {\n        initializeWebSocketConnection()\n      }\n    })\n    \n    onUnmounted(() => {\n      cleanup()\n    })\n    \n    // 暴露方法给父组件\n    const startDetection = () => {\n      initializeWebSocketConnection()\n    }\n    \n    const stopDetection = () => {\n      cleanup()\n    }\n    \n    return {\n      // 响应式数据\n      isConnected,\n      lastUpdateTime,\n      showProgress,\n      northViewer,\n      southViewer,\n      eastViewer,\n      westViewer,\n      directionStats,\n      directionFrameData,\n\n      // 计算属性\n      connectionStatusType,\n      connectionStatusText,\n      totalVehicleCount,\n      overallProgress,\n      progressStatus,\n\n      // 方法\n      getDirectionStatusType,\n      getDirectionStatusText,\n      handleFrameReceived,\n      getPeakDirection,\n      getDirectionName,\n      getAverageFrameRate,\n      getProcessingTime,\n      getRecentIncrease,\n      getPeakDirectionPercentage,\n      getEfficiencyLevel,\n      getCongestionLevel,\n      getCongestionTrend,\n      getTrafficFlowBalance,\n      getBalanceTrend,\n      cleanup,\n      startDetection,\n      stopDetection\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-realtime-viewer {\n  background: #ffffff;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.connection-status {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding: 12px 16px;\n  background: #f8fafc;\n  border-radius: 8px;\n}\n\n.last-update {\n  font-size: 14px;\n  color: #6b7280;\n}\n\n.detection-grid {\n  display: grid !important;\n  grid-template-columns: 1fr 1fr !important;\n  grid-template-rows: 1fr 1fr !important;\n  gap: 24px !important;\n  margin-bottom: 24px;\n  min-height: 600px;\n}\n\n.detection-item {\n  background: #f9fafb;\n  border-radius: 8px;\n  padding: 16px;\n  border: 2px solid #e5e7eb;\n  transition: all 0.3s ease;\n}\n\n.detection-item:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);\n}\n\n.detection-item.north {\n  grid-column: 1 !important;\n  grid-row: 1 !important;\n}\n\n.detection-item.east {\n  grid-column: 2 !important;\n  grid-row: 1 !important;\n}\n\n.detection-item.west {\n  grid-column: 1 !important;\n  grid-row: 2 !important;\n}\n\n.detection-item.south {\n  grid-column: 2 !important;\n  grid-row: 2 !important;\n}\n\n\n\n.direction-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 12px;\n}\n\n.direction-label {\n  font-weight: 500;\n  color: #374151;\n}\n\n.detection-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.direction-viewer {\n  flex: 1;\n  min-height: 300px;\n  margin-bottom: 12px;\n}\n\n/* 总览统计样式 */\n.overview-stats {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 20px 0;\n  padding: 16px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  color: white;\n}\n\n.total-vehicles-summary {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.total-vehicles-summary .el-icon {\n  font-size: 24px;\n}\n\n.total-count {\n  font-size: 28px;\n  font-weight: 700;\n  line-height: 1;\n}\n\n.total-label {\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.peak-direction-info {\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.peak-text {\n  opacity: 0.95;\n}\n\n.direction-stats {\n  display: flex;\n  justify-content: space-around;\n  padding: 8px;\n  background: #ffffff;\n  border-radius: 6px;\n  border: 1px solid #e5e7eb;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  display: block;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.stat-label {\n  display: block;\n  font-size: 12px;\n  color: #6b7280;\n  margin-top: 2px;\n}\n\n.detection-progress {\n  margin-top: 16px;\n}\n\n.progress-text {\n  font-size: 14px;\n  font-weight: 500;\n}\n\n/* 增强统计信息样式 */\n.enhanced-global-stats {\n  margin-top: 24px;\n  background: #f8fafc;\n  border-radius: 12px;\n  padding: 20px;\n}\n\n.stats-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.stats-header h3 {\n  margin: 0;\n  color: #1f2937;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.connection-status-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n}\n\n.connection-status-info .el-icon {\n  font-size: 16px;\n}\n\n.connection-status-info .el-icon.connected {\n  color: #10b981;\n}\n\n.connection-status-info .el-icon.disconnected {\n  color: #ef4444;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 8px;\n  padding: 16px;\n  border-left: 4px solid #e5e7eb;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.stat-card.primary {\n  border-left-color: #3b82f6;\n}\n\n.stat-card.success {\n  border-left-color: #10b981;\n}\n\n.stat-card.warning {\n  border-left-color: #f59e0b;\n}\n\n.stat-card.info {\n  border-left-color: #06b6d4;\n}\n\n.stat-card.danger {\n  border-left-color: #ef4444;\n}\n\n.stat-card.purple {\n  border-left-color: #8b5cf6;\n}\n\n.stat-card {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.stat-icon {\n  font-size: 24px;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #f3f4f6;\n  border-radius: 8px;\n}\n\n.stat-content {\n  flex: 1;\n}\n\n.stat-value {\n  font-size: 20px;\n  font-weight: 700;\n  color: #1f2937;\n  line-height: 1.2;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #6b7280;\n  margin-top: 2px;\n}\n\n.stat-trend {\n  font-size: 11px;\n  font-weight: 500;\n  margin-top: 4px;\n  padding: 2px 6px;\n  border-radius: 4px;\n  background: #f3f4f6;\n  color: #6b7280;\n}\n\n.stat-trend.positive {\n  background: #dcfce7;\n  color: #16a34a;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .detection-grid {\n    grid-template-columns: 1fr;\n    grid-template-rows: auto;\n  }\n  \n  .detection-item {\n    grid-column: 1 !important;\n    grid-row: auto !important;\n  }\n}\n\n@media (max-width: 768px) {\n  .four-way-realtime-viewer {\n    padding: 16px;\n  }\n  \n  .detection-grid {\n    gap: 16px;\n  }\n  \n  .direction-viewer {\n    min-height: 150px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;EAE9BA,KAAK,EAAC;AAAmB;;EAKtBA,KAAK,EAAC;AAAa;;EAMtBA,KAAK,EAAC;AAAgB;;EAGpBA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAkB;;EAOxBA,KAAK,EAAC;AAAmB;;EAYvBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAY;;EAGrBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAY;;EAS3BA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAkB;;EAOxBA,KAAK,EAAC;AAAmB;;EAYvBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAY;;EAGrBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAY;;EAU3BA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAkB;;EAOxBA,KAAK,EAAC;AAAmB;;EAYvBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAY;;EAGrBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAY;;EAQ3BA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAkB;;EAOxBA,KAAK,EAAC;AAAmB;;EAYvBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAY;;EAGrBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAY;;EAS7BA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAwB;;EAE3BA,KAAK,EAAC;AAAa;;EAGtBA,KAAK,EAAC;AAAqB;;EACxBA,KAAK,EAAC;AAAW;;EApK/BC,GAAA;EAyK6BD,KAAK,EAAC;;;EAQnBA,KAAK,EAAC;AAAe;;EAM5BA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAwB;;EAWhCA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAElBA,KAAK,EAAC;AAAqB;;EAM/BA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAElBA,KAAK,EAAC;AAAY;;EAMtBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAMtBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAElBA,KAAK,EAAC;AAAY;;EAItBA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAElBA,KAAK,EAAC;AAAY;;EAItBA,KAAK,EAAC;AAAkB;;EAEtBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAElBA,KAAK,EAAC;AAAY;;;;;;;;;;;;uBA3PjCE,mBAAA,CAgQM,OAhQNC,UAgQM,GA/PJC,mBAAA,aAAgB,EAChBC,mBAAA,CAQM,OARNC,UAQM,GAPJC,YAAA,CAGSC,iBAAA;IAHAC,IAAI,EAAEC,MAAA,CAAAC,oBAAoB;IAAEC,IAAI,EAAC;;IAJhDC,OAAA,EAAAC,QAAA,CAKQ,MAAiC,CAAjCP,YAAA,CAAiCQ,kBAAA;MALzCF,OAAA,EAAAC,QAAA,CAKiB,MAAc,CAAdP,YAAA,CAAcS,qBAAA,E;MAL/BC,CAAA;QAAAC,gBAAA,CAKyC,GACjC,GAAAC,gBAAA,CAAGT,MAAA,CAAAU,oBAAoB,iB;IAN/BH,CAAA;+BAQMZ,mBAAA,CAEO,QAFPgB,UAEO,EAFmB,SAClB,GAAAF,gBAAA,CAAGT,MAAA,CAAAY,cAAc,0B,GAI3BlB,mBAAA,qBAAwB,EACxBC,mBAAA,CA4IM,OA5INkB,UA4IM,GA3IJnB,mBAAA,cAAiB,EACjBA,mBAAA,eAAkB,EAClBC,mBAAA,CA+BM,OA/BNmB,UA+BM,GA9BJnB,mBAAA,CAMM,OANNoB,UAMM,GALJlB,YAAA,CAA0BQ,kBAAA;IAnBpCF,OAAA,EAAAC,QAAA,CAmBmB,MAAO,CAAPP,YAAA,CAAOmB,cAAA,E;IAnB1BT,CAAA;gCAoBUZ,mBAAA,CAA+C;IAAzCL,KAAK,EAAC;EAAiB,GAAC,YAAU,sBACxCO,YAAA,CAESC,iBAAA;IAFAC,IAAI,EAAEC,MAAA,CAAAiB,sBAAsB;IAAWf,IAAI,EAAC;;IArB/DC,OAAA,EAAAC,QAAA,CAsBY,MAAqC,CAtBjDI,gBAAA,CAAAC,gBAAA,CAsBeT,MAAA,CAAAkB,sBAAsB,0B;IAtBrCX,CAAA;iCAyBQZ,mBAAA,CAsBM,OAtBNwB,UAsBM,GArBJtB,YAAA,CAUEuB,8BAAA;IATAC,GAAG,EAAC,aAAa;IACjBC,SAAS,EAAC,OAAO;IAChB,YAAU,EAAEtB,MAAA,CAAAuB,kBAAkB,CAACC,KAAK;IACpCC,MAAM,EAAEzB,MAAA,CAAA0B,cAAc,CAACF,KAAK,CAACC,MAAM;IACnCE,QAAQ,EAAE,CAAC;IACX,eAAa,EAAE,KAAK;IACpBC,cAAa,EAAAC,MAAA,QAAAA,MAAA,MAAGC,IAAI,IAAKC,OAAO,CAACC,GAAG,yBAAyBF,IAAI;IACjEG,YAAW,EAAAJ,MAAA,QAAAA,MAAA,MAAGC,IAAI,IAAKC,OAAO,CAACC,GAAG,uBAAuBF,IAAI;IAC9DxC,KAAK,EAAC;qDAERK,mBAAA,CASM,OATNuC,UASM,GARJvC,mBAAA,CAGM,OAHNwC,UAGM,GAFJxC,mBAAA,CAAuE,QAAvEyC,WAAuE,EAAA3B,gBAAA,CAA3CT,MAAA,CAAA0B,cAAc,CAACF,KAAK,CAACa,YAAY,kB,0BAC7D1C,mBAAA,CAAmC;IAA7BL,KAAK,EAAC;EAAY,GAAC,KAAG,qB,GAE9BK,mBAAA,CAGM,OAHN2C,WAGM,GAFJ3C,mBAAA,CAAoE,QAApE4C,WAAoE,EAAA9B,gBAAA,CAAxCT,MAAA,CAAA0B,cAAc,CAACF,KAAK,CAACgB,SAAS,kB,4BAC1D7C,mBAAA,CAAkC;IAA5BL,KAAK,EAAC;EAAY,GAAC,IAAE,qB,SAMnCI,mBAAA,cAAiB,EACjBA,mBAAA,eAAkB,EAClBC,mBAAA,CA+BM,OA/BN8C,WA+BM,GA9BJ9C,mBAAA,CAMM,OANN+C,WAMM,GALJ7C,YAAA,CAA2BQ,kBAAA;IAtDrCF,OAAA,EAAAC,QAAA,CAsDmB,MAAQ,CAARP,YAAA,CAAQ8C,eAAA,E;IAtD3BpC,CAAA;kCAuDUZ,mBAAA,CAA8C;IAAxCL,KAAK,EAAC;EAAiB,GAAC,WAAS,sBACvCO,YAAA,CAESC,iBAAA;IAFAC,IAAI,EAAEC,MAAA,CAAAiB,sBAAsB;IAAUf,IAAI,EAAC;;IAxD9DC,OAAA,EAAAC,QAAA,CAyDY,MAAoC,CAzDhDI,gBAAA,CAAAC,gBAAA,CAyDeT,MAAA,CAAAkB,sBAAsB,yB;IAzDrCX,CAAA;iCA4DQZ,mBAAA,CAsBM,OAtBNiD,WAsBM,GArBJ/C,YAAA,CAUEuB,8BAAA;IATAC,GAAG,EAAC,YAAY;IAChBC,SAAS,EAAC,MAAM;IACf,YAAU,EAAEtB,MAAA,CAAAuB,kBAAkB,CAACsB,IAAI;IACnCpB,MAAM,EAAEzB,MAAA,CAAA0B,cAAc,CAACmB,IAAI,CAACpB,MAAM;IAClCE,QAAQ,EAAE,CAAC;IACX,eAAa,EAAE,KAAK;IACpBC,cAAa,EAAAC,MAAA,QAAAA,MAAA,MAAGC,IAAI,IAAKC,OAAO,CAACC,GAAG,wBAAwBF,IAAI;IAChEG,YAAW,EAAAJ,MAAA,QAAAA,MAAA,MAAGC,IAAI,IAAKC,OAAO,CAACC,GAAG,sBAAsBF,IAAI;IAC7DxC,KAAK,EAAC;qDAERK,mBAAA,CASM,OATNmD,WASM,GARJnD,mBAAA,CAGM,OAHNoD,WAGM,GAFJpD,mBAAA,CAAsE,QAAtEqD,WAAsE,EAAAvC,gBAAA,CAA1CT,MAAA,CAAA0B,cAAc,CAACmB,IAAI,CAACR,YAAY,kB,4BAC5D1C,mBAAA,CAAmC;IAA7BL,KAAK,EAAC;EAAY,GAAC,KAAG,qB,GAE9BK,mBAAA,CAGM,OAHNsD,WAGM,GAFJtD,mBAAA,CAAmE,QAAnEuD,WAAmE,EAAAzC,gBAAA,CAAvCT,MAAA,CAAA0B,cAAc,CAACmB,IAAI,CAACL,SAAS,kB,4BACzD7C,mBAAA,CAAkC;IAA5BL,KAAK,EAAC;EAAY,GAAC,IAAE,qB,SAQnCI,mBAAA,eAAkB,EAClBC,mBAAA,CA+BM,OA/BNwD,WA+BM,GA9BJxD,mBAAA,CAMM,OANNyD,WAMM,GALJvD,YAAA,CAA4BQ,kBAAA;IA1FtCF,OAAA,EAAAC,QAAA,CA0FmB,MAAS,CAATP,YAAA,CAASwD,gBAAA,E;IA1F5B9C,CAAA;kCA2FUZ,mBAAA,CAA8C;IAAxCL,KAAK,EAAC;EAAiB,GAAC,WAAS,sBACvCO,YAAA,CAESC,iBAAA;IAFAC,IAAI,EAAEC,MAAA,CAAAiB,sBAAsB;IAAUf,IAAI,EAAC;;IA5F9DC,OAAA,EAAAC,QAAA,CA6FY,MAAoC,CA7FhDI,gBAAA,CAAAC,gBAAA,CA6FeT,MAAA,CAAAkB,sBAAsB,yB;IA7FrCX,CAAA;iCAgGQZ,mBAAA,CAsBM,OAtBN2D,WAsBM,GArBJzD,YAAA,CAUEuB,8BAAA;IATAC,GAAG,EAAC,YAAY;IAChBC,SAAS,EAAC,MAAM;IACf,YAAU,EAAEtB,MAAA,CAAAuB,kBAAkB,CAACgC,IAAI;IACnC9B,MAAM,EAAEzB,MAAA,CAAA0B,cAAc,CAAC6B,IAAI,CAAC9B,MAAM;IAClCE,QAAQ,EAAE,CAAC;IACX,eAAa,EAAE,KAAK;IACpBC,cAAa,EAAAC,MAAA,QAAAA,MAAA,MAAGC,IAAI,IAAKC,OAAO,CAACC,GAAG,wBAAwBF,IAAI;IAChEG,YAAW,EAAAJ,MAAA,QAAAA,MAAA,MAAGC,IAAI,IAAKC,OAAO,CAACC,GAAG,sBAAsBF,IAAI;IAC7DxC,KAAK,EAAC;qDAERK,mBAAA,CASM,OATN6D,WASM,GARJ7D,mBAAA,CAGM,OAHN8D,WAGM,GAFJ9D,mBAAA,CAAsE,QAAtE+D,WAAsE,EAAAjD,gBAAA,CAA1CT,MAAA,CAAA0B,cAAc,CAAC6B,IAAI,CAAClB,YAAY,kB,4BAC5D1C,mBAAA,CAAmC;IAA7BL,KAAK,EAAC;EAAY,GAAC,KAAG,qB,GAE9BK,mBAAA,CAGM,OAHNgE,WAGM,GAFJhE,mBAAA,CAAmE,QAAnEiE,WAAmE,EAAAnD,gBAAA,CAAvCT,MAAA,CAAA0B,cAAc,CAAC6B,IAAI,CAACf,SAAS,kB,4BACzD7C,mBAAA,CAAkC;IAA5BL,KAAK,EAAC;EAAY,GAAC,IAAE,qB,SAMnCI,mBAAA,eAAkB,EAClBC,mBAAA,CA+BM,OA/BNkE,WA+BM,GA9BJlE,mBAAA,CAMM,OANNmE,WAMM,GALJjE,YAAA,CAA6BQ,kBAAA;IA5HvCF,OAAA,EAAAC,QAAA,CA4HmB,MAAU,CAAVP,YAAA,CAAUkE,iBAAA,E;IA5H7BxD,CAAA;kCA6HUZ,mBAAA,CAA+C;IAAzCL,KAAK,EAAC;EAAiB,GAAC,YAAU,sBACxCO,YAAA,CAESC,iBAAA;IAFAC,IAAI,EAAEC,MAAA,CAAAiB,sBAAsB;IAAWf,IAAI,EAAC;;IA9H/DC,OAAA,EAAAC,QAAA,CA+HY,MAAqC,CA/HjDI,gBAAA,CAAAC,gBAAA,CA+HeT,MAAA,CAAAkB,sBAAsB,0B;IA/HrCX,CAAA;iCAkIQZ,mBAAA,CAsBM,OAtBNqE,WAsBM,GArBJnE,YAAA,CAUEuB,8BAAA;IATAC,GAAG,EAAC,aAAa;IACjBC,SAAS,EAAC,OAAO;IAChB,YAAU,EAAEtB,MAAA,CAAAuB,kBAAkB,CAAC0C,KAAK;IACpCxC,MAAM,EAAEzB,MAAA,CAAA0B,cAAc,CAACuC,KAAK,CAACxC,MAAM;IACnCE,QAAQ,EAAE,CAAC;IACX,eAAa,EAAE,KAAK;IACpBC,cAAa,EAAAC,MAAA,QAAAA,MAAA,MAAGC,IAAI,IAAKC,OAAO,CAACC,GAAG,yBAAyBF,IAAI;IACjEG,YAAW,EAAAJ,MAAA,QAAAA,MAAA,MAAGC,IAAI,IAAKC,OAAO,CAACC,GAAG,uBAAuBF,IAAI;IAC9DxC,KAAK,EAAC;qDAERK,mBAAA,CASM,OATNuE,WASM,GARJvE,mBAAA,CAGM,OAHNwE,WAGM,GAFJxE,mBAAA,CAAuE,QAAvEyE,WAAuE,EAAA3D,gBAAA,CAA3CT,MAAA,CAAA0B,cAAc,CAACuC,KAAK,CAAC5B,YAAY,kB,4BAC7D1C,mBAAA,CAAmC;IAA7BL,KAAK,EAAC;EAAY,GAAC,KAAG,qB,GAE9BK,mBAAA,CAGM,OAHN0E,WAGM,GAFJ1E,mBAAA,CAAoE,QAApE2E,WAAoE,EAAA7D,gBAAA,CAAxCT,MAAA,CAAA0B,cAAc,CAACuC,KAAK,CAACzB,SAAS,kB,4BAC1D7C,mBAAA,CAAkC;IAA5BL,KAAK,EAAC;EAAY,GAAC,IAAE,qB,WAOrCI,mBAAA,UAAa,EACbC,mBAAA,CASM,OATN4E,WASM,GARJ5E,mBAAA,CAIM,OAJN6E,WAIM,GAHJ3E,YAAA,CAA2BQ,kBAAA;IA/JnCF,OAAA,EAAAC,QAAA,CA+JiB,MAAQ,CAARP,YAAA,CAAQ4E,eAAA,E;IA/JzBlE,CAAA;MAgKQZ,mBAAA,CAAwD,QAAxD+E,WAAwD,EAAAjE,gBAAA,CAA3BT,MAAA,CAAA2E,iBAAiB,kB,4BAC9ChF,mBAAA,CAAqC;IAA/BL,KAAK,EAAC;EAAa,GAAC,MAAI,qB,GAEhCK,mBAAA,CAEM,OAFNiF,WAEM,GADJjF,mBAAA,CAA6D,QAA7DkF,WAA6D,EAArC,QAAM,GAAApE,gBAAA,CAAGT,MAAA,CAAA8E,gBAAgB,mB,KAIrDpF,mBAAA,WAAc,EACHM,MAAA,CAAA+E,YAAY,I,cAAvBvF,mBAAA,CAWM,OAXNwF,WAWM,GAVJnF,YAAA,CAScoF,sBAAA;IARXC,UAAU,EAAElF,MAAA,CAAAmF,eAAe;IAC3B1D,MAAM,EAAEzB,MAAA,CAAAoF,cAAc;IACtB,cAAY,EAAE,CAAC;IACf,WAAS,EAAE;;IAEDjF,OAAO,EAAAC,QAAA,CAChB,CAAyD;MADrC8E;IAAU,OAC9BvF,mBAAA,CAAyD,QAAzD0F,WAAyD,EAAA5E,gBAAA,CAA1ByE,UAAU,IAAG,QAAM,gB;IAjL5D3E,CAAA;mDAAAb,mBAAA,gBAsLIA,mBAAA,eAAkB,EAClBC,mBAAA,CAyEM,OAzEN2F,WAyEM,GAxEJ3F,mBAAA,CAWM,OAXN4F,WAWM,G,4BAVJ5F,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CAQM,OARN6F,WAQM,GAPJ3F,YAAA,CAEUQ,kBAAA;IAFAf,KAAK,EA3LzBmG,eAAA;MAAA,aA2L0CzF,MAAA,CAAA0F,WAAW;MAAA,iBAAmB1F,MAAA,CAAA0F;IAAW;;IA3LnFvF,OAAA,EAAAC,QAAA,CA4LY,MAAc,CAAdP,YAAA,CAAcS,qBAAA,E;IA5L1BC,CAAA;gCA8LUZ,mBAAA,CAA8C,cAAAc,gBAAA,CAArCT,MAAA,CAAA0F,WAAW,kCACN1F,MAAA,CAAAY,cAAc,I,cAA5B+E,YAAA,CAES7F,iBAAA;IAjMnBP,GAAA;IA+LwCW,IAAI,EAAC,OAAO;IAACH,IAAI,EAAC;;IA/L1DI,OAAA,EAAAC,QAAA,CAgMY,MAAoB,CAhMhCI,gBAAA,CAAAC,gBAAA,CAgMeT,MAAA,CAAAY,cAAc,iB;IAhM7BL,CAAA;QAAAb,mBAAA,e,KAqMMC,mBAAA,CA0DM,OA1DNiG,WA0DM,GAzDJjG,mBAAA,CASM,OATNkG,WASM,G,4BARJlG,mBAAA,CAA+B;IAA1BL,KAAK,EAAC;EAAW,GAAC,IAAE,sBACzBK,mBAAA,CAMM,OANNmG,WAMM,GALJnG,mBAAA,CAAqD,OAArDoG,WAAqD,EAAAtF,gBAAA,CAA1BT,MAAA,CAAA2E,iBAAiB,kB,4BAC5ChF,mBAAA,CAAkC;IAA7BL,KAAK,EAAC;EAAY,GAAC,MAAI,sBAC5BK,mBAAA,CAEM,OAFNqG,WAEM,EAF2B,IAC9B,GAAAvF,gBAAA,CAAGT,MAAA,CAAAiG,iBAAiB,mB,KAK3BtG,mBAAA,CASM,OATNuG,WASM,G,4BARJvG,mBAAA,CAA+B;IAA1BL,KAAK,EAAC;EAAW,GAAC,IAAE,sBACzBK,mBAAA,CAMM,OANNwG,WAMM,GALJxG,mBAAA,CAAsD,OAAtDyG,WAAsD,EAAA3F,gBAAA,CAA3BT,MAAA,CAAA8E,gBAAgB,oB,4BAC3CnF,mBAAA,CAAmC;IAA9BL,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC7BK,mBAAA,CAEM,OAFN0G,WAEM,EAAA5F,gBAAA,CADDT,MAAA,CAAAsG,0BAA0B,MAAK,IACpC,gB,KAIJ3G,mBAAA,CAOM,OAPN4G,WAOM,G,4BANJ5G,mBAAA,CAA8B;IAAzBL,KAAK,EAAC;EAAW,GAAC,GAAC,sBACxBK,mBAAA,CAIM,OAJN6G,WAIM,GAHJ7G,mBAAA,CAAyD,OAAzD8G,WAAyD,EAAAhG,gBAAA,CAA9BT,MAAA,CAAA0G,mBAAmB,oB,4BAC9C/G,mBAAA,CAAkC;IAA7BL,KAAK,EAAC;EAAY,GAAC,MAAI,sB,4BAC5BK,mBAAA,CAAgC;IAA3BL,KAAK,EAAC;EAAY,GAAC,IAAE,qB,KAI9BK,mBAAA,CAOM,OAPNgH,WAOM,G,4BANJhH,mBAAA,CAA+B;IAA1BL,KAAK,EAAC;EAAW,GAAC,IAAE,sBACzBK,mBAAA,CAIM,OAJNiH,WAIM,GAHJjH,mBAAA,CAAwD,OAAxDkH,WAAwD,EAAApG,gBAAA,CAA7BT,MAAA,CAAA8G,iBAAiB,MAAK,GAAC,iB,4BAClDnH,mBAAA,CAAkC;IAA7BL,KAAK,EAAC;EAAY,GAAC,MAAI,sBAC5BK,mBAAA,CAAwD,OAAxDoH,WAAwD,EAAAtG,gBAAA,CAA7BT,MAAA,CAAAgH,kBAAkB,mB,KAIjDrH,mBAAA,CAOM,OAPNsH,WAOM,G,4BANJtH,mBAAA,CAA+B;IAA1BL,KAAK,EAAC;EAAW,GAAC,IAAE,sBACzBK,mBAAA,CAIM,OAJNuH,WAIM,GAHJvH,mBAAA,CAAwD,OAAxDwH,WAAwD,EAAA1G,gBAAA,CAA7BT,MAAA,CAAAoH,kBAAkB,oB,4BAC7CzH,mBAAA,CAAkC;IAA7BL,KAAK,EAAC;EAAY,GAAC,MAAI,sBAC5BK,mBAAA,CAAwD,OAAxD0H,WAAwD,EAAA5G,gBAAA,CAA7BT,MAAA,CAAAsH,kBAAkB,mB,KAIjD3H,mBAAA,CAOM,OAPN4H,WAOM,G,4BANJ5H,mBAAA,CAA+B;IAA1BL,KAAK,EAAC;EAAW,GAAC,IAAE,sBACzBK,mBAAA,CAIM,OAJN6H,WAIM,GAHJ7H,mBAAA,CAA4D,OAA5D8H,WAA4D,EAAAhH,gBAAA,CAAjCT,MAAA,CAAA0H,qBAAqB,MAAK,GAAC,iB,4BACtD/H,mBAAA,CAAmC;IAA9BL,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC7BK,mBAAA,CAAqD,OAArDgI,WAAqD,EAAAlH,gBAAA,CAA1BT,MAAA,CAAA4H,eAAe,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}