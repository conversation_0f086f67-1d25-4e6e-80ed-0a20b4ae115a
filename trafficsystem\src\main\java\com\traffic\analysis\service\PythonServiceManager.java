package com.traffic.analysis.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.concurrent.TimeUnit;

/**
 * Python服务管理器
 * 负责启动、停止和监控Python模型服务
 */
@Service
public class PythonServiceManager {

    private static final Logger logger = LoggerFactory.getLogger(PythonServiceManager.class);

    @Value("${python.executable:python}")
    private String pythonExecutable;

    @Value("${python.service.auto-start:true}")
    private boolean autoStart;

    @Value("${python.service.independent-control:true}")
    private boolean independentControl;

    @Value("${python.service.model-api.enabled:true}")
    private boolean modelApiEnabled;

    @Value("${python.service.model-api.script:python_model/model_api.py}")
    private String modelApiScript;

    @Value("${python.service.model-api.port:5001}")
    private int modelApiPort;

    // 四方向并行处理服务配置
    @Value("${python.service.four-way-parallel.enabled:false}")
    private boolean fourWayParallelEnabled;

    @Value("${python.service.model-api-south.enabled:false}")
    private boolean modelApiSouthEnabled;

    @Value("${python.service.model-api-south.script:python_model/model_api.py}")
    private String modelApiSouthScript;

    @Value("${python.service.model-api-south.port:5002}")
    private int modelApiSouthPort;

    @Value("${python.service.model-api-west.enabled:false}")
    private boolean modelApiWestEnabled;

    @Value("${python.service.model-api-west.script:python_model/model_api.py}")
    private String modelApiWestScript;

    @Value("${python.service.model-api-west.port:5003}")
    private int modelApiWestPort;

    @Value("${python.service.model-api-north.enabled:false}")
    private boolean modelApiNorthEnabled;

    @Value("${python.service.model-api-north.script:python_model/model_api.py}")
    private String modelApiNorthScript;

    @Value("${python.service.model-api-north.port:5004}")
    private int modelApiNorthPort;

    @Value("${python.service.api-controller.enabled:true}")
    private boolean apiControllerEnabled;

    @Value("${python.service.api-controller.script:python_model/api_controller.py}")
    private String apiControllerScript;

    @Value("${python.service.api-controller.port:5000}")
    private int apiControllerPort;

    @Value("${python.service.startup-delay:3000}")
    private long startupDelay;

    @Value("${python.service.health-check-timeout:30000}")
    private long healthCheckTimeout;

    @Value("${python.service.max-startup-attempts:3}")
    private int maxStartupAttempts;

    private final List<Process> runningProcesses = new ArrayList<>();
    private final RestTemplate restTemplate = new RestTemplate();

    // 存储特定服务的进程映射
    private Process modelApiProcess = null;
    private Process apiControllerProcess = null;

    // 四方向并行处理服务进程
    private Process modelApiSouthProcess = null;
    private Process modelApiWestProcess = null;
    private Process modelApiNorthProcess = null;

    /**
     * 启动所有Python服务
     */
    public void startAllServices() {
        if (!autoStart) {
            logger.info("Python服务自动启动已禁用");
            return;
        }

        logger.info("开始启动Python服务...");

        try {
            // 启动主模型API服务（处理east方向）
            if (modelApiEnabled) {
                startModelApiService();
            }

            // 启动四方向并行处理服务
            if (fourWayParallelEnabled) {
                logger.info("启动四方向并行处理服务...");

                if (modelApiSouthEnabled) {
                    startModelApiSouthService();
                }

                if (modelApiWestEnabled) {
                    startModelApiWestService();
                }

                if (modelApiNorthEnabled) {
                    startModelApiNorthService();
                }
            }

            // 等待一段时间让模型服务启动
            if (modelApiEnabled && apiControllerEnabled) {
                logger.info("等待{}毫秒让模型服务启动...", startupDelay);
                Thread.sleep(startupDelay);
            }

            // 启动API控制器服务
            if (apiControllerEnabled) {
                startApiControllerService();
            }

            logger.info("Python服务启动完成");
        } catch (Exception e) {
            logger.error("启动Python服务时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 启动模型API服务
     */
    private void startModelApiService() {
        logger.info("启动模型API服务: {}", modelApiScript);
        
        for (int attempt = 1; attempt <= maxStartupAttempts; attempt++) {
            try {
                Process process = startPythonScript(modelApiScript, "模型API服务");
                if (process != null) {
                    runningProcesses.add(process);
                    modelApiProcess = process; // 记录模型API进程

                    // 简单等待服务启动
                    logger.info("模型API服务进程已启动，端口: {}", modelApiPort);

                    return; // 启动成功，退出重试循环
                }
            } catch (Exception e) {
                logger.warn("第{}次启动模型API服务失败: {}", attempt, e.getMessage());
                if (attempt == maxStartupAttempts) {
                    logger.error("模型API服务启动失败，已达到最大重试次数");
                } else {
                    try {
                        Thread.sleep(2000); // 等待2秒后重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 启动API控制器服务
     */
    private void startApiControllerService() {
        logger.info("启动API控制器服务: {}", apiControllerScript);
        
        for (int attempt = 1; attempt <= maxStartupAttempts; attempt++) {
            try {
                Process process = startPythonScript(apiControllerScript, "API控制器服务");
                if (process != null) {
                    runningProcesses.add(process);
                    apiControllerProcess = process; // 记录API控制器进程

                    // 简单等待服务启动
                    logger.info("API控制器服务进程已启动，端口: {}", apiControllerPort);

                    return; // 启动成功，退出重试循环
                }
            } catch (Exception e) {
                logger.warn("第{}次启动API控制器服务失败: {}", attempt, e.getMessage());
                if (attempt == maxStartupAttempts) {
                    logger.error("API控制器服务启动失败，已达到最大重试次数");
                } else {
                    try {
                        Thread.sleep(2000); // 等待2秒后重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 启动Python脚本
     */
    private Process startPythonScript(String scriptPath, String serviceName) throws IOException {
        File scriptFile = new File(scriptPath);
        if (!scriptFile.exists()) {
            logger.error("Python脚本不存在: {}", scriptFile.getAbsolutePath());
            return null;
        }

        File workingDir = scriptFile.getParentFile();
        String scriptName = scriptFile.getName();

        ProcessBuilder processBuilder = new ProcessBuilder(pythonExecutable, scriptName);
        processBuilder.directory(workingDir);
        processBuilder.redirectErrorStream(true);

        logger.info("启动{}，工作目录: {}, 命令: {} {}", serviceName, workingDir.getAbsolutePath(), pythonExecutable, scriptName);

        Process process = processBuilder.start();

        // 启动线程读取进程输出
        Thread outputReader = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("[{}] {}", serviceName, line);
                }
            } catch (IOException e) {
                logger.warn("读取{}输出时出错: {}", serviceName, e.getMessage());
            }
        });
        outputReader.setDaemon(true);
        outputReader.setName(serviceName + "-OutputReader");
        outputReader.start();

        return process;
    }

    /**
     * 检查服务健康状态
     */
    private boolean checkServiceHealth(String healthUrl) {
        try {
            long startTime = System.currentTimeMillis();
            while (System.currentTimeMillis() - startTime < healthCheckTimeout) {
                try {
                    String response = restTemplate.getForObject(healthUrl, String.class);
                    logger.debug("健康检查响应: {}", response);
                    // 只要能成功获取响应就认为服务健康
                    if (response != null) {
                        return true;
                    }
                } catch (Exception e) {
                    logger.debug("健康检查失败: {}", e.getMessage());
                    // 服务还未启动，继续等待
                }
                Thread.sleep(1000); // 每秒检查一次
            }
        } catch (Exception e) {
            logger.warn("健康检查出错: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 停止所有Python服务
     */
    @PreDestroy
    public void stopAllServices() {
        logger.info("正在停止Python服务...");
        
        for (Process process : runningProcesses) {
            try {
                if (process.isAlive()) {
                    logger.info("停止Python进程: {}", process.pid());
                    process.destroy();
                    
                    // 等待进程优雅退出
                    if (!process.waitFor(10, TimeUnit.SECONDS)) {
                        logger.warn("强制终止Python进程: {}", process.pid());
                        process.destroyForcibly();
                    }
                }
            } catch (Exception e) {
                logger.warn("停止Python进程时出错: {}", e.getMessage());
            }
        }
        
        runningProcesses.clear();
        modelApiProcess = null;
        apiControllerProcess = null;
        logger.info("Python服务已停止");
    }

    /**
     * 获取服务状态
     */
    public boolean isServiceRunning(String serviceUrl) {
        try {
            String response = restTemplate.getForObject(serviceUrl + "/health", String.class);
            logger.debug("服务状态检查: {} -> {}", serviceUrl, response);
            // 只要能成功获取响应就认为服务运行中
            return response != null;
        } catch (Exception e) {
            logger.debug("服务状态检查失败: {} -> {}", serviceUrl, e.getMessage());
            return false;
        }
    }

    /**
     * 获取模型API服务状态
     */
    public boolean isModelApiRunning() {
        return isServiceRunning("http://localhost:" + modelApiPort);
    }

    /**
     * 获取API控制器服务状态
     */
    public boolean isApiControllerRunning() {
        return isServiceRunning("http://localhost:" + apiControllerPort);
    }

    /**
     * 获取South方向模型API服务状态
     */
    public boolean isModelApiSouthRunning() {
        return isServiceRunning("http://localhost:" + modelApiSouthPort);
    }

    /**
     * 获取West方向模型API服务状态
     */
    public boolean isModelApiWestRunning() {
        return isServiceRunning("http://localhost:" + modelApiWestPort);
    }

    /**
     * 获取North方向模型API服务状态
     */
    public boolean isModelApiNorthRunning() {
        return isServiceRunning("http://localhost:" + modelApiNorthPort);
    }

    /**
     * 获取所有四方向服务状态
     */
    public Map<String, Boolean> getFourWayServicesStatus() {
        Map<String, Boolean> status = new HashMap<>();
        status.put("east", isModelApiRunning());  // East方向使用主模型API服务
        status.put("south", isModelApiSouthRunning());
        status.put("west", isModelApiWestRunning());
        status.put("north", isModelApiNorthRunning());
        return status;
    }

    /**
     * 公开方法：启动模型API服务
     */
    public boolean startModelApiServicePublic() {
        if (!modelApiEnabled) {
            logger.warn("模型API服务已禁用");
            return false;
        }

        logger.info("公开启动模型API服务");
        try {
            startModelApiService();
            return true;
        } catch (Exception e) {
            logger.error("启动模型API服务失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 公开方法：启动API控制器服务
     */
    public boolean startApiControllerServicePublic() {
        if (!apiControllerEnabled) {
            logger.warn("API控制器服务已禁用");
            return false;
        }

        logger.info("公开启动API控制器服务");
        try {
            startApiControllerService();
            return true;
        } catch (Exception e) {
            logger.error("启动API控制器服务失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 重启所有服务
     */
    public void restartAllServices() {
        logger.info("重启所有Python服务");
        stopAllServices();
        try {
            Thread.sleep(3000); // 等待3秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        startAllServices();
    }

    /**
     * 独立启动模型API服务
     */
    public boolean startModelApiServiceIndependent() {
        if (!independentControl) {
            logger.warn("独立控制模式已禁用，无法独立启动模型API服务");
            return false;
        }

        logger.info("独立启动模型API服务");
        try {
            // 检查服务是否已经运行
            if (isModelApiRunning()) {
                logger.info("模型API服务已在运行");
                return true;
            }

            startModelApiService();

            // 等待服务启动
            Thread.sleep(3000);

            boolean success = isModelApiRunning();
            logger.info("模型API服务独立启动{}", success ? "成功" : "失败");
            return success;

        } catch (Exception e) {
            logger.error("独立启动模型API服务失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 独立停止模型API服务
     */
    public boolean stopModelApiServiceIndependent() {
        if (!independentControl) {
            logger.warn("独立控制模式已禁用，无法独立停止模型API服务");
            return false;
        }

        logger.info("独立停止模型API服务");
        try {
            // 停止所有与模型API相关的进程
            boolean processKilled = false;

            // 首先停止记录的主进程
            if (modelApiProcess != null && modelApiProcess.isAlive()) {
                logger.info("停止模型API主进程: {}", modelApiProcess.pid());
                modelApiProcess.destroy();

                if (!modelApiProcess.waitFor(5, TimeUnit.SECONDS)) {
                    logger.warn("强制终止模型API主进程: {}", modelApiProcess.pid());
                    modelApiProcess.destroyForcibly();
                }

                runningProcesses.remove(modelApiProcess);
                modelApiProcess = null;
                processKilled = true;
                logger.info("模型API主进程已停止");
            }

            // 停止所有可能的子进程（Flask开发模式会启动多个进程）
            try {
                // 使用系统命令停止监听5001端口的所有进程
                if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                    ProcessBuilder pb = new ProcessBuilder("cmd", "/c",
                        "for /f \"tokens=5\" %a in ('netstat -aon ^| findstr :5001') do taskkill /f /pid %a");
                    Process killProcess = pb.start();
                    killProcess.waitFor(5, TimeUnit.SECONDS);
                    logger.info("已执行Windows端口清理命令");
                } else {
                    ProcessBuilder pb = new ProcessBuilder("bash", "-c",
                        "lsof -ti:5001 | xargs kill -9");
                    Process killProcess = pb.start();
                    killProcess.waitFor(5, TimeUnit.SECONDS);
                    logger.info("已执行Linux端口清理命令");
                }
                processKilled = true;
            } catch (Exception e) {
                logger.warn("执行端口清理命令失败: {}", e.getMessage());
            }

            if (!processKilled) {
                logger.info("未找到运行中的模型API进程");
            }

            // 等待服务完全停止，使用重试机制检查状态
            boolean success = false;
            for (int i = 0; i < 5; i++) {
                Thread.sleep(1000);
                if (!isModelApiRunning()) {
                    success = true;
                    break;
                }
                logger.debug("第{}次检查模型API服务状态，仍在运行", i + 1);
            }

            logger.info("模型API服务独立停止{}", success ? "成功" : "失败");
            return success;

        } catch (Exception e) {
            logger.error("独立停止模型API服务失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 独立启动API控制器服务
     */
    public boolean startApiControllerServiceIndependent() {
        if (!independentControl) {
            logger.warn("独立控制模式已禁用，无法独立启动API控制器服务");
            return false;
        }

        logger.info("独立启动API控制器服务");
        try {
            // 检查服务是否已经运行
            if (isApiControllerRunning()) {
                logger.info("API控制器服务已在运行");
                return true;
            }

            startApiControllerService();

            // 等待服务启动
            Thread.sleep(3000);

            boolean success = isApiControllerRunning();
            logger.info("API控制器服务独立启动{}", success ? "成功" : "失败");
            return success;

        } catch (Exception e) {
            logger.error("独立启动API控制器服务失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 独立停止API控制器服务
     */
    public boolean stopApiControllerServiceIndependent() {
        if (!independentControl) {
            logger.warn("独立控制模式已禁用，无法独立停止API控制器服务");
            return false;
        }

        logger.info("独立停止API控制器服务");
        try {
            // 检查API控制器进程是否存在并停止
            if (apiControllerProcess != null && apiControllerProcess.isAlive()) {
                logger.info("停止API控制器进程: {}", apiControllerProcess.pid());
                apiControllerProcess.destroy();

                if (!apiControllerProcess.waitFor(10, TimeUnit.SECONDS)) {
                    logger.warn("强制终止API控制器进程: {}", apiControllerProcess.pid());
                    apiControllerProcess.destroyForcibly();
                }

                // 从运行进程列表中移除
                runningProcesses.remove(apiControllerProcess);
                apiControllerProcess = null;

                logger.info("API控制器进程已停止");
            } else {
                logger.info("API控制器进程未运行或已停止");
            }

            // 等待服务完全停止，使用重试机制检查状态
            boolean success = false;
            for (int i = 0; i < 5; i++) {
                Thread.sleep(1000);
                if (!isApiControllerRunning()) {
                    success = true;
                    break;
                }
                logger.debug("第{}次检查API控制器服务状态，仍在运行", i + 1);
            }

            logger.info("API控制器服务独立停止{}", success ? "成功" : "失败");
            return success;

        } catch (Exception e) {
            logger.error("独立停止API控制器服务失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 启动South方向模型API服务
     */
    private void startModelApiSouthService() {
        logger.info("启动South方向模型API服务: {}", modelApiSouthScript);

        for (int attempt = 1; attempt <= maxStartupAttempts; attempt++) {
            try {
                Process process = startPythonScriptWithPort(modelApiSouthScript, "South方向模型API服务", modelApiSouthPort);
                if (process != null) {
                    runningProcesses.add(process);
                    modelApiSouthProcess = process;
                    logger.info("South方向模型API服务进程已启动，端口: {}", modelApiSouthPort);
                    return;
                }
            } catch (Exception e) {
                logger.warn("第{}次启动South方向模型API服务失败: {}", attempt, e.getMessage());
                if (attempt == maxStartupAttempts) {
                    logger.error("South方向模型API服务启动失败，已达到最大重试次数");
                } else {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 启动West方向模型API服务
     */
    private void startModelApiWestService() {
        logger.info("启动West方向模型API服务: {}", modelApiWestScript);

        for (int attempt = 1; attempt <= maxStartupAttempts; attempt++) {
            try {
                Process process = startPythonScriptWithPort(modelApiWestScript, "West方向模型API服务", modelApiWestPort);
                if (process != null) {
                    runningProcesses.add(process);
                    modelApiWestProcess = process;
                    logger.info("West方向模型API服务进程已启动，端口: {}", modelApiWestPort);
                    return;
                }
            } catch (Exception e) {
                logger.warn("第{}次启动West方向模型API服务失败: {}", attempt, e.getMessage());
                if (attempt == maxStartupAttempts) {
                    logger.error("West方向模型API服务启动失败，已达到最大重试次数");
                } else {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 启动North方向模型API服务
     */
    private void startModelApiNorthService() {
        logger.info("启动North方向模型API服务: {}", modelApiNorthScript);

        for (int attempt = 1; attempt <= maxStartupAttempts; attempt++) {
            try {
                Process process = startPythonScriptWithPort(modelApiNorthScript, "North方向模型API服务", modelApiNorthPort);
                if (process != null) {
                    runningProcesses.add(process);
                    modelApiNorthProcess = process;
                    logger.info("North方向模型API服务进程已启动，端口: {}", modelApiNorthPort);
                    return;
                }
            } catch (Exception e) {
                logger.warn("第{}次启动North方向模型API服务失败: {}", attempt, e.getMessage());
                if (attempt == maxStartupAttempts) {
                    logger.error("North方向模型API服务启动失败，已达到最大重试次数");
                } else {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 启动Python脚本并指定端口
     */
    private Process startPythonScriptWithPort(String scriptPath, String serviceName, int port) throws IOException {
        logger.info("启动Python脚本: {} (端口: {})", scriptPath, port);

        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command(pythonExecutable, scriptPath);

        // 设置环境变量指定端口
        processBuilder.environment().put("FLASK_PORT", String.valueOf(port));
        processBuilder.environment().put("PORT", String.valueOf(port));

        processBuilder.redirectErrorStream(true);

        Process process = processBuilder.start();

        // 启动输出读取线程
        Thread outputThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("[{}] {}", serviceName, line);
                }
            } catch (IOException e) {
                logger.debug("读取{}输出时出错: {}", serviceName, e.getMessage());
            }
        });
        outputThread.setDaemon(true);
        outputThread.setName(serviceName + "-OutputReader");
        outputThread.start();

        logger.info("{}进程已启动，PID: {}", serviceName, process.pid());
        return process;
    }

    /**
     * 检查是否支持独立控制
     */
    public boolean isIndependentControlEnabled() {
        return independentControl;
    }
}
