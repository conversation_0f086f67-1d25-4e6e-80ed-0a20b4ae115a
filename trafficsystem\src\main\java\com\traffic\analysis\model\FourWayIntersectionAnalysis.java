package com.traffic.analysis.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * 四方向十字路口交通分析数据模型
 * 对应MongoDB的intersection_analysis_four_way集合
 */
@Data
@Document(collection = "intersection_analysis_four_way")
public class FourWayIntersectionAnalysis {
    
    @Id
    private String id;
    
    @Field("task_id")
    private String taskId;
    
    @Field("user_id")
    private String userId;
    
    @Field("username")
    private String username;
    
    @Field("role")
    private String role;
    
    @Field("status")
    private String status; // queued, processing, completed, failed
    
    @Field("progress")
    private int progress; // 0-100
    
    @Field("created_at")
    private LocalDateTime createdAt;
    
    @Field("updated_at")
    private LocalDateTime updatedAt;
    
    @Field("processing_start_time")
    private LocalDateTime processingStartTime;
    
    @Field("processing_end_time")
    private LocalDateTime processingEndTime;
    
    /**
     * 四个方向的视频数据
     */
    @Field("directions")
    private Map<Direction, DirectionVideoData> directions = new HashMap<>();
    
    /**
     * 智能交通分析结果
     */
    @Field("traffic_analysis")
    private TrafficAnalysisResult trafficAnalysis;
    
    /**
     * 报告数据
     */
    @Field("report_data")
    private ReportData reportData;
    
    /**
     * 错误信息
     */
    @Field("error_message")
    private String errorMessage;
    
    /**
     * 处理消息
     */
    @Field("message")
    private String message;
    
    /**
     * 分析类型标识
     */
    @Field("analysis_type")
    private String analysisType = "four_way_intersection";
    
    /**
     * 报告数据内部类
     */
    @Data
    public static class ReportData {
        /**
         * 分析摘要
         */
        private String summary;
        
        /**
         * 改进建议列表
         */
        private List<String> recommendations = new ArrayList<>();
        
        /**
         * 图表数据
         */
        private Map<String, Object> chartsData = new HashMap<>();
        
        /**
         * 报告生成时间
         */
        private LocalDateTime generatedAt;
        
        /**
         * 报告版本
         */
        private String version = "1.0";
        
        /**
         * 关键指标
         */
        private Map<String, Object> keyMetrics = new HashMap<>();
    }
    
    /**
     * 获取指定方向的视频数据
     */
    public DirectionVideoData getDirectionData(Direction direction) {
        return directions.get(direction);
    }
    
    /**
     * 设置指定方向的视频数据
     */
    public void setDirectionData(Direction direction, DirectionVideoData data) {
        if (directions == null) {
            directions = new HashMap<>();
        }
        directions.put(direction, data);
    }
    
    /**
     * 获取总车辆数量
     */
    public int getTotalVehicleCount() {
        return directions.values().stream()
                .mapToInt(DirectionVideoData::getVehicleCount)
                .sum();
    }
    
    /**
     * 获取完成的方向数量
     */
    public int getCompletedDirectionsCount() {
        return (int) directions.values().stream()
                .filter(data -> "completed".equals(data.getStatus()))
                .count();
    }
    
    /**
     * 检查是否所有方向都已完成处理
     */
    public boolean isAllDirectionsCompleted() {
        return directions.size() == 4 && 
               directions.values().stream()
                   .allMatch(data -> "completed".equals(data.getStatus()));
    }
    
    /**
     * 检查是否有任何方向处理失败
     */
    public boolean hasAnyDirectionFailed() {
        return directions.values().stream()
                .anyMatch(data -> "failed".equals(data.getStatus()));
    }
    
    /**
     * 计算整体处理进度
     */
    public void calculateOverallProgress() {
        if (directions.isEmpty()) {
            this.progress = 0;
            return;
        }
        
        int totalProgress = directions.values().stream()
                .mapToInt(DirectionVideoData::getProgress)
                .sum();
        
        this.progress = totalProgress / directions.size();
    }
    
    /**
     * 更新状态和时间戳
     */
    public void updateStatus(String newStatus) {
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();
        
        if ("processing".equals(newStatus) && processingStartTime == null) {
            this.processingStartTime = LocalDateTime.now();
        } else if (("completed".equals(newStatus) || "failed".equals(newStatus)) 
                   && processingEndTime == null) {
            this.processingEndTime = LocalDateTime.now();
        }
    }
    
    /**
     * 获取处理耗时（秒）
     */
    public long getProcessingDurationSeconds() {
        if (processingStartTime == null) {
            return 0;
        }
        
        LocalDateTime endTime = processingEndTime != null ? processingEndTime : LocalDateTime.now();
        return java.time.Duration.between(processingStartTime, endTime).getSeconds();
    }
    
    /**
     * 初始化四个方向的数据结构
     */
    public void initializeDirections() {
        for (Direction direction : Direction.values()) {
            DirectionVideoData data = new DirectionVideoData();
            data.setStatus("queued");
            data.setProgress(0);
            directions.put(direction, data);
        }
    }
}
