<template>
  <div class="four-way-upload-container">
    <!-- 标题和说明 -->
    <div class="upload-header">
      <h2 class="upload-title">
        <el-icon><VideoCamera /></el-icon>
        四方向智能交通分析
      </h2>
      <p class="upload-description">
        请上传十字路口四个方向的视频文件，系统将进行智能交通流量分析并生成优化建议
      </p>
    </div>

    <!-- 四方向视频上传网格 - 2x2布局 -->
    <div class="upload-grid">
      <!-- 上排：北向和东向 -->
      <div class="upload-item north">
        <div class="direction-header">
          <el-icon><Top /></el-icon>
          <span class="direction-label">北向 (North)</span>
          <el-tag type="info" size="small">南北方向交通</el-tag>
        </div>
        <VideoUploadArea
          ref="northUpload"
          :direction="'north'"
          :video-file="form.northVideoFile"
          @file-selected="handleFileSelected"
          @file-removed="handleFileRemoved"
        />
      </div>

      <div class="upload-item east">
        <div class="direction-header">
          <el-icon><Right /></el-icon>
          <span class="direction-label">东向 (East)</span>
          <el-tag type="warning" size="small">东西方向交通</el-tag>
        </div>
        <VideoUploadArea
          ref="eastUpload"
          :direction="'east'"
          :video-file="form.eastVideoFile"
          @file-selected="handleFileSelected"
          @file-removed="handleFileRemoved"
        />
      </div>

      <!-- 下排：西向和南向 -->
      <div class="upload-item west">
        <div class="direction-header">
          <el-icon><Back /></el-icon>
          <span class="direction-label">西向 (West)</span>
          <el-tag type="warning" size="small">东西方向交通</el-tag>
        </div>
        <VideoUploadArea
          ref="westUpload"
          :direction="'west'"
          :video-file="form.westVideoFile"
          @file-selected="handleFileSelected"
          @file-removed="handleFileRemoved"
        />
      </div>

      <div class="upload-item south">
        <div class="direction-header">
          <el-icon><Bottom /></el-icon>
          <span class="direction-label">南向 (South)</span>
          <el-tag type="info" size="small">南北方向交通</el-tag>
        </div>
        <VideoUploadArea
          ref="southUpload"
          :direction="'south'"
          :video-file="form.southVideoFile"
          @file-selected="handleFileSelected"
          @file-removed="handleFileRemoved"
        />
      </div>
    </div>

    <!-- 中心交叉路口图标 -->
    <div class="intersection-center">
      <div class="intersection-icon">
        <el-icon size="48"><Grid /></el-icon>
        <span class="intersection-text">十字路口智能交通分析</span>
      </div>
    </div>

    <!-- 上传进度和状态 -->
    <div v-if="uploading || uploadProgress > 0" class="upload-status-section">
      <!-- 整体上传进度 -->
      <div class="overall-progress">
        <div class="progress-header">
          <h3>上传进度</h3>
          <el-tag :type="getOverallStatusType()" size="small">
            {{ getOverallStatusText() }}
          </el-tag>
        </div>
        <el-progress
          :percentage="uploadProgress"
          :status="uploadProgress < 100 ? (uploadError ? 'exception' : '') : 'success'"
          :stroke-width="20"
          :show-text="true"
          text-inside
          class="main-progress"
        />
        <div class="progress-details">
          <span>{{ getProgressText() }}</span>
          <span v-if="uploadSpeed > 0" class="upload-speed">{{ formatSpeed(uploadSpeed) }}</span>
        </div>
      </div>

      <!-- 各方向上传详情 -->
      <div class="direction-progress-grid">
        <div
          v-for="direction in directions"
          :key="direction"
          class="direction-progress-item"
          :class="{ 'completed': directionProgress[direction] >= 100, 'error': directionErrors[direction] }"
        >
          <div class="direction-info">
            <el-icon>
              <component :is="getDirectionIcon(direction)" />
            </el-icon>
            <span class="direction-name">{{ getDirectionName(direction) }}</span>
            <el-tag
              :type="getDirectionProgressType(direction)"
              size="small"
            >
              {{ getDirectionProgressText(direction) }}
            </el-tag>
          </div>
          <el-progress
            :percentage="directionProgress[direction]"
            :status="directionErrors[direction] ? 'exception' : (directionProgress[direction] >= 100 ? 'success' : '')"
            :stroke-width="8"
            :show-text="false"
            class="direction-progress-bar"
          />
          <div class="direction-details">
            <span class="file-size">{{ getDirectionFileSize(direction) }}</span>
            <span v-if="directionErrors[direction]" class="error-text">{{ directionErrors[direction] }}</span>
          </div>
        </div>
      </div>

      <!-- 错误处理和重试 -->
      <div v-if="uploadError" class="error-section">
        <el-alert
          :title="uploadError"
          type="error"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="error-actions">
              <el-button size="small" @click="retryUpload" :loading="retrying">
                <el-icon><RefreshLeft /></el-icon>
                重试上传
              </el-button>
              <el-button size="small" type="info" @click="resetUpload">
                重新选择文件
              </el-button>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="upload-actions">
      <el-button 
        type="primary" 
        size="large"
        :loading="uploading"
        :disabled="!canSubmit"
        @click="submitUpload"
        class="submit-btn"
      >
        <el-icon><Upload /></el-icon>
        开始智能分析
      </el-button>
      
      <el-button 
        size="large"
        @click="resetForm"
        :disabled="uploading"
        class="reset-btn"
      >
        <el-icon><RefreshLeft /></el-icon>
        重置
      </el-button>
    </div>

    <!-- 文件信息汇总 -->
    <div v-if="hasAnyFile" class="file-summary">
      <h3>已选择的视频文件</h3>
      <div class="file-list">
        <div v-for="direction in directions" :key="direction" class="file-item">
          <span class="file-direction">{{ getDirectionName(direction) }}:</span>
          <span v-if="form[`${direction}VideoFile`]" class="file-info">
            {{ form[`${direction}VideoFile`].name }} 
            ({{ formatFileSize(form[`${direction}VideoFile`].size) }})
          </span>
          <span v-else class="file-empty">未选择</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  VideoCamera, Upload, RefreshLeft, Grid,
  Top, Bottom, ArrowLeft as Back, Right
} from '@element-plus/icons-vue'
import VideoUploadArea from './VideoUploadArea.vue'
import { uploadFourWayIntersectionVideos } from '@/api/video'

export default {
  name: 'FourWayVideoUpload',
  components: {
    VideoCamera, Upload, RefreshLeft, Grid,
    Top, Bottom, Back, Right,
    VideoUploadArea
  },
  emits: ['upload-success', 'upload-error', 'upload-progress', 'status-change'],
  setup(props, { emit }) {
    // 响应式数据
    const uploading = ref(false)
    const uploadProgress = ref(0)
    const uploadError = ref('')
    const retrying = ref(false)
    const uploadSpeed = ref(0)

    const form = reactive({
      eastVideoFile: null,
      southVideoFile: null,
      westVideoFile: null,
      northVideoFile: null
    })

    const directions = ['east', 'south', 'west', 'north']

    // 各方向上传进度和状态
    const directionProgress = reactive({
      east: 0,
      south: 0,
      west: 0,
      north: 0
    })

    const directionErrors = reactive({
      east: '',
      south: '',
      west: '',
      north: ''
    })

    // 上传统计
    const uploadStats = reactive({
      startTime: null,
      totalSize: 0,
      uploadedSize: 0,
      estimatedTime: 0
    })
    
    // 计算属性
    const hasAnyFile = computed(() => {
      return directions.some(direction => form[`${direction}VideoFile`])
    })
    
    const canSubmit = computed(() => {
      return directions.every(direction => form[`${direction}VideoFile`]) && !uploading.value
    })

    // 新增计算属性
    const totalFileSize = computed(() => {
      return directions.reduce((total, direction) => {
        const file = form[`${direction}VideoFile`]
        return total + (file ? file.size : 0)
      }, 0)
    })

    // 状态相关方法
    const getOverallStatusType = () => {
      if (uploadError.value) return 'danger'
      if (uploadProgress.value >= 100) return 'success'
      if (uploading.value) return 'primary'
      return 'info'
    }

    const getOverallStatusText = () => {
      if (uploadError.value) return '上传失败'
      if (uploadProgress.value >= 100) return '上传完成'
      if (uploading.value) return '上传中'
      return '准备上传'
    }

    const getProgressText = () => {
      if (uploadError.value) return uploadError.value
      if (uploadProgress.value >= 100) return '所有文件上传完成'
      if (uploading.value) return `正在上传... ${uploadProgress.value.toFixed(1)}%`
      return '等待开始上传'
    }

    const formatSpeed = (bytesPerSecond) => {
      if (bytesPerSecond < 1024) return `${bytesPerSecond.toFixed(0)} B/s`
      if (bytesPerSecond < 1024 * 1024) return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`
      return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`
    }

    const getDirectionIcon = (direction) => {
      const icons = {
        east: Right,
        south: Bottom,
        west: Back,
        north: Top
      }
      return icons[direction] || VideoCamera
    }

    const getDirectionProgressType = (direction) => {
      if (directionErrors[direction]) return 'danger'
      if (directionProgress[direction] >= 100) return 'success'
      if (directionProgress[direction] > 0) return 'primary'
      return 'info'
    }

    const getDirectionProgressText = (direction) => {
      if (directionErrors[direction]) return '上传失败'
      if (directionProgress[direction] >= 100) return '完成'
      if (directionProgress[direction] > 0) return '上传中'
      return '等待'
    }

    const getDirectionFileSize = (direction) => {
      const file = form[`${direction}VideoFile`]
      return file ? formatFileSize(file.size) : '0 B'
    }

    // 方法
    const getDirectionName = (direction) => {
      const names = {
        east: '东向',
        south: '南向',
        west: '西向',
        north: '北向'
      }
      return names[direction] || direction
    }
    
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
    
    const handleFileSelected = (direction, file) => {
      form[`${direction}VideoFile`] = file
      console.log(`${direction}方向视频已选择:`, file.name)
    }
    
    const handleFileRemoved = (direction) => {
      form[`${direction}VideoFile`] = null
      console.log(`${direction}方向视频已移除`)
    }
    
    const resetForm = () => {
      directions.forEach(direction => {
        form[`${direction}VideoFile`] = null
        directionProgress[direction] = 0
        directionErrors[direction] = ''
      })
      uploadProgress.value = 0
      uploading.value = false
      uploadError.value = ''
      uploadSpeed.value = 0
      uploadStats.startTime = null
      uploadStats.totalSize = 0
      uploadStats.uploadedSize = 0
    }

    const retryUpload = async () => {
      if (retrying.value) return

      retrying.value = true
      uploadError.value = ''

      // 清除错误状态
      directions.forEach(direction => {
        directionErrors[direction] = ''
        directionProgress[direction] = 0
      })

      try {
        await submitUpload()
      } finally {
        retrying.value = false
      }
    }

    const resetUpload = () => {
      resetForm()
      emit('status-change', { status: 'reset' })
    }
    
    const submitUpload = async () => {
      if (!canSubmit.value) {
        ElMessage.warning('请选择所有四个方向的视频文件')
        return
      }

      uploading.value = true
      uploadProgress.value = 0
      uploadError.value = ''
      uploadStats.startTime = Date.now()
      uploadStats.totalSize = totalFileSize.value
      uploadStats.uploadedSize = 0

      // 发出状态变化事件
      emit('status-change', { status: 'uploading', progress: 0 })

      try {
        console.log('开始上传四方向视频...', {
          totalSize: formatFileSize(uploadStats.totalSize),
          fileCount: directions.length
        })

        // 创建FormData
        const formData = new FormData()
        directions.forEach(direction => {
          const file = form[`${direction}VideoFile`]
          if (file) {
            formData.append(`${direction}Video`, file)
            console.log(`添加${getDirectionName(direction)}视频:`, file.name, formatFileSize(file.size))
          }
        })

        // 增强的进度模拟（更真实的上传体验）
        let currentProgress = 0
        const progressInterval = setInterval(() => {
          if (currentProgress < 85) {
            // 模拟各方向的上传进度
            directions.forEach((direction, index) => {
              const baseProgress = currentProgress + (index * 2)
              const randomVariation = Math.random() * 5
              directionProgress[direction] = Math.min(95, baseProgress + randomVariation)
            })

            currentProgress += Math.random() * 3 + 1
            uploadProgress.value = Math.min(90, currentProgress)

            // 计算上传速度
            const elapsed = (Date.now() - uploadStats.startTime) / 1000
            const uploadedBytes = (uploadProgress.value / 100) * uploadStats.totalSize
            uploadSpeed.value = elapsed > 0 ? uploadedBytes / elapsed : 0

            // 发出进度事件
            emit('upload-progress', {
              overall: uploadProgress.value,
              directions: { ...directionProgress },
              speed: uploadSpeed.value,
              elapsed
            })
          }
        }, 300)

        // 调用上传API
        const response = await uploadFourWayIntersectionVideos(formData)

        clearInterval(progressInterval)

        // 完成所有方向的进度
        directions.forEach(direction => {
          directionProgress[direction] = 100
        })
        uploadProgress.value = 100

        console.log('四方向视频上传成功:', response)
        ElMessage.success('四方向视频上传成功，正在进行智能分析')

        emit('upload-success', response)
        emit('status-change', { status: 'completed', taskId: response.data?.taskId })

      } catch (error) {
        console.error('四方向视频上传失败:', error)

        uploadError.value = error.message || '上传失败，请重试'

        // 标记失败的方向
        directions.forEach(direction => {
          if (directionProgress[direction] < 100) {
            directionErrors[direction] = '上传失败'
          }
        })

        ElMessage.error('上传失败: ' + uploadError.value)
        emit('upload-error', error)
        emit('status-change', { status: 'error', error: uploadError.value })
      } finally {
        uploading.value = false
      }
    }
    
    return {
      // 响应式数据
      uploading,
      uploadProgress,
      uploadError,
      retrying,
      uploadSpeed,
      form,
      directions,
      directionProgress,
      directionErrors,
      uploadStats,

      // 计算属性
      hasAnyFile,
      canSubmit,
      totalFileSize,

      // 状态方法
      getOverallStatusType,
      getOverallStatusText,
      getProgressText,
      formatSpeed,
      getDirectionIcon,
      getDirectionProgressType,
      getDirectionProgressText,
      getDirectionFileSize,

      // 基础方法
      getDirectionName,
      formatFileSize,
      handleFileSelected,
      handleFileRemoved,
      resetForm,
      retryUpload,
      resetUpload,
      submitUpload
    }
  }
}
</script>

<style scoped>
.four-way-upload-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.upload-header {
  text-align: center;
  margin-bottom: 30px;
}

.upload-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 24px;
  color: #2c3e50;
  margin-bottom: 10px;
}

.upload-description {
  color: #7f8c8d;
  font-size: 16px;
  line-height: 1.5;
}

.upload-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
  min-height: 500px;
}

.upload-item {
  display: flex;
  flex-direction: column;
}

.upload-item.north {
  grid-column: 1;
  grid-row: 1;
}

.upload-item.east {
  grid-column: 2;
  grid-row: 1;
}

.upload-item.west {
  grid-column: 1;
  grid-row: 2;
}

.upload-item.south {
  grid-column: 2;
  grid-row: 2;
}

.intersection-center {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  margin-bottom: 20px;
}

.intersection-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #409eff;
}

.intersection-text {
  font-size: 14px;
  font-weight: 500;
}

.direction-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.direction-label {
  font-weight: 500;
  color: #2c3e50;
}

/* 上传状态样式 */
.upload-status-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.overall-progress {
  margin-bottom: 25px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
}

.main-progress {
  margin-bottom: 10px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #6c757d;
}

.upload-speed {
  font-weight: 500;
  color: #28a745;
}

.direction-progress-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.direction-progress-item {
  background: white;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
}

.direction-progress-item.completed {
  border-color: #28a745;
  background: #f8fff9;
}

.direction-progress-item.error {
  border-color: #dc3545;
  background: #fff5f5;
}

.direction-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.direction-name {
  font-weight: 500;
  color: #2c3e50;
  flex: 1;
}

.direction-progress-bar {
  margin-bottom: 8px;
}

.direction-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #6c757d;
}

.error-text {
  color: #dc3545;
  font-weight: 500;
}

.error-section {
  margin-top: 20px;
}

.error-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.upload-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 30px;
}

.submit-btn, .reset-btn {
  min-width: 160px;
  height: 48px;
  font-size: 16px;
}

.file-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.file-summary h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.file-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 10px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-direction {
  font-weight: 500;
  color: #409eff;
  min-width: 60px;
}

.file-info {
  color: #2c3e50;
}

.file-empty {
  color: #95a5a6;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: 15px;
    min-height: auto;
  }

  .upload-item {
    grid-column: 1 !important;
    grid-row: auto !important;
  }

  .intersection-center {
    order: -1;
    margin: 15px 0;
  }
}
</style>
