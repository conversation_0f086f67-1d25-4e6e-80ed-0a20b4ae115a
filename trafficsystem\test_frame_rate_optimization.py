#!/usr/bin/env python3
"""
四方向视频检测帧率优化测试脚本
测试优化前后的帧率性能差异
"""

import time
import json
import logging
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_frame_push_performance():
    """测试帧推送性能"""
    logger.info("开始帧率优化性能测试")
    
    # 模拟测试参数
    test_scenarios = [
        {
            "name": "短视频实时预览",
            "total_frames": 300,
            "video_duration": 10,
            "realtime_mode": True
        },
        {
            "name": "长视频实时预览", 
            "total_frames": 1800,
            "video_duration": 60,
            "realtime_mode": True
        },
        {
            "name": "长视频完整分析",
            "total_frames": 1800,
            "video_duration": 60,
            "realtime_mode": False
        }
    ]
    
    results = {}
    
    for scenario in test_scenarios:
        logger.info(f"测试场景: {scenario['name']}")
        
        # 导入优化后的函数
        try:
            from python_model.model_api import should_push_frame
            
            # 模拟帧推送测试
            pushed_frames = 0
            start_time = time.time()
            
            for frame_num in range(1, scenario['total_frames'] + 1):
                # 模拟检测数量（随机变化）
                detection_count = frame_num % 5
                
                # 测试是否应该推送
                should_push = should_push_frame(
                    frame_num, 
                    detection_count, 
                    scenario['total_frames'],
                    scenario['video_duration'],
                    scenario['realtime_mode']
                )
                
                if should_push:
                    pushed_frames += 1
            
            end_time = time.time()
            test_duration = end_time - start_time
            
            # 计算性能指标
            effective_fps = pushed_frames / scenario['video_duration']
            processing_speed = scenario['total_frames'] / test_duration
            push_ratio = pushed_frames / scenario['total_frames']
            
            results[scenario['name']] = {
                "total_frames": scenario['total_frames'],
                "pushed_frames": pushed_frames,
                "video_duration": scenario['video_duration'],
                "effective_fps": round(effective_fps, 2),
                "processing_speed": round(processing_speed, 2),
                "push_ratio": round(push_ratio, 3),
                "test_duration": round(test_duration, 3)
            }
            
            logger.info(f"  推送帧数: {pushed_frames}/{scenario['total_frames']}")
            logger.info(f"  有效帧率: {effective_fps:.2f} fps")
            logger.info(f"  推送比例: {push_ratio:.1%}")
            
        except Exception as e:
            logger.error(f"测试场景 {scenario['name']} 失败: {str(e)}")
            results[scenario['name']] = {"error": str(e)}
    
    return results

def generate_performance_report(results):
    """生成性能报告"""
    report = {
        "test_time": datetime.now().isoformat(),
        "optimization_summary": {
            "target_fps": "5-10 fps",
            "previous_fps": "0.2 fps", 
            "optimization_goals": [
                "提升实时预览帧率",
                "降低图像处理开销",
                "优化资源分配",
                "改进前端显示机制"
            ]
        },
        "test_results": results,
        "performance_analysis": {}
    }
    
    # 分析性能改进
    for scenario_name, result in results.items():
        if "error" not in result:
            fps = result["effective_fps"]
            improvement = fps / 0.2  # 相对于原来0.2fps的改进倍数
            
            report["performance_analysis"][scenario_name] = {
                "fps_improvement": f"{improvement:.1f}x",
                "meets_target": fps >= 5.0,
                "performance_level": "优秀" if fps >= 8 else "良好" if fps >= 5 else "需改进"
            }
    
    return report

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("四方向视频检测帧率优化测试")
    logger.info("=" * 60)
    
    # 执行性能测试
    results = test_frame_push_performance()
    
    # 生成报告
    report = generate_performance_report(results)
    
    # 保存测试报告
    report_file = f"frame_rate_optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    logger.info(f"测试报告已保存到: {report_file}")
    
    # 输出总结
    logger.info("\n" + "=" * 60)
    logger.info("测试总结:")
    logger.info("=" * 60)
    
    for scenario, analysis in report["performance_analysis"].items():
        logger.info(f"{scenario}:")
        logger.info(f"  帧率改进: {analysis['fps_improvement']}")
        logger.info(f"  性能等级: {analysis['performance_level']}")
        logger.info(f"  达到目标: {'是' if analysis['meets_target'] else '否'}")
    
    logger.info("\n优化建议:")
    if any(not analysis["meets_target"] for analysis in report["performance_analysis"].values()):
        logger.info("- 考虑进一步降低图像质量")
        logger.info("- 优化网络传输协议")
        logger.info("- 实现更智能的帧选择算法")
    else:
        logger.info("- 当前优化效果良好，建议监控实际运行性能")
        logger.info("- 可以考虑根据网络状况动态调整参数")

if __name__ == "__main__":
    main()
