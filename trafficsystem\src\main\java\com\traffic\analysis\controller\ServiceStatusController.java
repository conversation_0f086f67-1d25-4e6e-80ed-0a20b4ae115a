package com.traffic.analysis.controller;

import com.traffic.analysis.service.PythonServiceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 服务状态控制器
 * 提供Python服务状态查询和管理接口
 */
@RestController
@RequestMapping("/api/service")
public class ServiceStatusController {

    private static final Logger logger = LoggerFactory.getLogger(ServiceStatusController.class);

    @Autowired
    private PythonServiceManager pythonServiceManager;

    /**
     * 获取所有服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getServiceStatus() {
        logger.info("获取服务状态请求");
        
        Map<String, Object> status = new HashMap<>();
        
        try {
            boolean modelApiRunning = pythonServiceManager.isModelApiRunning();
            boolean apiControllerRunning = pythonServiceManager.isApiControllerRunning();

            // 获取四方向服务状态
            Map<String, Boolean> fourWayServicesStatus = pythonServiceManager.getFourWayServicesStatus();

            status.put("modelApi", Map.of(
                "running", modelApiRunning,
                "port", 5001,
                "url", "http://localhost:5001"
            ));

            status.put("apiController", Map.of(
                "running", apiControllerRunning,
                "port", 5000,
                "url", "http://localhost:5000"
            ));

            // 添加四方向服务状态
            status.put("fourWayServices", Map.of(
                "east", Map.of(
                    "running", fourWayServicesStatus.get("east"),
                    "port", 5001,
                    "url", "http://localhost:5001"
                ),
                "south", Map.of(
                    "running", fourWayServicesStatus.get("south"),
                    "port", 5002,
                    "url", "http://localhost:5002"
                ),
                "west", Map.of(
                    "running", fourWayServicesStatus.get("west"),
                    "port", 5003,
                    "url", "http://localhost:5003"
                ),
                "north", Map.of(
                    "running", fourWayServicesStatus.get("north"),
                    "port", 5004,
                    "url", "http://localhost:5004"
                )
            ));

            // 检查所有服务是否运行
            boolean allFourWayRunning = fourWayServicesStatus.values().stream().allMatch(Boolean::booleanValue);

            status.put("overall", Map.of(
                "allRunning", modelApiRunning && apiControllerRunning,
                "fourWayAllRunning", allFourWayRunning,
                "timestamp", System.currentTimeMillis()
            ));

            logger.info("服务状态: 模型API={}, API控制器={}, 四方向服务={}",
                       modelApiRunning, apiControllerRunning, fourWayServicesStatus);
            
            return ResponseEntity.ok(status);
            
        } catch (Exception e) {
            logger.error("获取服务状态时出错: {}", e.getMessage(), e);
            status.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(status);
        }
    }

    /**
     * 重启Python服务
     */
    @PostMapping("/restart")
    public ResponseEntity<Map<String, Object>> restartServices() {
        logger.info("收到重启Python服务请求");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 停止现有服务
            pythonServiceManager.stopAllServices();
            
            // 等待一段时间
            Thread.sleep(3000);
            
            // 重新启动服务
            pythonServiceManager.startAllServices();
            
            result.put("success", true);
            result.put("message", "Python服务重启成功");
            result.put("timestamp", System.currentTimeMillis());
            
            logger.info("Python服务重启完成");
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            logger.error("重启Python服务时出错: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            boolean modelApiRunning = pythonServiceManager.isModelApiRunning();
            boolean apiControllerRunning = pythonServiceManager.isApiControllerRunning();
            boolean allHealthy = modelApiRunning && apiControllerRunning;
            
            health.put("status", allHealthy ? "healthy" : "unhealthy");
            health.put("services", Map.of(
                "modelApi", modelApiRunning,
                "apiController", apiControllerRunning
            ));
            health.put("timestamp", System.currentTimeMillis());
            
            if (allHealthy) {
                return ResponseEntity.ok(health);
            } else {
                return ResponseEntity.status(503).body(health); // Service Unavailable
            }
            
        } catch (Exception e) {
            logger.error("健康检查时出错: {}", e.getMessage(), e);
            health.put("status", "error");
            health.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(health);
        }
    }
}
