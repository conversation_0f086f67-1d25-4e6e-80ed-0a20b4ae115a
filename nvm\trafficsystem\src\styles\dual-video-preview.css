/**
 * 双视频实时预览模块样式优化
 * 专为十字路口视频分析设计
 */

/* 主容器样式 */
.realtime-preview-section {
  margin: 24px 0;
  padding: 24px;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  border-radius: 16px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  box-shadow: 
    0 10px 25px rgba(0, 0, 0, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.realtime-preview-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* 标题样式 */
.preview-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #f1f5f9;
  margin-bottom: 24px;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  position: relative;
  z-index: 1;
}

.preview-title::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #10b981);
  border-radius: 2px;
}

/* 双视频网格布局 */
.video-preview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  position: relative;
  z-index: 1;
}

/* 视频容器样式 */
.video-preview-container {
  background: linear-gradient(145deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9));
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(148, 163, 184, 0.15);
  box-shadow: 
    0 8px 20px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.video-preview-container:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 12px 30px rgba(0, 0, 0, 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(148, 163, 184, 0.3);
}

/* 视频头部样式 */
.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.video-header h4 {
  color: #f1f5f9;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-header h4::before {
  content: '📹';
  font-size: 1.2rem;
}

/* 状态标签样式 */
.video-header .el-tag {
  font-weight: 500;
  font-size: 0.75rem;
  padding: 4px 12px;
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 视频预览区域 */
.video-preview-area {
  min-height: 280px;
  background: linear-gradient(135deg, #0f172a, #1e293b);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
  font-size: 0.9rem;
  border: 2px dashed rgba(148, 163, 184, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.video-preview-area:hover {
  border-color: rgba(148, 163, 184, 0.4);
  background: linear-gradient(135deg, #1e293b, #334155);
}

/* 等待状态动画 */
.video-preview-area.waiting {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 检测帧显示样式 */
.detection-frame-container {
  position: relative;
  width: 100%;
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1));
  border: 2px solid rgba(16, 185, 129, 0.4);
  border-radius: 8px;
  overflow: hidden;
  animation: detectGlow 2s ease-in-out infinite alternate;
}

@keyframes detectGlow {
  from {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.5);
  }
}

.detection-frame-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 6px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* 检测信息覆盖层 */
.detection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.detection-header {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.detection-time {
  background: rgba(0, 0, 0, 0.8);
  color: #f1f5f9;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.detection-info {
  position: absolute;
  bottom: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.85);
  color: #f1f5f9;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.8rem;
  display: flex;
  flex-direction: column;
  gap: 4px;
  border-left: 4px solid #10b981;
  backdrop-filter: blur(10px);
  min-width: 140px;
}

.detection-status {
  color: #10b981;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 统计信息样式 */
.video-stats {
  margin-top: 12px;
  padding: 12px;
  background: rgba(15, 23, 42, 0.6);
  border-radius: 6px;
  font-size: 0.8rem;
  color: #94a3b8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.video-stats .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.video-stats .stat-value {
  color: #f1f5f9;
  font-weight: 600;
  font-size: 0.9rem;
}

.video-stats .stat-label {
  color: #64748b;
  font-size: 0.7rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .video-preview-grid {
    gap: 20px;
  }
  
  .video-preview-container {
    padding: 16px;
  }
  
  .video-preview-area {
    min-height: 240px;
  }
}

@media (max-width: 768px) {
  .realtime-preview-section {
    margin: 16px 0;
    padding: 16px;
  }
  
  .video-preview-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .preview-title {
    font-size: 1.3rem;
    margin-bottom: 20px;
  }
  
  .video-preview-area {
    min-height: 200px;
  }
  
  .detection-frame-container {
    height: 200px;
  }
}

/* 加载动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(148, 163, 184, 0.3);
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 成功状态指示器 */
.success-indicator {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 12px;
  height: 12px;
  background: #10b981;
  border-radius: 50%;
  animation: successPulse 2s infinite;
}

@keyframes successPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}
