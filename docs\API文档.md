# 交通分析系统 API 文档

## 概述

本文档描述了交通分析系统的RESTful API接口，包括四方向交通分析、视频处理、用户管理等功能的详细接口说明。

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **API版本**: v1.0
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

所有API请求都需要在请求头中包含有效的JWT Token：

```http
Authorization: Bearer <your-jwt-token>
```

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误类型",
  "message": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-20T10:30:00Z",
  "path": "/api/endpoint"
}
```

## 四方向交通分析 API

### 1. 上传四方向视频

**接口**: `POST /video-analysis/four-way/upload`

**描述**: 上传四个方向的视频文件并开始分析

**请求参数**:
- `eastVideo` (file, required): 东方向视频文件
- `southVideo` (file, required): 南方向视频文件  
- `westVideo` (file, required): 西方向视频文件
- `northVideo` (file, required): 北方向视频文件
- `analysisType` (string, optional): 分析类型 (basic/advanced/intelligent)
- `sensitivityLevel` (number, optional): 检测敏感度 (0.1-1.0)
- `description` (string, optional): 分析描述
- `intersectionName` (string, optional): 交叉口名称
- `enablePrediction` (boolean, optional): 是否启用预测分析
- `enableAlerts` (boolean, optional): 是否启用实时警报

**响应示例**:
```json
{
  "success": true,
  "data": {
    "taskId": "four-way-20240120-001",
    "status": "queued",
    "message": "四方向视频上传成功，开始分析",
    "createdAt": "2024-01-20T10:30:00Z",
    "estimatedDuration": 1800
  }
}
```

### 2. 获取四方向分析结果

**接口**: `GET /video-analysis/four-way/{taskId}/result`

**描述**: 获取指定任务的分析结果

**路径参数**:
- `taskId` (string, required): 任务ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "taskId": "four-way-20240120-001",
    "status": "completed",
    "progress": 100,
    "directions": {
      "east": {
        "status": "completed",
        "vehicleCount": 45,
        "avgSpeed": 35.2,
        "congestionLevel": "light"
      },
      "south": {
        "status": "completed", 
        "vehicleCount": 38,
        "avgSpeed": 28.5,
        "congestionLevel": "moderate"
      },
      "west": {
        "status": "completed",
        "vehicleCount": 52,
        "avgSpeed": 22.1,
        "congestionLevel": "heavy"
      },
      "north": {
        "status": "completed",
        "vehicleCount": 41,
        "avgSpeed": 31.8,
        "congestionLevel": "light"
      }
    },
    "trafficAnalysis": {
      "totalVehicleCount": 176,
      "peakDirection": "west",
      "trafficFlowBalance": 0.73,
      "congestionLevel": "moderate",
      "congestionIndex": 0.45,
      "recommendations": [
        "建议优化西方向信号配时",
        "考虑增加西方向车道数量"
      ]
    },
    "completedAt": "2024-01-20T11:00:00Z"
  }
}
```

### 3. 获取四方向任务列表

**接口**: `GET /video-analysis/four-way/tasks`

**描述**: 获取用户的四方向分析任务列表

**查询参数**:
- `page` (integer, optional): 页码，默认0
- `size` (integer, optional): 每页大小，默认10
- `status` (string, optional): 任务状态过滤
- `sortBy` (string, optional): 排序字段，默认createdAt
- `sortDir` (string, optional): 排序方向，默认desc

**响应示例**:
```json
{
  "success": true,
  "data": {
    "content": [
      {
        "taskId": "four-way-20240120-001",
        "status": "completed",
        "progress": 100,
        "createdAt": "2024-01-20T10:30:00Z",
        "completedAt": "2024-01-20T11:00:00Z",
        "analysisType": "intelligent",
        "totalVehicleCount": 176
      }
    ],
    "totalElements": 25,
    "totalPages": 3,
    "size": 10,
    "number": 0
  }
}
```

### 4. 删除四方向分析任务

**接口**: `DELETE /video-analysis/four-way/{taskId}`

**描述**: 删除指定的四方向分析任务

**路径参数**:
- `taskId` (string, required): 任务ID

**响应示例**:
```json
{
  "success": true,
  "message": "四方向分析任务删除成功"
}
```

### 5. 重新分析四方向视频

**接口**: `POST /video-analysis/four-way/{taskId}/retry`

**描述**: 重新分析指定的四方向视频任务

**路径参数**:
- `taskId` (string, required): 任务ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "taskId": "four-way-20240120-001",
    "status": "queued",
    "message": "四方向视频重新分析已启动"
  }
}
```

### 6. 获取四方向实时数据

**接口**: `GET /video-analysis/four-way/{taskId}/realtime`

**描述**: 获取四方向分析的实时数据

**路径参数**:
- `taskId` (string, required): 任务ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "taskId": "four-way-20240120-001",
    "status": "processing",
    "progress": 65,
    "directions": {
      "east": {
        "status": "processing",
        "progress": 70,
        "vehicleCount": 32,
        "lastFrameTime": "2024-01-20T10:45:30Z"
      },
      "south": {
        "status": "processing",
        "progress": 68,
        "vehicleCount": 28,
        "lastFrameTime": "2024-01-20T10:45:28Z"
      },
      "west": {
        "status": "processing", 
        "progress": 62,
        "vehicleCount": 41,
        "lastFrameTime": "2024-01-20T10:45:32Z"
      },
      "north": {
        "status": "processing",
        "progress": 60,
        "vehicleCount": 35,
        "lastFrameTime": "2024-01-20T10:45:25Z"
      }
    },
    "timestamp": 1705747530000
  }
}
```

### 7. 获取智能分析建议

**接口**: `GET /video-analysis/four-way/{taskId}/recommendations`

**描述**: 获取基于AI分析的智能交通优化建议

**路径参数**:
- `taskId` (string, required): 任务ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "taskId": "four-way-20240120-001",
    "recommendations": [
      {
        "type": "signal",
        "title": "信号灯配时优化",
        "description": "建议将西方向绿灯时间延长15秒",
        "priority": "high",
        "expectedImprovement": "减少拥堵30-40%"
      },
      {
        "type": "infrastructure",
        "title": "车道配置优化", 
        "description": "建议在西方向增加一条右转专用道",
        "priority": "medium",
        "expectedImprovement": "提升通行效率20-25%"
      }
    ],
    "generatedAt": 1705747530000
  }
}
```

### 8. 生成四方向分析报告

**接口**: `GET /video-analysis/four-way/{taskId}/report`

**描述**: 生成四方向交通分析报告

**路径参数**:
- `taskId` (string, required): 任务ID

**查询参数**:
- `format` (string, optional): 报告格式 (pdf/excel/json)，默认json

**响应示例**:
```json
{
  "success": true,
  "data": {
    "reportId": "report-20240120-001",
    "taskId": "four-way-20240120-001",
    "reportType": "detailed",
    "summary": {
      "totalVehicles": 176,
      "analysisTime": "30分钟",
      "peakDirection": "西方向",
      "congestionLevel": "中度拥堵",
      "efficiency": 0.73
    },
    "recommendations": [
      "优化信号灯配时",
      "增加西方向车道"
    ],
    "downloadUrl": "/api/reports/download/report-20240120-001.pdf",
    "generatedAt": "2024-01-20T11:30:00Z"
  }
}
```

## 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| INVALID_VIDEO_FORMAT | 400 | 不支持的视频格式 |
| FILE_TOO_LARGE | 413 | 文件大小超过限制 |
| MISSING_DIRECTION_VIDEO | 400 | 缺少方向视频文件 |
| TASK_NOT_FOUND | 404 | 任务未找到 |
| ANALYSIS_IN_PROGRESS | 409 | 分析正在进行中 |
| INSUFFICIENT_RESOURCES | 503 | 系统资源不足 |
| AUTHENTICATION_FAILED | 401 | 认证失败 |
| ACCESS_DENIED | 403 | 权限不足 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |

## 使用示例

### JavaScript/Axios 示例

```javascript
// 上传四方向视频
const uploadFourWayVideos = async (videos, config) => {
  const formData = new FormData();
  formData.append('eastVideo', videos.east);
  formData.append('southVideo', videos.south);
  formData.append('westVideo', videos.west);
  formData.append('northVideo', videos.north);
  formData.append('analysisType', config.analysisType);
  formData.append('sensitivityLevel', config.sensitivityLevel);
  
  try {
    const response = await axios.post('/api/video-analysis/four-way/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('上传失败:', error.response.data);
    throw error;
  }
};

// 获取分析结果
const getAnalysisResult = async (taskId) => {
  try {
    const response = await axios.get(`/api/video-analysis/four-way/${taskId}/result`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error) {
    console.error('获取结果失败:', error.response.data);
    throw error;
  }
};
```

### Python/Requests 示例

```python
import requests

# 上传四方向视频
def upload_four_way_videos(videos, config, token):
    files = {
        'eastVideo': open(videos['east'], 'rb'),
        'southVideo': open(videos['south'], 'rb'),
        'westVideo': open(videos['west'], 'rb'),
        'northVideo': open(videos['north'], 'rb')
    }
    
    data = {
        'analysisType': config['analysisType'],
        'sensitivityLevel': config['sensitivityLevel']
    }
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.post(
        'http://localhost:8080/api/video-analysis/four-way/upload',
        files=files,
        data=data,
        headers=headers
    )
    
    return response.json()

# 获取分析结果
def get_analysis_result(task_id, token):
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.get(
        f'http://localhost:8080/api/video-analysis/four-way/{task_id}/result',
        headers=headers
    )
    
    return response.json()
```
