package com.traffic.analysis.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.traffic.analysis.model.FourWayIntersectionAnalysis;
import com.traffic.analysis.service.VideoAnalysisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 四方向交通分析集成测试
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
public class FourWayAnalysisIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private VideoAnalysisService videoAnalysisService;

    @MockBean
    private MongoTemplate mongoTemplate;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .apply(springSecurity())
                .build();
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testUploadFourWayVideos_Success() throws Exception {
        // 准备测试数据
        MockMultipartFile eastVideo = new MockMultipartFile(
                "eastVideo", "east.mp4", "video/mp4", "east video content".getBytes());
        MockMultipartFile southVideo = new MockMultipartFile(
                "southVideo", "south.mp4", "video/mp4", "south video content".getBytes());
        MockMultipartFile westVideo = new MockMultipartFile(
                "westVideo", "west.mp4", "video/mp4", "west video content".getBytes());
        MockMultipartFile northVideo = new MockMultipartFile(
                "northVideo", "north.mp4", "video/mp4", "north video content".getBytes());

        // 模拟服务返回
        FourWayIntersectionAnalysis mockAnalysis = createMockAnalysis();
        when(videoAnalysisService.uploadFourWayVideos(any(), any(), any(), any(), anyString(), anyString(), anyString()))
                .thenReturn(mockAnalysis);

        // 执行测试
        mockMvc.perform(multipart("/api/video-analysis/four-way/upload")
                        .file(eastVideo)
                        .file(southVideo)
                        .file(westVideo)
                        .file(northVideo)
                        .param("analysisType", "basic")
                        .param("description", "测试四方向分析"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value("test-task-id"))
                .andExpect(jsonPath("$.status").value("queued"))
                .andExpect(jsonPath("$.message").value("四方向视频上传成功，开始分析"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testUploadFourWayVideos_MissingVideo() throws Exception {
        // 只上传3个视频，缺少北方向
        MockMultipartFile eastVideo = new MockMultipartFile(
                "eastVideo", "east.mp4", "video/mp4", "east video content".getBytes());
        MockMultipartFile southVideo = new MockMultipartFile(
                "southVideo", "south.mp4", "video/mp4", "south video content".getBytes());
        MockMultipartFile westVideo = new MockMultipartFile(
                "westVideo", "west.mp4", "video/mp4", "west video content".getBytes());

        // 执行测试
        mockMvc.perform(multipart("/api/video-analysis/four-way/upload")
                        .file(eastVideo)
                        .file(southVideo)
                        .file(westVideo)
                        .param("analysisType", "basic"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").exists())
                .andExpect(jsonPath("$.message").value("必须上传所有四个方向的视频文件"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testGetFourWayAnalysisResult_Success() throws Exception {
        // 准备测试数据
        FourWayIntersectionAnalysis mockAnalysis = createMockAnalysis();
        mockAnalysis.setStatus("completed");
        
        when(videoAnalysisService.findFourWayAnalysisByTaskId("test-task-id"))
                .thenReturn(Optional.of(mockAnalysis));

        // 执行测试
        mockMvc.perform(get("/api/video-analysis/four-way/test-task-id/result"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value("test-task-id"))
                .andExpect(jsonPath("$.status").value("completed"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testGetFourWayAnalysisResult_NotFound() throws Exception {
        // 模拟任务不存在
        when(videoAnalysisService.findFourWayAnalysisByTaskId("non-existent-task"))
                .thenReturn(Optional.empty());

        // 执行测试
        mockMvc.perform(get("/api/video-analysis/four-way/non-existent-task/result"))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testGetFourWayTaskList_Success() throws Exception {
        // 执行测试
        mockMvc.perform(get("/api/video-analysis/four-way/tasks")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testDeleteFourWayTask_Success() throws Exception {
        // 模拟删除成功
        when(videoAnalysisService.deleteFourWayTask("test-task-id", "testuser"))
                .thenReturn(true);

        // 执行测试
        mockMvc.perform(delete("/api/video-analysis/four-way/test-task-id"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("四方向分析任务删除成功"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testDeleteFourWayTask_NotFound() throws Exception {
        // 模拟删除失败
        when(videoAnalysisService.deleteFourWayTask("non-existent-task", "testuser"))
                .thenReturn(false);

        // 执行测试
        mockMvc.perform(delete("/api/video-analysis/four-way/non-existent-task"))
                .andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testRetryFourWayAnalysis_Success() throws Exception {
        // 准备测试数据
        FourWayIntersectionAnalysis mockAnalysis = createMockAnalysis();
        mockAnalysis.setStatus("queued");
        
        when(videoAnalysisService.retryFourWayAnalysis("test-task-id"))
                .thenReturn(mockAnalysis);

        // 执行测试
        mockMvc.perform(post("/api/video-analysis/four-way/test-task-id/retry"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value("test-task-id"))
                .andExpect(jsonPath("$.status").value("queued"))
                .andExpect(jsonPath("$.message").value("四方向视频重新分析已启动"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testGetFourWayRealtimeData_Success() throws Exception {
        // 模拟实时数据
        when(videoAnalysisService.getFourWayRealtimeData("test-task-id"))
                .thenReturn(createMockRealtimeData());

        // 执行测试
        mockMvc.perform(get("/api/video-analysis/four-way/test-task-id/realtime"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value("test-task-id"))
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testGetFourWaySystemStatus_Success() throws Exception {
        // 模拟系统状态
        when(videoAnalysisService.getFourWaySystemStatus())
                .thenReturn(createMockSystemStatus());

        // 执行测试
        mockMvc.perform(get("/api/video-analysis/four-way/system/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.healthStatus").exists());
    }

    @Test
    void testUnauthorizedAccess() throws Exception {
        // 测试未授权访问
        mockMvc.perform(get("/api/video-analysis/four-way/tasks"))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testInvalidVideoFormat() throws Exception {
        // 上传非视频文件
        MockMultipartFile invalidFile = new MockMultipartFile(
                "eastVideo", "test.txt", "text/plain", "not a video".getBytes());

        mockMvc.perform(multipart("/api/video-analysis/four-way/upload")
                        .file(invalidFile)
                        .file(createMockVideoFile("southVideo"))
                        .file(createMockVideoFile("westVideo"))
                        .file(createMockVideoFile("northVideo")))
                .andExpect(status().isBadRequest());
    }

    // 辅助方法
    private FourWayIntersectionAnalysis createMockAnalysis() {
        FourWayIntersectionAnalysis analysis = new FourWayIntersectionAnalysis();
        analysis.setTaskId("test-task-id");
        analysis.setUserId("testuser");
        analysis.setUsername("testuser");
        analysis.setRole("user");
        analysis.setStatus("queued");
        analysis.setProgress(0);
        analysis.setMessage("四方向视频上传成功，开始分析");
        analysis.setCreatedAt(LocalDateTime.now());
        analysis.setAnalysisType("basic");
        return analysis;
    }

    private java.util.Map<String, Object> createMockRealtimeData() {
        java.util.Map<String, Object> data = new java.util.HashMap<>();
        data.put("taskId", "test-task-id");
        data.put("status", "processing");
        data.put("progress", 50);
        data.put("success", true);
        return data;
    }

    private java.util.Map<String, Object> createMockSystemStatus() {
        java.util.Map<String, Object> status = new java.util.HashMap<>();
        status.put("totalTasks", 100);
        status.put("processingTasks", 5);
        status.put("completedTasks", 90);
        status.put("failedTasks", 5);
        status.put("healthStatus", "healthy");
        status.put("success", true);
        return status;
    }

    private MockMultipartFile createMockVideoFile(String paramName) {
        return new MockMultipartFile(
                paramName, paramName + ".mp4", "video/mp4", "mock video content".getBytes());
    }
}
