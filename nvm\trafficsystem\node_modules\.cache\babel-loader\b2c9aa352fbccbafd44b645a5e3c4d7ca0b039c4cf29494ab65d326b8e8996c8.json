{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport SockJS from 'sockjs-client';\nimport { Client } from '@stomp/stompjs';\nimport { SOCKJS_ENDPOINT, STOMP_TOPIC_PREFIX } from '@/config';\n\n/**\n * STOMP客户端服务\n * 用于管理WebSocket连接和消息订阅\n */\nclass StompService {\n  constructor() {\n    this.client = null;\n    this.subscriptions = new Map();\n    this.connected = false;\n    this.connectionPromise = null;\n    this.connectAttempts = 0; // 连接尝试计数\n    this.debug = true; // 启用详细调试\n\n    // 帧数据管理\n    this.frameSubscriptions = new Map(); // 帧数据订阅\n    this.frameBuffers = new Map(); // 任务帧缓冲区\n    this.maxFrameBufferSize = 50; // 每个任务最大缓冲帧数\n\n    // 网络性能监控\n    this.networkStats = {\n      frameReceiveRate: 0,\n      // 帧接收速率（帧/秒）\n      averageFrameSize: 0,\n      // 平均帧大小（KB）\n      connectionQuality: 'good',\n      // 连接质量：good, fair, poor\n      lastReceiveTime: 0,\n      frameCount: 0,\n      totalFrameSize: 0\n    };\n  }\n\n  /**\n   * 记录调试信息\n   * @param {string} message - 调试消息\n   * @param {Object} data - 附加数据\n   */\n  log(message, data = null) {\n    // 生产环境下静默处理\n  }\n\n  /**\n   * 记录错误信息\n   * @param {string} message - 错误消息\n   * @param {Error} error - 错误对象\n   */\n  logError(message, error = null) {\n    // 生产环境下静默处理\n  }\n\n  /**\n   * 初始化STOMP客户端\n   * @returns {Promise} 连接成功的Promise\n   */\n  init() {\n    if (this.connectionPromise) {\n      this.log('复用现有连接Promise');\n      return this.connectionPromise;\n    }\n    this.connectAttempts++;\n    this.log(`开始初始化STOMP客户端 (尝试 #${this.connectAttempts})`);\n    this.log(`连接端点: ${SOCKJS_ENDPOINT}`);\n    this.connectionPromise = new Promise((resolve, reject) => {\n      try {\n        // 创建SockJS连接\n        this.log(`尝试创建SockJS连接到 ${SOCKJS_ENDPOINT}`);\n        const socket = new SockJS(SOCKJS_ENDPOINT);\n        socket.onopen = () => {\n          this.log('SockJS连接已打开');\n        };\n        socket.onclose = event => {\n          this.logError(`SockJS连接已关闭，code: ${event.code}, reason: ${event.reason}`);\n        };\n        socket.onerror = error => {\n          this.logError('SockJS连接错误', error);\n        };\n\n        // 创建STOMP客户端\n        this.log('创建STOMP客户端');\n        this.client = new Client({\n          webSocketFactory: () => socket,\n          debug: str => {\n            if (this.debug) {\n              console.debug('[STOMP DEBUG] ' + str);\n            }\n          },\n          reconnectDelay: 5000,\n          // 5秒重连\n          heartbeatIncoming: 4000,\n          heartbeatOutgoing: 4000\n        });\n\n        // 连接成功回调\n        this.client.onConnect = frame => {\n          this.log('STOMP连接已建立', frame);\n          this.connected = true;\n          this.connectAttempts = 0; // 重置连接尝试计数\n          resolve(true);\n        };\n\n        // 连接错误回调\n        this.client.onStompError = frame => {\n          this.logError('STOMP协议错误', frame);\n          reject(new Error('STOMP协议错误: ' + frame.headers.message));\n        };\n\n        // 连接断开回调\n        this.client.onDisconnect = () => {\n          this.log('STOMP连接已断开');\n          this.connected = false;\n          this.connectionPromise = null;\n        };\n\n        // WebSocket错误回调\n        this.client.onWebSocketError = event => {\n          this.logError('WebSocket错误', event);\n        };\n\n        // WebSocket关闭回调\n        this.client.onWebSocketClose = event => {\n          this.logError(`WebSocket连接关闭: code=${event.code}, reason=${event.reason}`, event);\n        };\n\n        // 激活连接\n        this.log('激活STOMP客户端连接');\n        this.client.activate();\n      } catch (error) {\n        this.logError('STOMP初始化过程中发生错误', error);\n        this.connectionPromise = null;\n        reject(error);\n      }\n    });\n    return this.connectionPromise;\n  }\n\n  /**\n   * 订阅主题\n   * @param {string} topic - 主题名称，不含前缀\n   * @param {Function} callback - 收到消息的回调函数\n   * @returns {Promise<Object>} 订阅对象\n   */\n  async subscribe(topic, callback) {\n    this.log(`尝试订阅主题: ${topic}`);\n    if (!this.connected) {\n      this.log('STOMP客户端未连接，尝试初始化连接');\n      try {\n        await this.init();\n      } catch (error) {\n        this.logError('订阅前初始化STOMP客户端失败', error);\n        throw error;\n      }\n    }\n    const fullTopic = `${STOMP_TOPIC_PREFIX}/${topic}`;\n    this.log(`完整主题路径: ${fullTopic}`);\n    if (this.subscriptions.has(fullTopic)) {\n      this.log(`已存在对 ${fullTopic} 的订阅，复用现有订阅`);\n      return this.subscriptions.get(fullTopic);\n    }\n    try {\n      this.log(`订阅主题: ${fullTopic}`);\n      const subscription = this.client.subscribe(fullTopic, message => {\n        try {\n          this.log(`收到主题 ${fullTopic} 的消息`);\n          const data = JSON.parse(message.body);\n          callback(data);\n        } catch (error) {\n          this.logError(`解析 ${fullTopic} 的消息失败`, error);\n        }\n      });\n      this.subscriptions.set(fullTopic, subscription);\n      this.log(`订阅成功: ${fullTopic}, ID: ${subscription.id}`);\n      return subscription;\n    } catch (error) {\n      this.logError(`订阅 ${fullTopic} 失败`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * 取消订阅\n   * @param {string} topic - 主题名称，不含前缀\n   */\n  unsubscribe(topic) {\n    const fullTopic = `${STOMP_TOPIC_PREFIX}/${topic}`;\n    this.log(`尝试取消订阅: ${fullTopic}`);\n    if (this.subscriptions.has(fullTopic)) {\n      try {\n        const subscription = this.subscriptions.get(fullTopic);\n        this.log(`找到订阅，ID: ${subscription.id}，开始取消订阅`);\n        subscription.unsubscribe();\n        this.subscriptions.delete(fullTopic);\n        this.log(`已成功取消订阅主题: ${fullTopic}`);\n      } catch (error) {\n        this.logError(`取消订阅 ${fullTopic} 失败`, error);\n        // 即使发生错误，也移除订阅记录\n        this.subscriptions.delete(fullTopic);\n      }\n    } else {\n      this.log(`未找到对 ${fullTopic} 的订阅记录，无需取消`);\n    }\n  }\n\n  /**\n   * 订阅任务的实时帧数据\n   * @param {string} taskId - 任务ID\n   * @param {Function} frameCallback - 接收帧数据的回调函数\n   * @returns {Promise<Object>} 订阅对象\n   */\n  async subscribeFrameUpdates(taskId, frameCallback) {\n    this.log(`订阅任务 ${taskId} 的实时帧数据`);\n\n    // 确保主题路径与后端一致\n    const frameTopic = `frame-updates/${taskId}`;\n    try {\n      // 确保WebSocket连接已建立，如果断开则重连\n      await this.ensureConnection();\n\n      // 初始化任务的帧缓冲区\n      if (!this.frameBuffers.has(taskId)) {\n        this.frameBuffers.set(taskId, []);\n      }\n      this.log(`准备订阅帧更新主题: ${frameTopic}`);\n\n      // 订阅帧更新主题\n      const subscription = await this.subscribe(frameTopic, frameData => {\n        this.log(`收到帧数据: 任务${taskId}, 帧${frameData.frameNumber || 'unknown'}`);\n        this.handleFrameData(taskId, frameData, frameCallback);\n      });\n\n      // 记录帧订阅\n      this.frameSubscriptions.set(taskId, subscription);\n      this.log(`✓ 成功订阅任务 ${taskId} 的帧数据，主题: ${frameTopic}`);\n      return subscription;\n    } catch (error) {\n      this.logError(`✗ 订阅任务 ${taskId} 的帧数据失败`, error);\n\n      // 如果是连接问题，尝试重连后再次订阅\n      if (error.message && error.message.includes('STOMP connection')) {\n        this.log('检测到连接问题，尝试重连...');\n        try {\n          await this.reconnect();\n          return await this.subscribeFrameUpdates(taskId, frameCallback);\n        } catch (reconnectError) {\n          this.logError('重连失败', reconnectError);\n          throw reconnectError;\n        }\n      }\n      throw error;\n    }\n  }\n\n  /**\n   * 订阅任务的进度更新\n   * @param {string} taskId - 任务ID\n   * @param {Function} progressCallback - 接收进度数据的回调函数\n   * @returns {Promise<Object>} 订阅对象\n   */\n  async subscribeProgressUpdates(taskId, progressCallback) {\n    this.log(`订阅任务 ${taskId} 的进度更新`);\n\n    // 确保主题路径与后端一致\n    const progressTopic = `video-progress/${taskId}`;\n    try {\n      // 确保WebSocket连接已建立\n      await this.ensureConnection();\n      this.log(`准备订阅进度更新主题: ${progressTopic}`);\n\n      // 订阅进度更新主题\n      const subscription = await this.subscribe(progressTopic, progressData => {\n        this.log(`收到进度数据: 任务${taskId}, 进度${progressData.progress || 'unknown'}%`);\n        if (typeof progressCallback === 'function') {\n          progressCallback(progressData);\n        }\n      });\n\n      // 记录进度订阅\n      const subscriptionKey = `progress_${taskId}`;\n      this.frameSubscriptions.set(subscriptionKey, {\n        subscription,\n        taskId,\n        type: 'progress'\n      });\n      this.log(`✓ 成功订阅任务 ${taskId} 的进度更新，主题: ${progressTopic}`);\n      return subscription;\n    } catch (error) {\n      this.logError(`✗ 订阅任务 ${taskId} 的进度更新失败`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * 处理接收到的帧数据\n   * @param {string} taskId - 任务ID\n   * @param {Object} frameData - 帧数据\n   * @param {Function} callback - 回调函数\n   */\n  handleFrameData(taskId, frameData, callback) {\n    try {\n      this.log(`处理任务 ${taskId} 的帧数据: 帧${frameData.frameNumber}`);\n\n      // 更新网络性能统计\n      this.updateNetworkStats(frameData);\n\n      // 验证帧数据\n      if (!this.validateFrameData(frameData)) {\n        this.logError(`任务 ${taskId} 的帧数据格式无效`, frameData);\n        return;\n      }\n\n      // 添加到缓冲区\n      this.addFrameToBuffer(taskId, frameData);\n\n      // 调用回调函数\n      if (typeof callback === 'function') {\n        callback(frameData);\n      }\n    } catch (error) {\n      this.logError(`处理任务 ${taskId} 的帧数据失败`, error);\n    }\n  }\n\n  /**\n   * 更新网络性能统计\n   * @param {Object} frameData - 帧数据\n   */\n  updateNetworkStats(frameData) {\n    try {\n      const now = Date.now();\n\n      // 计算帧大小（估算）\n      const frameSize = frameData.imageData ? frameData.imageData.length * 0.75 / 1024 : 0; // Base64转字节再转KB\n\n      // 更新统计\n      this.networkStats.frameCount++;\n      this.networkStats.totalFrameSize += frameSize;\n      this.networkStats.averageFrameSize = this.networkStats.totalFrameSize / this.networkStats.frameCount;\n\n      // 计算接收速率\n      if (this.networkStats.lastReceiveTime > 0) {\n        const timeDiff = (now - this.networkStats.lastReceiveTime) / 1000; // 转换为秒\n        if (timeDiff > 0) {\n          // 使用指数移动平均\n          const alpha = 0.3;\n          const currentRate = 1 / timeDiff;\n          this.networkStats.frameReceiveRate = alpha * currentRate + (1 - alpha) * this.networkStats.frameReceiveRate;\n        }\n      }\n      this.networkStats.lastReceiveTime = now;\n\n      // 评估连接质量\n      this.evaluateConnectionQuality();\n      this.log(`网络统计更新: 速率=${this.networkStats.frameReceiveRate.toFixed(2)}fps, ` + `平均大小=${this.networkStats.averageFrameSize.toFixed(1)}KB, ` + `质量=${this.networkStats.connectionQuality}`);\n    } catch (error) {\n      this.logError('更新网络统计失败', error);\n    }\n  }\n\n  /**\n   * 评估连接质量\n   */\n  evaluateConnectionQuality() {\n    const {\n      frameReceiveRate,\n      averageFrameSize\n    } = this.networkStats;\n\n    // 根据帧接收速率和平均帧大小评估质量\n    if (frameReceiveRate >= 0.8 && averageFrameSize < 200) {\n      this.networkStats.connectionQuality = 'good';\n    } else if (frameReceiveRate >= 0.5 && averageFrameSize < 300) {\n      this.networkStats.connectionQuality = 'fair';\n    } else {\n      this.networkStats.connectionQuality = 'poor';\n    }\n  }\n\n  /**\n   * 获取网络性能统计\n   * @returns {Object} 网络统计信息\n   */\n  getNetworkStats() {\n    return {\n      ...this.networkStats\n    };\n  }\n\n  /**\n   * 重置网络统计\n   */\n  resetNetworkStats() {\n    this.networkStats = {\n      frameReceiveRate: 0,\n      averageFrameSize: 0,\n      connectionQuality: 'good',\n      lastReceiveTime: 0,\n      frameCount: 0,\n      totalFrameSize: 0\n    };\n    this.log('网络统计已重置');\n  }\n\n  /**\n   * 验证帧数据格式\n   * @param {Object} frameData - 帧数据\n   * @returns {boolean} 是否有效\n   */\n  validateFrameData(frameData) {\n    return frameData && typeof frameData.taskId === 'string' && typeof frameData.frameNumber === 'number' && typeof frameData.imageData === 'string' && frameData.type === 'frame_update';\n  }\n\n  /**\n   * 添加帧到缓冲区\n   * @param {string} taskId - 任务ID\n   * @param {Object} frameData - 帧数据\n   */\n  addFrameToBuffer(taskId, frameData) {\n    try {\n      let buffer = this.frameBuffers.get(taskId);\n      if (!buffer) {\n        buffer = [];\n        this.frameBuffers.set(taskId, buffer);\n      }\n\n      // 添加帧数据\n      buffer.push({\n        ...frameData,\n        receivedAt: Date.now()\n      });\n\n      // 限制缓冲区大小\n      while (buffer.length > this.maxFrameBufferSize) {\n        buffer.shift(); // 移除最旧的帧\n      }\n      this.log(`任务 ${taskId} 帧缓冲区大小: ${buffer.length}`);\n    } catch (error) {\n      this.logError(`添加帧到缓冲区失败`, error);\n    }\n  }\n\n  /**\n   * 获取任务的缓冲帧数据\n   * @param {string} taskId - 任务ID\n   * @returns {Array} 帧数据数组\n   */\n  getFrameBuffer(taskId) {\n    return this.frameBuffers.get(taskId) || [];\n  }\n\n  /**\n   * 清理任务的帧数据\n   * @param {string} taskId - 任务ID\n   */\n  clearFrameBuffer(taskId) {\n    this.log(`清理任务 ${taskId} 的帧缓冲区`);\n\n    // 取消帧订阅\n    if (this.frameSubscriptions.has(taskId)) {\n      try {\n        const frameTopic = `frame-updates/${taskId}`;\n        this.unsubscribe(frameTopic);\n        this.frameSubscriptions.delete(taskId);\n      } catch (error) {\n        this.logError(`取消任务 ${taskId} 的帧订阅失败`, error);\n      }\n    }\n\n    // 取消进度订阅\n    const progressSubscriptionKey = `progress_${taskId}`;\n    if (this.frameSubscriptions.has(progressSubscriptionKey)) {\n      try {\n        const progressTopic = `video-progress/${taskId}`;\n        this.unsubscribe(progressTopic);\n        this.frameSubscriptions.delete(progressSubscriptionKey);\n      } catch (error) {\n        this.logError(`取消任务 ${taskId} 的进度订阅失败`, error);\n      }\n    }\n\n    // 清理帧缓冲区\n    this.frameBuffers.delete(taskId);\n  }\n\n  /**\n   * 获取帧数据统计信息\n   * @param {string} taskId - 任务ID\n   * @returns {Object} 统计信息\n   */\n  getFrameStats(taskId) {\n    const buffer = this.frameBuffers.get(taskId) || [];\n    const hasSubscription = this.frameSubscriptions.has(taskId);\n    return {\n      taskId,\n      frameCount: buffer.length,\n      hasSubscription,\n      latestFrame: buffer.length > 0 ? buffer[buffer.length - 1] : null,\n      oldestFrame: buffer.length > 0 ? buffer[0] : null\n    };\n  }\n\n  /**\n   * 确保WebSocket连接已建立\n   */\n  async ensureConnection() {\n    if (!this.connected || !this.client || !this.client.connected) {\n      this.log('连接已断开，尝试重新连接...');\n      await this.reconnect();\n    }\n  }\n\n  /**\n   * 重新连接WebSocket\n   */\n  async reconnect() {\n    this.log('开始重新连接WebSocket...');\n    try {\n      // 先断开现有连接\n      if (this.client) {\n        try {\n          this.client.deactivate();\n        } catch (e) {\n          // 忽略断开连接时的错误\n        }\n      }\n\n      // 重置状态\n      this.connected = false;\n      this.connectionPromise = null;\n      this.connectAttempts = 0;\n\n      // 重新初始化连接\n      await this.init();\n      this.log('✓ WebSocket重连成功');\n    } catch (error) {\n      this.logError('WebSocket重连失败', error);\n      throw error;\n    }\n  }\n\n  // ==================== 四方向交通分析WebSocket支持 ====================\n\n  /**\n   * 订阅四方向任务的实时帧数据\n   * @param {string} taskId - 任务ID\n   * @param {Function} frameCallback - 接收帧数据的回调函数\n   * @param {Function} progressCallback - 接收进度数据的回调函数\n   * @returns {Promise<Object>} 订阅对象\n   */\n  async subscribeFourWayFrameUpdates(taskId, frameCallback, progressCallback) {\n    this.log(`订阅四方向任务 ${taskId} 的实时帧数据和进度`);\n    try {\n      // 确保WebSocket连接已建立\n      await this.ensureConnection();\n\n      // 初始化四方向任务的帧缓冲区\n      const directions = ['east', 'south', 'west', 'north'];\n      for (const direction of directions) {\n        const directionTaskId = `${direction}_${taskId}`;\n        if (!this.frameBuffers.has(directionTaskId)) {\n          this.frameBuffers.set(directionTaskId, []);\n        }\n      }\n\n      // 订阅四方向帧更新主题\n      const framesTopic = `four-way-frames/${taskId}`;\n      this.log(`准备订阅四方向帧更新主题: ${framesTopic}`);\n      const framesSubscription = await this.subscribe(framesTopic, frameData => {\n        this.log(`收到四方向帧数据: 任务${taskId}, 方向${frameData.direction}, 帧${frameData.frameNumber || 'unknown'}`);\n        this.handleFourWayFrameData(taskId, frameData, frameCallback);\n      });\n\n      // 订阅四方向进度更新主题\n      const progressTopic = `four-way-progress/${taskId}`;\n      const progressSubscription = await this.subscribe(progressTopic, progressData => {\n        this.log(`收到四方向进度数据: 任务${taskId}, 方向${progressData.direction}, 进度${progressData.progress}%`);\n        if (typeof progressCallback === 'function') {\n          progressCallback(progressData);\n        }\n      });\n\n      // 记录四方向订阅\n      const subscriptionKey = `four_way_${taskId}`;\n      this.frameSubscriptions.set(subscriptionKey, {\n        frames: framesSubscription,\n        progress: progressSubscription,\n        taskId,\n        type: 'four_way'\n      });\n      this.log(`✓ 成功订阅四方向任务 ${taskId} 的帧数据和进度`);\n      return {\n        frames: framesSubscription,\n        progress: progressSubscription\n      };\n    } catch (error) {\n      this.logError(`✗ 订阅四方向任务 ${taskId} 的数据失败`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * 订阅特定方向的帧数据\n   * @param {string} taskId - 任务ID\n   * @param {string} direction - 方向（east, south, west, north）\n   * @param {Function} frameCallback - 接收帧数据的回调函数\n   * @returns {Promise<Object>} 订阅对象\n   */\n  async subscribeDirectionFrameUpdates(taskId, direction, frameCallback) {\n    this.log(`订阅任务 ${taskId} 的 ${direction} 方向帧数据`);\n    try {\n      // 确保WebSocket连接已建立\n      await this.ensureConnection();\n\n      // 初始化方向帧缓冲区\n      const directionTaskId = `${direction}_${taskId}`;\n      if (!this.frameBuffers.has(directionTaskId)) {\n        this.frameBuffers.set(directionTaskId, []);\n      }\n\n      // 订阅特定方向的帧更新主题\n      const directionTopic = `four-way-frames/${taskId}/${direction}`;\n      const subscription = await this.subscribe(directionTopic, frameData => {\n        this.log(`收到${direction}方向帧数据: 任务${taskId}, 帧${frameData.frameNumber || 'unknown'}`);\n        this.handleFourWayFrameData(taskId, frameData, frameCallback);\n      });\n\n      // 记录方向订阅\n      const subscriptionKey = `direction_${direction}_${taskId}`;\n      this.frameSubscriptions.set(subscriptionKey, {\n        subscription,\n        taskId,\n        direction,\n        type: 'direction'\n      });\n      this.log(`✓ 成功订阅任务 ${taskId} 的 ${direction} 方向帧数据`);\n      return subscription;\n    } catch (error) {\n      this.logError(`✗ 订阅任务 ${taskId} 的 ${direction} 方向帧数据失败`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * 处理接收到的四方向帧数据\n   * @param {string} taskId - 任务ID\n   * @param {Object} frameData - 帧数据\n   * @param {Function} callback - 回调函数\n   */\n  handleFourWayFrameData(taskId, frameData, callback) {\n    try {\n      this.log(`处理四方向任务 ${taskId} 的帧数据: 方向${frameData.direction}, 帧${frameData.frameNumber}`);\n\n      // 详细的帧数据日志\n      console.log('🔍 四方向帧数据详情:', {\n        taskId,\n        direction: frameData.direction,\n        frameNumber: frameData.frameNumber,\n        detectionCount: frameData.detectionCount,\n        hasImageData: !!frameData.imageData,\n        imageDataLength: frameData.imageData ? frameData.imageData.length : 0,\n        imageDataType: frameData.imageData ? frameData.imageData.startsWith('data:image/') ? 'data-url' : 'base64' : 'none',\n        timestamp: frameData.timestamp\n      });\n\n      // 更新网络性能统计\n      this.updateNetworkStats(frameData);\n\n      // 验证四方向帧数据\n      if (!this.validateFourWayFrameData(frameData)) {\n        this.logError(`任务 ${taskId} 的四方向帧数据格式无效`, frameData);\n        return;\n      }\n\n      // 添加到方向缓冲区\n      const directionTaskId = `${frameData.direction}_${taskId}`;\n      this.addFrameToBuffer(directionTaskId, frameData);\n\n      // 调用回调函数\n      if (typeof callback === 'function') {\n        console.log(`📞 调用四方向帧数据回调函数: ${frameData.direction}方向`);\n        callback(frameData);\n      } else {\n        console.warn(`⚠️ 四方向帧数据回调函数不可用: ${typeof callback}`);\n      }\n    } catch (error) {\n      this.logError(`处理四方向任务 ${taskId} 的帧数据失败`, error);\n    }\n  }\n\n  /**\n   * 验证四方向帧数据格式\n   * @param {Object} frameData - 帧数据\n   * @returns {boolean} 是否有效\n   */\n  validateFourWayFrameData(frameData) {\n    if (!frameData) {\n      console.warn('❌ 四方向帧数据验证失败: frameData为空');\n      return false;\n    }\n    const validations = [{\n      field: 'taskId',\n      value: frameData.taskId,\n      type: 'string'\n    }, {\n      field: 'direction',\n      value: frameData.direction,\n      type: 'string'\n    }, {\n      field: 'frameNumber',\n      value: frameData.frameNumber,\n      type: 'number'\n    }, {\n      field: 'imageData',\n      value: frameData.imageData,\n      type: 'string'\n    }];\n    for (const validation of validations) {\n      if (typeof validation.value !== validation.type) {\n        console.warn(`❌ 四方向帧数据验证失败: ${validation.field} 类型错误`, {\n          expected: validation.type,\n          actual: typeof validation.value,\n          value: validation.value\n        });\n        return false;\n      }\n    }\n\n    // 验证方向值\n    if (!['east', 'south', 'west', 'north'].includes(frameData.direction)) {\n      console.warn(`❌ 四方向帧数据验证失败: 无效的方向值`, {\n        direction: frameData.direction,\n        validDirections: ['east', 'south', 'west', 'north']\n      });\n      return false;\n    }\n\n    // 验证图像数据格式（可以是base64或data-url）\n    if (!frameData.imageData || frameData.imageData.length === 0) {\n      console.warn(`❌ 四方向帧数据验证失败: imageData为空`);\n      return false;\n    }\n    console.log('✅ 四方向帧数据验证通过:', {\n      taskId: frameData.taskId,\n      direction: frameData.direction,\n      frameNumber: frameData.frameNumber,\n      imageDataLength: frameData.imageData.length\n    });\n    return true;\n  }\n\n  /**\n   * 获取四方向任务的帧数据统计\n   * @param {string} taskId - 任务ID\n   * @returns {Object} 统计信息\n   */\n  getFourWayFrameStats(taskId) {\n    const directions = ['east', 'south', 'west', 'north'];\n    const stats = {\n      taskId,\n      directions: {},\n      totalFrames: 0,\n      hasSubscription: this.frameSubscriptions.has(`four_way_${taskId}`)\n    };\n    for (const direction of directions) {\n      const directionTaskId = `${direction}_${taskId}`;\n      const buffer = this.frameBuffers.get(directionTaskId) || [];\n      stats.directions[direction] = {\n        frameCount: buffer.length,\n        latestFrame: buffer.length > 0 ? buffer[buffer.length - 1] : null,\n        oldestFrame: buffer.length > 0 ? buffer[0] : null\n      };\n      stats.totalFrames += buffer.length;\n    }\n    return stats;\n  }\n\n  /**\n   * 清理四方向任务的帧数据\n   * @param {string} taskId - 任务ID\n   */\n  clearFourWayFrameBuffer(taskId) {\n    this.log(`清理四方向任务 ${taskId} 的帧缓冲区`);\n    try {\n      // 取消四方向订阅\n      const fourWaySubscriptionKey = `four_way_${taskId}`;\n      if (this.frameSubscriptions.has(fourWaySubscriptionKey)) {\n        const subscriptions = this.frameSubscriptions.get(fourWaySubscriptionKey);\n\n        // 取消帧订阅\n        if (subscriptions.frames) {\n          const framesTopic = `four-way-frames/${taskId}`;\n          this.unsubscribe(framesTopic);\n        }\n\n        // 取消进度订阅\n        if (subscriptions.progress) {\n          const progressTopic = `four-way-progress/${taskId}`;\n          this.unsubscribe(progressTopic);\n        }\n        this.frameSubscriptions.delete(fourWaySubscriptionKey);\n      }\n\n      // 取消各方向订阅\n      const directions = ['east', 'south', 'west', 'north'];\n      for (const direction of directions) {\n        const directionSubscriptionKey = `direction_${direction}_${taskId}`;\n        if (this.frameSubscriptions.has(directionSubscriptionKey)) {\n          const directionTopic = `four-way-frames/${taskId}/${direction}`;\n          this.unsubscribe(directionTopic);\n          this.frameSubscriptions.delete(directionSubscriptionKey);\n        }\n\n        // 清理方向帧缓冲区\n        const directionTaskId = `${direction}_${taskId}`;\n        this.frameBuffers.delete(directionTaskId);\n      }\n      this.log(`✓ 已清理四方向任务 ${taskId} 的所有帧数据`);\n    } catch (error) {\n      this.logError(`清理四方向任务 ${taskId} 的帧数据失败`, error);\n    }\n  }\n\n  /**\n   * 断开连接\n   */\n  disconnect() {\n    if (this.client && this.connected) {\n      this.log('开始断开STOMP客户端连接');\n      try {\n        // 清理所有帧数据\n        this.log(`清理所有帧缓冲区，当前任务数: ${this.frameBuffers.size}`);\n        this.frameBuffers.clear();\n        this.frameSubscriptions.clear();\n\n        // 取消所有订阅\n        this.log(`取消所有订阅，当前订阅数: ${this.subscriptions.size}`);\n        this.subscriptions.forEach((subscription, topic) => {\n          try {\n            this.log(`取消订阅: ${topic}, ID: ${subscription.id}`);\n            subscription.unsubscribe();\n          } catch (error) {\n            this.logError(`取消订阅 ${topic} 失败`, error);\n          }\n        });\n        this.subscriptions.clear();\n        this.log('所有订阅已清除');\n\n        // 断开连接\n        this.log('执行STOMP客户端断开连接');\n        this.client.deactivate();\n        this.connected = false;\n        this.connectionPromise = null;\n        this.log('STOMP客户端已成功断开连接');\n      } catch (error) {\n        this.logError('断开STOMP连接过程中发生错误', error);\n        // 重置状态\n        this.connected = false;\n        this.connectionPromise = null;\n        this.frameBuffers.clear();\n        this.frameSubscriptions.clear();\n      }\n    } else {\n      this.log('STOMP客户端未连接或不存在，无需断开');\n    }\n  }\n}\n\n// 导出单例实例\nexport default new StompService();", "map": {"version": 3, "names": ["SockJS", "Client", "SOCKJS_ENDPOINT", "STOMP_TOPIC_PREFIX", "StompService", "constructor", "client", "subscriptions", "Map", "connected", "connectionPromise", "connectAttempts", "debug", "frameSubscriptions", "frameBuffers", "maxFrameBufferSize", "networkStats", "frameReceiveRate", "averageFrameSize", "connectionQuality", "lastReceiveTime", "frameCount", "totalFrameSize", "log", "message", "data", "logError", "error", "init", "Promise", "resolve", "reject", "socket", "onopen", "onclose", "event", "code", "reason", "onerror", "webSocketFactory", "str", "console", "reconnectDelay", "heartbeatIncoming", "heartbeatOutgoing", "onConnect", "frame", "onStompError", "Error", "headers", "onDisconnect", "onWebSocketError", "onWebSocketClose", "activate", "subscribe", "topic", "callback", "fullTopic", "has", "get", "subscription", "JSON", "parse", "body", "set", "id", "unsubscribe", "delete", "subscribeFrameUpdates", "taskId", "frameCallback", "frameTopic", "ensureConnection", "frameData", "frameNumber", "handleFrameData", "includes", "reconnect", "reconnectError", "subscribeProgressUpdates", "progressCallback", "progressTopic", "progressData", "progress", "subscriptionKey", "type", "updateNetworkStats", "validateFrameData", "addFrameToBuffer", "now", "Date", "frameSize", "imageData", "length", "timeDiff", "alpha", "currentRate", "evaluateConnectionQuality", "toFixed", "getNetworkStats", "resetNetworkStats", "buffer", "push", "receivedAt", "shift", "get<PERSON>rame<PERSON>uffer", "clear<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "progressSubscriptionKey", "getFrameStats", "hasSubscription", "latestFrame", "oldestFrame", "deactivate", "e", "subscribeFourWayFrameUpdates", "directions", "direction", "directionTaskId", "framesTopic", "framesSubscription", "handleFourWayFrameData", "progressSubscription", "frames", "subscribeDirectionFrameUpdates", "directionTopic", "detectionCount", "hasImageData", "imageDataLength", "imageDataType", "startsWith", "timestamp", "validateFourWayFrameData", "warn", "validations", "field", "value", "validation", "expected", "actual", "validDirections", "getFourWayFrameStats", "stats", "totalFrames", "clearFourWayFrameBuffer", "fourWaySubscriptionKey", "directionSubscriptionKey", "disconnect", "size", "clear", "for<PERSON>ach"], "sources": ["D:/code/nvm/trafficsystem/src/utils/stomp-service.js"], "sourcesContent": ["import SockJS from 'sockjs-client';\nimport { Client } from '@stomp/stompjs';\nimport { SOCKJS_ENDPOINT, STOMP_TOPIC_PREFIX } from '@/config';\n\n/**\n * STOMP客户端服务\n * 用于管理WebSocket连接和消息订阅\n */\nclass StompService {\n  constructor() {\n    this.client = null;\n    this.subscriptions = new Map();\n    this.connected = false;\n    this.connectionPromise = null;\n    this.connectAttempts = 0; // 连接尝试计数\n    this.debug = true; // 启用详细调试\n\n    // 帧数据管理\n    this.frameSubscriptions = new Map(); // 帧数据订阅\n    this.frameBuffers = new Map(); // 任务帧缓冲区\n    this.maxFrameBufferSize = 50; // 每个任务最大缓冲帧数\n\n    // 网络性能监控\n    this.networkStats = {\n      frameReceiveRate: 0, // 帧接收速率（帧/秒）\n      averageFrameSize: 0, // 平均帧大小（KB）\n      connectionQuality: 'good', // 连接质量：good, fair, poor\n      lastReceiveTime: 0,\n      frameCount: 0,\n      totalFrameSize: 0\n    };\n  }\n\n  /**\n   * 记录调试信息\n   * @param {string} message - 调试消息\n   * @param {Object} data - 附加数据\n   */\n  log(message, data = null) {\n    // 生产环境下静默处理\n  }\n\n  /**\n   * 记录错误信息\n   * @param {string} message - 错误消息\n   * @param {Error} error - 错误对象\n   */\n  logError(message, error = null) {\n    // 生产环境下静默处理\n  }\n\n  /**\n   * 初始化STOMP客户端\n   * @returns {Promise} 连接成功的Promise\n   */\n  init() {\n    if (this.connectionPromise) {\n      this.log('复用现有连接Promise');\n      return this.connectionPromise;\n    }\n\n    this.connectAttempts++;\n    this.log(`开始初始化STOMP客户端 (尝试 #${this.connectAttempts})`);\n    this.log(`连接端点: ${SOCKJS_ENDPOINT}`);\n\n    this.connectionPromise = new Promise((resolve, reject) => {\n      try {\n        // 创建SockJS连接\n        this.log(`尝试创建SockJS连接到 ${SOCKJS_ENDPOINT}`);\n        const socket = new SockJS(SOCKJS_ENDPOINT);\n        \n        socket.onopen = () => {\n          this.log('SockJS连接已打开');\n        };\n        \n        socket.onclose = (event) => {\n          this.logError(`SockJS连接已关闭，code: ${event.code}, reason: ${event.reason}`);\n        };\n        \n        socket.onerror = (error) => {\n          this.logError('SockJS连接错误', error);\n        };\n        \n        // 创建STOMP客户端\n        this.log('创建STOMP客户端');\n        this.client = new Client({\n          webSocketFactory: () => socket,\n          debug: (str) => {\n            if (this.debug) {\n              console.debug('[STOMP DEBUG] ' + str);\n            }\n          },\n          reconnectDelay: 5000, // 5秒重连\n          heartbeatIncoming: 4000,\n          heartbeatOutgoing: 4000\n        });\n\n        // 连接成功回调\n        this.client.onConnect = (frame) => {\n          this.log('STOMP连接已建立', frame);\n          this.connected = true;\n          this.connectAttempts = 0; // 重置连接尝试计数\n          resolve(true);\n        };\n\n        // 连接错误回调\n        this.client.onStompError = (frame) => {\n          this.logError('STOMP协议错误', frame);\n          reject(new Error('STOMP协议错误: ' + frame.headers.message));\n        };\n\n        // 连接断开回调\n        this.client.onDisconnect = () => {\n          this.log('STOMP连接已断开');\n          this.connected = false;\n          this.connectionPromise = null;\n        };\n        \n        // WebSocket错误回调\n        this.client.onWebSocketError = (event) => {\n          this.logError('WebSocket错误', event);\n        };\n        \n        // WebSocket关闭回调\n        this.client.onWebSocketClose = (event) => {\n          this.logError(`WebSocket连接关闭: code=${event.code}, reason=${event.reason}`, event);\n        };\n\n        // 激活连接\n        this.log('激活STOMP客户端连接');\n        this.client.activate();\n      } catch (error) {\n        this.logError('STOMP初始化过程中发生错误', error);\n        this.connectionPromise = null;\n        reject(error);\n      }\n    });\n\n    return this.connectionPromise;\n  }\n\n  /**\n   * 订阅主题\n   * @param {string} topic - 主题名称，不含前缀\n   * @param {Function} callback - 收到消息的回调函数\n   * @returns {Promise<Object>} 订阅对象\n   */\n  async subscribe(topic, callback) {\n    this.log(`尝试订阅主题: ${topic}`);\n    \n    if (!this.connected) {\n      this.log('STOMP客户端未连接，尝试初始化连接');\n      try {\n        await this.init();\n      } catch (error) {\n        this.logError('订阅前初始化STOMP客户端失败', error);\n        throw error;\n      }\n    }\n\n    const fullTopic = `${STOMP_TOPIC_PREFIX}/${topic}`;\n    this.log(`完整主题路径: ${fullTopic}`);\n    \n    if (this.subscriptions.has(fullTopic)) {\n      this.log(`已存在对 ${fullTopic} 的订阅，复用现有订阅`);\n      return this.subscriptions.get(fullTopic);\n    }\n\n    try {\n      this.log(`订阅主题: ${fullTopic}`);\n      const subscription = this.client.subscribe(fullTopic, (message) => {\n        try {\n          this.log(`收到主题 ${fullTopic} 的消息`);\n          const data = JSON.parse(message.body);\n          callback(data);\n        } catch (error) {\n          this.logError(`解析 ${fullTopic} 的消息失败`, error);\n        }\n      });\n\n      this.subscriptions.set(fullTopic, subscription);\n      this.log(`订阅成功: ${fullTopic}, ID: ${subscription.id}`);\n      return subscription;\n    } catch (error) {\n      this.logError(`订阅 ${fullTopic} 失败`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * 取消订阅\n   * @param {string} topic - 主题名称，不含前缀\n   */\n  unsubscribe(topic) {\n    const fullTopic = `${STOMP_TOPIC_PREFIX}/${topic}`;\n    this.log(`尝试取消订阅: ${fullTopic}`);\n    \n    if (this.subscriptions.has(fullTopic)) {\n      try {\n        const subscription = this.subscriptions.get(fullTopic);\n        this.log(`找到订阅，ID: ${subscription.id}，开始取消订阅`);\n        subscription.unsubscribe();\n        this.subscriptions.delete(fullTopic);\n        this.log(`已成功取消订阅主题: ${fullTopic}`);\n      } catch (error) {\n        this.logError(`取消订阅 ${fullTopic} 失败`, error);\n        // 即使发生错误，也移除订阅记录\n        this.subscriptions.delete(fullTopic);\n      }\n    } else {\n      this.log(`未找到对 ${fullTopic} 的订阅记录，无需取消`);\n    }\n  }\n\n  /**\n   * 订阅任务的实时帧数据\n   * @param {string} taskId - 任务ID\n   * @param {Function} frameCallback - 接收帧数据的回调函数\n   * @returns {Promise<Object>} 订阅对象\n   */\n  async subscribeFrameUpdates(taskId, frameCallback) {\n    this.log(`订阅任务 ${taskId} 的实时帧数据`);\n\n    // 确保主题路径与后端一致\n    const frameTopic = `frame-updates/${taskId}`;\n\n    try {\n      // 确保WebSocket连接已建立，如果断开则重连\n      await this.ensureConnection();\n\n      // 初始化任务的帧缓冲区\n      if (!this.frameBuffers.has(taskId)) {\n        this.frameBuffers.set(taskId, []);\n      }\n\n      this.log(`准备订阅帧更新主题: ${frameTopic}`);\n\n      // 订阅帧更新主题\n      const subscription = await this.subscribe(frameTopic, (frameData) => {\n        this.log(`收到帧数据: 任务${taskId}, 帧${frameData.frameNumber || 'unknown'}`);\n        this.handleFrameData(taskId, frameData, frameCallback);\n      });\n\n      // 记录帧订阅\n      this.frameSubscriptions.set(taskId, subscription);\n\n      this.log(`✓ 成功订阅任务 ${taskId} 的帧数据，主题: ${frameTopic}`);\n      return subscription;\n\n    } catch (error) {\n      this.logError(`✗ 订阅任务 ${taskId} 的帧数据失败`, error);\n\n      // 如果是连接问题，尝试重连后再次订阅\n      if (error.message && error.message.includes('STOMP connection')) {\n        this.log('检测到连接问题，尝试重连...');\n        try {\n          await this.reconnect();\n          return await this.subscribeFrameUpdates(taskId, frameCallback);\n        } catch (reconnectError) {\n          this.logError('重连失败', reconnectError);\n          throw reconnectError;\n        }\n      }\n\n      throw error;\n    }\n  }\n\n  /**\n   * 订阅任务的进度更新\n   * @param {string} taskId - 任务ID\n   * @param {Function} progressCallback - 接收进度数据的回调函数\n   * @returns {Promise<Object>} 订阅对象\n   */\n  async subscribeProgressUpdates(taskId, progressCallback) {\n    this.log(`订阅任务 ${taskId} 的进度更新`);\n\n    // 确保主题路径与后端一致\n    const progressTopic = `video-progress/${taskId}`;\n\n    try {\n      // 确保WebSocket连接已建立\n      await this.ensureConnection();\n\n      this.log(`准备订阅进度更新主题: ${progressTopic}`);\n\n      // 订阅进度更新主题\n      const subscription = await this.subscribe(progressTopic, (progressData) => {\n        this.log(`收到进度数据: 任务${taskId}, 进度${progressData.progress || 'unknown'}%`);\n        if (typeof progressCallback === 'function') {\n          progressCallback(progressData);\n        }\n      });\n\n      // 记录进度订阅\n      const subscriptionKey = `progress_${taskId}`;\n      this.frameSubscriptions.set(subscriptionKey, {\n        subscription,\n        taskId,\n        type: 'progress'\n      });\n\n      this.log(`✓ 成功订阅任务 ${taskId} 的进度更新，主题: ${progressTopic}`);\n      return subscription;\n\n    } catch (error) {\n      this.logError(`✗ 订阅任务 ${taskId} 的进度更新失败`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * 处理接收到的帧数据\n   * @param {string} taskId - 任务ID\n   * @param {Object} frameData - 帧数据\n   * @param {Function} callback - 回调函数\n   */\n  handleFrameData(taskId, frameData, callback) {\n    try {\n      this.log(`处理任务 ${taskId} 的帧数据: 帧${frameData.frameNumber}`);\n\n      // 更新网络性能统计\n      this.updateNetworkStats(frameData);\n\n      // 验证帧数据\n      if (!this.validateFrameData(frameData)) {\n        this.logError(`任务 ${taskId} 的帧数据格式无效`, frameData);\n        return;\n      }\n\n      // 添加到缓冲区\n      this.addFrameToBuffer(taskId, frameData);\n\n      // 调用回调函数\n      if (typeof callback === 'function') {\n        callback(frameData);\n      }\n\n    } catch (error) {\n      this.logError(`处理任务 ${taskId} 的帧数据失败`, error);\n    }\n  }\n\n  /**\n   * 更新网络性能统计\n   * @param {Object} frameData - 帧数据\n   */\n  updateNetworkStats(frameData) {\n    try {\n      const now = Date.now();\n\n      // 计算帧大小（估算）\n      const frameSize = frameData.imageData ? frameData.imageData.length * 0.75 / 1024 : 0; // Base64转字节再转KB\n\n      // 更新统计\n      this.networkStats.frameCount++;\n      this.networkStats.totalFrameSize += frameSize;\n      this.networkStats.averageFrameSize = this.networkStats.totalFrameSize / this.networkStats.frameCount;\n\n      // 计算接收速率\n      if (this.networkStats.lastReceiveTime > 0) {\n        const timeDiff = (now - this.networkStats.lastReceiveTime) / 1000; // 转换为秒\n        if (timeDiff > 0) {\n          // 使用指数移动平均\n          const alpha = 0.3;\n          const currentRate = 1 / timeDiff;\n          this.networkStats.frameReceiveRate =\n            alpha * currentRate + (1 - alpha) * this.networkStats.frameReceiveRate;\n        }\n      }\n\n      this.networkStats.lastReceiveTime = now;\n\n      // 评估连接质量\n      this.evaluateConnectionQuality();\n\n      this.log(`网络统计更新: 速率=${this.networkStats.frameReceiveRate.toFixed(2)}fps, ` +\n               `平均大小=${this.networkStats.averageFrameSize.toFixed(1)}KB, ` +\n               `质量=${this.networkStats.connectionQuality}`);\n\n    } catch (error) {\n      this.logError('更新网络统计失败', error);\n    }\n  }\n\n  /**\n   * 评估连接质量\n   */\n  evaluateConnectionQuality() {\n    const { frameReceiveRate, averageFrameSize } = this.networkStats;\n\n    // 根据帧接收速率和平均帧大小评估质量\n    if (frameReceiveRate >= 0.8 && averageFrameSize < 200) {\n      this.networkStats.connectionQuality = 'good';\n    } else if (frameReceiveRate >= 0.5 && averageFrameSize < 300) {\n      this.networkStats.connectionQuality = 'fair';\n    } else {\n      this.networkStats.connectionQuality = 'poor';\n    }\n  }\n\n  /**\n   * 获取网络性能统计\n   * @returns {Object} 网络统计信息\n   */\n  getNetworkStats() {\n    return { ...this.networkStats };\n  }\n\n  /**\n   * 重置网络统计\n   */\n  resetNetworkStats() {\n    this.networkStats = {\n      frameReceiveRate: 0,\n      averageFrameSize: 0,\n      connectionQuality: 'good',\n      lastReceiveTime: 0,\n      frameCount: 0,\n      totalFrameSize: 0\n    };\n    this.log('网络统计已重置');\n  }\n\n  /**\n   * 验证帧数据格式\n   * @param {Object} frameData - 帧数据\n   * @returns {boolean} 是否有效\n   */\n  validateFrameData(frameData) {\n    return frameData &&\n           typeof frameData.taskId === 'string' &&\n           typeof frameData.frameNumber === 'number' &&\n           typeof frameData.imageData === 'string' &&\n           frameData.type === 'frame_update';\n  }\n\n  /**\n   * 添加帧到缓冲区\n   * @param {string} taskId - 任务ID\n   * @param {Object} frameData - 帧数据\n   */\n  addFrameToBuffer(taskId, frameData) {\n    try {\n      let buffer = this.frameBuffers.get(taskId);\n      if (!buffer) {\n        buffer = [];\n        this.frameBuffers.set(taskId, buffer);\n      }\n\n      // 添加帧数据\n      buffer.push({\n        ...frameData,\n        receivedAt: Date.now()\n      });\n\n      // 限制缓冲区大小\n      while (buffer.length > this.maxFrameBufferSize) {\n        buffer.shift(); // 移除最旧的帧\n      }\n\n      this.log(`任务 ${taskId} 帧缓冲区大小: ${buffer.length}`);\n\n    } catch (error) {\n      this.logError(`添加帧到缓冲区失败`, error);\n    }\n  }\n\n  /**\n   * 获取任务的缓冲帧数据\n   * @param {string} taskId - 任务ID\n   * @returns {Array} 帧数据数组\n   */\n  getFrameBuffer(taskId) {\n    return this.frameBuffers.get(taskId) || [];\n  }\n\n  /**\n   * 清理任务的帧数据\n   * @param {string} taskId - 任务ID\n   */\n  clearFrameBuffer(taskId) {\n    this.log(`清理任务 ${taskId} 的帧缓冲区`);\n\n    // 取消帧订阅\n    if (this.frameSubscriptions.has(taskId)) {\n      try {\n        const frameTopic = `frame-updates/${taskId}`;\n        this.unsubscribe(frameTopic);\n        this.frameSubscriptions.delete(taskId);\n      } catch (error) {\n        this.logError(`取消任务 ${taskId} 的帧订阅失败`, error);\n      }\n    }\n\n    // 取消进度订阅\n    const progressSubscriptionKey = `progress_${taskId}`;\n    if (this.frameSubscriptions.has(progressSubscriptionKey)) {\n      try {\n        const progressTopic = `video-progress/${taskId}`;\n        this.unsubscribe(progressTopic);\n        this.frameSubscriptions.delete(progressSubscriptionKey);\n      } catch (error) {\n        this.logError(`取消任务 ${taskId} 的进度订阅失败`, error);\n      }\n    }\n\n    // 清理帧缓冲区\n    this.frameBuffers.delete(taskId);\n  }\n\n  /**\n   * 获取帧数据统计信息\n   * @param {string} taskId - 任务ID\n   * @returns {Object} 统计信息\n   */\n  getFrameStats(taskId) {\n    const buffer = this.frameBuffers.get(taskId) || [];\n    const hasSubscription = this.frameSubscriptions.has(taskId);\n\n    return {\n      taskId,\n      frameCount: buffer.length,\n      hasSubscription,\n      latestFrame: buffer.length > 0 ? buffer[buffer.length - 1] : null,\n      oldestFrame: buffer.length > 0 ? buffer[0] : null\n    };\n  }\n\n  /**\n   * 确保WebSocket连接已建立\n   */\n  async ensureConnection() {\n    if (!this.connected || !this.client || !this.client.connected) {\n      this.log('连接已断开，尝试重新连接...');\n      await this.reconnect();\n    }\n  }\n\n  /**\n   * 重新连接WebSocket\n   */\n  async reconnect() {\n    this.log('开始重新连接WebSocket...');\n\n    try {\n      // 先断开现有连接\n      if (this.client) {\n        try {\n          this.client.deactivate();\n        } catch (e) {\n          // 忽略断开连接时的错误\n        }\n      }\n\n      // 重置状态\n      this.connected = false;\n      this.connectionPromise = null;\n      this.connectAttempts = 0;\n\n      // 重新初始化连接\n      await this.init();\n\n      this.log('✓ WebSocket重连成功');\n\n    } catch (error) {\n      this.logError('WebSocket重连失败', error);\n      throw error;\n    }\n  }\n\n  // ==================== 四方向交通分析WebSocket支持 ====================\n\n  /**\n   * 订阅四方向任务的实时帧数据\n   * @param {string} taskId - 任务ID\n   * @param {Function} frameCallback - 接收帧数据的回调函数\n   * @param {Function} progressCallback - 接收进度数据的回调函数\n   * @returns {Promise<Object>} 订阅对象\n   */\n  async subscribeFourWayFrameUpdates(taskId, frameCallback, progressCallback) {\n    this.log(`订阅四方向任务 ${taskId} 的实时帧数据和进度`);\n\n    try {\n      // 确保WebSocket连接已建立\n      await this.ensureConnection();\n\n      // 初始化四方向任务的帧缓冲区\n      const directions = ['east', 'south', 'west', 'north'];\n      for (const direction of directions) {\n        const directionTaskId = `${direction}_${taskId}`;\n        if (!this.frameBuffers.has(directionTaskId)) {\n          this.frameBuffers.set(directionTaskId, []);\n        }\n      }\n\n      // 订阅四方向帧更新主题\n      const framesTopic = `four-way-frames/${taskId}`;\n      this.log(`准备订阅四方向帧更新主题: ${framesTopic}`);\n\n      const framesSubscription = await this.subscribe(framesTopic, (frameData) => {\n        this.log(`收到四方向帧数据: 任务${taskId}, 方向${frameData.direction}, 帧${frameData.frameNumber || 'unknown'}`);\n        this.handleFourWayFrameData(taskId, frameData, frameCallback);\n      });\n\n      // 订阅四方向进度更新主题\n      const progressTopic = `four-way-progress/${taskId}`;\n      const progressSubscription = await this.subscribe(progressTopic, (progressData) => {\n        this.log(`收到四方向进度数据: 任务${taskId}, 方向${progressData.direction}, 进度${progressData.progress}%`);\n        if (typeof progressCallback === 'function') {\n          progressCallback(progressData);\n        }\n      });\n\n      // 记录四方向订阅\n      const subscriptionKey = `four_way_${taskId}`;\n      this.frameSubscriptions.set(subscriptionKey, {\n        frames: framesSubscription,\n        progress: progressSubscription,\n        taskId,\n        type: 'four_way'\n      });\n\n      this.log(`✓ 成功订阅四方向任务 ${taskId} 的帧数据和进度`);\n      return {\n        frames: framesSubscription,\n        progress: progressSubscription\n      };\n\n    } catch (error) {\n      this.logError(`✗ 订阅四方向任务 ${taskId} 的数据失败`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * 订阅特定方向的帧数据\n   * @param {string} taskId - 任务ID\n   * @param {string} direction - 方向（east, south, west, north）\n   * @param {Function} frameCallback - 接收帧数据的回调函数\n   * @returns {Promise<Object>} 订阅对象\n   */\n  async subscribeDirectionFrameUpdates(taskId, direction, frameCallback) {\n    this.log(`订阅任务 ${taskId} 的 ${direction} 方向帧数据`);\n\n    try {\n      // 确保WebSocket连接已建立\n      await this.ensureConnection();\n\n      // 初始化方向帧缓冲区\n      const directionTaskId = `${direction}_${taskId}`;\n      if (!this.frameBuffers.has(directionTaskId)) {\n        this.frameBuffers.set(directionTaskId, []);\n      }\n\n      // 订阅特定方向的帧更新主题\n      const directionTopic = `four-way-frames/${taskId}/${direction}`;\n      const subscription = await this.subscribe(directionTopic, (frameData) => {\n        this.log(`收到${direction}方向帧数据: 任务${taskId}, 帧${frameData.frameNumber || 'unknown'}`);\n        this.handleFourWayFrameData(taskId, frameData, frameCallback);\n      });\n\n      // 记录方向订阅\n      const subscriptionKey = `direction_${direction}_${taskId}`;\n      this.frameSubscriptions.set(subscriptionKey, {\n        subscription,\n        taskId,\n        direction,\n        type: 'direction'\n      });\n\n      this.log(`✓ 成功订阅任务 ${taskId} 的 ${direction} 方向帧数据`);\n      return subscription;\n\n    } catch (error) {\n      this.logError(`✗ 订阅任务 ${taskId} 的 ${direction} 方向帧数据失败`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * 处理接收到的四方向帧数据\n   * @param {string} taskId - 任务ID\n   * @param {Object} frameData - 帧数据\n   * @param {Function} callback - 回调函数\n   */\n  handleFourWayFrameData(taskId, frameData, callback) {\n    try {\n      this.log(`处理四方向任务 ${taskId} 的帧数据: 方向${frameData.direction}, 帧${frameData.frameNumber}`);\n\n      // 详细的帧数据日志\n      console.log('🔍 四方向帧数据详情:', {\n        taskId,\n        direction: frameData.direction,\n        frameNumber: frameData.frameNumber,\n        detectionCount: frameData.detectionCount,\n        hasImageData: !!frameData.imageData,\n        imageDataLength: frameData.imageData ? frameData.imageData.length : 0,\n        imageDataType: frameData.imageData ? (frameData.imageData.startsWith('data:image/') ? 'data-url' : 'base64') : 'none',\n        timestamp: frameData.timestamp\n      });\n\n      // 更新网络性能统计\n      this.updateNetworkStats(frameData);\n\n      // 验证四方向帧数据\n      if (!this.validateFourWayFrameData(frameData)) {\n        this.logError(`任务 ${taskId} 的四方向帧数据格式无效`, frameData);\n        return;\n      }\n\n      // 添加到方向缓冲区\n      const directionTaskId = `${frameData.direction}_${taskId}`;\n      this.addFrameToBuffer(directionTaskId, frameData);\n\n      // 调用回调函数\n      if (typeof callback === 'function') {\n        console.log(`📞 调用四方向帧数据回调函数: ${frameData.direction}方向`);\n        callback(frameData);\n      } else {\n        console.warn(`⚠️ 四方向帧数据回调函数不可用: ${typeof callback}`);\n      }\n\n    } catch (error) {\n      this.logError(`处理四方向任务 ${taskId} 的帧数据失败`, error);\n    }\n  }\n\n  /**\n   * 验证四方向帧数据格式\n   * @param {Object} frameData - 帧数据\n   * @returns {boolean} 是否有效\n   */\n  validateFourWayFrameData(frameData) {\n    if (!frameData) {\n      console.warn('❌ 四方向帧数据验证失败: frameData为空');\n      return false;\n    }\n\n    const validations = [\n      { field: 'taskId', value: frameData.taskId, type: 'string' },\n      { field: 'direction', value: frameData.direction, type: 'string' },\n      { field: 'frameNumber', value: frameData.frameNumber, type: 'number' },\n      { field: 'imageData', value: frameData.imageData, type: 'string' }\n    ];\n\n    for (const validation of validations) {\n      if (typeof validation.value !== validation.type) {\n        console.warn(`❌ 四方向帧数据验证失败: ${validation.field} 类型错误`, {\n          expected: validation.type,\n          actual: typeof validation.value,\n          value: validation.value\n        });\n        return false;\n      }\n    }\n\n    // 验证方向值\n    if (!['east', 'south', 'west', 'north'].includes(frameData.direction)) {\n      console.warn(`❌ 四方向帧数据验证失败: 无效的方向值`, {\n        direction: frameData.direction,\n        validDirections: ['east', 'south', 'west', 'north']\n      });\n      return false;\n    }\n\n    // 验证图像数据格式（可以是base64或data-url）\n    if (!frameData.imageData || frameData.imageData.length === 0) {\n      console.warn(`❌ 四方向帧数据验证失败: imageData为空`);\n      return false;\n    }\n\n    console.log('✅ 四方向帧数据验证通过:', {\n      taskId: frameData.taskId,\n      direction: frameData.direction,\n      frameNumber: frameData.frameNumber,\n      imageDataLength: frameData.imageData.length\n    });\n\n    return true;\n  }\n\n  /**\n   * 获取四方向任务的帧数据统计\n   * @param {string} taskId - 任务ID\n   * @returns {Object} 统计信息\n   */\n  getFourWayFrameStats(taskId) {\n    const directions = ['east', 'south', 'west', 'north'];\n    const stats = {\n      taskId,\n      directions: {},\n      totalFrames: 0,\n      hasSubscription: this.frameSubscriptions.has(`four_way_${taskId}`)\n    };\n\n    for (const direction of directions) {\n      const directionTaskId = `${direction}_${taskId}`;\n      const buffer = this.frameBuffers.get(directionTaskId) || [];\n\n      stats.directions[direction] = {\n        frameCount: buffer.length,\n        latestFrame: buffer.length > 0 ? buffer[buffer.length - 1] : null,\n        oldestFrame: buffer.length > 0 ? buffer[0] : null\n      };\n\n      stats.totalFrames += buffer.length;\n    }\n\n    return stats;\n  }\n\n  /**\n   * 清理四方向任务的帧数据\n   * @param {string} taskId - 任务ID\n   */\n  clearFourWayFrameBuffer(taskId) {\n    this.log(`清理四方向任务 ${taskId} 的帧缓冲区`);\n\n    try {\n      // 取消四方向订阅\n      const fourWaySubscriptionKey = `four_way_${taskId}`;\n      if (this.frameSubscriptions.has(fourWaySubscriptionKey)) {\n        const subscriptions = this.frameSubscriptions.get(fourWaySubscriptionKey);\n\n        // 取消帧订阅\n        if (subscriptions.frames) {\n          const framesTopic = `four-way-frames/${taskId}`;\n          this.unsubscribe(framesTopic);\n        }\n\n        // 取消进度订阅\n        if (subscriptions.progress) {\n          const progressTopic = `four-way-progress/${taskId}`;\n          this.unsubscribe(progressTopic);\n        }\n\n        this.frameSubscriptions.delete(fourWaySubscriptionKey);\n      }\n\n      // 取消各方向订阅\n      const directions = ['east', 'south', 'west', 'north'];\n      for (const direction of directions) {\n        const directionSubscriptionKey = `direction_${direction}_${taskId}`;\n        if (this.frameSubscriptions.has(directionSubscriptionKey)) {\n          const directionTopic = `four-way-frames/${taskId}/${direction}`;\n          this.unsubscribe(directionTopic);\n          this.frameSubscriptions.delete(directionSubscriptionKey);\n        }\n\n        // 清理方向帧缓冲区\n        const directionTaskId = `${direction}_${taskId}`;\n        this.frameBuffers.delete(directionTaskId);\n      }\n\n      this.log(`✓ 已清理四方向任务 ${taskId} 的所有帧数据`);\n\n    } catch (error) {\n      this.logError(`清理四方向任务 ${taskId} 的帧数据失败`, error);\n    }\n  }\n\n  /**\n   * 断开连接\n   */\n  disconnect() {\n    if (this.client && this.connected) {\n      this.log('开始断开STOMP客户端连接');\n\n      try {\n        // 清理所有帧数据\n        this.log(`清理所有帧缓冲区，当前任务数: ${this.frameBuffers.size}`);\n        this.frameBuffers.clear();\n        this.frameSubscriptions.clear();\n\n        // 取消所有订阅\n        this.log(`取消所有订阅，当前订阅数: ${this.subscriptions.size}`);\n        this.subscriptions.forEach((subscription, topic) => {\n          try {\n            this.log(`取消订阅: ${topic}, ID: ${subscription.id}`);\n            subscription.unsubscribe();\n          } catch (error) {\n            this.logError(`取消订阅 ${topic} 失败`, error);\n          }\n        });\n        this.subscriptions.clear();\n        this.log('所有订阅已清除');\n\n        // 断开连接\n        this.log('执行STOMP客户端断开连接');\n        this.client.deactivate();\n        this.connected = false;\n        this.connectionPromise = null;\n        this.log('STOMP客户端已成功断开连接');\n      } catch (error) {\n        this.logError('断开STOMP连接过程中发生错误', error);\n        // 重置状态\n        this.connected = false;\n        this.connectionPromise = null;\n        this.frameBuffers.clear();\n        this.frameSubscriptions.clear();\n      }\n    } else {\n      this.log('STOMP客户端未连接或不存在，无需断开');\n    }\n  }\n}\n\n// 导出单例实例\nexport default new StompService(); "], "mappings": ";;;AAAA,OAAOA,MAAM,MAAM,eAAe;AAClC,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,eAAe,EAAEC,kBAAkB,QAAQ,UAAU;;AAE9D;AACA;AACA;AACA;AACA,MAAMC,YAAY,CAAC;EACjBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC,CAAC;IAC1B,IAAI,CAACC,KAAK,GAAG,IAAI,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACC,kBAAkB,GAAG,IAAIL,GAAG,CAAC,CAAC,CAAC,CAAC;IACrC,IAAI,CAACM,YAAY,GAAG,IAAIN,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/B,IAAI,CAACO,kBAAkB,GAAG,EAAE,CAAC,CAAC;;IAE9B;IACA,IAAI,CAACC,YAAY,GAAG;MAClBC,gBAAgB,EAAE,CAAC;MAAE;MACrBC,gBAAgB,EAAE,CAAC;MAAE;MACrBC,iBAAiB,EAAE,MAAM;MAAE;MAC3BC,eAAe,EAAE,CAAC;MAClBC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAE;IAClB,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;EACEC,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,IAAI,EAAE;IACxB;EAAA;;EAGF;AACF;AACA;AACA;AACA;EACEC,QAAQA,CAACF,OAAO,EAAEG,KAAK,GAAG,IAAI,EAAE;IAC9B;EAAA;;EAGF;AACF;AACA;AACA;EACEC,IAAIA,CAAA,EAAG;IACL,IAAI,IAAI,CAAClB,iBAAiB,EAAE;MAC1B,IAAI,CAACa,GAAG,CAAC,eAAe,CAAC;MACzB,OAAO,IAAI,CAACb,iBAAiB;IAC/B;IAEA,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACY,GAAG,CAAC,sBAAsB,IAAI,CAACZ,eAAe,GAAG,CAAC;IACvD,IAAI,CAACY,GAAG,CAAC,SAASrB,eAAe,EAAE,CAAC;IAEpC,IAAI,CAACQ,iBAAiB,GAAG,IAAImB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACxD,IAAI;QACF;QACA,IAAI,CAACR,GAAG,CAAC,iBAAiBrB,eAAe,EAAE,CAAC;QAC5C,MAAM8B,MAAM,GAAG,IAAIhC,MAAM,CAACE,eAAe,CAAC;QAE1C8B,MAAM,CAACC,MAAM,GAAG,MAAM;UACpB,IAAI,CAACV,GAAG,CAAC,aAAa,CAAC;QACzB,CAAC;QAEDS,MAAM,CAACE,OAAO,GAAIC,KAAK,IAAK;UAC1B,IAAI,CAACT,QAAQ,CAAC,qBAAqBS,KAAK,CAACC,IAAI,aAAaD,KAAK,CAACE,MAAM,EAAE,CAAC;QAC3E,CAAC;QAEDL,MAAM,CAACM,OAAO,GAAIX,KAAK,IAAK;UAC1B,IAAI,CAACD,QAAQ,CAAC,YAAY,EAAEC,KAAK,CAAC;QACpC,CAAC;;QAED;QACA,IAAI,CAACJ,GAAG,CAAC,YAAY,CAAC;QACtB,IAAI,CAACjB,MAAM,GAAG,IAAIL,MAAM,CAAC;UACvBsC,gBAAgB,EAAEA,CAAA,KAAMP,MAAM;UAC9BpB,KAAK,EAAG4B,GAAG,IAAK;YACd,IAAI,IAAI,CAAC5B,KAAK,EAAE;cACd6B,OAAO,CAAC7B,KAAK,CAAC,gBAAgB,GAAG4B,GAAG,CAAC;YACvC;UACF,CAAC;UACDE,cAAc,EAAE,IAAI;UAAE;UACtBC,iBAAiB,EAAE,IAAI;UACvBC,iBAAiB,EAAE;QACrB,CAAC,CAAC;;QAEF;QACA,IAAI,CAACtC,MAAM,CAACuC,SAAS,GAAIC,KAAK,IAAK;UACjC,IAAI,CAACvB,GAAG,CAAC,YAAY,EAAEuB,KAAK,CAAC;UAC7B,IAAI,CAACrC,SAAS,GAAG,IAAI;UACrB,IAAI,CAACE,eAAe,GAAG,CAAC,CAAC,CAAC;UAC1BmB,OAAO,CAAC,IAAI,CAAC;QACf,CAAC;;QAED;QACA,IAAI,CAACxB,MAAM,CAACyC,YAAY,GAAID,KAAK,IAAK;UACpC,IAAI,CAACpB,QAAQ,CAAC,WAAW,EAAEoB,KAAK,CAAC;UACjCf,MAAM,CAAC,IAAIiB,KAAK,CAAC,aAAa,GAAGF,KAAK,CAACG,OAAO,CAACzB,OAAO,CAAC,CAAC;QAC1D,CAAC;;QAED;QACA,IAAI,CAAClB,MAAM,CAAC4C,YAAY,GAAG,MAAM;UAC/B,IAAI,CAAC3B,GAAG,CAAC,YAAY,CAAC;UACtB,IAAI,CAACd,SAAS,GAAG,KAAK;UACtB,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC/B,CAAC;;QAED;QACA,IAAI,CAACJ,MAAM,CAAC6C,gBAAgB,GAAIhB,KAAK,IAAK;UACxC,IAAI,CAACT,QAAQ,CAAC,aAAa,EAAES,KAAK,CAAC;QACrC,CAAC;;QAED;QACA,IAAI,CAAC7B,MAAM,CAAC8C,gBAAgB,GAAIjB,KAAK,IAAK;UACxC,IAAI,CAACT,QAAQ,CAAC,uBAAuBS,KAAK,CAACC,IAAI,YAAYD,KAAK,CAACE,MAAM,EAAE,EAAEF,KAAK,CAAC;QACnF,CAAC;;QAED;QACA,IAAI,CAACZ,GAAG,CAAC,cAAc,CAAC;QACxB,IAAI,CAACjB,MAAM,CAAC+C,QAAQ,CAAC,CAAC;MACxB,CAAC,CAAC,OAAO1B,KAAK,EAAE;QACd,IAAI,CAACD,QAAQ,CAAC,iBAAiB,EAAEC,KAAK,CAAC;QACvC,IAAI,CAACjB,iBAAiB,GAAG,IAAI;QAC7BqB,MAAM,CAACJ,KAAK,CAAC;MACf;IACF,CAAC,CAAC;IAEF,OAAO,IAAI,CAACjB,iBAAiB;EAC/B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAM4C,SAASA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC/B,IAAI,CAACjC,GAAG,CAAC,WAAWgC,KAAK,EAAE,CAAC;IAE5B,IAAI,CAAC,IAAI,CAAC9C,SAAS,EAAE;MACnB,IAAI,CAACc,GAAG,CAAC,qBAAqB,CAAC;MAC/B,IAAI;QACF,MAAM,IAAI,CAACK,IAAI,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOD,KAAK,EAAE;QACd,IAAI,CAACD,QAAQ,CAAC,kBAAkB,EAAEC,KAAK,CAAC;QACxC,MAAMA,KAAK;MACb;IACF;IAEA,MAAM8B,SAAS,GAAG,GAAGtD,kBAAkB,IAAIoD,KAAK,EAAE;IAClD,IAAI,CAAChC,GAAG,CAAC,WAAWkC,SAAS,EAAE,CAAC;IAEhC,IAAI,IAAI,CAAClD,aAAa,CAACmD,GAAG,CAACD,SAAS,CAAC,EAAE;MACrC,IAAI,CAAClC,GAAG,CAAC,QAAQkC,SAAS,aAAa,CAAC;MACxC,OAAO,IAAI,CAAClD,aAAa,CAACoD,GAAG,CAACF,SAAS,CAAC;IAC1C;IAEA,IAAI;MACF,IAAI,CAAClC,GAAG,CAAC,SAASkC,SAAS,EAAE,CAAC;MAC9B,MAAMG,YAAY,GAAG,IAAI,CAACtD,MAAM,CAACgD,SAAS,CAACG,SAAS,EAAGjC,OAAO,IAAK;QACjE,IAAI;UACF,IAAI,CAACD,GAAG,CAAC,QAAQkC,SAAS,MAAM,CAAC;UACjC,MAAMhC,IAAI,GAAGoC,IAAI,CAACC,KAAK,CAACtC,OAAO,CAACuC,IAAI,CAAC;UACrCP,QAAQ,CAAC/B,IAAI,CAAC;QAChB,CAAC,CAAC,OAAOE,KAAK,EAAE;UACd,IAAI,CAACD,QAAQ,CAAC,MAAM+B,SAAS,QAAQ,EAAE9B,KAAK,CAAC;QAC/C;MACF,CAAC,CAAC;MAEF,IAAI,CAACpB,aAAa,CAACyD,GAAG,CAACP,SAAS,EAAEG,YAAY,CAAC;MAC/C,IAAI,CAACrC,GAAG,CAAC,SAASkC,SAAS,SAASG,YAAY,CAACK,EAAE,EAAE,CAAC;MACtD,OAAOL,YAAY;IACrB,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,MAAM+B,SAAS,KAAK,EAAE9B,KAAK,CAAC;MAC1C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACEuC,WAAWA,CAACX,KAAK,EAAE;IACjB,MAAME,SAAS,GAAG,GAAGtD,kBAAkB,IAAIoD,KAAK,EAAE;IAClD,IAAI,CAAChC,GAAG,CAAC,WAAWkC,SAAS,EAAE,CAAC;IAEhC,IAAI,IAAI,CAAClD,aAAa,CAACmD,GAAG,CAACD,SAAS,CAAC,EAAE;MACrC,IAAI;QACF,MAAMG,YAAY,GAAG,IAAI,CAACrD,aAAa,CAACoD,GAAG,CAACF,SAAS,CAAC;QACtD,IAAI,CAAClC,GAAG,CAAC,YAAYqC,YAAY,CAACK,EAAE,SAAS,CAAC;QAC9CL,YAAY,CAACM,WAAW,CAAC,CAAC;QAC1B,IAAI,CAAC3D,aAAa,CAAC4D,MAAM,CAACV,SAAS,CAAC;QACpC,IAAI,CAAClC,GAAG,CAAC,cAAckC,SAAS,EAAE,CAAC;MACrC,CAAC,CAAC,OAAO9B,KAAK,EAAE;QACd,IAAI,CAACD,QAAQ,CAAC,QAAQ+B,SAAS,KAAK,EAAE9B,KAAK,CAAC;QAC5C;QACA,IAAI,CAACpB,aAAa,CAAC4D,MAAM,CAACV,SAAS,CAAC;MACtC;IACF,CAAC,MAAM;MACL,IAAI,CAAClC,GAAG,CAAC,QAAQkC,SAAS,aAAa,CAAC;IAC1C;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMW,qBAAqBA,CAACC,MAAM,EAAEC,aAAa,EAAE;IACjD,IAAI,CAAC/C,GAAG,CAAC,QAAQ8C,MAAM,SAAS,CAAC;;IAEjC;IACA,MAAME,UAAU,GAAG,iBAAiBF,MAAM,EAAE;IAE5C,IAAI;MACF;MACA,MAAM,IAAI,CAACG,gBAAgB,CAAC,CAAC;;MAE7B;MACA,IAAI,CAAC,IAAI,CAAC1D,YAAY,CAAC4C,GAAG,CAACW,MAAM,CAAC,EAAE;QAClC,IAAI,CAACvD,YAAY,CAACkD,GAAG,CAACK,MAAM,EAAE,EAAE,CAAC;MACnC;MAEA,IAAI,CAAC9C,GAAG,CAAC,cAAcgD,UAAU,EAAE,CAAC;;MAEpC;MACA,MAAMX,YAAY,GAAG,MAAM,IAAI,CAACN,SAAS,CAACiB,UAAU,EAAGE,SAAS,IAAK;QACnE,IAAI,CAAClD,GAAG,CAAC,YAAY8C,MAAM,MAAMI,SAAS,CAACC,WAAW,IAAI,SAAS,EAAE,CAAC;QACtE,IAAI,CAACC,eAAe,CAACN,MAAM,EAAEI,SAAS,EAAEH,aAAa,CAAC;MACxD,CAAC,CAAC;;MAEF;MACA,IAAI,CAACzD,kBAAkB,CAACmD,GAAG,CAACK,MAAM,EAAET,YAAY,CAAC;MAEjD,IAAI,CAACrC,GAAG,CAAC,YAAY8C,MAAM,aAAaE,UAAU,EAAE,CAAC;MACrD,OAAOX,YAAY;IAErB,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,UAAU2C,MAAM,SAAS,EAAE1C,KAAK,CAAC;;MAE/C;MACA,IAAIA,KAAK,CAACH,OAAO,IAAIG,KAAK,CAACH,OAAO,CAACoD,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAC/D,IAAI,CAACrD,GAAG,CAAC,iBAAiB,CAAC;QAC3B,IAAI;UACF,MAAM,IAAI,CAACsD,SAAS,CAAC,CAAC;UACtB,OAAO,MAAM,IAAI,CAACT,qBAAqB,CAACC,MAAM,EAAEC,aAAa,CAAC;QAChE,CAAC,CAAC,OAAOQ,cAAc,EAAE;UACvB,IAAI,CAACpD,QAAQ,CAAC,MAAM,EAAEoD,cAAc,CAAC;UACrC,MAAMA,cAAc;QACtB;MACF;MAEA,MAAMnD,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMoD,wBAAwBA,CAACV,MAAM,EAAEW,gBAAgB,EAAE;IACvD,IAAI,CAACzD,GAAG,CAAC,QAAQ8C,MAAM,QAAQ,CAAC;;IAEhC;IACA,MAAMY,aAAa,GAAG,kBAAkBZ,MAAM,EAAE;IAEhD,IAAI;MACF;MACA,MAAM,IAAI,CAACG,gBAAgB,CAAC,CAAC;MAE7B,IAAI,CAACjD,GAAG,CAAC,eAAe0D,aAAa,EAAE,CAAC;;MAExC;MACA,MAAMrB,YAAY,GAAG,MAAM,IAAI,CAACN,SAAS,CAAC2B,aAAa,EAAGC,YAAY,IAAK;QACzE,IAAI,CAAC3D,GAAG,CAAC,aAAa8C,MAAM,OAAOa,YAAY,CAACC,QAAQ,IAAI,SAAS,GAAG,CAAC;QACzE,IAAI,OAAOH,gBAAgB,KAAK,UAAU,EAAE;UAC1CA,gBAAgB,CAACE,YAAY,CAAC;QAChC;MACF,CAAC,CAAC;;MAEF;MACA,MAAME,eAAe,GAAG,YAAYf,MAAM,EAAE;MAC5C,IAAI,CAACxD,kBAAkB,CAACmD,GAAG,CAACoB,eAAe,EAAE;QAC3CxB,YAAY;QACZS,MAAM;QACNgB,IAAI,EAAE;MACR,CAAC,CAAC;MAEF,IAAI,CAAC9D,GAAG,CAAC,YAAY8C,MAAM,cAAcY,aAAa,EAAE,CAAC;MACzD,OAAOrB,YAAY;IAErB,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,UAAU2C,MAAM,UAAU,EAAE1C,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEgD,eAAeA,CAACN,MAAM,EAAEI,SAAS,EAAEjB,QAAQ,EAAE;IAC3C,IAAI;MACF,IAAI,CAACjC,GAAG,CAAC,QAAQ8C,MAAM,WAAWI,SAAS,CAACC,WAAW,EAAE,CAAC;;MAE1D;MACA,IAAI,CAACY,kBAAkB,CAACb,SAAS,CAAC;;MAElC;MACA,IAAI,CAAC,IAAI,CAACc,iBAAiB,CAACd,SAAS,CAAC,EAAE;QACtC,IAAI,CAAC/C,QAAQ,CAAC,MAAM2C,MAAM,WAAW,EAAEI,SAAS,CAAC;QACjD;MACF;;MAEA;MACA,IAAI,CAACe,gBAAgB,CAACnB,MAAM,EAAEI,SAAS,CAAC;;MAExC;MACA,IAAI,OAAOjB,QAAQ,KAAK,UAAU,EAAE;QAClCA,QAAQ,CAACiB,SAAS,CAAC;MACrB;IAEF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,QAAQ2C,MAAM,SAAS,EAAE1C,KAAK,CAAC;IAC/C;EACF;;EAEA;AACF;AACA;AACA;EACE2D,kBAAkBA,CAACb,SAAS,EAAE;IAC5B,IAAI;MACF,MAAMgB,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;;MAEtB;MACA,MAAME,SAAS,GAAGlB,SAAS,CAACmB,SAAS,GAAGnB,SAAS,CAACmB,SAAS,CAACC,MAAM,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC;;MAEtF;MACA,IAAI,CAAC7E,YAAY,CAACK,UAAU,EAAE;MAC9B,IAAI,CAACL,YAAY,CAACM,cAAc,IAAIqE,SAAS;MAC7C,IAAI,CAAC3E,YAAY,CAACE,gBAAgB,GAAG,IAAI,CAACF,YAAY,CAACM,cAAc,GAAG,IAAI,CAACN,YAAY,CAACK,UAAU;;MAEpG;MACA,IAAI,IAAI,CAACL,YAAY,CAACI,eAAe,GAAG,CAAC,EAAE;QACzC,MAAM0E,QAAQ,GAAG,CAACL,GAAG,GAAG,IAAI,CAACzE,YAAY,CAACI,eAAe,IAAI,IAAI,CAAC,CAAC;QACnE,IAAI0E,QAAQ,GAAG,CAAC,EAAE;UAChB;UACA,MAAMC,KAAK,GAAG,GAAG;UACjB,MAAMC,WAAW,GAAG,CAAC,GAAGF,QAAQ;UAChC,IAAI,CAAC9E,YAAY,CAACC,gBAAgB,GAChC8E,KAAK,GAAGC,WAAW,GAAG,CAAC,CAAC,GAAGD,KAAK,IAAI,IAAI,CAAC/E,YAAY,CAACC,gBAAgB;QAC1E;MACF;MAEA,IAAI,CAACD,YAAY,CAACI,eAAe,GAAGqE,GAAG;;MAEvC;MACA,IAAI,CAACQ,yBAAyB,CAAC,CAAC;MAEhC,IAAI,CAAC1E,GAAG,CAAC,cAAc,IAAI,CAACP,YAAY,CAACC,gBAAgB,CAACiF,OAAO,CAAC,CAAC,CAAC,OAAO,GAClE,QAAQ,IAAI,CAAClF,YAAY,CAACE,gBAAgB,CAACgF,OAAO,CAAC,CAAC,CAAC,MAAM,GAC3D,MAAM,IAAI,CAAClF,YAAY,CAACG,iBAAiB,EAAE,CAAC;IAEvD,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,UAAU,EAAEC,KAAK,CAAC;IAClC;EACF;;EAEA;AACF;AACA;EACEsE,yBAAyBA,CAAA,EAAG;IAC1B,MAAM;MAAEhF,gBAAgB;MAAEC;IAAiB,CAAC,GAAG,IAAI,CAACF,YAAY;;IAEhE;IACA,IAAIC,gBAAgB,IAAI,GAAG,IAAIC,gBAAgB,GAAG,GAAG,EAAE;MACrD,IAAI,CAACF,YAAY,CAACG,iBAAiB,GAAG,MAAM;IAC9C,CAAC,MAAM,IAAIF,gBAAgB,IAAI,GAAG,IAAIC,gBAAgB,GAAG,GAAG,EAAE;MAC5D,IAAI,CAACF,YAAY,CAACG,iBAAiB,GAAG,MAAM;IAC9C,CAAC,MAAM;MACL,IAAI,CAACH,YAAY,CAACG,iBAAiB,GAAG,MAAM;IAC9C;EACF;;EAEA;AACF;AACA;AACA;EACEgF,eAAeA,CAAA,EAAG;IAChB,OAAO;MAAE,GAAG,IAAI,CAACnF;IAAa,CAAC;EACjC;;EAEA;AACF;AACA;EACEoF,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACpF,YAAY,GAAG;MAClBC,gBAAgB,EAAE,CAAC;MACnBC,gBAAgB,EAAE,CAAC;MACnBC,iBAAiB,EAAE,MAAM;MACzBC,eAAe,EAAE,CAAC;MAClBC,UAAU,EAAE,CAAC;MACbC,cAAc,EAAE;IAClB,CAAC;IACD,IAAI,CAACC,GAAG,CAAC,SAAS,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;EACEgE,iBAAiBA,CAACd,SAAS,EAAE;IAC3B,OAAOA,SAAS,IACT,OAAOA,SAAS,CAACJ,MAAM,KAAK,QAAQ,IACpC,OAAOI,SAAS,CAACC,WAAW,KAAK,QAAQ,IACzC,OAAOD,SAAS,CAACmB,SAAS,KAAK,QAAQ,IACvCnB,SAAS,CAACY,IAAI,KAAK,cAAc;EAC1C;;EAEA;AACF;AACA;AACA;AACA;EACEG,gBAAgBA,CAACnB,MAAM,EAAEI,SAAS,EAAE;IAClC,IAAI;MACF,IAAI4B,MAAM,GAAG,IAAI,CAACvF,YAAY,CAAC6C,GAAG,CAACU,MAAM,CAAC;MAC1C,IAAI,CAACgC,MAAM,EAAE;QACXA,MAAM,GAAG,EAAE;QACX,IAAI,CAACvF,YAAY,CAACkD,GAAG,CAACK,MAAM,EAAEgC,MAAM,CAAC;MACvC;;MAEA;MACAA,MAAM,CAACC,IAAI,CAAC;QACV,GAAG7B,SAAS;QACZ8B,UAAU,EAAEb,IAAI,CAACD,GAAG,CAAC;MACvB,CAAC,CAAC;;MAEF;MACA,OAAOY,MAAM,CAACR,MAAM,GAAG,IAAI,CAAC9E,kBAAkB,EAAE;QAC9CsF,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;MAClB;MAEA,IAAI,CAACjF,GAAG,CAAC,MAAM8C,MAAM,YAAYgC,MAAM,CAACR,MAAM,EAAE,CAAC;IAEnD,CAAC,CAAC,OAAOlE,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,WAAW,EAAEC,KAAK,CAAC;IACnC;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE8E,cAAcA,CAACpC,MAAM,EAAE;IACrB,OAAO,IAAI,CAACvD,YAAY,CAAC6C,GAAG,CAACU,MAAM,CAAC,IAAI,EAAE;EAC5C;;EAEA;AACF;AACA;AACA;EACEqC,gBAAgBA,CAACrC,MAAM,EAAE;IACvB,IAAI,CAAC9C,GAAG,CAAC,QAAQ8C,MAAM,QAAQ,CAAC;;IAEhC;IACA,IAAI,IAAI,CAACxD,kBAAkB,CAAC6C,GAAG,CAACW,MAAM,CAAC,EAAE;MACvC,IAAI;QACF,MAAME,UAAU,GAAG,iBAAiBF,MAAM,EAAE;QAC5C,IAAI,CAACH,WAAW,CAACK,UAAU,CAAC;QAC5B,IAAI,CAAC1D,kBAAkB,CAACsD,MAAM,CAACE,MAAM,CAAC;MACxC,CAAC,CAAC,OAAO1C,KAAK,EAAE;QACd,IAAI,CAACD,QAAQ,CAAC,QAAQ2C,MAAM,SAAS,EAAE1C,KAAK,CAAC;MAC/C;IACF;;IAEA;IACA,MAAMgF,uBAAuB,GAAG,YAAYtC,MAAM,EAAE;IACpD,IAAI,IAAI,CAACxD,kBAAkB,CAAC6C,GAAG,CAACiD,uBAAuB,CAAC,EAAE;MACxD,IAAI;QACF,MAAM1B,aAAa,GAAG,kBAAkBZ,MAAM,EAAE;QAChD,IAAI,CAACH,WAAW,CAACe,aAAa,CAAC;QAC/B,IAAI,CAACpE,kBAAkB,CAACsD,MAAM,CAACwC,uBAAuB,CAAC;MACzD,CAAC,CAAC,OAAOhF,KAAK,EAAE;QACd,IAAI,CAACD,QAAQ,CAAC,QAAQ2C,MAAM,UAAU,EAAE1C,KAAK,CAAC;MAChD;IACF;;IAEA;IACA,IAAI,CAACb,YAAY,CAACqD,MAAM,CAACE,MAAM,CAAC;EAClC;;EAEA;AACF;AACA;AACA;AACA;EACEuC,aAAaA,CAACvC,MAAM,EAAE;IACpB,MAAMgC,MAAM,GAAG,IAAI,CAACvF,YAAY,CAAC6C,GAAG,CAACU,MAAM,CAAC,IAAI,EAAE;IAClD,MAAMwC,eAAe,GAAG,IAAI,CAAChG,kBAAkB,CAAC6C,GAAG,CAACW,MAAM,CAAC;IAE3D,OAAO;MACLA,MAAM;MACNhD,UAAU,EAAEgF,MAAM,CAACR,MAAM;MACzBgB,eAAe;MACfC,WAAW,EAAET,MAAM,CAACR,MAAM,GAAG,CAAC,GAAGQ,MAAM,CAACA,MAAM,CAACR,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;MACjEkB,WAAW,EAAEV,MAAM,CAACR,MAAM,GAAG,CAAC,GAAGQ,MAAM,CAAC,CAAC,CAAC,GAAG;IAC/C,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAM7B,gBAAgBA,CAAA,EAAG;IACvB,IAAI,CAAC,IAAI,CAAC/D,SAAS,IAAI,CAAC,IAAI,CAACH,MAAM,IAAI,CAAC,IAAI,CAACA,MAAM,CAACG,SAAS,EAAE;MAC7D,IAAI,CAACc,GAAG,CAAC,iBAAiB,CAAC;MAC3B,MAAM,IAAI,CAACsD,SAAS,CAAC,CAAC;IACxB;EACF;;EAEA;AACF;AACA;EACE,MAAMA,SAASA,CAAA,EAAG;IAChB,IAAI,CAACtD,GAAG,CAAC,oBAAoB,CAAC;IAE9B,IAAI;MACF;MACA,IAAI,IAAI,CAACjB,MAAM,EAAE;QACf,IAAI;UACF,IAAI,CAACA,MAAM,CAAC0G,UAAU,CAAC,CAAC;QAC1B,CAAC,CAAC,OAAOC,CAAC,EAAE;UACV;QAAA;MAEJ;;MAEA;MACA,IAAI,CAACxG,SAAS,GAAG,KAAK;MACtB,IAAI,CAACC,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACC,eAAe,GAAG,CAAC;;MAExB;MACA,MAAM,IAAI,CAACiB,IAAI,CAAC,CAAC;MAEjB,IAAI,CAACL,GAAG,CAAC,iBAAiB,CAAC;IAE7B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,eAAe,EAAEC,KAAK,CAAC;MACrC,MAAMA,KAAK;IACb;EACF;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMuF,4BAA4BA,CAAC7C,MAAM,EAAEC,aAAa,EAAEU,gBAAgB,EAAE;IAC1E,IAAI,CAACzD,GAAG,CAAC,WAAW8C,MAAM,YAAY,CAAC;IAEvC,IAAI;MACF;MACA,MAAM,IAAI,CAACG,gBAAgB,CAAC,CAAC;;MAE7B;MACA,MAAM2C,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;MACrD,KAAK,MAAMC,SAAS,IAAID,UAAU,EAAE;QAClC,MAAME,eAAe,GAAG,GAAGD,SAAS,IAAI/C,MAAM,EAAE;QAChD,IAAI,CAAC,IAAI,CAACvD,YAAY,CAAC4C,GAAG,CAAC2D,eAAe,CAAC,EAAE;UAC3C,IAAI,CAACvG,YAAY,CAACkD,GAAG,CAACqD,eAAe,EAAE,EAAE,CAAC;QAC5C;MACF;;MAEA;MACA,MAAMC,WAAW,GAAG,mBAAmBjD,MAAM,EAAE;MAC/C,IAAI,CAAC9C,GAAG,CAAC,iBAAiB+F,WAAW,EAAE,CAAC;MAExC,MAAMC,kBAAkB,GAAG,MAAM,IAAI,CAACjE,SAAS,CAACgE,WAAW,EAAG7C,SAAS,IAAK;QAC1E,IAAI,CAAClD,GAAG,CAAC,eAAe8C,MAAM,OAAOI,SAAS,CAAC2C,SAAS,MAAM3C,SAAS,CAACC,WAAW,IAAI,SAAS,EAAE,CAAC;QACnG,IAAI,CAAC8C,sBAAsB,CAACnD,MAAM,EAAEI,SAAS,EAAEH,aAAa,CAAC;MAC/D,CAAC,CAAC;;MAEF;MACA,MAAMW,aAAa,GAAG,qBAAqBZ,MAAM,EAAE;MACnD,MAAMoD,oBAAoB,GAAG,MAAM,IAAI,CAACnE,SAAS,CAAC2B,aAAa,EAAGC,YAAY,IAAK;QACjF,IAAI,CAAC3D,GAAG,CAAC,gBAAgB8C,MAAM,OAAOa,YAAY,CAACkC,SAAS,OAAOlC,YAAY,CAACC,QAAQ,GAAG,CAAC;QAC5F,IAAI,OAAOH,gBAAgB,KAAK,UAAU,EAAE;UAC1CA,gBAAgB,CAACE,YAAY,CAAC;QAChC;MACF,CAAC,CAAC;;MAEF;MACA,MAAME,eAAe,GAAG,YAAYf,MAAM,EAAE;MAC5C,IAAI,CAACxD,kBAAkB,CAACmD,GAAG,CAACoB,eAAe,EAAE;QAC3CsC,MAAM,EAAEH,kBAAkB;QAC1BpC,QAAQ,EAAEsC,oBAAoB;QAC9BpD,MAAM;QACNgB,IAAI,EAAE;MACR,CAAC,CAAC;MAEF,IAAI,CAAC9D,GAAG,CAAC,eAAe8C,MAAM,UAAU,CAAC;MACzC,OAAO;QACLqD,MAAM,EAAEH,kBAAkB;QAC1BpC,QAAQ,EAAEsC;MACZ,CAAC;IAEH,CAAC,CAAC,OAAO9F,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,aAAa2C,MAAM,QAAQ,EAAE1C,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMgG,8BAA8BA,CAACtD,MAAM,EAAE+C,SAAS,EAAE9C,aAAa,EAAE;IACrE,IAAI,CAAC/C,GAAG,CAAC,QAAQ8C,MAAM,MAAM+C,SAAS,QAAQ,CAAC;IAE/C,IAAI;MACF;MACA,MAAM,IAAI,CAAC5C,gBAAgB,CAAC,CAAC;;MAE7B;MACA,MAAM6C,eAAe,GAAG,GAAGD,SAAS,IAAI/C,MAAM,EAAE;MAChD,IAAI,CAAC,IAAI,CAACvD,YAAY,CAAC4C,GAAG,CAAC2D,eAAe,CAAC,EAAE;QAC3C,IAAI,CAACvG,YAAY,CAACkD,GAAG,CAACqD,eAAe,EAAE,EAAE,CAAC;MAC5C;;MAEA;MACA,MAAMO,cAAc,GAAG,mBAAmBvD,MAAM,IAAI+C,SAAS,EAAE;MAC/D,MAAMxD,YAAY,GAAG,MAAM,IAAI,CAACN,SAAS,CAACsE,cAAc,EAAGnD,SAAS,IAAK;QACvE,IAAI,CAAClD,GAAG,CAAC,KAAK6F,SAAS,YAAY/C,MAAM,MAAMI,SAAS,CAACC,WAAW,IAAI,SAAS,EAAE,CAAC;QACpF,IAAI,CAAC8C,sBAAsB,CAACnD,MAAM,EAAEI,SAAS,EAAEH,aAAa,CAAC;MAC/D,CAAC,CAAC;;MAEF;MACA,MAAMc,eAAe,GAAG,aAAagC,SAAS,IAAI/C,MAAM,EAAE;MAC1D,IAAI,CAACxD,kBAAkB,CAACmD,GAAG,CAACoB,eAAe,EAAE;QAC3CxB,YAAY;QACZS,MAAM;QACN+C,SAAS;QACT/B,IAAI,EAAE;MACR,CAAC,CAAC;MAEF,IAAI,CAAC9D,GAAG,CAAC,YAAY8C,MAAM,MAAM+C,SAAS,QAAQ,CAAC;MACnD,OAAOxD,YAAY;IAErB,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,UAAU2C,MAAM,MAAM+C,SAAS,UAAU,EAAEzF,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE6F,sBAAsBA,CAACnD,MAAM,EAAEI,SAAS,EAAEjB,QAAQ,EAAE;IAClD,IAAI;MACF,IAAI,CAACjC,GAAG,CAAC,WAAW8C,MAAM,YAAYI,SAAS,CAAC2C,SAAS,MAAM3C,SAAS,CAACC,WAAW,EAAE,CAAC;;MAEvF;MACAjC,OAAO,CAAClB,GAAG,CAAC,cAAc,EAAE;QAC1B8C,MAAM;QACN+C,SAAS,EAAE3C,SAAS,CAAC2C,SAAS;QAC9B1C,WAAW,EAAED,SAAS,CAACC,WAAW;QAClCmD,cAAc,EAAEpD,SAAS,CAACoD,cAAc;QACxCC,YAAY,EAAE,CAAC,CAACrD,SAAS,CAACmB,SAAS;QACnCmC,eAAe,EAAEtD,SAAS,CAACmB,SAAS,GAAGnB,SAAS,CAACmB,SAAS,CAACC,MAAM,GAAG,CAAC;QACrEmC,aAAa,EAAEvD,SAAS,CAACmB,SAAS,GAAInB,SAAS,CAACmB,SAAS,CAACqC,UAAU,CAAC,aAAa,CAAC,GAAG,UAAU,GAAG,QAAQ,GAAI,MAAM;QACrHC,SAAS,EAAEzD,SAAS,CAACyD;MACvB,CAAC,CAAC;;MAEF;MACA,IAAI,CAAC5C,kBAAkB,CAACb,SAAS,CAAC;;MAElC;MACA,IAAI,CAAC,IAAI,CAAC0D,wBAAwB,CAAC1D,SAAS,CAAC,EAAE;QAC7C,IAAI,CAAC/C,QAAQ,CAAC,MAAM2C,MAAM,cAAc,EAAEI,SAAS,CAAC;QACpD;MACF;;MAEA;MACA,MAAM4C,eAAe,GAAG,GAAG5C,SAAS,CAAC2C,SAAS,IAAI/C,MAAM,EAAE;MAC1D,IAAI,CAACmB,gBAAgB,CAAC6B,eAAe,EAAE5C,SAAS,CAAC;;MAEjD;MACA,IAAI,OAAOjB,QAAQ,KAAK,UAAU,EAAE;QAClCf,OAAO,CAAClB,GAAG,CAAC,oBAAoBkD,SAAS,CAAC2C,SAAS,IAAI,CAAC;QACxD5D,QAAQ,CAACiB,SAAS,CAAC;MACrB,CAAC,MAAM;QACLhC,OAAO,CAAC2F,IAAI,CAAC,qBAAqB,OAAO5E,QAAQ,EAAE,CAAC;MACtD;IAEF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,WAAW2C,MAAM,SAAS,EAAE1C,KAAK,CAAC;IAClD;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEwG,wBAAwBA,CAAC1D,SAAS,EAAE;IAClC,IAAI,CAACA,SAAS,EAAE;MACdhC,OAAO,CAAC2F,IAAI,CAAC,2BAA2B,CAAC;MACzC,OAAO,KAAK;IACd;IAEA,MAAMC,WAAW,GAAG,CAClB;MAAEC,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE9D,SAAS,CAACJ,MAAM;MAAEgB,IAAI,EAAE;IAAS,CAAC,EAC5D;MAAEiD,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE9D,SAAS,CAAC2C,SAAS;MAAE/B,IAAI,EAAE;IAAS,CAAC,EAClE;MAAEiD,KAAK,EAAE,aAAa;MAAEC,KAAK,EAAE9D,SAAS,CAACC,WAAW;MAAEW,IAAI,EAAE;IAAS,CAAC,EACtE;MAAEiD,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE9D,SAAS,CAACmB,SAAS;MAAEP,IAAI,EAAE;IAAS,CAAC,CACnE;IAED,KAAK,MAAMmD,UAAU,IAAIH,WAAW,EAAE;MACpC,IAAI,OAAOG,UAAU,CAACD,KAAK,KAAKC,UAAU,CAACnD,IAAI,EAAE;QAC/C5C,OAAO,CAAC2F,IAAI,CAAC,iBAAiBI,UAAU,CAACF,KAAK,OAAO,EAAE;UACrDG,QAAQ,EAAED,UAAU,CAACnD,IAAI;UACzBqD,MAAM,EAAE,OAAOF,UAAU,CAACD,KAAK;UAC/BA,KAAK,EAAEC,UAAU,CAACD;QACpB,CAAC,CAAC;QACF,OAAO,KAAK;MACd;IACF;;IAEA;IACA,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC3D,QAAQ,CAACH,SAAS,CAAC2C,SAAS,CAAC,EAAE;MACrE3E,OAAO,CAAC2F,IAAI,CAAC,sBAAsB,EAAE;QACnChB,SAAS,EAAE3C,SAAS,CAAC2C,SAAS;QAC9BuB,eAAe,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO;MACpD,CAAC,CAAC;MACF,OAAO,KAAK;IACd;;IAEA;IACA,IAAI,CAAClE,SAAS,CAACmB,SAAS,IAAInB,SAAS,CAACmB,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5DpD,OAAO,CAAC2F,IAAI,CAAC,2BAA2B,CAAC;MACzC,OAAO,KAAK;IACd;IAEA3F,OAAO,CAAClB,GAAG,CAAC,eAAe,EAAE;MAC3B8C,MAAM,EAAEI,SAAS,CAACJ,MAAM;MACxB+C,SAAS,EAAE3C,SAAS,CAAC2C,SAAS;MAC9B1C,WAAW,EAAED,SAAS,CAACC,WAAW;MAClCqD,eAAe,EAAEtD,SAAS,CAACmB,SAAS,CAACC;IACvC,CAAC,CAAC;IAEF,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACE+C,oBAAoBA,CAACvE,MAAM,EAAE;IAC3B,MAAM8C,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;IACrD,MAAM0B,KAAK,GAAG;MACZxE,MAAM;MACN8C,UAAU,EAAE,CAAC,CAAC;MACd2B,WAAW,EAAE,CAAC;MACdjC,eAAe,EAAE,IAAI,CAAChG,kBAAkB,CAAC6C,GAAG,CAAC,YAAYW,MAAM,EAAE;IACnE,CAAC;IAED,KAAK,MAAM+C,SAAS,IAAID,UAAU,EAAE;MAClC,MAAME,eAAe,GAAG,GAAGD,SAAS,IAAI/C,MAAM,EAAE;MAChD,MAAMgC,MAAM,GAAG,IAAI,CAACvF,YAAY,CAAC6C,GAAG,CAAC0D,eAAe,CAAC,IAAI,EAAE;MAE3DwB,KAAK,CAAC1B,UAAU,CAACC,SAAS,CAAC,GAAG;QAC5B/F,UAAU,EAAEgF,MAAM,CAACR,MAAM;QACzBiB,WAAW,EAAET,MAAM,CAACR,MAAM,GAAG,CAAC,GAAGQ,MAAM,CAACA,MAAM,CAACR,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;QACjEkB,WAAW,EAAEV,MAAM,CAACR,MAAM,GAAG,CAAC,GAAGQ,MAAM,CAAC,CAAC,CAAC,GAAG;MAC/C,CAAC;MAEDwC,KAAK,CAACC,WAAW,IAAIzC,MAAM,CAACR,MAAM;IACpC;IAEA,OAAOgD,KAAK;EACd;;EAEA;AACF;AACA;AACA;EACEE,uBAAuBA,CAAC1E,MAAM,EAAE;IAC9B,IAAI,CAAC9C,GAAG,CAAC,WAAW8C,MAAM,QAAQ,CAAC;IAEnC,IAAI;MACF;MACA,MAAM2E,sBAAsB,GAAG,YAAY3E,MAAM,EAAE;MACnD,IAAI,IAAI,CAACxD,kBAAkB,CAAC6C,GAAG,CAACsF,sBAAsB,CAAC,EAAE;QACvD,MAAMzI,aAAa,GAAG,IAAI,CAACM,kBAAkB,CAAC8C,GAAG,CAACqF,sBAAsB,CAAC;;QAEzE;QACA,IAAIzI,aAAa,CAACmH,MAAM,EAAE;UACxB,MAAMJ,WAAW,GAAG,mBAAmBjD,MAAM,EAAE;UAC/C,IAAI,CAACH,WAAW,CAACoD,WAAW,CAAC;QAC/B;;QAEA;QACA,IAAI/G,aAAa,CAAC4E,QAAQ,EAAE;UAC1B,MAAMF,aAAa,GAAG,qBAAqBZ,MAAM,EAAE;UACnD,IAAI,CAACH,WAAW,CAACe,aAAa,CAAC;QACjC;QAEA,IAAI,CAACpE,kBAAkB,CAACsD,MAAM,CAAC6E,sBAAsB,CAAC;MACxD;;MAEA;MACA,MAAM7B,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;MACrD,KAAK,MAAMC,SAAS,IAAID,UAAU,EAAE;QAClC,MAAM8B,wBAAwB,GAAG,aAAa7B,SAAS,IAAI/C,MAAM,EAAE;QACnE,IAAI,IAAI,CAACxD,kBAAkB,CAAC6C,GAAG,CAACuF,wBAAwB,CAAC,EAAE;UACzD,MAAMrB,cAAc,GAAG,mBAAmBvD,MAAM,IAAI+C,SAAS,EAAE;UAC/D,IAAI,CAAClD,WAAW,CAAC0D,cAAc,CAAC;UAChC,IAAI,CAAC/G,kBAAkB,CAACsD,MAAM,CAAC8E,wBAAwB,CAAC;QAC1D;;QAEA;QACA,MAAM5B,eAAe,GAAG,GAAGD,SAAS,IAAI/C,MAAM,EAAE;QAChD,IAAI,CAACvD,YAAY,CAACqD,MAAM,CAACkD,eAAe,CAAC;MAC3C;MAEA,IAAI,CAAC9F,GAAG,CAAC,cAAc8C,MAAM,SAAS,CAAC;IAEzC,CAAC,CAAC,OAAO1C,KAAK,EAAE;MACd,IAAI,CAACD,QAAQ,CAAC,WAAW2C,MAAM,SAAS,EAAE1C,KAAK,CAAC;IAClD;EACF;;EAEA;AACF;AACA;EACEuH,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC5I,MAAM,IAAI,IAAI,CAACG,SAAS,EAAE;MACjC,IAAI,CAACc,GAAG,CAAC,gBAAgB,CAAC;MAE1B,IAAI;QACF;QACA,IAAI,CAACA,GAAG,CAAC,mBAAmB,IAAI,CAACT,YAAY,CAACqI,IAAI,EAAE,CAAC;QACrD,IAAI,CAACrI,YAAY,CAACsI,KAAK,CAAC,CAAC;QACzB,IAAI,CAACvI,kBAAkB,CAACuI,KAAK,CAAC,CAAC;;QAE/B;QACA,IAAI,CAAC7H,GAAG,CAAC,iBAAiB,IAAI,CAAChB,aAAa,CAAC4I,IAAI,EAAE,CAAC;QACpD,IAAI,CAAC5I,aAAa,CAAC8I,OAAO,CAAC,CAACzF,YAAY,EAAEL,KAAK,KAAK;UAClD,IAAI;YACF,IAAI,CAAChC,GAAG,CAAC,SAASgC,KAAK,SAASK,YAAY,CAACK,EAAE,EAAE,CAAC;YAClDL,YAAY,CAACM,WAAW,CAAC,CAAC;UAC5B,CAAC,CAAC,OAAOvC,KAAK,EAAE;YACd,IAAI,CAACD,QAAQ,CAAC,QAAQ6B,KAAK,KAAK,EAAE5B,KAAK,CAAC;UAC1C;QACF,CAAC,CAAC;QACF,IAAI,CAACpB,aAAa,CAAC6I,KAAK,CAAC,CAAC;QAC1B,IAAI,CAAC7H,GAAG,CAAC,SAAS,CAAC;;QAEnB;QACA,IAAI,CAACA,GAAG,CAAC,gBAAgB,CAAC;QAC1B,IAAI,CAACjB,MAAM,CAAC0G,UAAU,CAAC,CAAC;QACxB,IAAI,CAACvG,SAAS,GAAG,KAAK;QACtB,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACa,GAAG,CAAC,iBAAiB,CAAC;MAC7B,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd,IAAI,CAACD,QAAQ,CAAC,kBAAkB,EAAEC,KAAK,CAAC;QACxC;QACA,IAAI,CAAClB,SAAS,GAAG,KAAK;QACtB,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACI,YAAY,CAACsI,KAAK,CAAC,CAAC;QACzB,IAAI,CAACvI,kBAAkB,CAACuI,KAAK,CAAC,CAAC;MACjC;IACF,CAAC,MAAM;MACL,IAAI,CAAC7H,GAAG,CAAC,sBAAsB,CAAC;IAClC;EACF;AACF;;AAEA;AACA,eAAe,IAAInB,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}