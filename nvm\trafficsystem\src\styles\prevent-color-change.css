/* 简化的导航栏和菜单样式 */
.el-menu-item,
.el-submenu__title,
.el-dropdown-menu__item,
.el-button--text,
.el-dropdown {
  color: #e5e7eb !important;
}

.el-menu-item:hover,
.el-submenu__title:hover,
.el-dropdown-menu__item:hover,
.el-button--text:hover,
.el-menu-item.is-active {
  color: #3b82f6 !important;
  background-color: rgba(59, 130, 246, 0.1) !important;
}

.el-menu.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom-color: #3b82f6 !important;
}

/* 简化的下拉框样式 */
.el-select .el-input__wrapper,
.el-input__wrapper {
  background-color: rgba(25, 32, 50, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
}

.el-select .el-input__wrapper:hover {
  border-color: rgba(59, 130, 246, 0.5) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

.el-select .el-input__wrapper.is-focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.el-select .el-input__inner {
  color: #ffffff !important;
  background-color: transparent !important;
}

.el-select .el-input__inner::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

/* 下拉菜单样式 */
.el-select-dropdown {
  background-color: rgba(25, 32, 50, 0.98) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5) !important;
}

.el-select-dropdown__item {
  color: #ffffff !important;
  padding: 0 16px !important;
  height: 38px !important;
  transition: all 0.2s ease !important;
}

.el-select-dropdown__item:hover {
  background-color: rgba(59, 130, 246, 0.15) !important;
}

.el-select-dropdown__item.selected {
  background-color: rgba(79, 70, 229, 0.3) !important;
  font-weight: 600 !important;
}

.el-dropdown__popper,
.el-popover {
  background-color: rgba(25, 32, 50, 0.98) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
}

/* 单选按钮样式 */
.el-radio-button__inner {
  color: #e5e7eb !important;
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s;
}

.el-radio-button__inner:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border-color: #3b82f6 !important;
}

.el-radio-button__input:checked + .el-radio-button__inner {
  color: #ffffff !important;
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
}



/* 按钮样式 */
.el-button {
  transition: all 0.3s;
  font-weight: 500;
}

.el-button:hover {
  transform: translateY(-1px);
}

.el-button--primary {
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
  color: #ffffff !important;
}

.el-button--primary:hover {
  background-color: #2563eb !important;
  border-color: #2563eb !important;
}

