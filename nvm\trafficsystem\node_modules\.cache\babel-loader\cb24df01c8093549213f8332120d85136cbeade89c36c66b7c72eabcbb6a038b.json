{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, withModifiers as _withModifiers, normalizeClass as _normalizeClass, Fragment as _Fragment, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"upload-area\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  id: \"dropText\"\n};\nconst _hoisted_3 = {\n  key: 1,\n  class: \"preview-container\"\n};\nconst _hoisted_4 = {\n  class: \"file-name\"\n};\nconst _hoisted_5 = {\n  class: \"file-size\"\n};\nconst _hoisted_6 = [\"src\"];\nconst _hoisted_7 = {\n  class: \"upload-area\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  id: \"dropText\"\n};\nconst _hoisted_9 = {\n  key: 1,\n  class: \"preview-container\"\n};\nconst _hoisted_10 = {\n  class: \"file-name\"\n};\nconst _hoisted_11 = {\n  class: \"file-size\"\n};\nconst _hoisted_12 = [\"src\"];\nconst _hoisted_13 = {\n  class: \"upload-area\"\n};\nconst _hoisted_14 = {\n  key: 0,\n  id: \"dropText\"\n};\nconst _hoisted_15 = {\n  key: 1,\n  class: \"preview-container\"\n};\nconst _hoisted_16 = {\n  class: \"file-name\"\n};\nconst _hoisted_17 = {\n  class: \"file-size\"\n};\nconst _hoisted_18 = [\"src\"];\nconst _hoisted_19 = {\n  class: \"four-way-upload-notice\"\n};\nconst _hoisted_20 = {\n  class: \"status-section\"\n};\nconst _hoisted_21 = {\n  key: 0,\n  class: \"loading-container\"\n};\nconst _hoisted_22 = {\n  key: 1,\n  class: \"error-container\"\n};\nconst _hoisted_23 = {\n  class: \"error-actions\"\n};\nconst _hoisted_24 = {\n  key: 2,\n  class: \"status-info\"\n};\nconst _hoisted_25 = {\n  key: 0\n};\nconst _hoisted_26 = {\n  key: 1,\n  class: \"error-message\"\n};\nconst _hoisted_27 = {\n  key: 2,\n  class: \"error-message\"\n};\nconst _hoisted_28 = {\n  key: 3,\n  class: \"error-message\"\n};\nconst _hoisted_29 = {\n  class: \"error-actions\"\n};\nconst _hoisted_30 = {\n  key: 0\n};\nconst _hoisted_31 = {\n  key: 1\n};\nconst _hoisted_32 = {\n  key: 2\n};\nconst _hoisted_33 = {\n  key: 2,\n  class: \"realtime-preview-section\"\n};\nconst _hoisted_34 = {\n  key: 0,\n  class: \"dual-video-preview\"\n};\nconst _hoisted_35 = {\n  class: \"preview-title\"\n};\nconst _hoisted_36 = {\n  class: \"video-preview-grid\"\n};\nconst _hoisted_37 = {\n  class: \"video-preview-container\"\n};\nconst _hoisted_38 = {\n  class: \"video-header\"\n};\nconst _hoisted_39 = {\n  class: \"video-preview-container\"\n};\nconst _hoisted_40 = {\n  class: \"video-header\"\n};\nconst _hoisted_41 = {\n  class: \"single-video-preview\"\n};\nconst _hoisted_42 = {\n  class: \"preview-title\"\n};\nconst _hoisted_43 = {\n  key: 0\n};\nconst _hoisted_44 = {\n  key: 1\n};\nconst _hoisted_45 = {\n  key: 4,\n  class: \"status-progress\"\n};\nconst _hoisted_46 = {\n  class: \"status-progress\"\n};\nconst _hoisted_47 = {\n  key: 6,\n  class: \"processing-time\"\n};\nconst _hoisted_48 = {\n  class: \"time-header\"\n};\nconst _hoisted_49 = {\n  class: \"time-value-container\"\n};\nconst _hoisted_50 = {\n  class: \"time-value\"\n};\nconst _hoisted_51 = {\n  key: 0,\n  class: \"timer-animation\"\n};\nconst _hoisted_52 = {\n  key: 0,\n  class: \"processing-complete\"\n};\nconst _hoisted_53 = {\n  class: \"status-actions\"\n};\nconst _hoisted_54 = {\n  key: 0,\n  class: \"completed-actions\"\n};\nconst _hoisted_55 = {\n  key: 1,\n  class: \"failed-actions\"\n};\nconst _hoisted_56 = {\n  key: 2,\n  class: \"processing-actions\"\n};\nconst _hoisted_57 = {\n  key: 2,\n  class: \"upload-progress\"\n};\nconst _hoisted_58 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_radio_button = _resolveComponent(\"el-radio-button\");\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_document = _resolveComponent(\"document\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_FourWayVideoUpload = _resolveComponent(\"FourWayVideoUpload\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_VideoCamera = _resolveComponent(\"VideoCamera\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_RealTimeFrameViewer = _resolveComponent(\"RealTimeFrameViewer\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_Timer = _resolveComponent(\"Timer\");\n  const _component_Check = _resolveComponent(\"Check\");\n  const _component_Document = _resolveComponent(\"Document\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" 上传表单部分 - 当没有任务ID时显示 \"), !$setup.taskId ? (_openBlock(), _createBlock(_component_el_form, {\n    key: 0,\n    model: $setup.form,\n    \"label-position\": \"top\",\n    class: \"upload-form\",\n    rules: $setup.rules,\n    ref: \"uploadForm\"\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 道路类型选择 \"), _createVNode(_component_el_form_item, {\n      label: \"道路类型\",\n      prop: \"roadType\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_radio_group, {\n        modelValue: $setup.form.roadType,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.form.roadType = $event),\n        size: \"large\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_radio_button, {\n          value: \"normal\",\n          label: \"普通道路\"\n        }), _createVNode(_component_el_radio_button, {\n          value: \"intersection\",\n          label: \"十字路口\"\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 普通道路的单视频上传 \"), $setup.form.roadType === 'normal' ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 0\n    }, [_createVNode(_component_el_form_item, {\n      label: \"上传视频\",\n      prop: \"singleVideo\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", {\n        class: _normalizeClass([\"drop-area\", {\n          'drag-over': $setup.isDragOver,\n          'has-file': $setup.form.singleVideoFile\n        }]),\n        onClick: _cache[1] || (_cache[1] = (...args) => $setup.triggerFileInput && $setup.triggerFileInput(...args)),\n        onDrop: _cache[2] || (_cache[2] = _withModifiers((...args) => $setup.handleFileDrop && $setup.handleFileDrop(...args), [\"prevent\"])),\n        onDragover: _cache[3] || (_cache[3] = _withModifiers(() => {}, [\"prevent\"])),\n        onDragenter: _cache[4] || (_cache[4] = _withModifiers((...args) => $setup.onDragEnter && $setup.onDragEnter(...args), [\"prevent\"])),\n        onDragleave: _cache[5] || (_cache[5] = _withModifiers((...args) => $setup.onDragLeave && $setup.onDragLeave(...args), [\"prevent\"]))\n      }, [!$setup.form.singleVideoFile ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, _cache[22] || (_cache[22] = [_createElementVNode(\"div\", {\n        class: \"icon-container\"\n      }, [_createElementVNode(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"64\",\n        height: \"64\",\n        fill: \"currentColor\",\n        class: \"upload-icon\",\n        viewBox: \"0 0 16 16\"\n      }, [_createElementVNode(\"path\", {\n        d: \"M6.002 5.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0\"\n      }), _createElementVNode(\"path\", {\n        d: \"M2.002 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2zm12 1a1 1 0 0 1 1 1v6.5l-3.777-1.947a.5.5 0 0 0-.577.093l-3.71 3.71-2.66-1.772a.5.5 0 0 0-.63.062L1.002 12V3a1 1 0 0 1 1-1z\"\n      })]), _createElementVNode(\"div\", {\n        class: \"upload-arrow\"\n      }, [_createElementVNode(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"24\",\n        height: \"24\",\n        fill: \"currentColor\",\n        class: \"arrow-icon\",\n        viewBox: \"0 0 16 16\"\n      }, [_createElementVNode(\"path\", {\n        \"fill-rule\": \"evenodd\",\n        d: \"M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1z\"\n      })])])], -1 /* HOISTED */), _createElementVNode(\"p\", {\n        class: \"upload-title\"\n      }, [_createTextVNode(\"拖拽视频文件到此处，或 \"), _createElementVNode(\"span\", {\n        class: \"click-upload\"\n      }, \"点击上传\")], -1 /* HOISTED */), _createElementVNode(\"p\", {\n        class: \"upload-hint\"\n      }, \"支持 mp4, avi, mov, quicktime 格式视频，文件大小不超过500MB\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createVNode(_component_el_icon, {\n        class: \"file-icon\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_document)]),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"p\", _hoisted_4, _toDisplayString($setup.form.singleVideoFile.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_5, _toDisplayString($setup.formatFileSize($setup.form.singleVideoFile.size)), 1 /* TEXT */), _createVNode(_component_el_button, {\n        class: \"remove-file-btn\",\n        size: \"small\",\n        onClick: _withModifiers($setup.handleSingleVideoRemove, [\"stop\"])\n      }, {\n        default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"移除\")])),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"]), $setup.singleVideoPreviewUrl ? (_openBlock(), _createElementBlock(\"video\", {\n        key: 0,\n        src: $setup.singleVideoPreviewUrl,\n        controls: \"\",\n        class: \"preview-video-inline\"\n      }, null, 8 /* PROPS */, _hoisted_6)) : _createCommentVNode(\"v-if\", true)]))], 34 /* CLASS, NEED_HYDRATION */), _createElementVNode(\"input\", {\n        type: \"file\",\n        ref: \"fileInput\",\n        style: {\n          \"display\": \"none\"\n        },\n        onChange: _cache[6] || (_cache[6] = (...args) => $setup.handleFileChange && $setup.handleFileChange(...args)),\n        accept: \"video/mp4,video/avi,video/mov,video/quicktime\"\n      }, null, 544 /* NEED_HYDRATION, NEED_PATCH */)])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"视频方向\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_select, {\n        modelValue: $setup.form.direction,\n        \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.form.direction = $event),\n        placeholder: \"请选择视频方向\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_option, {\n          label: \"横向\",\n          value: \"horizontal\"\n        }), _createVNode(_component_el_option, {\n          label: \"纵向\",\n          value: \"vertical\"\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    })], 64 /* STABLE_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 十字路口的视频上传 \"), _createCommentVNode(\" 上传模式选择 \"), _createVNode(_component_el_form_item, {\n      label: \"上传模式\",\n      prop: \"intersectionMode\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_radio_group, {\n        modelValue: $setup.form.intersectionMode,\n        \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.form.intersectionMode = $event),\n        size: \"large\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_radio_button, {\n          value: \"dual\",\n          label: \"双视频模式（横向+纵向）\"\n        }), _createVNode(_component_el_radio_button, {\n          value: \"four-way\",\n          label: \"四方向模式（东南西北）\"\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 双视频上传模式 \"), $setup.form.intersectionMode === 'dual' ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 0\n    }, [_createVNode(_component_el_form_item, {\n      label: \"横向视频\",\n      prop: \"horizontalVideo\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", {\n        class: _normalizeClass([\"drop-area\", {\n          'drag-over': $setup.isDragOverHorizontal,\n          'has-file': $setup.form.horizontalVideoFile\n        }]),\n        onClick: _cache[9] || (_cache[9] = (...args) => $setup.triggerHorizontalFileInput && $setup.triggerHorizontalFileInput(...args)),\n        onDrop: _cache[10] || (_cache[10] = _withModifiers((...args) => $setup.handleHorizontalFileDrop && $setup.handleHorizontalFileDrop(...args), [\"prevent\"])),\n        onDragover: _cache[11] || (_cache[11] = _withModifiers(() => {}, [\"prevent\"])),\n        onDragenter: _cache[12] || (_cache[12] = _withModifiers((...args) => $setup.onDragEnterHorizontal && $setup.onDragEnterHorizontal(...args), [\"prevent\"])),\n        onDragleave: _cache[13] || (_cache[13] = _withModifiers((...args) => $setup.onDragLeaveHorizontal && $setup.onDragLeaveHorizontal(...args), [\"prevent\"]))\n      }, [!$setup.form.horizontalVideoFile ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, _cache[24] || (_cache[24] = [_createElementVNode(\"div\", {\n        class: \"icon-container\"\n      }, [_createElementVNode(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"64\",\n        height: \"64\",\n        fill: \"currentColor\",\n        class: \"upload-icon\",\n        viewBox: \"0 0 16 16\"\n      }, [_createElementVNode(\"path\", {\n        d: \"M6.002 5.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0\"\n      }), _createElementVNode(\"path\", {\n        d: \"M2.002 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2zm12 1a1 1 0 0 1 1 1v6.5l-3.777-1.947a.5.5 0 0 0-.577.093l-3.71 3.71-2.66-1.772a.5.5 0 0 0-.63.062L1.002 12V3a1 1 0 0 1 1-1z\"\n      })]), _createElementVNode(\"div\", {\n        class: \"upload-arrow\"\n      }, [_createElementVNode(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"24\",\n        height: \"24\",\n        fill: \"currentColor\",\n        class: \"arrow-icon\",\n        viewBox: \"0 0 16 16\"\n      }, [_createElementVNode(\"path\", {\n        \"fill-rule\": \"evenodd\",\n        d: \"M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1z\"\n      })])])], -1 /* HOISTED */), _createElementVNode(\"p\", {\n        class: \"upload-title\"\n      }, [_createTextVNode(\"拖拽横向视频文件到此处，或 \"), _createElementVNode(\"span\", {\n        class: \"click-upload\"\n      }, \"点击上传\")], -1 /* HOISTED */), _createElementVNode(\"p\", {\n        class: \"upload-hint\"\n      }, \"横向视频应拍摄东西方向的交通\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_el_icon, {\n        class: \"file-icon\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_document)]),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"p\", _hoisted_10, _toDisplayString($setup.form.horizontalVideoFile.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_11, _toDisplayString($setup.formatFileSize($setup.form.horizontalVideoFile.size)), 1 /* TEXT */), _createVNode(_component_el_button, {\n        class: \"remove-file-btn\",\n        size: \"small\",\n        onClick: _withModifiers($setup.handleHorizontalVideoRemove, [\"stop\"])\n      }, {\n        default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"移除\")])),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"]), $setup.horizontalVideoPreviewUrl ? (_openBlock(), _createElementBlock(\"video\", {\n        key: 0,\n        src: $setup.horizontalVideoPreviewUrl,\n        controls: \"\",\n        class: \"preview-video-inline\"\n      }, null, 8 /* PROPS */, _hoisted_12)) : _createCommentVNode(\"v-if\", true)]))], 34 /* CLASS, NEED_HYDRATION */), _createElementVNode(\"input\", {\n        type: \"file\",\n        ref: \"horizontalFileInput\",\n        style: {\n          \"display\": \"none\"\n        },\n        onChange: _cache[14] || (_cache[14] = (...args) => $setup.handleHorizontalFileChange && $setup.handleHorizontalFileChange(...args)),\n        accept: \"video/mp4,video/avi,video/mov,video/quicktime\"\n      }, null, 544 /* NEED_HYDRATION, NEED_PATCH */)])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      label: \"纵向视频\",\n      prop: \"verticalVideo\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", {\n        class: _normalizeClass([\"drop-area\", {\n          'drag-over': $setup.isDragOverVertical,\n          'has-file': $setup.form.verticalVideoFile\n        }]),\n        onClick: _cache[15] || (_cache[15] = (...args) => $setup.triggerVerticalFileInput && $setup.triggerVerticalFileInput(...args)),\n        onDrop: _cache[16] || (_cache[16] = _withModifiers((...args) => $setup.handleVerticalFileDrop && $setup.handleVerticalFileDrop(...args), [\"prevent\"])),\n        onDragover: _cache[17] || (_cache[17] = _withModifiers(() => {}, [\"prevent\"])),\n        onDragenter: _cache[18] || (_cache[18] = _withModifiers((...args) => $setup.onDragEnterVertical && $setup.onDragEnterVertical(...args), [\"prevent\"])),\n        onDragleave: _cache[19] || (_cache[19] = _withModifiers((...args) => $setup.onDragLeaveVertical && $setup.onDragLeaveVertical(...args), [\"prevent\"]))\n      }, [!$setup.form.verticalVideoFile ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, _cache[26] || (_cache[26] = [_createElementVNode(\"div\", {\n        class: \"icon-container\"\n      }, [_createElementVNode(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"64\",\n        height: \"64\",\n        fill: \"currentColor\",\n        class: \"upload-icon\",\n        viewBox: \"0 0 16 16\"\n      }, [_createElementVNode(\"path\", {\n        d: \"M6.002 5.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0\"\n      }), _createElementVNode(\"path\", {\n        d: \"M2.002 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2zm12 1a1 1 0 0 1 1 1v6.5l-3.777-1.947a.5.5 0 0 0-.577.093l-3.71 3.71-2.66-1.772a.5.5 0 0 0-.63.062L1.002 12V3a1 1 0 0 1 1-1z\"\n      })]), _createElementVNode(\"div\", {\n        class: \"upload-arrow\"\n      }, [_createElementVNode(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"24\",\n        height: \"24\",\n        fill: \"currentColor\",\n        class: \"arrow-icon\",\n        viewBox: \"0 0 16 16\"\n      }, [_createElementVNode(\"path\", {\n        \"fill-rule\": \"evenodd\",\n        d: \"M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1z\"\n      })])])], -1 /* HOISTED */), _createElementVNode(\"p\", {\n        class: \"upload-title\"\n      }, [_createTextVNode(\"拖拽纵向视频文件到此处，或 \"), _createElementVNode(\"span\", {\n        class: \"click-upload\"\n      }, \"点击上传\")], -1 /* HOISTED */), _createElementVNode(\"p\", {\n        class: \"upload-hint\"\n      }, \"纵向视频应拍摄南北方向的交通\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createVNode(_component_el_icon, {\n        class: \"file-icon\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_document)]),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"p\", _hoisted_16, _toDisplayString($setup.form.verticalVideoFile.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_17, _toDisplayString($setup.formatFileSize($setup.form.verticalVideoFile.size)), 1 /* TEXT */), _createVNode(_component_el_button, {\n        class: \"remove-file-btn\",\n        size: \"small\",\n        onClick: _withModifiers($setup.handleVerticalVideoRemove, [\"stop\"])\n      }, {\n        default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"移除\")])),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"]), $setup.verticalVideoPreviewUrl ? (_openBlock(), _createElementBlock(\"video\", {\n        key: 0,\n        src: $setup.verticalVideoPreviewUrl,\n        controls: \"\",\n        class: \"preview-video-inline\"\n      }, null, 8 /* PROPS */, _hoisted_18)) : _createCommentVNode(\"v-if\", true)]))], 34 /* CLASS, NEED_HYDRATION */), _createElementVNode(\"input\", {\n        type: \"file\",\n        ref: \"verticalFileInput\",\n        style: {\n          \"display\": \"none\"\n        },\n        onChange: _cache[20] || (_cache[20] = (...args) => $setup.handleVerticalFileChange && $setup.handleVerticalFileChange(...args)),\n        accept: \"video/mp4,video/avi,video/mov,video/quicktime\"\n      }, null, 544 /* NEED_HYDRATION, NEED_PATCH */)])]),\n      _: 1 /* STABLE */\n    })], 64 /* STABLE_FRAGMENT */)) : $setup.form.intersectionMode === 'four-way' ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 四方向上传模式 \"), _createElementVNode(\"div\", _hoisted_19, [_createVNode(_component_el_alert, {\n      title: \"四方向智能交通分析\",\n      type: \"info\",\n      closable: false,\n      \"show-icon\": \"\"\n    }, {\n      default: _withCtx(() => _cache[28] || (_cache[28] = [_createElementVNode(\"p\", null, \"请上传十字路口四个方向的视频文件，系统将进行智能交通流量分析并生成优化建议。\", -1 /* HOISTED */)])),\n      _: 1 /* STABLE */\n    })]), _createCommentVNode(\" 使用四方向上传组件 \"), _createVNode(_component_FourWayVideoUpload, {\n      onUploadSuccess: $setup.handleFourWayUploadSuccess,\n      onUploadError: $setup.handleFourWayUploadError\n    }, null, 8 /* PROPS */, [\"onUploadSuccess\", \"onUploadError\"])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */)), _createCommentVNode(\" 提交按钮 - 四方向模式下不显示 \"), !($setup.form.roadType === 'intersection' && $setup.form.intersectionMode === 'four-way') ? (_openBlock(), _createBlock(_component_el_form_item, {\n      key: 2\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.submitUpload,\n        loading: $setup.uploading\n      }, {\n        default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"开始分析\")])),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\", \"loading\"]), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\"重置\")])),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"onClick\"])]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 处理状态部分 - 当有任务ID时显示 \"), _createElementVNode(\"div\", _hoisted_20, [$setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_createVNode(_component_el_skeleton, {\n    rows: 3,\n    animated: \"\"\n  })])) : $setup.statusError ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_createVNode(_component_el_alert, {\n    title: \"获取任务状态失败\",\n    type: \"error\",\n    description: $setup.statusError,\n    \"show-icon\": \"\"\n  }, null, 8 /* PROPS */, [\"description\"]), _createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.fetchStatus\n  }, {\n    default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\"重试\")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    onClick: $setup.resetTask\n  }, {\n    default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\"重新上传\")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_createCommentVNode(\" 分析失败状态 \"), $setup.taskInfo.status === 'failed' ? (_openBlock(), _createBlock(_component_el_alert, {\n    key: 0,\n    type: \"error\",\n    closable: false,\n    \"show-icon\": \"\",\n    class: \"custom-alert\"\n  }, {\n    title: _withCtx(() => _cache[33] || (_cache[33] = [_createElementVNode(\"strong\", null, \"分析失败\", -1 /* HOISTED */), _createTextVNode(\" - 视频处理遇到错误 \")])),\n    default: _withCtx(() => [_createElementVNode(\"p\", null, \"任务ID: \" + _toDisplayString($setup.taskId), 1 /* TEXT */), $setup.taskInfo.videoName ? (_openBlock(), _createElementBlock(\"p\", _hoisted_25, \"视频名称: \" + _toDisplayString($setup.taskInfo.videoName || '未命名视频'), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.taskInfo.error ? (_openBlock(), _createElementBlock(\"p\", _hoisted_26, \"错误信息: \" + _toDisplayString($setup.taskInfo.error), 1 /* TEXT */)) : $setup.taskInfo.message ? (_openBlock(), _createElementBlock(\"p\", _hoisted_27, _toDisplayString($setup.taskInfo.message), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"p\", _hoisted_28, \"分析过程中发生未知错误\")), _cache[36] || (_cache[36] = _createElementVNode(\"div\", {\n      class: \"error-suggestions\"\n    }, [_createElementVNode(\"p\", null, [_createElementVNode(\"strong\", null, \"可能的原因：\")]), _createElementVNode(\"ul\", null, [_createElementVNode(\"li\", null, \"模型服务未启动或不在线\"), _createElementVNode(\"li\", null, \"视频格式不支持或文件损坏\"), _createElementVNode(\"li\", null, \"服务器资源不足或处理超时\"), _createElementVNode(\"li\", null, \"网络连接中断\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_29, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.fetchStatus\n    }, {\n      default: _withCtx(() => _cache[34] || (_cache[34] = [_createTextVNode(\"重新检查状态\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      onClick: $setup.resetTask\n    }, {\n      default: _withCtx(() => _cache[35] || (_cache[35] = [_createTextVNode(\"重新上传\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    _: 1 /* STABLE */\n  })) : $setup.taskInfo.status !== 'completed' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 分析中状态 \"), _createVNode(_component_el_alert, {\n    type: \"info\",\n    closable: false,\n    \"show-icon\": \"\",\n    class: \"custom-alert\"\n  }, {\n    title: _withCtx(() => _cache[37] || (_cache[37] = [_createElementVNode(\"strong\", null, \"分析中\", -1 /* HOISTED */), _createTextVNode(\" - 您的视频正在处理 \")])),\n    default: _withCtx(() => [_createElementVNode(\"p\", null, \"任务ID: \" + _toDisplayString($setup.taskId), 1 /* TEXT */), $setup.taskInfo.videoName ? (_openBlock(), _createElementBlock(\"p\", _hoisted_30, \"视频名称: \" + _toDisplayString($setup.taskInfo.videoName || '未命名视频'), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.taskInfo.message ? (_openBlock(), _createElementBlock(\"p\", _hoisted_31, _toDisplayString($setup.taskInfo.message), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.processingElapsedTime > 0 ? (_openBlock(), _createElementBlock(\"p\", _hoisted_32, \"处理时间: \" + _toDisplayString($setup.processingElapsedTime.toFixed(2)) + \" 秒\", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 实时预览组件 - 仅在十字路口视频分析中显示 \"), $setup.taskInfo.status !== 'completed' && $setup.taskInfo.status !== 'failed' && $setup.showRealtimePreview ? (_openBlock(), _createElementBlock(\"div\", _hoisted_33, [_createCommentVNode(\" 十字路口双视频预览 \"), $setup.form?.roadType === 'intersection' || true ? (_openBlock(), _createElementBlock(\"div\", _hoisted_34, [_createElementVNode(\"h3\", _hoisted_35, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_VideoCamera)]),\n    _: 1 /* STABLE */\n  }), _cache[38] || (_cache[38] = _createTextVNode(\" 实时视频分析预览 \"))]), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_cache[40] || (_cache[40] = _createElementVNode(\"h4\", null, \"水平方向视频\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: \"primary\",\n    size: \"small\"\n  }, {\n    default: _withCtx(() => _cache[39] || (_cache[39] = [_createTextVNode(\"实时分析中\")])),\n    _: 1 /* STABLE */\n  })]), _createVNode(_component_RealTimeFrameViewer, {\n    ref: \"horizontalFrameViewer\",\n    \"task-id\": `h_${$setup.taskId}`,\n    \"auto-start\": true,\n    \"max-buffer-frames\": 30,\n    title: '水平方向',\n    onFrameReceived: $setup.handleHorizontalFrameReceived,\n    onPlaybackStateChange: $setup.handlePlaybackStateChange\n  }, null, 8 /* PROPS */, [\"task-id\", \"onFrameReceived\", \"onPlaybackStateChange\"])]), _createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, [_cache[42] || (_cache[42] = _createElementVNode(\"h4\", null, \"垂直方向视频\", -1 /* HOISTED */)), _createVNode(_component_el_tag, {\n    type: \"success\",\n    size: \"small\"\n  }, {\n    default: _withCtx(() => _cache[41] || (_cache[41] = [_createTextVNode(\"实时分析中\")])),\n    _: 1 /* STABLE */\n  })]), _createVNode(_component_RealTimeFrameViewer, {\n    ref: \"verticalFrameViewer\",\n    \"task-id\": `v_${$setup.taskId}`,\n    \"auto-start\": true,\n    \"max-buffer-frames\": 30,\n    title: '垂直方向',\n    onFrameReceived: $setup.handleVerticalFrameReceived,\n    onPlaybackStateChange: $setup.handlePlaybackStateChange\n  }, null, 8 /* PROPS */, [\"task-id\", \"onFrameReceived\", \"onPlaybackStateChange\"])])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 单视频预览 \"), _createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"h3\", _hoisted_42, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_VideoCamera)]),\n    _: 1 /* STABLE */\n  }), _cache[43] || (_cache[43] = _createTextVNode(\" 实时视频分析预览 \"))]), _createVNode(_component_RealTimeFrameViewer, {\n    ref: \"frameViewer\",\n    \"task-id\": $setup.taskId,\n    \"auto-start\": true,\n    \"max-buffer-frames\": 30,\n    onFrameReceived: $setup.handleFrameReceived,\n    onPlaybackStateChange: $setup.handlePlaybackStateChange\n  }, null, 8 /* PROPS */, [\"task-id\", \"onFrameReceived\", \"onPlaybackStateChange\"])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 3\n  }, [_createCommentVNode(\" 分析完成状态 \"), _createVNode(_component_el_alert, {\n    type: \"success\",\n    closable: false,\n    \"show-icon\": \"\",\n    class: \"custom-alert\"\n  }, {\n    title: _withCtx(() => _cache[44] || (_cache[44] = [_createElementVNode(\"strong\", null, \"分析完成\", -1 /* HOISTED */), _createTextVNode(\" - 您的视频已处理完毕 \")])),\n    default: _withCtx(() => [_createElementVNode(\"p\", null, \"任务ID: \" + _toDisplayString($setup.taskId), 1 /* TEXT */), $setup.taskInfo.videoName ? (_openBlock(), _createElementBlock(\"p\", _hoisted_43, \"视频名称: \" + _toDisplayString($setup.taskInfo.videoName || '未命名视频'), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), $setup.processingElapsedTime > 0 ? (_openBlock(), _createElementBlock(\"p\", _hoisted_44, \"处理总时间: \" + _toDisplayString($setup.processingElapsedTime.toFixed(2)) + \" 秒\", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), $setup.taskInfo.status !== 'failed' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_45, [_cache[45] || (_cache[45] = _createElementVNode(\"p\", null, \"处理进度:\", -1 /* HOISTED */)), _createVNode(_component_el_progress, {\n    percentage: $setup.taskInfo.progress || 0,\n    status: $setup.progressStatus,\n    \"stroke-width\": 18,\n    \"text-inside\": \"\",\n    \"show-text\": true,\n    class: \"custom-progress\"\n  }, null, 8 /* PROPS */, [\"percentage\", \"status\"])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 5\n  }, [_createCommentVNode(\" 失败状态的进度条 \"), _createElementVNode(\"div\", _hoisted_46, [_cache[47] || (_cache[47] = _createElementVNode(\"p\", null, \"处理状态:\", -1 /* HOISTED */)), _createVNode(_component_el_progress, {\n    percentage: 100,\n    status: \"exception\",\n    \"stroke-width\": 18,\n    \"text-inside\": \"\",\n    \"show-text\": true,\n    class: \"custom-progress\"\n  }, {\n    default: _withCtx(({\n      percentage\n    }) => _cache[46] || (_cache[46] = [_createElementVNode(\"span\", {\n      class: \"progress-text\"\n    }, \"分析失败\", -1 /* HOISTED */)])),\n    _: 1 /* STABLE */\n  })])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 处理时间显示 \"), $setup.processingElapsedTime > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_47, [_createElementVNode(\"div\", _hoisted_48, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_Timer)]),\n    _: 1 /* STABLE */\n  }), _cache[48] || (_cache[48] = _createElementVNode(\"span\", null, \"处理时间\", -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"span\", _hoisted_50, _toDisplayString($setup.processingElapsedTime.toFixed(2)), 1 /* TEXT */), _cache[50] || (_cache[50] = _createElementVNode(\"span\", {\n    class: \"time-unit\"\n  }, \"秒\", -1 /* HOISTED */)), $setup.taskInfo.status === 'processing' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_51, _cache[49] || (_cache[49] = [_createElementVNode(\"span\", {\n    class: \"dot\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"dot\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"span\", {\n    class: \"dot\"\n  }, null, -1 /* HOISTED */)]))) : _createCommentVNode(\"v-if\", true)]), $setup.taskInfo.status === 'completed' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_52, [_createVNode(_component_el_icon, {\n    color: \"#10b981\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_Check)]),\n    _: 1 /* STABLE */\n  }), _cache[51] || (_cache[51] = _createTextVNode(\" 处理完成 \"))])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_53, [$setup.taskInfo.status === 'completed' && $setup.taskInfo.resultId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_54, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    class: \"view-result-btn\",\n    onClick: $setup.viewResult,\n    size: \"large\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, {\n      class: \"view-icon\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_Document)]),\n      _: 1 /* STABLE */\n    }), _cache[52] || (_cache[52] = _createTextVNode(\" 查看详情 \"))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n    onClick: $setup.resetTask,\n    class: \"reset-btn\",\n    size: \"large\"\n  }, {\n    default: _withCtx(() => _cache[53] || (_cache[53] = [_createTextVNode(\" 重新上传 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])) : $setup.taskInfo.status === 'failed' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_55, [_createVNode(_component_el_button, {\n    type: \"warning\",\n    onClick: $setup.retryAnalysis,\n    loading: $setup.retrying,\n    size: \"large\"\n  }, {\n    default: _withCtx(() => _cache[54] || (_cache[54] = [_createTextVNode(\" 重新分析 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"loading\"]), _createVNode(_component_el_button, {\n    onClick: $setup.resetTask,\n    size: \"large\"\n  }, {\n    default: _withCtx(() => _cache[55] || (_cache[55] = [_createTextVNode(\" 重新上传 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_56, [_createVNode(_component_el_button, {\n    onClick: $setup.resetTask,\n    size: \"large\"\n  }, {\n    default: _withCtx(() => _cache[56] || (_cache[56] = [_createTextVNode(\" 重新上传 \")])),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]))])]))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 上传进度条 - 仅在上传过程中显示 \"), $setup.uploadProgress > 0 && $setup.uploadProgress < 100 && !$setup.taskId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_57, [_cache[57] || (_cache[57] = _createElementVNode(\"p\", null, \"上传进度:\", -1 /* HOISTED */)), _createVNode(_component_el_progress, {\n    percentage: $setup.uploadProgress,\n    status: $setup.uploadProgress < 100 ? '' : 'success',\n    \"stroke-width\": 18,\n    \"show-text\": true,\n    \"text-inside\": \"\",\n    class: \"custom-progress\"\n  }, null, 8 /* PROPS */, [\"percentage\", \"status\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 视频预览对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"视频预览\",\n    modelValue: $setup.previewDialogVisible,\n    \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $setup.previewDialogVisible = $event),\n    width: \"70%\",\n    class: \"preview-dialog\"\n  }, {\n    default: _withCtx(() => [$setup.currentPreviewUrl ? (_openBlock(), _createElementBlock(\"video\", {\n      key: 0,\n      src: $setup.currentPreviewUrl,\n      controls: \"\",\n      style: {\n        \"width\": \"100%\"\n      }\n    }, null, 8 /* PROPS */, _hoisted_58)) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "key", "id", "_createElementBlock", "_Fragment", "_createCommentVNode", "$setup", "taskId", "_createBlock", "_component_el_form", "model", "form", "rules", "ref", "default", "_withCtx", "_createVNode", "_component_el_form_item", "label", "prop", "_component_el_radio_group", "modelValue", "roadType", "_cache", "$event", "size", "_component_el_radio_button", "value", "_", "_createElementVNode", "_hoisted_1", "_normalizeClass", "isDragOver", "singleVideoFile", "onClick", "args", "triggerFileInput", "onDrop", "_withModifiers", "handleFileDrop", "onDragover", "onDragenter", "onDragEnter", "onDragleave", "onDragLeave", "_hoisted_2", "xmlns", "width", "height", "fill", "viewBox", "d", "_createTextVNode", "_hoisted_3", "_component_el_icon", "_component_document", "_hoisted_4", "_toDisplayString", "name", "_hoisted_5", "formatFileSize", "_component_el_button", "handleSingleVideoRemove", "singleVideoPreviewUrl", "src", "controls", "_hoisted_6", "type", "style", "onChange", "handleFileChange", "accept", "_component_el_select", "direction", "placeholder", "_component_el_option", "intersectionMode", "_hoisted_7", "isDragOverHorizontal", "horizontalVideoFile", "triggerHorizontalFileInput", "handleHorizontalFileDrop", "onDragEnterHorizontal", "onDragLeaveHorizontal", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "handleHorizontalVideoRemove", "horizontalVideoPreviewUrl", "_hoisted_12", "handleHorizontalFileChange", "_hoisted_13", "isDragOverVertical", "verticalVideoFile", "triggerVerticalFileInput", "handleVerticalFileDrop", "onDragEnterVertical", "onDragLeaveVertical", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "handleVerticalVideoRemove", "verticalVideoPreviewUrl", "_hoisted_18", "handleVerticalFileChange", "_hoisted_19", "_component_el_alert", "title", "closable", "_component_FourWayVideoUpload", "onUploadSuccess", "handleFourWayUploadSuccess", "onUploadError", "handleFourWayUploadError", "submitUpload", "loading", "uploading", "resetForm", "_hoisted_20", "_hoisted_21", "_component_el_skeleton", "rows", "animated", "statusError", "_hoisted_22", "description", "_hoisted_23", "fetchStatus", "resetTask", "_hoisted_24", "taskInfo", "status", "videoName", "_hoisted_25", "error", "_hoisted_26", "message", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "processingElapsedTime", "_hoisted_32", "toFixed", "showRealtimePreview", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_component_VideoCamera", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_component_el_tag", "_component_RealTimeFrameViewer", "onFrameReceived", "handleHorizontalFrameReceived", "onPlaybackStateChange", "handlePlaybackStateChange", "_hoisted_39", "_hoisted_40", "handleVerticalFrameReceived", "_hoisted_41", "_hoisted_42", "handleFrameReceived", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_component_el_progress", "percentage", "progress", "progressStatus", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_component_Timer", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "color", "_component_Check", "_hoisted_53", "resultId", "_hoisted_54", "viewResult", "_component_Document", "_hoisted_55", "retryAnalysis", "retrying", "_hoisted_56", "uploadProgress", "_hoisted_57", "_component_el_dialog", "previewDialogVisible", "currentPreviewUrl", "_hoisted_58"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\VideoUploadForm.vue"], "sourcesContent": ["<template>\r\n  <!-- 上传表单部分 - 当没有任务ID时显示 -->\r\n  <el-form v-if=\"!taskId\" :model=\"form\" label-position=\"top\" class=\"upload-form\" :rules=\"rules\" ref=\"uploadForm\">\r\n    <!-- 道路类型选择 -->\r\n    <el-form-item label=\"道路类型\" prop=\"roadType\">\r\n      <el-radio-group v-model=\"form.roadType\" size=\"large\">\r\n        <el-radio-button value=\"normal\" label=\"普通道路\"></el-radio-button>\r\n        <el-radio-button value=\"intersection\" label=\"十字路口\"></el-radio-button>\r\n      </el-radio-group>\r\n    </el-form-item>\r\n    \r\n    <!-- 普通道路的单视频上传 -->\r\n    <template v-if=\"form.roadType === 'normal'\">\r\n      <el-form-item label=\"上传视频\" prop=\"singleVideo\">\r\n        <div class=\"upload-area\">\r\n          <div class=\"drop-area\" \r\n               :class=\"{ 'drag-over': isDragOver, 'has-file': form.singleVideoFile }\"\r\n               @click=\"triggerFileInput\" \r\n               @drop.prevent=\"handleFileDrop\" \r\n               @dragover.prevent\r\n               @dragenter.prevent=\"onDragEnter\"\r\n               @dragleave.prevent=\"onDragLeave\">\r\n            <div v-if=\"!form.singleVideoFile\" id=\"dropText\">\r\n              <div class=\"icon-container\">\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"64\" height=\"64\" fill=\"currentColor\" class=\"upload-icon\" viewBox=\"0 0 16 16\">\r\n                  <path d=\"M6.002 5.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0\"/>\r\n                  <path d=\"M2.002 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2zm12 1a1 1 0 0 1 1 1v6.5l-3.777-1.947a.5.5 0 0 0-.577.093l-3.71 3.71-2.66-1.772a.5.5 0 0 0-.63.062L1.002 12V3a1 1 0 0 1 1-1z\"/>\r\n                </svg>\r\n                <div class=\"upload-arrow\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"currentColor\" class=\"arrow-icon\" viewBox=\"0 0 16 16\">\r\n                    <path fill-rule=\"evenodd\" d=\"M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1z\"/>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <p class=\"upload-title\">拖拽视频文件到此处，或 <span class=\"click-upload\">点击上传</span></p>\r\n              <p class=\"upload-hint\">支持 mp4, avi, mov, quicktime 格式视频，文件大小不超过500MB</p>\r\n            </div>\r\n            <div v-else class=\"preview-container\">\r\n              <el-icon class=\"file-icon\"><document /></el-icon>\r\n              <p class=\"file-name\">{{ form.singleVideoFile.name }}</p>\r\n              <span class=\"file-size\">{{ formatFileSize(form.singleVideoFile.size) }}</span>\r\n              <el-button class=\"remove-file-btn\" size=\"small\" @click.stop=\"handleSingleVideoRemove\">移除</el-button>\r\n              <video \r\n                v-if=\"singleVideoPreviewUrl\" \r\n                :src=\"singleVideoPreviewUrl\" \r\n                controls \r\n                class=\"preview-video-inline\"\r\n              ></video>\r\n            </div>\r\n          </div>\r\n          \r\n          <input\r\n            type=\"file\"\r\n            ref=\"fileInput\"\r\n            style=\"display: none;\"\r\n            @change=\"handleFileChange\"\r\n            accept=\"video/mp4,video/avi,video/mov,video/quicktime\"\r\n          />\r\n        </div>\r\n      </el-form-item>\r\n      \r\n      <el-form-item label=\"视频方向\">\r\n        <el-select v-model=\"form.direction\" placeholder=\"请选择视频方向\">\r\n          <el-option label=\"横向\" value=\"horizontal\"></el-option>\r\n          <el-option label=\"纵向\" value=\"vertical\"></el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n    </template>\r\n    \r\n    <!-- 十字路口的视频上传 -->\r\n    <template v-else>\r\n      <!-- 上传模式选择 -->\r\n      <el-form-item label=\"上传模式\" prop=\"intersectionMode\">\r\n        <el-radio-group v-model=\"form.intersectionMode\" size=\"large\">\r\n          <el-radio-button value=\"dual\" label=\"双视频模式（横向+纵向）\"></el-radio-button>\r\n          <el-radio-button value=\"four-way\" label=\"四方向模式（东南西北）\"></el-radio-button>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <!-- 双视频上传模式 -->\r\n      <template v-if=\"form.intersectionMode === 'dual'\">\r\n        <el-form-item label=\"横向视频\" prop=\"horizontalVideo\">\r\n        <div class=\"upload-area\">\r\n          <div class=\"drop-area\" \r\n               :class=\"{ 'drag-over': isDragOverHorizontal, 'has-file': form.horizontalVideoFile }\" \r\n               @click=\"triggerHorizontalFileInput\" \r\n               @drop.prevent=\"handleHorizontalFileDrop\" \r\n               @dragover.prevent\r\n               @dragenter.prevent=\"onDragEnterHorizontal\"\r\n               @dragleave.prevent=\"onDragLeaveHorizontal\">\r\n            <div v-if=\"!form.horizontalVideoFile\" id=\"dropText\">\r\n              <div class=\"icon-container\">\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"64\" height=\"64\" fill=\"currentColor\" class=\"upload-icon\" viewBox=\"0 0 16 16\">\r\n                  <path d=\"M6.002 5.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0\"/>\r\n                  <path d=\"M2.002 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2zm12 1a1 1 0 0 1 1 1v6.5l-3.777-1.947a.5.5 0 0 0-.577.093l-3.71 3.71-2.66-1.772a.5.5 0 0 0-.63.062L1.002 12V3a1 1 0 0 1 1-1z\"/>\r\n                </svg>\r\n                <div class=\"upload-arrow\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"currentColor\" class=\"arrow-icon\" viewBox=\"0 0 16 16\">\r\n                    <path fill-rule=\"evenodd\" d=\"M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1z\"/>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <p class=\"upload-title\">拖拽横向视频文件到此处，或 <span class=\"click-upload\">点击上传</span></p>\r\n              <p class=\"upload-hint\">横向视频应拍摄东西方向的交通</p>\r\n            </div>\r\n            <div v-else class=\"preview-container\">\r\n              <el-icon class=\"file-icon\"><document /></el-icon>\r\n              <p class=\"file-name\">{{ form.horizontalVideoFile.name }}</p>\r\n              <span class=\"file-size\">{{ formatFileSize(form.horizontalVideoFile.size) }}</span>\r\n              <el-button class=\"remove-file-btn\" size=\"small\" @click.stop=\"handleHorizontalVideoRemove\">移除</el-button>\r\n              <video \r\n                v-if=\"horizontalVideoPreviewUrl\" \r\n                :src=\"horizontalVideoPreviewUrl\" \r\n                controls \r\n                class=\"preview-video-inline\"\r\n              ></video>\r\n            </div>\r\n          </div>\r\n          \r\n          <input\r\n            type=\"file\"\r\n            ref=\"horizontalFileInput\"\r\n            style=\"display: none;\"\r\n            @change=\"handleHorizontalFileChange\"\r\n            accept=\"video/mp4,video/avi,video/mov,video/quicktime\"\r\n          />\r\n        </div>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"纵向视频\" prop=\"verticalVideo\">\r\n        <div class=\"upload-area\">\r\n          <div class=\"drop-area\" \r\n               :class=\"{ 'drag-over': isDragOverVertical, 'has-file': form.verticalVideoFile }\" \r\n               @click=\"triggerVerticalFileInput\" \r\n               @drop.prevent=\"handleVerticalFileDrop\" \r\n               @dragover.prevent\r\n               @dragenter.prevent=\"onDragEnterVertical\"\r\n               @dragleave.prevent=\"onDragLeaveVertical\">\r\n            <div v-if=\"!form.verticalVideoFile\" id=\"dropText\">\r\n              <div class=\"icon-container\">\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"64\" height=\"64\" fill=\"currentColor\" class=\"upload-icon\" viewBox=\"0 0 16 16\">\r\n                  <path d=\"M6.002 5.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0\"/>\r\n                  <path d=\"M2.002 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2zm12 1a1 1 0 0 1 1 1v6.5l-3.777-1.947a.5.5 0 0 0-.577.093l-3.71 3.71-2.66-1.772a.5.5 0 0 0-.63.062L1.002 12V3a1 1 0 0 1 1-1z\"/>\r\n                </svg>\r\n                <div class=\"upload-arrow\">\r\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" fill=\"currentColor\" class=\"arrow-icon\" viewBox=\"0 0 16 16\">\r\n                    <path fill-rule=\"evenodd\" d=\"M8 1a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L7.5 13.293V1.5A.5.5 0 0 1 8 1z\"/>\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n              <p class=\"upload-title\">拖拽纵向视频文件到此处，或 <span class=\"click-upload\">点击上传</span></p>\r\n              <p class=\"upload-hint\">纵向视频应拍摄南北方向的交通</p>\r\n            </div>\r\n            <div v-else class=\"preview-container\">\r\n              <el-icon class=\"file-icon\"><document /></el-icon>\r\n              <p class=\"file-name\">{{ form.verticalVideoFile.name }}</p>\r\n              <span class=\"file-size\">{{ formatFileSize(form.verticalVideoFile.size) }}</span>\r\n              <el-button class=\"remove-file-btn\" size=\"small\" @click.stop=\"handleVerticalVideoRemove\">移除</el-button>\r\n              <video \r\n                v-if=\"verticalVideoPreviewUrl\" \r\n                :src=\"verticalVideoPreviewUrl\" \r\n                controls \r\n                class=\"preview-video-inline\"\r\n              ></video>\r\n            </div>\r\n          </div>\r\n          \r\n          <input\r\n            type=\"file\"\r\n            ref=\"verticalFileInput\"\r\n            style=\"display: none;\"\r\n            @change=\"handleVerticalFileChange\"\r\n            accept=\"video/mp4,video/avi,video/mov,video/quicktime\"\r\n          />\r\n        </div>\r\n      </el-form-item>\r\n      </template>\r\n\r\n      <!-- 四方向上传模式 -->\r\n      <template v-else-if=\"form.intersectionMode === 'four-way'\">\r\n        <div class=\"four-way-upload-notice\">\r\n          <el-alert\r\n            title=\"四方向智能交通分析\"\r\n            type=\"info\"\r\n            :closable=\"false\"\r\n            show-icon\r\n          >\r\n            <template #default>\r\n              <p>请上传十字路口四个方向的视频文件，系统将进行智能交通流量分析并生成优化建议。</p>\r\n            </template>\r\n          </el-alert>\r\n        </div>\r\n\r\n        <!-- 使用四方向上传组件 -->\r\n        <FourWayVideoUpload\r\n          @upload-success=\"handleFourWayUploadSuccess\"\r\n          @upload-error=\"handleFourWayUploadError\"\r\n        />\r\n      </template>\r\n    </template>\r\n\r\n    <!-- 提交按钮 - 四方向模式下不显示 -->\r\n    <el-form-item v-if=\"!(form.roadType === 'intersection' && form.intersectionMode === 'four-way')\">\r\n      <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"uploading\">开始分析</el-button>\r\n      <el-button @click=\"resetForm\">重置</el-button>\r\n    </el-form-item>\r\n  </el-form>\r\n\r\n  <!-- 处理状态部分 - 当有任务ID时显示 -->\r\n  <div v-else class=\"status-section\">\r\n    <div v-if=\"loading\" class=\"loading-container\">\r\n      <el-skeleton :rows=\"3\" animated />\r\n    </div>\r\n    \r\n    <div v-else-if=\"statusError\" class=\"error-container\">\r\n      <el-alert\r\n        title=\"获取任务状态失败\"\r\n        type=\"error\"\r\n        :description=\"statusError\"\r\n        show-icon\r\n      />\r\n      <div class=\"error-actions\">\r\n        <el-button type=\"primary\" @click=\"fetchStatus\">重试</el-button>\r\n        <el-button @click=\"resetTask\">重新上传</el-button>\r\n      </div>\r\n    </div>\r\n    \r\n    <div v-else class=\"status-info\">\r\n      <!-- 分析失败状态 -->\r\n      <el-alert\r\n        v-if=\"taskInfo.status === 'failed'\"\r\n        type=\"error\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        class=\"custom-alert\"\r\n      >\r\n        <template #title>\r\n          <strong>分析失败</strong> - 视频处理遇到错误\r\n        </template>\r\n        <p>任务ID: {{ taskId }}</p>\r\n        <p v-if=\"taskInfo.videoName\">视频名称: {{ taskInfo.videoName || '未命名视频' }}</p>\r\n        <p v-if=\"taskInfo.error\" class=\"error-message\">错误信息: {{ taskInfo.error }}</p>\r\n        <p v-else-if=\"taskInfo.message\" class=\"error-message\">{{ taskInfo.message }}</p>\r\n        <p v-else class=\"error-message\">分析过程中发生未知错误</p>\r\n        <div class=\"error-suggestions\">\r\n          <p><strong>可能的原因：</strong></p>\r\n          <ul>\r\n            <li>模型服务未启动或不在线</li>\r\n            <li>视频格式不支持或文件损坏</li>\r\n            <li>服务器资源不足或处理超时</li>\r\n            <li>网络连接中断</li>\r\n          </ul>\r\n        </div>\r\n        <div class=\"error-actions\">\r\n          <el-button type=\"primary\" @click=\"fetchStatus\">重新检查状态</el-button>\r\n          <el-button @click=\"resetTask\">重新上传</el-button>\r\n        </div>\r\n      </el-alert>\r\n\r\n      <!-- 分析中状态 -->\r\n      <el-alert\r\n        v-else-if=\"taskInfo.status !== 'completed'\"\r\n        type=\"info\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        class=\"custom-alert\"\r\n      >\r\n        <template #title>\r\n          <strong>分析中</strong> - 您的视频正在处理\r\n        </template>\r\n        <p>任务ID: {{ taskId }}</p>\r\n        <p v-if=\"taskInfo.videoName\">视频名称: {{ taskInfo.videoName || '未命名视频' }}</p>\r\n        <p v-if=\"taskInfo.message\">{{ taskInfo.message }}</p>\r\n        <p v-if=\"processingElapsedTime > 0\">处理时间: {{ processingElapsedTime.toFixed(2) }} 秒</p>\r\n      </el-alert>\r\n\r\n      <!-- 实时预览组件 - 仅在十字路口视频分析中显示 -->\r\n      <div v-if=\"taskInfo.status !== 'completed' && taskInfo.status !== 'failed' && showRealtimePreview\" class=\"realtime-preview-section\">\r\n\r\n        <!-- 十字路口双视频预览 -->\r\n        <div v-if=\"form?.roadType === 'intersection' || true\" class=\"dual-video-preview\">\r\n          <h3 class=\"preview-title\">\r\n            <el-icon><VideoCamera /></el-icon>\r\n            实时视频分析预览\r\n          </h3>\r\n\r\n          <div class=\"video-preview-grid\">\r\n            <div class=\"video-preview-container\">\r\n              <div class=\"video-header\">\r\n                <h4>水平方向视频</h4>\r\n                <el-tag type=\"primary\" size=\"small\">实时分析中</el-tag>\r\n              </div>\r\n              <RealTimeFrameViewer\r\n                ref=\"horizontalFrameViewer\"\r\n                :task-id=\"`h_${taskId}`\"\r\n                :auto-start=\"true\"\r\n                :max-buffer-frames=\"30\"\r\n                :title=\"'水平方向'\"\r\n                @frame-received=\"handleHorizontalFrameReceived\"\r\n                @playback-state-change=\"handlePlaybackStateChange\"\r\n              />\r\n            </div>\r\n\r\n            <div class=\"video-preview-container\">\r\n              <div class=\"video-header\">\r\n                <h4>垂直方向视频</h4>\r\n                <el-tag type=\"success\" size=\"small\">实时分析中</el-tag>\r\n              </div>\r\n              <RealTimeFrameViewer\r\n                ref=\"verticalFrameViewer\"\r\n                :task-id=\"`v_${taskId}`\"\r\n                :auto-start=\"true\"\r\n                :max-buffer-frames=\"30\"\r\n                :title=\"'垂直方向'\"\r\n                @frame-received=\"handleVerticalFrameReceived\"\r\n                @playback-state-change=\"handlePlaybackStateChange\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 单视频预览 -->\r\n        <div v-else class=\"single-video-preview\">\r\n          <h3 class=\"preview-title\">\r\n            <el-icon><VideoCamera /></el-icon>\r\n            实时视频分析预览\r\n          </h3>\r\n          <RealTimeFrameViewer\r\n            ref=\"frameViewer\"\r\n            :task-id=\"taskId\"\r\n            :auto-start=\"true\"\r\n            :max-buffer-frames=\"30\"\r\n            @frame-received=\"handleFrameReceived\"\r\n            @playback-state-change=\"handlePlaybackStateChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分析完成状态 -->\r\n      <el-alert\r\n        v-else\r\n        type=\"success\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        class=\"custom-alert\"\r\n      >\r\n        <template #title>\r\n          <strong>分析完成</strong> - 您的视频已处理完毕\r\n        </template>\r\n        <p>任务ID: {{ taskId }}</p>\r\n        <p v-if=\"taskInfo.videoName\">视频名称: {{ taskInfo.videoName || '未命名视频' }}</p>\r\n        <p v-if=\"processingElapsedTime > 0\">处理总时间: {{ processingElapsedTime.toFixed(2) }} 秒</p>\r\n      </el-alert>\r\n      \r\n      <div class=\"status-progress\" v-if=\"taskInfo.status !== 'failed'\">\r\n        <p>处理进度:</p>\r\n        <el-progress\r\n          :percentage=\"taskInfo.progress || 0\"\r\n          :status=\"progressStatus\"\r\n          :stroke-width=\"18\"\r\n          text-inside\r\n          :show-text=\"true\"\r\n          class=\"custom-progress\"\r\n        />\r\n      </div>\r\n\r\n      <!-- 失败状态的进度条 -->\r\n      <div class=\"status-progress\" v-else>\r\n        <p>处理状态:</p>\r\n        <el-progress\r\n          :percentage=\"100\"\r\n          status=\"exception\"\r\n          :stroke-width=\"18\"\r\n          text-inside\r\n          :show-text=\"true\"\r\n          class=\"custom-progress\"\r\n        >\r\n          <template #default=\"{ percentage }\">\r\n            <span class=\"progress-text\">分析失败</span>\r\n          </template>\r\n        </el-progress>\r\n      </div>\r\n      \r\n      <!-- 处理时间显示 -->\r\n      <div v-if=\"processingElapsedTime > 0\" class=\"processing-time\">\r\n        <div class=\"time-header\">\r\n          <el-icon><Timer /></el-icon> \r\n          <span>处理时间</span>\r\n        </div>\r\n        <div class=\"time-value-container\">\r\n          <span class=\"time-value\">{{ processingElapsedTime.toFixed(2) }}</span> \r\n          <span class=\"time-unit\">秒</span>\r\n          <div v-if=\"taskInfo.status === 'processing'\" class=\"timer-animation\">\r\n            <span class=\"dot\"></span>\r\n            <span class=\"dot\"></span>\r\n            <span class=\"dot\"></span>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"taskInfo.status === 'completed'\" class=\"processing-complete\">\r\n          <el-icon color=\"#10b981\"><Check /></el-icon> 处理完成\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"status-actions\">\r\n        <div v-if=\"taskInfo.status === 'completed' && taskInfo.resultId\" class=\"completed-actions\">\r\n          <el-button \r\n            type=\"primary\" \r\n            class=\"view-result-btn\"\r\n            @click=\"viewResult\"\r\n            size=\"large\"\r\n          >\r\n            <el-icon class=\"view-icon\"><Document /></el-icon> 查看详情\r\n          </el-button>\r\n          \r\n          <el-button \r\n            @click=\"resetTask\"\r\n            class=\"reset-btn\"\r\n            size=\"large\"\r\n          >\r\n            重新上传\r\n          </el-button>\r\n        </div>\r\n        \r\n        <div v-else-if=\"taskInfo.status === 'failed'\" class=\"failed-actions\">\r\n          <el-button \r\n            type=\"warning\" \r\n            @click=\"retryAnalysis\"\r\n            :loading=\"retrying\"\r\n            size=\"large\"\r\n          >\r\n            重新分析\r\n          </el-button>\r\n          \r\n          <el-button \r\n            @click=\"resetTask\"\r\n            size=\"large\"\r\n          >\r\n            重新上传\r\n          </el-button>\r\n        </div>\r\n        \r\n        <div v-else class=\"processing-actions\">\r\n          <el-button \r\n            @click=\"resetTask\"\r\n            size=\"large\"\r\n          >\r\n            重新上传\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- 上传进度条 - 仅在上传过程中显示 -->\r\n  <div v-if=\"uploadProgress > 0 && uploadProgress < 100 && !taskId\" class=\"upload-progress\">\r\n    <p>上传进度:</p>\r\n    <el-progress \r\n      :percentage=\"uploadProgress\" \r\n      :status=\"uploadProgress < 100 ? '' : 'success'\"\r\n      :stroke-width=\"18\"\r\n      :show-text=\"true\"\r\n      text-inside\r\n      class=\"custom-progress\"\r\n    ></el-progress>\r\n  </div>\r\n\r\n  <!-- 视频预览对话框 -->\r\n  <el-dialog\r\n    title=\"视频预览\"\r\n    v-model=\"previewDialogVisible\"\r\n    width=\"70%\"\r\n    class=\"preview-dialog\"\r\n  >\r\n    <video \r\n      v-if=\"currentPreviewUrl\" \r\n      :src=\"currentPreviewUrl\" \r\n      controls \r\n      style=\"width: 100%;\"\r\n    ></video>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted, onUnmounted, onBeforeUnmount, watch, nextTick } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport { \r\n  uploadAndAnalyzeVideo, \r\n  uploadVideoDirectApi, \r\n  uploadIntersectionDirectApi,\r\n  getVideoTaskStatus,\r\n  retryVideoAnalysis,\r\n  saveVideoProcessingTime\r\n} from '@/api/video'\r\nimport { STOMP_VIDEO_PROGRESS } from '@/config'\r\nimport stompService from '@/utils/stomp-service'\r\nimport apiClient from '@/utils/http-common'\r\nimport {\r\n  UploadFilled,\r\n  Document,\r\n  Timer,\r\n  Check,\r\n  VideoCamera\r\n} from '@element-plus/icons-vue'\r\nimport RealTimeFrameViewer from '@/components/video/RealTimeFrameViewer.vue'\r\nimport FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue'\r\n\r\nexport default {\r\n  name: 'VideoUploadForm',\r\n  components: {\r\n    UploadFilled,\r\n    Document,\r\n    Timer,\r\n    Check,\r\n    VideoCamera,\r\n    RealTimeFrameViewer,\r\n    FourWayVideoUpload\r\n  },\r\n  emits: ['analysisComplete', 'analysisStatusChange'],\r\n  props: {\r\n    initialTaskId: {\r\n      type: String,\r\n      default: null\r\n    }\r\n  },\r\n  setup(props, { emit }) {\r\n    const router = useRouter()\r\n    \r\n    // 基本状态初始化\r\n    const uploadForm = ref(null)\r\n    const fileInput = ref(null)\r\n    const taskId = ref(props.initialTaskId || null)\r\n    const uploading = ref(false)\r\n    const uploadProgress = ref(0)\r\n    const maxSize = 500 * 1024 * 1024 // 500MB\r\n    \r\n    // 添加处理时间计时器\r\n    const processingStartTime = ref(null)\r\n    const processingElapsedTime = ref(0)\r\n    const processingTimer = ref(null)\r\n    const processingTimeInterval = ref(null)\r\n    \r\n    // 拖拽状态\r\n    const isDragOver = ref(false);\r\n    const isDragOverHorizontal = ref(false);\r\n    const isDragOverVertical = ref(false);\r\n\r\n    // 实时预览相关状态\r\n    const frameViewer = ref(null);\r\n    const showRealtimePreview = ref(false);\r\n    const frameSubscription = ref(null);\r\n    \r\n    // 表单数据\r\n    const form = reactive({\r\n      roadType: 'normal', // 默认为普通道路\r\n      intersectionMode: 'dual', // 默认为双视频模式\r\n      direction: 'horizontal', // 默认为横向\r\n      singleVideoFile: null,\r\n      horizontalVideoFile: null,\r\n      verticalVideoFile: null\r\n    })\r\n    \r\n    // 表单验证规则\r\n    const rules = {\r\n      roadType: [\r\n        { required: true, message: '请选择道路类型', trigger: 'change' }\r\n      ],\r\n      singleVideo: [\r\n        { required: true, message: '请上传视频文件', trigger: 'change', validator: (rule, value, callback) => {\r\n          if (form.roadType === 'normal' && !form.singleVideoFile) {\r\n            callback(new Error('请上传视频文件'));\r\n          } else {\r\n            callback();\r\n          }\r\n        }}\r\n      ],\r\n      horizontalVideo: [\r\n        { required: true, message: '请上传横向视频', trigger: 'change', validator: (rule, value, callback) => {\r\n          if (form.roadType === 'intersection' && !form.horizontalVideoFile) {\r\n            callback(new Error('请上传横向视频'));\r\n          } else {\r\n            callback();\r\n          }\r\n        }}\r\n      ],\r\n      verticalVideo: [\r\n        { required: true, message: '请上传纵向视频', trigger: 'change', validator: (rule, value, callback) => {\r\n          if (form.roadType === 'intersection' && !form.verticalVideoFile) {\r\n            callback(new Error('请上传纵向视频'));\r\n          } else {\r\n            callback();\r\n          }\r\n        }}\r\n      ]\r\n    }\r\n    \r\n    // 视频预览URL - 使用base64 Data URL\r\n    const singleVideoPreviewUrl = ref('')\r\n    const horizontalVideoPreviewUrl = ref('')\r\n    const verticalVideoPreviewUrl = ref('')\r\n    const currentPreviewUrl = ref('')\r\n    const previewDialogVisible = ref(false)\r\n    \r\n    // 任务状态相关\r\n    const loading = ref(false)\r\n    const statusError = ref('')\r\n    const taskInfo = ref({})\r\n    const retrying = ref(false)\r\n    const statusInterval = ref(null)\r\n    let stompSubscription = null\r\n    \r\n    // 计算属性：进度条状态\r\n    const progressStatus = computed(() => {\r\n      const status = taskInfo.value.status;\r\n      if (status === 'completed') return 'success';\r\n      if (status === 'failed') return 'exception';\r\n      return '';\r\n    })\r\n    \r\n    // 监听道路类型变化，重置表单\r\n    watch(() => form.roadType, (newValue) => {\r\n      // 清除已上传的文件\r\n      if (newValue === 'normal') {\r\n        if (form.horizontalVideoFile) handleHorizontalVideoRemove()\r\n        if (form.verticalVideoFile) handleVerticalVideoRemove()\r\n      } else {\r\n        if (form.singleVideoFile) handleSingleVideoRemove()\r\n      }\r\n    })\r\n    \r\n    // 拖拽处理方法 - 单视频\r\n    const onDragEnter = () => {\r\n      isDragOver.value = true;\r\n    };\r\n    \r\n    const onDragLeave = () => {\r\n      isDragOver.value = false;\r\n    };\r\n    \r\n    // 拖拽处理方法 - 横向视频\r\n    const onDragEnterHorizontal = () => {\r\n      isDragOverHorizontal.value = true;\r\n    };\r\n    \r\n    const onDragLeaveHorizontal = () => {\r\n      isDragOverHorizontal.value = false;\r\n    };\r\n    \r\n    // 拖拽处理方法 - 纵向视频\r\n    const onDragEnterVertical = () => {\r\n      isDragOverVertical.value = true;\r\n    };\r\n    \r\n    const onDragLeaveVertical = () => {\r\n      isDragOverVertical.value = false;\r\n    };\r\n    \r\n    // 格式化文件大小显示\r\n    const formatFileSize = (bytes) => {\r\n      if (bytes === 0) return '0 B';\r\n      const k = 1024;\r\n      const sizes = ['B', 'KB', 'MB', 'GB'];\r\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n    }\r\n    \r\n    // 验证文件类型\r\n    const validateFileType = (file) => {\r\n      const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/quicktime'];\r\n      if (!allowedTypes.includes(file.type)) {\r\n        ElMessage.error('不支持的文件类型，请上传mp4、avi、mov或quicktime格式的视频');\r\n        return false;\r\n      }\r\n      return true;\r\n    }\r\n    \r\n    // 检查文件大小\r\n    const checkFileSize = (file) => {\r\n      if (file.size > maxSize) {\r\n        ElMessage.error(`文件大小超过限制，请上传小于 ${formatFileSize(maxSize)} 的文件`);\r\n        return false;\r\n      }\r\n      return true;\r\n    }\r\n    \r\n    // 创建视频预览\r\n    const createVideoPreview = (file, previewUrl) => {\r\n      return new Promise((resolve, reject) => {\r\n        const videoURL = URL.createObjectURL(file);\r\n        previewUrl.value = videoURL;\r\n        resolve(videoURL);\r\n      });\r\n    }\r\n    \r\n    // 单视频上传处理 - 文件输入和拖拽\r\n    const triggerFileInput = () => {\r\n      fileInput.value.click();\r\n    };\r\n    \r\n    const handleFileDrop = (event) => {\r\n      isDragOver.value = false;\r\n      const files = event.dataTransfer.files;\r\n      if (files.length > 0) {\r\n        handleFileChange({ target: { files: Array.from(files) } });\r\n      }\r\n    };\r\n    \r\n    const handleFileChange = (event) => {\r\n      const files = event.target.files;\r\n      if (files.length > 0) {\r\n        handleUpload({ file: files[0] });\r\n      }\r\n    };\r\n    \r\n    const handleUpload = (options) => {\r\n      const file = options.file;\r\n      \r\n      if (!file) {\r\n\r\n        return;\r\n      }\r\n      \r\n      // 验证文件类型和大小\r\n      if (!validateFileType(file) || !checkFileSize(file)) {\r\n        return;\r\n      }\r\n      \r\n      // 设置表单数据\r\n      form.singleVideoFile = file;\r\n\r\n      \r\n      // 创建预览\r\n      createVideoPreview(file, singleVideoPreviewUrl)\r\n        .catch(err => {\r\n\r\n          ElMessage.warning('视频预览生成失败，但您仍可以上传');\r\n        });\r\n        \r\n      // 如果是input change事件，清空它以允许选择相同的文件\r\n      if (fileInput.value) {\r\n        fileInput.value.value = '';\r\n      }\r\n    };\r\n    \r\n    const handleSingleVideoRemove = (e) => {\r\n      if (e) {\r\n        e.stopPropagation(); // 防止冒泡到上传区域\r\n      }\r\n      \r\n      form.singleVideoFile = null;\r\n      // 清除预览URL\r\n      singleVideoPreviewUrl.value = '';\r\n      \r\n      // 清空文件输入\r\n      if (fileInput.value) {\r\n        fileInput.value.value = '';\r\n      }\r\n    };\r\n\r\n    // 横向视频上传处理\r\n    const horizontalFileInput = ref(null);\r\n    const verticalFileInput = ref(null);\r\n    \r\n    // 触发横向视频文件选择\r\n    const triggerHorizontalFileInput = () => {\r\n      horizontalFileInput.value.click();\r\n    };\r\n    \r\n    // 处理横向视频拖拽\r\n    const handleHorizontalFileDrop = (event) => {\r\n      const files = event.dataTransfer.files;\r\n      if (files.length > 0) {\r\n        handleHorizontalFileChange({ target: { files: Array.from(files) } });\r\n      }\r\n    };\r\n    \r\n    // 处理横向视频文件选择\r\n    const handleHorizontalFileChange = (event) => {\r\n      const files = event.target.files;\r\n      if (files.length > 0) {\r\n        handleHorizontalVideoUpload(files[0]);\r\n      }\r\n    };\r\n    \r\n    // 处理横向视频上传\r\n    const handleHorizontalVideoUpload = (file) => {\r\n      if (!validateFileType(file) || !checkFileSize(file)) {\r\n        return;\r\n      }\r\n      \r\n      // 清除之前的预览URL\r\n      horizontalVideoPreviewUrl.value = '';\r\n      \r\n      // 设置表单数据\r\n      form.horizontalVideoFile = file;\r\n      console.log('设置横向视频文件:', file.name, file);\r\n      \r\n      // 创建预览\r\n      createVideoPreview(file, horizontalVideoPreviewUrl)\r\n        .catch(err => {\r\n          console.error('创建横向视频预览失败:', err);\r\n          ElMessage.warning('视频预览生成失败，但您仍可以上传');\r\n        });\r\n      \r\n      // 清空输入以允许选择相同的文件\r\n      if (horizontalFileInput.value) {\r\n        horizontalFileInput.value.value = '';\r\n      }\r\n    };\r\n    \r\n    // 横向视频删除\r\n    const handleHorizontalVideoRemove = (e) => {\r\n      if (e) {\r\n        e.stopPropagation(); // 防止冒泡到上传区域\r\n      }\r\n      \r\n      form.horizontalVideoFile = null;\r\n      // 清除预览URL\r\n      horizontalVideoPreviewUrl.value = '';\r\n      \r\n      // 清空文件输入\r\n      if (horizontalFileInput.value) {\r\n        horizontalFileInput.value.value = '';\r\n      }\r\n    };\r\n    \r\n    // 触发纵向视频文件选择\r\n    const triggerVerticalFileInput = () => {\r\n      verticalFileInput.value.click();\r\n    };\r\n    \r\n    // 处理纵向视频拖拽\r\n    const handleVerticalFileDrop = (event) => {\r\n      const files = event.dataTransfer.files;\r\n      if (files.length > 0) {\r\n        handleVerticalFileChange({ target: { files: Array.from(files) } });\r\n      }\r\n    };\r\n    \r\n    // 处理纵向视频文件选择\r\n    const handleVerticalFileChange = (event) => {\r\n      const files = event.target.files;\r\n      if (files.length > 0) {\r\n        handleVerticalVideoUpload(files[0]);\r\n      }\r\n    };\r\n    \r\n    // 处理纵向视频上传\r\n    const handleVerticalVideoUpload = (file) => {\r\n      if (!validateFileType(file) || !checkFileSize(file)) {\r\n        return;\r\n      }\r\n      \r\n      // 清除之前的预览URL\r\n      verticalVideoPreviewUrl.value = '';\r\n      \r\n      // 设置表单数据\r\n      form.verticalVideoFile = file;\r\n\r\n      \r\n      // 创建预览\r\n      createVideoPreview(file, verticalVideoPreviewUrl)\r\n        .catch(err => {\r\n\r\n          ElMessage.warning('视频预览生成失败，但您仍可以上传');\r\n        });\r\n      \r\n      // 清空输入以允许选择相同的文件\r\n      if (verticalFileInput.value) {\r\n        verticalFileInput.value.value = '';\r\n      }\r\n    };\r\n    \r\n    // 纵向视频删除\r\n    const handleVerticalVideoRemove = (e) => {\r\n      if (e) {\r\n        e.stopPropagation(); // 防止冒泡到上传区域\r\n      }\r\n      \r\n      form.verticalVideoFile = null;\r\n      // 清除预览URL\r\n      verticalVideoPreviewUrl.value = '';\r\n      \r\n      // 清空文件输入\r\n      if (verticalFileInput.value) {\r\n        verticalFileInput.value.value = '';\r\n      }\r\n    };\r\n\r\n    // 使用WebSocket连接来接收上传和处理进度\r\n    const initWebSocket = async (taskId) => {\r\n      try {\r\n        console.log(`🔌 初始化视频进度WebSocket订阅: ${STOMP_VIDEO_PROGRESS}/${taskId}`);\r\n\r\n        // 确保WebSocket连接稳定\r\n        await stompService.ensureConnection();\r\n        console.log(`✅ WebSocket连接已确认`);\r\n\r\n        // 使用STOMP服务订阅视频进度主题\r\n        const subscription = await stompService.subscribe(`${STOMP_VIDEO_PROGRESS}/${taskId}`, (data) => {\r\n          try {\r\n            if (data.type === 'upload_progress') {\r\n              uploadProgress.value = data.progress;\r\n            } else if (data.type === 'processing_progress') {\r\n              // 处理进度更新\r\n              if (taskInfo.value) {\r\n                taskInfo.value.progress = data.progress || taskInfo.value.progress;\r\n                taskInfo.value.status = data.status || taskInfo.value.status;\r\n                taskInfo.value.message = data.message || taskInfo.value.message;\r\n                \r\n                // 当收到第一个处理状态时启动计时器\r\n                if (data.status === 'processing' && !processingStartTime.value) {\r\n                  startProcessingTimer();\r\n                }\r\n                \r\n                if (data.status === 'completed' && data.resultId) {\r\n                  taskInfo.value.resultId = data.resultId;\r\n                  \r\n                  // 停止计时器并记录处理时间\r\n                  stopProcessingTimer();\r\n                  \r\n                  // 自动跳转到结果页面\r\n                  setTimeout(() => {\r\n                    viewResult();\r\n                  }, 2000);\r\n                }\r\n              }\r\n            }\r\n          } catch (err) {\r\n            console.error('处理WebSocket消息失败:', err);\r\n          }\r\n        });\r\n\r\n        console.log(`✅ 视频进度订阅成功`);\r\n        stompSubscription = subscription;\r\n\r\n      } catch (error) {\r\n        console.error('❌ 初始化WebSocket订阅失败:', error);\r\n\r\n        // 如果是连接问题，尝试重连\r\n        if (error.message && error.message.includes('STOMP connection')) {\r\n          console.log('🔄 检测到连接问题，尝试重连...');\r\n          try {\r\n            await stompService.reconnect();\r\n            // 重连成功后重新订阅\r\n            return await initWebSocket(taskId);\r\n          } catch (reconnectError) {\r\n            console.error('❌ 重连失败:', reconnectError);\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 模拟上传进度（当后端不支持实时进度更新时使用）\r\n    const simulateUploadProgress = () => {\r\n      let progress = 0\r\n      const interval = setInterval(() => {\r\n        if (progress < 98) {\r\n          progress += (98 - progress) / 10\r\n          uploadProgress.value = Math.floor(progress)\r\n        } else {\r\n          clearInterval(interval)\r\n        }\r\n      }, 200)\r\n      \r\n      return interval\r\n    }\r\n    \r\n    // 获取任务状态\r\n    const fetchStatus = async () => {\r\n      if (!taskId.value) return;\r\n      \r\n      loading.value = true;\r\n      statusError.value = '';\r\n      \r\n      try {\r\n        const response = await getVideoTaskStatus(taskId.value);\r\n\r\n        if (response && response.data) {\r\n          taskInfo.value = response.data;\r\n\r\n          // 如果任务状态为处理中且计时器未启动，则启动计时器\r\n          if (taskInfo.value.status === 'processing' && !processingStartTime.value) {\r\n            startProcessingTimer();\r\n          }\r\n\r\n          // 如果任务已完成或失败，停止轮询和计时器\r\n          if (['completed', 'failed'].includes(taskInfo.value.status)) {\r\n            clearInterval(statusInterval.value);\r\n            stopProcessingTimer();\r\n\r\n            // 如果任务失败，显示详细错误信息\r\n            if (taskInfo.value.status === 'failed') {\r\n              const errorMsg = taskInfo.value.error || taskInfo.value.message || '分析过程中发生未知错误';\r\n              ElMessage.error(`视频分析失败: ${errorMsg}`);\r\n\r\n              // 根据错误类型提供更具体的提示\r\n              if (errorMsg.includes('模型') || errorMsg.includes('model')) {\r\n                ElMessage.warning('建议检查模型服务是否正常运行');\r\n              } else if (errorMsg.includes('网络') || errorMsg.includes('连接')) {\r\n                ElMessage.warning('建议检查网络连接状态');\r\n              } else if (errorMsg.includes('格式') || errorMsg.includes('编码')) {\r\n                ElMessage.warning('建议检查视频格式是否支持');\r\n              }\r\n            }\r\n          }\r\n\r\n          // 如果任务完成，自动跳转到结果页\r\n          if (taskInfo.value.status === 'completed' && taskInfo.value.resultId) {\r\n            setTimeout(() => {\r\n              viewResult();\r\n            }, 2000);\r\n          }\r\n        } else {\r\n          throw new Error('获取任务状态失败');\r\n        }\r\n      } catch (err) {\r\n        // 增强错误处理\r\n        let errorMessage = '获取任务状态失败';\r\n\r\n        if (err.response) {\r\n          if (err.response.status === 404) {\r\n            errorMessage = '任务不存在或已被删除';\r\n          } else if (err.response.status === 401) {\r\n            errorMessage = '认证失败，请重新登录';\r\n          } else if (err.response.status === 500) {\r\n            errorMessage = '服务器内部错误，可能是模型服务未启动';\r\n          } else if (err.response.status === 503) {\r\n            errorMessage = '服务暂时不可用，模型可能不在线';\r\n          } else {\r\n            errorMessage = `服务器错误 (${err.response.status})`;\r\n          }\r\n        } else if (err.request) {\r\n          errorMessage = '无法连接到服务器，请检查网络连接';\r\n        } else {\r\n          errorMessage = err.message || '获取任务状态失败';\r\n        }\r\n\r\n        statusError.value = errorMessage;\r\n        console.error('获取任务状态错误:', err);\r\n        ElMessage.error(errorMessage);\r\n      } finally {\r\n        loading.value = false;\r\n      }\r\n    };\r\n    \r\n    // 查看结果\r\n    const viewResult = () => {\r\n      if (taskInfo.value.resultId) {\r\n        // 直接跳转到视频结果页面，使用智能ID路径\r\n        console.log('跳转到视频结果页面，结果ID:', taskInfo.value.resultId);\r\n        router.push(`/video-result/id/${taskInfo.value.resultId}`);\r\n\r\n        // 同时触发事件，保持向后兼容\r\n        emit('analysisComplete', taskInfo.value.resultId);\r\n      } else if (taskId.value) {\r\n        // 如果没有resultId，尝试使用taskId\r\n        console.log('使用任务ID跳转到视频结果页面，任务ID:', taskId.value);\r\n        router.push(`/video-result/id/${taskId.value}`);\r\n\r\n        emit('analysisComplete', taskId.value);\r\n      } else {\r\n        ElMessage.warning('结果ID不存在，无法查看结果');\r\n      }\r\n    };\r\n    \r\n    // 重新分析\r\n    const retryAnalysis = async () => {\r\n      retrying.value = true;\r\n\r\n      try {\r\n        const response = await retryVideoAnalysis(taskId.value);\r\n\r\n        if (response && response.data && response.data.success) {\r\n          ElMessage.success('已重新提交分析任务');\r\n          // 重置状态并开始轮询\r\n          taskInfo.value.status = 'queued';\r\n          taskInfo.value.progress = 0;\r\n          taskInfo.value.error = null; // 清除之前的错误信息\r\n          taskInfo.value.message = '重新分析中...';\r\n          startPolling();\r\n        } else {\r\n          throw new Error('重新分析失败');\r\n        }\r\n      } catch (err) {\r\n        // 增强错误处理\r\n        let errorMessage = '重新分析失败';\r\n\r\n        if (err.response) {\r\n          if (err.response.status === 404) {\r\n            errorMessage = '任务不存在或已被删除，无法重新分析';\r\n          } else if (err.response.status === 401) {\r\n            errorMessage = '认证失败，请重新登录';\r\n          } else if (err.response.status === 500) {\r\n            errorMessage = '服务器内部错误，可能是模型服务未启动';\r\n          } else if (err.response.status === 503) {\r\n            errorMessage = '服务暂时不可用，模型可能不在线';\r\n          } else {\r\n            errorMessage = `重新分析失败 (${err.response.status})：${err.response.data?.message || '未知错误'}`;\r\n          }\r\n        } else if (err.request) {\r\n          errorMessage = '无法连接到服务器，请检查网络连接';\r\n        } else {\r\n          errorMessage = err.message || '重新分析失败';\r\n        }\r\n\r\n        ElMessage.error(errorMessage);\r\n        console.error('重新分析失败:', err);\r\n      } finally {\r\n        retrying.value = false;\r\n      }\r\n    };\r\n    \r\n    // 重置任务（清除taskId，返回到上传状态）\r\n    const resetTask = () => {\r\n      taskId.value = null;\r\n      taskInfo.value = {};\r\n      clearInterval(statusInterval.value);\r\n      \r\n      // 停止处理时间计时器\r\n      stopProcessingTimer();\r\n      \r\n      // 取消STOMP订阅\r\n      if (stompSubscription) {\r\n        stompService.unsubscribe(`${STOMP_VIDEO_PROGRESS}/${taskId.value}`);\r\n        stompSubscription = null;\r\n      }\r\n      \r\n      // 清除会话存储的任务ID\r\n      try {\r\n        sessionStorage.removeItem('uploadState');\r\n      } catch (err) {\r\n        console.warn('无法清除会话存储中的上传状态:', err);\r\n      }\r\n\r\n      // 清理实时预览资源\r\n      cleanupRealtimePreview();\r\n    };\r\n\r\n    // 初始化实时预览功能\r\n    const initRealtimePreview = async (taskId) => {\r\n      try {\r\n        console.log(`🚀 开始初始化任务 ${taskId} 的实时预览功能`);\r\n\r\n        // 检查STOMP服务状态\r\n        console.log(`📡 STOMP服务连接状态: ${stompService.connected}`);\r\n\r\n        // 启用实时预览显示\r\n        showRealtimePreview.value = true;\r\n        console.log(`✅ 实时预览显示已启用`);\r\n\r\n        // 等待组件渲染完成\r\n        await nextTick();\r\n        console.log(`🎨 组件渲染完成`);\r\n\r\n        // 根据道路类型订阅不同的帧数据\r\n        // 检查是否为十字路口视频（通过检查任务状态或默认为十字路口）\r\n        const isIntersection = form.value?.roadType === 'intersection' || true; // 默认为十字路口\r\n\r\n        if (isIntersection) {\r\n          console.log(`📺 开始订阅十字路口双视频帧数据...`);\r\n\r\n          // 订阅水平方向帧数据\r\n          const horizontalSubscription = await stompService.subscribeFrameUpdates(`h_${taskId}`, (frameData) => {\r\n            console.log(`📦 收到水平方向帧数据:`, frameData);\r\n            handleFrameData(frameData);\r\n          });\r\n\r\n          // 订阅垂直方向帧数据\r\n          const verticalSubscription = await stompService.subscribeFrameUpdates(`v_${taskId}`, (frameData) => {\r\n            console.log(`📦 收到垂直方向帧数据:`, frameData);\r\n            handleFrameData(frameData);\r\n          });\r\n\r\n          frameSubscription.value = {\r\n            horizontal: horizontalSubscription,\r\n            vertical: verticalSubscription\r\n          };\r\n\r\n          console.log(`✅ 十字路口双视频预览功能已启动，任务ID: ${taskId}`);\r\n\r\n        } else {\r\n          console.log(`📺 开始订阅单视频帧数据...`);\r\n\r\n          // 订阅单视频帧数据\r\n          frameSubscription.value = await stompService.subscribeFrameUpdates(taskId, (frameData) => {\r\n            console.log(`📦 收到帧数据:`, frameData);\r\n            handleFrameData(frameData);\r\n          });\r\n\r\n          console.log(`✅ 单视频预览功能已启动，任务ID: ${taskId}`);\r\n        }\r\n\r\n        console.log(`📊 订阅对象:`, frameSubscription.value);\r\n\r\n      } catch (error) {\r\n        console.error('❌ 初始化实时预览失败:', error);\r\n        console.error('错误详情:', error.stack);\r\n        showRealtimePreview.value = false;\r\n      }\r\n    };\r\n\r\n    // 处理接收到的帧数据\r\n    const handleFrameData = (frameData) => {\r\n      try {\r\n        console.log(`🎬 处理帧数据:`, {\r\n          frameNumber: frameData.frameNumber,\r\n          detectionCount: frameData.detectionCount,\r\n          hasImageData: !!frameData.imageData,\r\n          imageDataSize: frameData.imageData ? frameData.imageData.length : 0\r\n        });\r\n\r\n        if (frameViewer.value && frameViewer.value.addFrameData) {\r\n          console.log(`📺 向RealTimeFrameViewer添加帧数据`);\r\n          frameViewer.value.addFrameData(frameData);\r\n          console.log(`✅ 帧数据已添加到预览组件`);\r\n        } else {\r\n          console.warn(`⚠️ RealTimeFrameViewer组件不可用:`, {\r\n            frameViewerExists: !!frameViewer.value,\r\n            addFrameDataExists: frameViewer.value ? !!frameViewer.value.addFrameData : false\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ 处理帧数据失败:', error);\r\n        console.error('错误详情:', error.stack);\r\n      }\r\n    };\r\n\r\n    // 处理帧接收事件\r\n    const handleFrameReceived = (frameData) => {\r\n      console.log(`接收到帧数据: 帧${frameData.frameNumber}, 车辆数${frameData.detectionCount}`);\r\n    };\r\n\r\n    // 处理水平方向帧接收事件\r\n    const handleHorizontalFrameReceived = (frameData) => {\r\n      console.log(`🔄 水平方向帧数据: 帧${frameData.frameNumber}, 车辆数${frameData.detectionCount}`);\r\n    };\r\n\r\n    // 处理垂直方向帧接收事件\r\n    const handleVerticalFrameReceived = (frameData) => {\r\n      console.log(`🔄 垂直方向帧数据: 帧${frameData.frameNumber}, 车辆数${frameData.detectionCount}`);\r\n    };\r\n\r\n    // 处理播放状态变化\r\n    const handlePlaybackStateChange = (state) => {\r\n      console.log('播放状态变化:', state);\r\n    };\r\n\r\n    // 清理实时预览资源\r\n    const cleanupRealtimePreview = () => {\r\n      try {\r\n        if (frameSubscription.value && taskId.value) {\r\n          stompService.clearFrameBuffer(taskId.value);\r\n          frameSubscription.value = null;\r\n        }\r\n\r\n        showRealtimePreview.value = false;\r\n\r\n        if (frameViewer.value && frameViewer.value.clearFrameData) {\r\n          frameViewer.value.clearFrameData();\r\n        }\r\n\r\n        console.log('实时预览资源已清理');\r\n\r\n      } catch (error) {\r\n        console.error('清理实时预览资源失败:', error);\r\n      }\r\n    };\r\n    \r\n    // 开始轮询任务状态\r\n    const startPolling = () => {\r\n      // 先清除可能存在的轮询\r\n      clearInterval(statusInterval.value);\r\n      \r\n      // 立即获取一次状态\r\n      fetchStatus();\r\n      \r\n      // 初始化WebSocket连接\r\n      initWebSocket(taskId.value);\r\n      \r\n      // 作为备份，设置轮询间隔（每5秒）\r\n      statusInterval.value = setInterval(() => {\r\n        fetchStatus();\r\n      }, 5000);\r\n    };\r\n\r\n    // 启动处理时间计时器\r\n    const startProcessingTimer = () => {\r\n      // 如果已经启动过，则不重复启动\r\n      if (processingStartTime.value) return;\r\n      \r\n      console.log('开始视频处理计时...');\r\n      processingStartTime.value = Date.now();\r\n      processingElapsedTime.value = 0;\r\n      \r\n      // 设置定时更新处理时间\r\n      processingTimeInterval.value = setInterval(() => {\r\n        if (processingStartTime.value) {\r\n          processingElapsedTime.value = (Date.now() - processingStartTime.value) / 1000;\r\n        }\r\n      }, 1000);\r\n    };\r\n    \r\n    // 停止处理时间计时器\r\n    const stopProcessingTimer = async () => {\r\n      if (!processingStartTime.value) return;\r\n      \r\n      const endTime = Date.now();\r\n      const processingTimeSeconds = (endTime - processingStartTime.value) / 1000;\r\n      \r\n      console.log(`视频处理完成，耗时: ${processingTimeSeconds.toFixed(2)}秒`);\r\n      \r\n      // 清除计时器\r\n      clearInterval(processingTimeInterval.value);\r\n      \r\n      // 尝试将处理时间记录到数据库\r\n      try {\r\n        if (taskId.value) {\r\n          // 使用API函数发送处理时间\r\n          await saveVideoProcessingTime(taskId.value, parseFloat(processingTimeSeconds.toFixed(2)));\r\n          console.log('处理时间已记录到数据库');\r\n        }\r\n      } catch (err) {\r\n        console.error('记录处理时间失败:', err);\r\n      }\r\n      \r\n      // 重置计时状态\r\n      processingStartTime.value = null;\r\n      processingElapsedTime.value = processingTimeSeconds;\r\n    };\r\n\r\n    // 四方向上传成功处理\r\n    const handleFourWayUploadSuccess = (response) => {\r\n      console.log('四方向上传成功:', response)\r\n\r\n      if (response.taskId) {\r\n        taskId.value = response.taskId\r\n\r\n        // 发送分析状态变化事件\r\n        emit('analysisStatusChange', {\r\n          isAnalyzing: true,\r\n          taskId: response.taskId,\r\n          progress: 0,\r\n          message: '四方向视频上传成功，开始智能分析...'\r\n        })\r\n\r\n        // 跳转到四方向结果页面\r\n        router.push(`/four-way-result/${response.taskId}`)\r\n      }\r\n    }\r\n\r\n    // 四方向上传失败处理\r\n    const handleFourWayUploadError = (error) => {\r\n      console.error('四方向上传失败:', error)\r\n      ElMessage.error('四方向视频上传失败: ' + (error.message || '未知错误'))\r\n    }\r\n\r\n    // 提交上传\r\n    const submitUpload = async () => {\r\n      // 表单验证\r\n      if (!uploadForm.value) return\r\n\r\n      // 添加调试日志\r\n      console.log('开始上传视频，表单数据:', form)\r\n\r\n      // 如果是四方向模式，不在这里处理上传（由FourWayVideoUpload组件处理）\r\n      if (form.roadType === 'intersection' && form.intersectionMode === 'four-way') {\r\n        ElMessage.info('请使用上方的四方向上传界面')\r\n        return\r\n      }\r\n\r\n      // 检查是否上传了文件\r\n      if (form.roadType === 'normal') {\r\n        if (!form.singleVideoFile) {\r\n          ElMessage.warning('请先选择视频文件!')\r\n          return\r\n        }\r\n      } else if (form.intersectionMode === 'dual') {\r\n        if (!form.horizontalVideoFile || !form.verticalVideoFile) {\r\n          ElMessage.warning('请上传横向和纵向视频文件!')\r\n          return\r\n        }\r\n      }\r\n      \r\n      // 检查令牌有效性和格式\r\n      const token = localStorage.getItem('auth_token')\r\n      if (!token) {\r\n        ElMessage.error('未找到认证令牌，请先登录')\r\n        setTimeout(() => {\r\n          router.push('/login')\r\n        }, 1500)\r\n        return\r\n      }\r\n      \r\n      // 注册全局进度回调函数\r\n      window.onVideoUploadProgress = (percent) => {\r\n        uploadProgress.value = Math.floor(percent)\r\n      }\r\n      \r\n      uploading.value = true\r\n      uploadProgress.value = 0\r\n      \r\n      // 重置处理时间计时器\r\n      if (processingTimeInterval.value) {\r\n        clearInterval(processingTimeInterval.value);\r\n      }\r\n      processingStartTime.value = null;\r\n      processingElapsedTime.value = 0;\r\n      \r\n      // 使用模拟进度条\r\n      const progressInterval = simulateUploadProgress()\r\n      \r\n      try {\r\n        const formData = new FormData()\r\n        \r\n        // 使用新的直接上传API\r\n        let response\r\n        \r\n        if (form.roadType === 'normal') {\r\n          // 单视频上传\r\n          console.log('使用直接API上传单视频:', form.singleVideoFile.name, '大小:', form.singleVideoFile.size)\r\n          \r\n          try {\r\n            // 调用新的直接上传API\r\n            response = await uploadVideoDirectApi(\r\n              form.singleVideoFile,\r\n              form.direction,\r\n              'normal'\r\n            )\r\n            console.log('直接API上传成功，响应:', response)\r\n          } catch (directApiError) {\r\n            console.error('直接API上传失败，尝试使用传统方式:', directApiError)\r\n            \r\n            // 检查是否是认证错误\r\n            if (directApiError.authError || (directApiError.response && directApiError.response.status === 401)) {\r\n              ElMessage.error('认证失败，请重新登录')\r\n              clearInterval(progressInterval)\r\n              uploading.value = false\r\n              setTimeout(() => {\r\n                router.push('/login')\r\n              }, 1500)\r\n              return\r\n            }\r\n            \r\n            // 如果直接API失败，回退到传统方式\r\n            formData.append('video', form.singleVideoFile)\r\n            formData.append('roadType', 'normal')\r\n            formData.append('direction', form.direction)\r\n            \r\n            // 添加当前用户信息到FormData\r\n            try {\r\n              const userStr = localStorage.getItem('user');\r\n              if (userStr) {\r\n                const user = JSON.parse(userStr);\r\n                formData.append('userId', user.id);\r\n                formData.append('username', user.username);\r\n                formData.append('role', user.role || 'user');\r\n                \r\n                // 从令牌中提取信息作为备用\r\n                const token = localStorage.getItem('auth_token');\r\n                if (token) {\r\n                  const parts = token.split('_');\r\n                  if (parts.length >= 5) {\r\n                    formData.append('tokenHash', parts[0]);\r\n                    formData.append('tokenUsername', parts[1]);\r\n                    formData.append('tokenUserId', parts[2]);\r\n                    formData.append('tokenRole', parts[3]);\r\n                    formData.append('tokenTimestamp', parts[4]);\r\n                  }\r\n                  formData.append('auth_token', token); // 作为备用\r\n                }\r\n              } else {\r\n                console.warn('未找到用户信息');\r\n              }\r\n            } catch (e) {\r\n              console.error('添加用户信息失败:', e);\r\n            }\r\n            \r\n            // 传统API上传\r\n            response = await uploadAndAnalyzeVideo(formData)\r\n          }\r\n        } else {\r\n          // 十字路口双视频上传\r\n          console.log('使用直接API上传十字路口视频')\r\n          \r\n          try {\r\n            // 调用新的十字路口直接上传API\r\n            response = await uploadIntersectionDirectApi(\r\n              form.horizontalVideoFile,\r\n              form.verticalVideoFile\r\n            )\r\n            console.log('直接API上传成功，响应:', response)\r\n          } catch (directApiError) {\r\n            console.error('直接API上传失败，尝试使用传统方式:', directApiError)\r\n            \r\n            // 检查是否是认证错误\r\n            if (directApiError.authError || (directApiError.response && directApiError.response.status === 401)) {\r\n              ElMessage.error('认证失败，请重新登录')\r\n              clearInterval(progressInterval)\r\n              uploading.value = false\r\n              setTimeout(() => {\r\n                router.push('/login')\r\n              }, 1500)\r\n              return\r\n            }\r\n            \r\n            // 如果直接API失败，回退到传统方式\r\n            formData.append('horizontalVideo', form.horizontalVideoFile)\r\n            formData.append('verticalVideo', form.verticalVideoFile)\r\n            formData.append('roadType', 'intersection')\r\n            \r\n            // 添加当前用户信息到FormData\r\n            try {\r\n              const userStr = localStorage.getItem('user');\r\n              if (userStr) {\r\n                const user = JSON.parse(userStr);\r\n                formData.append('userId', user.id);\r\n                formData.append('username', user.username);\r\n                formData.append('role', user.role || 'user');\r\n              }\r\n            } catch (e) {\r\n              console.error('添加用户信息失败:', e);\r\n            }\r\n            \r\n            // 传统API上传\r\n            response = await uploadAndAnalyzeVideo(formData)\r\n          }\r\n        }\r\n        \r\n        // 处理响应结果\r\n        console.log('收到响应:', response)\r\n        \r\n        // 处理响应，兼容多种返回格式\r\n        let resultTaskId = null\r\n        \r\n        if (response && response.data) {\r\n          if (typeof response.data === 'string') {\r\n            try {\r\n              // 尝试解析可能的JSON字符串\r\n              const parsedData = JSON.parse(response.data)\r\n              resultTaskId = parsedData.taskId || parsedData.id\r\n            } catch (e) {\r\n              console.error('无法解析响应数据:', e)\r\n            }\r\n          } else if (response.data.taskId) {\r\n            resultTaskId = response.data.taskId\r\n          } else if (response.data.id) {\r\n            resultTaskId = response.data.id\r\n          }\r\n        } else if (response && (response.taskId || response.id)) {\r\n          resultTaskId = response.taskId || response.id\r\n        }\r\n        \r\n        if (resultTaskId) {\r\n          uploadProgress.value = 100\r\n          taskId.value = resultTaskId\r\n          \r\n          ElMessage.success('视频上传成功，正在分析中')\r\n          console.log('上传成功，任务ID:', resultTaskId)\r\n          \r\n          // 保存任务ID到会话存储\r\n          try {\r\n            sessionStorage.setItem('uploadState', JSON.stringify({ taskId: resultTaskId }))\r\n          } catch (err) {\r\n            console.warn('无法保存上传状态到会话存储:', err)\r\n          }\r\n          \r\n          // 开始轮询任务状态\r\n          startPolling();\r\n          \r\n          // 初始化WebSocket\r\n          initWebSocket(resultTaskId)\r\n\r\n          // 如果是十字路口视频，启用实时预览\r\n          if (form.roadType === 'intersection') {\r\n            initRealtimePreview(resultTaskId)\r\n          }\r\n          \r\n          // 更新浏览器URL但不导航（用于刷新页面时能恢复状态）\r\n          window.history.replaceState(\r\n            history.state, \r\n            '', \r\n            `/video-status/${resultTaskId}`\r\n          );\r\n        } else {\r\n          console.error('无法从响应中提取任务ID:', response)\r\n          throw new Error('上传失败，未收到有效的任务ID')\r\n        }\r\n      } catch (error) {\r\n        console.error('视频上传分析失败:', error)\r\n          \r\n        // 提供更友好的错误信息\r\n        let errorMessage = '视频上传分析失败: '\r\n        \r\n        if (error.authError || (error.response && error.response.status === 401)) {\r\n          errorMessage = '认证失败，请重新登录'\r\n          \r\n          setTimeout(() => {\r\n            router.push('/login')\r\n          }, 1500)\r\n        } else if (error.response) {\r\n          // 服务器返回的错误\r\n          if (error.response.status === 401) {\r\n            errorMessage += '认证失败，请重新登录'\r\n            \r\n            setTimeout(() => {\r\n              router.push('/login')\r\n            }, 1500)\r\n          } else if (error.response.status === 413) {\r\n            errorMessage += '文件太大，超出服务器限制'\r\n          } else if (error.response.status === 500) {\r\n            errorMessage += '服务器内部错误，请稍后重试'\r\n          } else if (error.response.data && error.response.data.message) {\r\n            errorMessage += error.response.data.message\r\n          } else {\r\n            errorMessage += `服务器错误 (${error.response.status})`\r\n          }\r\n        } else if (error.message) {\r\n          // 客户端网络错误\r\n          if (error.message.includes('Network Error')) {\r\n            errorMessage += '网络连接失败，请检查网络'\r\n          } else {\r\n            errorMessage += error.message\r\n          }\r\n        } else {\r\n          errorMessage += '未知错误'\r\n        }\r\n        \r\n        ElMessage.error(errorMessage)\r\n      } finally {\r\n        clearInterval(progressInterval)\r\n        uploading.value = false\r\n        \r\n        // 清除全局进度回调\r\n        window.onVideoUploadProgress = null\r\n      }\r\n    }\r\n    \r\n    // 重置表单\r\n    const resetForm = () => {\r\n      if (uploadForm.value) {\r\n        uploadForm.value.resetFields()\r\n      }\r\n      \r\n      form.roadType = 'normal'\r\n      form.direction = 'horizontal'\r\n      \r\n      // 清除视频文件和预览\r\n      if (form.singleVideoFile) handleSingleVideoRemove()\r\n      if (form.horizontalVideoFile) handleHorizontalVideoRemove()\r\n      if (form.verticalVideoFile) handleVerticalVideoRemove()\r\n      \r\n      // 清空文件\r\n      form.singleVideoFile = null\r\n      form.horizontalVideoFile = null\r\n      form.verticalVideoFile = null\r\n      \r\n      // 清空预览URL\r\n      singleVideoPreviewUrl.value = ''\r\n      horizontalVideoPreviewUrl.value = ''\r\n      verticalVideoPreviewUrl.value = ''\r\n      \r\n      uploadProgress.value = 0\r\n    }\r\n    \r\n    // 页面挂载前执行清理操作\r\n    const clearPreviousState = () => {\r\n      // 重置表单\r\n      resetForm();\r\n      \r\n      // 清除任务ID\r\n      taskId.value = null;\r\n      taskInfo.value = {};\r\n      \r\n      // 清除上传进度\r\n      uploadProgress.value = 0;\r\n      \r\n      // 清除错误信息\r\n      statusError.value = '';\r\n      \r\n      // 清空预览URL\r\n      singleVideoPreviewUrl.value = '';\r\n      horizontalVideoPreviewUrl.value = '';\r\n      verticalVideoPreviewUrl.value = '';\r\n      currentPreviewUrl.value = '';\r\n      \r\n      // 重置加载状态\r\n      loading.value = false;\r\n      uploading.value = false;\r\n      \r\n      // 清除计时器\r\n      stopProcessingTimer();\r\n      \r\n      // 清除可能存在的定时器\r\n      clearInterval(statusInterval.value);\r\n      \r\n      // 取消STOMP订阅\r\n      if (stompSubscription) {\r\n        try {\r\n          stompService.unsubscribe(`${STOMP_VIDEO_PROGRESS}/${taskId.value}`);\r\n          stompSubscription = null;\r\n        } catch (err) {\r\n          console.warn('取消WebSocket订阅失败:', err);\r\n        }\r\n      }\r\n      \r\n      // 清除会话存储的任务ID\r\n      try {\r\n        sessionStorage.removeItem('uploadState');\r\n          } catch (err) {\r\n\r\n          }\r\n      \r\n\r\n    };\r\n    \r\n    // 页面挂载后执行\r\n    onMounted(() => {\r\n      // 首先清除先前的状态\r\n      clearPreviousState();\r\n      \r\n      // 如果有initialTaskId，则开始轮询\r\n      if (taskId.value) {\r\n        startPolling();\r\n      }\r\n    });\r\n    \r\n    // 在组件销毁前执行\r\n    onBeforeUnmount(() => {\r\n      clearInterval(statusInterval.value);\r\n    });\r\n    \r\n    // 页面卸载时清理资源\r\n    onUnmounted(() => {\r\n      // 取消STOMP订阅\r\n      if (taskId.value && stompSubscription) {\r\n        stompService.unsubscribe(`${STOMP_VIDEO_PROGRESS}/${taskId.value}`);\r\n      }\r\n      \r\n      // 停止处理时间计时器\r\n      stopProcessingTimer();\r\n      \r\n      // 清空预览URL引用\r\n      singleVideoPreviewUrl.value = ''\r\n      horizontalVideoPreviewUrl.value = ''\r\n      verticalVideoPreviewUrl.value = ''\r\n      currentPreviewUrl.value = ''\r\n    });\r\n\r\n    return {\r\n      form,\r\n      rules,\r\n      uploadForm,\r\n      fileInput,\r\n      horizontalFileInput,\r\n      verticalFileInput,\r\n      uploading,\r\n      uploadProgress,\r\n      taskId,\r\n      taskInfo,\r\n      loading,\r\n      statusError,\r\n      retrying,\r\n      progressStatus,\r\n      singleVideoPreviewUrl,\r\n      horizontalVideoPreviewUrl,\r\n      verticalVideoPreviewUrl,\r\n      currentPreviewUrl,\r\n      previewDialogVisible,\r\n      processingElapsedTime,\r\n      formatFileSize,\r\n      handleSingleVideoRemove,\r\n      handleHorizontalVideoRemove,\r\n      handleVerticalVideoRemove,\r\n      submitUpload,\r\n      resetForm,\r\n      resetTask,\r\n      fetchStatus,\r\n      viewResult,\r\n      retryAnalysis,\r\n      triggerFileInput,\r\n      handleFileDrop,\r\n      handleFileChange,\r\n      triggerHorizontalFileInput,\r\n      handleHorizontalFileDrop,\r\n      handleHorizontalFileChange,\r\n      triggerVerticalFileInput,\r\n      handleVerticalFileDrop,\r\n      handleVerticalFileChange,\r\n      clearPreviousState,\r\n      isDragOver,\r\n      isDragOverHorizontal,\r\n      isDragOverVertical,\r\n      onDragEnter,\r\n      onDragLeave,\r\n      onDragEnterHorizontal,\r\n      onDragLeaveHorizontal,\r\n      onDragEnterVertical,\r\n      onDragLeaveVertical,\r\n      // 实时预览相关\r\n      frameViewer,\r\n      showRealtimePreview,\r\n      handleFrameReceived,\r\n      handleHorizontalFrameReceived,\r\n      handleVerticalFrameReceived,\r\n      handlePlaybackStateChange,\r\n      // 四方向上传相关\r\n      handleFourWayUploadSuccess,\r\n      handleFourWayUploadError\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.upload-form {\r\n  margin-bottom: 30px;\r\n  max-width: 700px; /* 控制表单整体宽度 */\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: stretch; /* 让子元素水平撑满 */\r\n}\r\n\r\n.drop-area {\r\n  border: 2px dashed rgba(255, 255, 255, 0.3);\r\n  border-radius: 8px;\r\n  padding: 3rem 2rem;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  min-height: 250px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  transition: all 0.3s;\r\n  background-color: rgba(17, 24, 39, 0.8);\r\n  width: 100%;\r\n}\r\n\r\n.drop-area.drag-over {\r\n  border-color: #6366f1;\r\n  background-color: rgba(99, 102, 241, 0.05);\r\n}\r\n\r\n.drop-area.has-file {\r\n  padding: 2rem;\r\n  border-style: solid;\r\n  min-height: 350px; /* 增加高度以适应视频播放器 */\r\n}\r\n\r\n.preview-container {\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n}\r\n\r\n/* 图标容器 */\r\n.icon-container {\r\n  position: relative;\r\n  display: inline-block;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 48px;\r\n  color: #6366f1;\r\n  filter: drop-shadow(0 0 8px rgba(99, 102, 241, 0.3));\r\n}\r\n\r\n.upload-arrow {\r\n  position: absolute;\r\n  bottom: -6px;\r\n  right: -6px;\r\n  background-color: #6366f1;\r\n  border-radius: 50%;\r\n  width: 24px;\r\n  height: 24px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  animation: bounce 2s infinite;\r\n}\r\n\r\n.arrow-icon {\r\n  color: white;\r\n  width: 16px;\r\n  height: 16px;\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 50%, 80%, 100% {\r\n    transform: translateY(0);\r\n  }\r\n  40% {\r\n    transform: translateY(-8px);\r\n  }\r\n  60% {\r\n    transform: translateY(-4px);\r\n  }\r\n}\r\n\r\n/* 文字样式 */\r\n.upload-title {\r\n  color: #ffffff;\r\n  font-size: 1.1rem;\r\n  font-weight: 500;\r\n  margin-bottom: 0.5rem;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.click-upload {\r\n  color: #6366f1;\r\n  font-weight: 500;\r\n}\r\n\r\n.upload-hint {\r\n  color: #e5e7eb !important;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n/* 内嵌视频预览样式 */\r\n.preview-video-inline {\r\n  width: 100%;\r\n  max-height: 200px; /* 控制内嵌视频的最大高度 */\r\n  border-radius: 8px;\r\n  object-fit: contain;\r\n  margin-top: 15px; /* 与其他元素的间距 */\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* 文件信息样式 */\r\n.file-name {\r\n  margin: 8px 0 4px;\r\n  font-size: 14px;\r\n  color: #e5e7eb; /* 亮色以增加可读性 */\r\n  text-align: center;\r\n  word-break: break-all;\r\n  max-width: 90%;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.file-size {\r\n  color: #d1d5db;\r\n  margin: 4px 0;\r\n  font-size: 12px;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 42px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.video-preview {\r\n  margin-top: 20px;\r\n}\r\n\r\n.upload-progress {\r\n  margin-top: 20px;\r\n  max-width: 700px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.upload-progress p {\r\n  color: #e5e7eb;\r\n  margin-bottom: 10px;\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-section {\r\n  padding: 20px 0;\r\n  max-width: 700px;\r\n  margin: 0 auto;\r\n  background: rgba(17, 24, 39, 0.4);\r\n  border-radius: 10px;\r\n  padding: 30px;\r\n  border: 1px solid rgba(255, 255, 255, 0.06);\r\n}\r\n\r\n.status-progress {\r\n  margin: 20px 0;\r\n}\r\n\r\n.status-progress p {\r\n  color: #e5e7eb;\r\n  margin-bottom: 10px;\r\n  font-size: 15px;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-actions {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.completed-actions, .failed-actions, .processing-actions {\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: center;\r\n}\r\n\r\n.view-result-btn {\r\n  min-width: 160px;\r\n  background-color: #4f46e5;\r\n  border-color: #4f46e5;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s;\r\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\r\n}\r\n\r\n.view-result-btn:hover {\r\n  background-color: #6366f1;\r\n  border-color: #6366f1;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);\r\n}\r\n\r\n.view-icon {\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.reset-btn {\r\n  min-width: 120px;\r\n}\r\n\r\n:deep(.custom-alert) {\r\n  background-color: rgba(17, 24, 39, 0.6) !important;\r\n  color: #e5e7eb !important;\r\n  border: 1px solid rgba(255, 255, 255, 0.1) !important;\r\n  padding: 15px !important;\r\n  border-radius: 8px !important;\r\n}\r\n\r\n:deep(.custom-alert .el-alert__title) {\r\n  color: #ffffff !important;\r\n  font-size: 16px !important;\r\n}\r\n\r\n:deep(.custom-alert .el-alert__description) {\r\n  color: #d1d5db !important;\r\n  margin-top: 8px !important;\r\n}\r\n\r\n:deep(.custom-alert.el-alert--info .el-alert__icon) {\r\n  color: #6366f1 !important;\r\n}\r\n\r\n:deep(.custom-alert.el-alert--success .el-alert__icon) {\r\n  color: #10b981 !important;\r\n}\r\n\r\n:deep(.custom-alert.el-alert--error .el-alert__icon) {\r\n  color: #ef4444 !important;\r\n}\r\n\r\n:deep(.el-skeleton) {\r\n  --el-skeleton-color: rgba(255, 255, 255, 0.05);\r\n  --el-skeleton-to-color: rgba(255, 255, 255, 0.15);\r\n}\r\n\r\n/* 表单项间距和布局 */\r\n:deep(.el-form-item) {\r\n  margin-bottom: 24px; /* 增加表单项之间的间距 */\r\n  width: 100%; /* 确保表单项宽度一致 */\r\n}\r\n\r\n/* 表单内容区域 */\r\n:deep(.el-form-item__content) {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: stretch;\r\n}\r\n\r\n/* 标签样式 */\r\n:deep(.el-form-item__label) {\r\n  color: #e5e7eb !important;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  padding-bottom: 8px;\r\n  text-align: left;\r\n  margin-left: 4px;\r\n}\r\n\r\n/* 强化单选按钮选中效果 */\r\n:deep(.el-radio-group) {\r\n  display: flex;\r\n  margin-bottom: 16px;\r\n  gap: 16px; /* 增加按钮间距 */\r\n  justify-content: center; /* 让按钮居中排列 */\r\n  width: 100%;\r\n}\r\n\r\n:deep(.el-radio-button) {\r\n  margin-right: 0;\r\n  flex: 1; /* 让按钮平均分配空间 */\r\n  max-width: 200px; /* 限制最大宽度 */\r\n}\r\n\r\n:deep(.el-radio-button__inner) {\r\n  background-color: #1f2937 !important;\r\n  border: 2px solid #374151 !important;\r\n  color: #e5e7eb !important;\r\n  padding: 12px 24px !important;\r\n  font-weight: 500 !important;\r\n  border-radius: 8px !important;\r\n  height: auto !important;\r\n  line-height: 1.5 !important;\r\n  box-shadow: none !important;\r\n  position: relative;\r\n  overflow: visible;\r\n  width: 100%; /* 使按钮宽度充满容器 */\r\n  text-align: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n:deep(.el-radio-button:first-child .el-radio-button__inner),\r\n:deep(.el-radio-button:last-child .el-radio-button__inner) {\r\n  border-radius: 8px !important;\r\n}\r\n\r\n:deep(.el-radio-button__original) {\r\n  opacity: 0;\r\n}\r\n\r\n:deep(.el-radio-button.is-active .el-radio-button__inner) {\r\n  background-color: #6366f1 !important;\r\n  border-color: #6366f1 !important;\r\n  color: white !important;\r\n  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.4) !important;\r\n  z-index: 1;\r\n}\r\n\r\n:deep(.el-radio-button.is-active .el-radio-button__inner::after) {\r\n  content: \"✓\";\r\n  position: absolute;\r\n  top: -8px;\r\n  right: -8px;\r\n  background-color: #6366f1;\r\n  color: white;\r\n  width: 22px;\r\n  height: 22px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 14px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* 下拉框容器 */\r\n:deep(.el-select-wrapper) {\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n/* 修复下拉框背景色和宽度 */\r\n:deep(.el-select) {\r\n  width: 300px !important; /* 将下拉框宽度限制为300px */\r\n  max-width: 100%; /* 最大宽度限制，使其与其他控件宽度一致 */\r\n}\r\n\r\n:deep(.el-select .el-input__wrapper) {\r\n  background-color: #1f2937 !important;\r\n  border: 2px solid #374151 !important;\r\n  box-shadow: none !important;\r\n  color: #e5e7eb !important;\r\n  width: 100% !important; /* 宽度占满 */\r\n  min-width: unset; /* 移除最小宽度限制 */\r\n  padding: 0 16px !important; /* 调整内边距 */\r\n  height: 48px !important; /* 固定高度 */\r\n}\r\n\r\n:deep(.el-select .el-input__inner) {\r\n  color: #e5e7eb !important;\r\n  background-color: transparent !important;\r\n  font-size: 15px; /* 增加字体大小 */\r\n}\r\n\r\n/* 控制下拉菜单的颜色和宽度 */\r\n:global(.el-select__popper) {\r\n  width: auto !important;\r\n  max-width: 300px !important; /* 与选择框宽度一致 */\r\n}\r\n\r\n:global(.el-popper.is-light) {\r\n  background-color: #1f2937 !important;\r\n  border: 1px solid #374151 !important;\r\n  color: #e5e7eb !important;\r\n}\r\n\r\n:global(.el-select-dropdown__item) {\r\n  color: #e5e7eb !important;\r\n  padding: 12px 20px !important; /* 增加选项内边距 */\r\n  font-size: 15px; /* 增加字体大小 */\r\n  display: flex !important; /* 确保垂直居中 */\r\n  align-items: center !important; /* 确保垂直居中 */\r\n  height: 36px !important; /* 固定高度 */\r\n  line-height: normal !important; /* 重置行高 */\r\n}\r\n\r\n:global(.el-select-dropdown__item span) {\r\n  display: inline-block !important;\r\n  line-height: normal !important;\r\n  vertical-align: middle !important;\r\n}\r\n\r\n:global(.el-select-dropdown__item.hover),\r\n:global(.el-select-dropdown__item:hover) {\r\n  background-color: rgba(99, 102, 241, 0.1) !important;\r\n}\r\n\r\n:global(.el-select-dropdown__item.selected) {\r\n  color: #6366f1 !important;\r\n  font-weight: bold !important;\r\n}\r\n\r\n/* 确保下拉框元素样式正确 */\r\n:global(.el-select__wrapper) {\r\n  background-color: #1f2937 !important;\r\n  border-color: #374151 !important;\r\n  width: 100% !important; /* 宽度占满 */\r\n}\r\n\r\n:global(.el-select__input) {\r\n  color: #e5e7eb !important;\r\n  background-color: transparent !important;\r\n}\r\n\r\n:global(.el-select-dropdown) {\r\n  background-color: #1f2937 !important;\r\n  border: 1px solid #374151 !important;\r\n  width: auto !important; /* 匹配选择框宽度 */\r\n}\r\n\r\n:global(.el-select .el-select__selection) {\r\n  color: #e5e7eb !important;\r\n  width: 100%; /* 确保内容区域宽度一致 */\r\n}\r\n\r\n:global(.el-select__selected-item) {\r\n  font-size: 15px; /* 增加字体大小 */\r\n}\r\n\r\n/* 按钮容器 */\r\n:deep(.el-form-item:last-child .el-form-item__content) {\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: center;\r\n  margin-top: 16px;\r\n}\r\n\r\n/* 美化按钮 */\r\n:deep(.el-button) {\r\n  border-radius: 8px;\r\n  transition: all 0.3s;\r\n  border: 2px solid rgba(255, 255, 255, 0.2);\r\n  background-color: rgba(255, 255, 255, 0.05);\r\n  color: #e5e7eb;\r\n  padding: 12px 28px;\r\n  height: 48px;\r\n  font-weight: 500;\r\n  font-size: 15px;\r\n  min-width: 140px; /* 按钮最小宽度增加 */\r\n  margin: 0 8px; /* 调整按钮间距，使用margin代替margin-right */\r\n}\r\n\r\n:deep(.el-button:hover) {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.3);\r\n  color: #ffffff;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n:deep(.el-button:active) {\r\n  transform: translateY(0);\r\n}\r\n\r\n:deep(.el-button--primary) {\r\n  background-color: #6366f1 !important; /* 使用与页面风格一致的颜色 */\r\n  border-color: #6366f1 !important;\r\n  color: #ffffff !important;\r\n}\r\n\r\n:deep(.el-button--primary:hover) {\r\n  background-color: #4f46e5 !important; /* 悬停时颜色稍深 */\r\n  border-color: #4f46e5 !important;\r\n  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);\r\n}\r\n\r\n:deep(.el-button--primary:active) {\r\n  background-color: #4338ca !important; /* 点击时颜色更深 */\r\n  border-color: #4338ca !important;\r\n  box-shadow: none;\r\n}\r\n\r\n/* 媒体查询 - 适配小屏幕 */\r\n@media (max-width: 768px) {\r\n  .upload-form {\r\n    padding: 0 10px;\r\n  }\r\n  \r\n  :deep(.el-radio-group) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  :deep(.el-radio-button) {\r\n    max-width: 100%;\r\n    width: 100%;\r\n    margin-bottom: 10px;\r\n  }\r\n  \r\n  :deep(.el-form-item:last-child .el-form-item__content) {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  :deep(.el-button) {\r\n    margin: 8px 0;\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.file-upload-area {\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: border-color 0.3s;\r\n  height: 180px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.file-upload-area:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.preview-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n  padding: 20px;\r\n}\r\n\r\n.file-icon {\r\n  font-size: 42px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.file-name {\r\n  margin: 8px 0 4px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n  text-align: center;\r\n  word-break: break-all;\r\n  max-width: 90%;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.file-size {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.remove-file-btn {\r\n  margin-top: 8px;\r\n}\r\n\r\n.video-preview {\r\n  margin-top: 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  padding: 15px;\r\n  background-color: #f9f9f9;\r\n}\r\n\r\n.video-preview h4 {\r\n  margin-top: 0;\r\n  margin-bottom: 15px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n  font-size: 16px;\r\n}\r\n\r\n.preview-video {\r\n  width: 100%;\r\n  max-height: 300px;\r\n  object-fit: contain;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 确保文件名不会重复显示 */\r\n.el-form-item__content .file-name {\r\n  display: block;\r\n}\r\n\r\n/* 隐藏视频预览外部可能显示的文件名 */\r\n.video-preview ~ .file-name,\r\n.video-preview + .file-name,\r\n.el-form-item__content > .file-name:not(.preview-container .file-name) {\r\n  display: none !important;\r\n}\r\n\r\n.preview-video-inline {\r\n  width: 100%;\r\n  max-height: 300px;\r\n  object-fit: contain;\r\n  border-radius: 4px;\r\n}\r\n\r\n:deep(.custom-progress) {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n:deep(.custom-progress .el-progress-bar__outer) {\r\n  background-color: rgba(255, 255, 255, 0.1) !important;\r\n  border-radius: 10px;\r\n}\r\n\r\n:deep(.custom-progress .el-progress-bar__inner) {\r\n  background-color: #6366f1 !important;\r\n  border-radius: 10px;\r\n}\r\n\r\n:deep(.custom-progress .el-progress-bar__innerText) {\r\n  color: #ffffff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 错误信息样式 */\r\n.error-message {\r\n  color: #f56565;\r\n  font-weight: 500;\r\n  margin: 8px 0;\r\n}\r\n\r\n.error-suggestions {\r\n  background: rgba(254, 215, 215, 0.1);\r\n  border: 1px solid rgba(254, 178, 178, 0.3);\r\n  border-radius: 8px;\r\n  padding: 12px;\r\n  margin: 16px 0;\r\n  text-align: left;\r\n}\r\n\r\n.error-suggestions p {\r\n  color: #f87171;\r\n  font-weight: 600;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.error-suggestions ul {\r\n  color: #fca5a5;\r\n  margin: 0;\r\n  padding-left: 20px;\r\n}\r\n\r\n.error-suggestions li {\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.error-actions {\r\n  margin-top: 16px;\r\n  display: flex;\r\n  gap: 12px;\r\n  justify-content: center;\r\n}\r\n\r\n.progress-text {\r\n  color: #f56565 !important;\r\n  font-weight: 600 !important;\r\n}\r\n\r\n:deep(.preview-dialog .el-dialog__header) {\r\n  background-color: rgba(26, 32, 50, 0.8);\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  padding: 15px 20px;\r\n}\r\n\r\n:deep(.preview-dialog .el-dialog__title) {\r\n  color: #e5e7eb;\r\n  font-weight: 600;\r\n}\r\n\r\n:deep(.preview-dialog .el-dialog__body) {\r\n  background-color: #111827;\r\n  padding: 20px;\r\n}\r\n\r\n:deep(.preview-dialog .el-dialog__close) {\r\n  color: #e5e7eb;\r\n}\r\n\r\n:deep(.preview-dialog .el-dialog__headerbtn:hover .el-dialog__close) {\r\n  color: #6366f1;\r\n}\r\n\r\n/* 处理时间显示样式 */\r\n.processing-time {\r\n  margin: 20px 0;\r\n  background-color: rgba(17, 24, 39, 0.6);\r\n  border-radius: 8px;\r\n  padding: 10px 15px;\r\n  border: 1px solid rgba(99, 102, 241, 0.2);\r\n}\r\n\r\n.processing-time p {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #e5e7eb;\r\n  font-size: 16px;\r\n  margin: 0;\r\n}\r\n\r\n.processing-time .el-icon {\r\n  color: #6366f1;\r\n  margin-right: 8px;\r\n  font-size: 18px;\r\n}\r\n\r\n.time-value {\r\n  color: #6366f1;\r\n  font-weight: 600;\r\n  margin: 0 5px;\r\n}\r\n\r\n.time-header {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #e5e7eb;\r\n  font-size: 16px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.time-value-container {\r\n  display: flex;\r\n  align-items: baseline;\r\n}\r\n\r\n.time-unit {\r\n  color: #d1d5db;\r\n  font-size: 14px;\r\n  margin-left: 5px;\r\n}\r\n\r\n.processing-complete {\r\n  color: #10b981;\r\n  font-size: 16px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.timer-animation {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-left: 10px;\r\n}\r\n\r\n.dot {\r\n  width: 4px;\r\n  height: 4px;\r\n  background-color: #d1d5db;\r\n  border-radius: 50%;\r\n  margin: 0 2px;\r\n  animation: blink 1s infinite;\r\n}\r\n\r\n@keyframes blink {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0; }\r\n}\r\n\r\n/* 实时预览部分样式 */\r\n.realtime-preview-section {\r\n  margin-top: 20px;\r\n  padding: 16px;\r\n  background: rgba(31, 41, 55, 0.5);\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.preview-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #e5e7eb;\r\n  margin-bottom: 1.5rem;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.dual-video-preview {\r\n  width: 100%;\r\n}\r\n\r\n.video-preview-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 2rem;\r\n  margin-top: 1rem;\r\n}\r\n\r\n.video-preview-container {\r\n  background: rgba(17, 24, 39, 0.8);\r\n  border-radius: 8px;\r\n  padding: 1rem;\r\n  border: 1px solid rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n.video-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 1rem;\r\n  padding-bottom: 0.5rem;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.video-header h4 {\r\n  color: #e5e7eb;\r\n  margin: 0;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.single-video-preview {\r\n  width: 100%;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .upload-form {\r\n    padding: 16px;\r\n  }\r\n\r\n  .upload-area {\r\n    padding: 20px;\r\n  }\r\n\r\n  .upload-area h3 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  .upload-area p {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .file-info {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n\r\n  .file-actions {\r\n    width: 100%;\r\n    justify-content: flex-start;\r\n  }\r\n\r\n  .realtime-preview-section {\r\n    margin-top: 16px;\r\n    padding: 12px;\r\n  }\r\n\r\n  .video-preview-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1rem;\r\n  }\r\n\r\n  .preview-title {\r\n    font-size: 1.1rem;\r\n  }\r\n\r\n  .video-preview-container {\r\n    padding: 0.75rem;\r\n  }\r\n}\r\n\r\n/* 平板设备响应式 */\r\n@media (max-width: 1024px) {\r\n  .video-preview-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 1.5rem;\r\n  }\r\n}\r\n\r\n/* 四方向上传样式 */\r\n.four-way-upload-notice {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.four-way-upload-notice :deep(.el-alert) {\r\n  background-color: rgba(99, 102, 241, 0.08);\r\n  border: 1px solid rgba(99, 102, 241, 0.1);\r\n  border-radius: 8px;\r\n}\r\n\r\n.four-way-upload-notice :deep(.el-alert__title) {\r\n  color: #e5e7eb;\r\n  font-weight: 600;\r\n}\r\n\r\n.four-way-upload-notice :deep(.el-alert__content) {\r\n  color: #d1d5db;\r\n}\r\n\r\n.four-way-upload-notice :deep(.el-alert__icon) {\r\n  color: #6366f1;\r\n}\r\n</style>"], "mappings": ";;EAcaA,KAAK,EAAC;AAAa;;EAdhCC,GAAA;EAsB8CC,EAAE,EAAC;;;EAtBjDD,GAAA;EAqCwBD,KAAK,EAAC;;;EAEbA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;mBAxCrC;;EAkFaA,KAAK,EAAC;AAAa;;EAlFhCC,GAAA;EA0FkDC,EAAE,EAAC;;;EA1FrDD,GAAA;EAyGwBD,KAAK,EAAC;;;EAEbA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;oBA5GrC;;EAkIaA,KAAK,EAAC;AAAa;;EAlIhCC,GAAA;EA0IgDC,EAAE,EAAC;;;EA1InDD,GAAA;EAyJwBD,KAAK,EAAC;;;EAEbA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;oBA5JrC;;EAoLaA,KAAK,EAAC;AAAwB;;EA6B7BA,KAAK,EAAC;AAAgB;;EAjNpCC,GAAA;EAkNwBD,KAAK,EAAC;;;EAlN9BC,GAAA;EAsNiCD,KAAK,EAAC;;;EAO5BA,KAAK,EAAC;AAAe;;EA7NhCC,GAAA;EAmOgBD,KAAK,EAAC;;;EAnOtBC,GAAA;AAAA;;EAAAA,GAAA;EAiPiCD,KAAK,EAAC;;;EAjPvCC,GAAA;EAkPwCD,KAAK,EAAC;;;EAlP9CC,GAAA;EAmPkBD,KAAK,EAAC;;;EAUXA,KAAK,EAAC;AAAe;;EA7PlCC,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;EAqRyGD,KAAK,EAAC;;;EArR/GC,GAAA;EAwR8DD,KAAK,EAAC;;;EACtDA,KAAK,EAAC;AAAe;;EAKpBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAc;;EAetBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAc;;EAkBnBA,KAAK,EAAC;AAAsB;;EAClCA,KAAK,EAAC;AAAe;;EAnUnCC,GAAA;AAAA;;EAAAA,GAAA;AAAA;;EAAAA,GAAA;EAkWWD,KAAK,EAAC;;;EAaNA,KAAK,EAAC;AAAiB;;EA/WlCC,GAAA;EAgY4CD,KAAK,EAAC;;;EACrCA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAsB;;EACzBA,KAAK,EAAC;AAAY;;EAtYlCC,GAAA;EAwYuDD,KAAK,EAAC;;;EAxY7DC,GAAA;EA8YoDD,KAAK,EAAC;;;EAK/CA,KAAK,EAAC;AAAgB;;EAnZjCC,GAAA;EAoZyED,KAAK,EAAC;;;EApZ/EC,GAAA;EAuasDD,KAAK,EAAC;;;EAva5DC,GAAA;EAyboBD,KAAK,EAAC;;;EAzb1BC,GAAA;EAscoED,KAAK,EAAC;;oBAtc1E;;;;;;;;;;;;;;;;;;;;;;uBAAAG,mBAAA,CAAAC,SAAA,SACEC,mBAAA,yBAA4B,E,CACZC,MAAA,CAAAC,MAAM,I,cAAtBC,YAAA,CA4MUC,kBAAA;IA9MZR,GAAA;IAE2BS,KAAK,EAAEJ,MAAA,CAAAK,IAAI;IAAE,gBAAc,EAAC,KAAK;IAACX,KAAK,EAAC,aAAa;IAAEY,KAAK,EAAEN,MAAA,CAAAM,KAAK;IAAEC,GAAG,EAAC;;IAFpGC,OAAA,EAAAC,QAAA,CAGI,MAAe,CAAfV,mBAAA,YAAe,EACfW,YAAA,CAKeC,uBAAA;MALDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAJpCL,OAAA,EAAAC,QAAA,CAKM,MAGiB,CAHjBC,YAAA,CAGiBI,yBAAA;QARvBC,UAAA,EAK+Bf,MAAA,CAAAK,IAAI,CAACW,QAAQ;QAL5C,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAK+BlB,MAAA,CAAAK,IAAI,CAACW,QAAQ,GAAAE,MAAA;QAAEC,IAAI,EAAC;;QALnDX,OAAA,EAAAC,QAAA,CAMQ,MAA+D,CAA/DC,YAAA,CAA+DU,0BAAA;UAA9CC,KAAK,EAAC,QAAQ;UAACT,KAAK,EAAC;YACtCF,YAAA,CAAqEU,0BAAA;UAApDC,KAAK,EAAC,cAAc;UAACT,KAAK,EAAC;;QAPpDU,CAAA;;MAAAA,CAAA;QAWIvB,mBAAA,gBAAmB,EACHC,MAAA,CAAAK,IAAI,CAACW,QAAQ,iB,cAA7BnB,mBAAA,CAuDWC,SAAA;MAnEfH,GAAA;IAAA,IAaMe,YAAA,CA8CeC,uBAAA;MA9CDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAbtCL,OAAA,EAAAC,QAAA,CAcQ,MA4CM,CA5CNc,mBAAA,CA4CM,OA5CNC,UA4CM,GA3CJD,mBAAA,CAkCM;QAlCD7B,KAAK,EAfpB+B,eAAA,EAeqB,WAAW;UAAA,aACMzB,MAAA,CAAA0B,UAAU;UAAA,YAAc1B,MAAA,CAAAK,IAAI,CAACsB;QAAe;QAClEC,OAAK,EAAAX,MAAA,QAAAA,MAAA,UAAAY,IAAA,KAAE7B,MAAA,CAAA8B,gBAAA,IAAA9B,MAAA,CAAA8B,gBAAA,IAAAD,IAAA,CAAgB;QACvBE,MAAI,EAAAd,MAAA,QAAAA,MAAA,MAlBpBe,cAAA,KAAAH,IAAA,KAkB8B7B,MAAA,CAAAiC,cAAA,IAAAjC,MAAA,CAAAiC,cAAA,IAAAJ,IAAA,CAAc;QAC5BK,UAAQ,EAAAjB,MAAA,QAAAA,MAAA,MAnBxBe,cAAA,CAmBe,QAAiB;QAChBG,WAAS,EAAAlB,MAAA,QAAAA,MAAA,MApBzBe,cAAA,KAAAH,IAAA,KAoBmC7B,MAAA,CAAAoC,WAAA,IAAApC,MAAA,CAAAoC,WAAA,IAAAP,IAAA,CAAW;QAC9BQ,WAAS,EAAApB,MAAA,QAAAA,MAAA,MArBzBe,cAAA,KAAAH,IAAA,KAqBmC7B,MAAA,CAAAsC,WAAA,IAAAtC,MAAA,CAAAsC,WAAA,IAAAT,IAAA,CAAW;WACtB7B,MAAA,CAAAK,IAAI,CAACsB,eAAe,I,cAAhC9B,mBAAA,CAcM,OAdN0C,UAcM,EAAAtB,MAAA,SAAAA,MAAA,QAbJM,mBAAA,CAUM;QAVD7B,KAAK,EAAC;MAAgB,IACzB6B,mBAAA,CAGM;QAHDiB,KAAK,EAAC,4BAA4B;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,IAAI,EAAC,cAAc;QAACjD,KAAK,EAAC,aAAa;QAACkD,OAAO,EAAC;UAC7GrB,mBAAA,CAA0D;QAApDsB,CAAC,EAAC;MAAgD,IACxDtB,mBAAA,CAAgN;QAA1MsB,CAAC,EAAC;MAAsM,G,GAEhNtB,mBAAA,CAIM;QAJD7B,KAAK,EAAC;MAAc,IACvB6B,mBAAA,CAEM;QAFDiB,KAAK,EAAC,4BAA4B;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,IAAI,EAAC,cAAc;QAACjD,KAAK,EAAC,YAAY;QAACkD,OAAO,EAAC;UAC5GrB,mBAAA,CAA2K;QAArK,WAAS,EAAC,SAAS;QAACsB,CAAC,EAAC;kCAIlCtB,mBAAA,CAA8E;QAA3E7B,KAAK,EAAC;MAAc,IAlCrCoD,gBAAA,CAkCsC,cAAY,GAAAvB,mBAAA,CAAsC;QAAhC7B,KAAK,EAAC;MAAc,GAAC,MAAI,E,qBACnE6B,mBAAA,CAAwE;QAArE7B,KAAK,EAAC;MAAa,GAAC,+CAA6C,oB,qBAEtEG,mBAAA,CAWM,OAXNkD,UAWM,GAVJrC,YAAA,CAAiDsC,kBAAA;QAAxCtD,KAAK,EAAC;MAAW;QAtCxCc,OAAA,EAAAC,QAAA,CAsCyC,MAAY,CAAZC,YAAA,CAAYuC,mBAAA,E;QAtCrD3B,CAAA;UAuCcC,mBAAA,CAAwD,KAAxD2B,UAAwD,EAAAC,gBAAA,CAAhCnD,MAAA,CAAAK,IAAI,CAACsB,eAAe,CAACyB,IAAI,kBACjD7B,mBAAA,CAA8E,QAA9E8B,UAA8E,EAAAF,gBAAA,CAAnDnD,MAAA,CAAAsD,cAAc,CAACtD,MAAA,CAAAK,IAAI,CAACsB,eAAe,CAACR,IAAI,mBACnET,YAAA,CAAoG6C,oBAAA;QAAzF7D,KAAK,EAAC,iBAAiB;QAACyB,IAAI,EAAC,OAAO;QAAES,OAAK,EAzCpEI,cAAA,CAyC2EhC,MAAA,CAAAwD,uBAAuB;;QAzClGhD,OAAA,EAAAC,QAAA,CAyCoG,MAAEQ,MAAA,SAAAA,MAAA,QAzCtG6B,gBAAA,CAyCoG,IAAE,E;QAzCtGxB,CAAA;sCA2CsBtB,MAAA,CAAAyD,qBAAqB,I,cAD7B5D,mBAAA,CAKS;QA/CvBF,GAAA;QA4CiB+D,GAAG,EAAE1D,MAAA,CAAAyD,qBAAqB;QAC3BE,QAAQ,EAAR,EAAQ;QACRjE,KAAK,EAAC;8BA9CtBkE,UAAA,KAAA7D,mBAAA,e,sCAmDUwB,mBAAA,CAME;QALAsC,IAAI,EAAC,MAAM;QACXtD,GAAG,EAAC,WAAW;QACfuD,KAAsB,EAAtB;UAAA;QAAA,CAAsB;QACrBC,QAAM,EAAA9C,MAAA,QAAAA,MAAA,UAAAY,IAAA,KAAE7B,MAAA,CAAAgE,gBAAA,IAAAhE,MAAA,CAAAgE,gBAAA,IAAAnC,IAAA,CAAgB;QACzBoC,MAAM,EAAC;;MAxDnB3C,CAAA;QA6DMZ,YAAA,CAKeC,uBAAA;MALDC,KAAK,EAAC;IAAM;MA7DhCJ,OAAA,EAAAC,QAAA,CA8DQ,MAGY,CAHZC,YAAA,CAGYwD,oBAAA;QAjEpBnD,UAAA,EA8D4Bf,MAAA,CAAAK,IAAI,CAAC8D,SAAS;QA9D1C,uBAAAlD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA8D4BlB,MAAA,CAAAK,IAAI,CAAC8D,SAAS,GAAAjD,MAAA;QAAEkD,WAAW,EAAC;;QA9DxD5D,OAAA,EAAAC,QAAA,CA+DU,MAAqD,CAArDC,YAAA,CAAqD2D,oBAAA;UAA1CzD,KAAK,EAAC,IAAI;UAACS,KAAK,EAAC;YAC5BX,YAAA,CAAmD2D,oBAAA;UAAxCzD,KAAK,EAAC,IAAI;UAACS,KAAK,EAAC;;QAhEtCC,CAAA;;MAAAA,CAAA;qDAsEIzB,mBAAA,CAiIWC,SAAA;MAvMfH,GAAA;IAAA,IAqEII,mBAAA,eAAkB,EAEhBA,mBAAA,YAAe,EACfW,YAAA,CAKeC,uBAAA;MALDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAxEtCL,OAAA,EAAAC,QAAA,CAyEQ,MAGiB,CAHjBC,YAAA,CAGiBI,yBAAA;QA5EzBC,UAAA,EAyEiCf,MAAA,CAAAK,IAAI,CAACiE,gBAAgB;QAzEtD,uBAAArD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAyEiClB,MAAA,CAAAK,IAAI,CAACiE,gBAAgB,GAAApD,MAAA;QAAEC,IAAI,EAAC;;QAzE7DX,OAAA,EAAAC,QAAA,CA0EU,MAAqE,CAArEC,YAAA,CAAqEU,0BAAA;UAApDC,KAAK,EAAC,MAAM;UAACT,KAAK,EAAC;YACpCF,YAAA,CAAwEU,0BAAA;UAAvDC,KAAK,EAAC,UAAU;UAACT,KAAK,EAAC;;QA3ElDU,CAAA;;MAAAA,CAAA;QA+EMvB,mBAAA,aAAgB,EACAC,MAAA,CAAAK,IAAI,CAACiE,gBAAgB,e,cAArCzE,mBAAA,CAgGWC,SAAA;MAhLjBH,GAAA;IAAA,IAiFQe,YAAA,CA8CaC,uBAAA;MA9CCC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAjFxCL,OAAA,EAAAC,QAAA,CAkFQ,MA4CM,CA5CNc,mBAAA,CA4CM,OA5CNgD,UA4CM,GA3CJhD,mBAAA,CAkCM;QAlCD7B,KAAK,EAnFpB+B,eAAA,EAmFqB,WAAW;UAAA,aACMzB,MAAA,CAAAwE,oBAAoB;UAAA,YAAcxE,MAAA,CAAAK,IAAI,CAACoE;QAAmB;QAChF7C,OAAK,EAAAX,MAAA,QAAAA,MAAA,UAAAY,IAAA,KAAE7B,MAAA,CAAA0E,0BAAA,IAAA1E,MAAA,CAAA0E,0BAAA,IAAA7C,IAAA,CAA0B;QACjCE,MAAI,EAAAd,MAAA,SAAAA,MAAA,OAtFpBe,cAAA,KAAAH,IAAA,KAsF8B7B,MAAA,CAAA2E,wBAAA,IAAA3E,MAAA,CAAA2E,wBAAA,IAAA9C,IAAA,CAAwB;QACtCK,UAAQ,EAAAjB,MAAA,SAAAA,MAAA,OAvFxBe,cAAA,CAuFe,QAAiB;QAChBG,WAAS,EAAAlB,MAAA,SAAAA,MAAA,OAxFzBe,cAAA,KAAAH,IAAA,KAwFmC7B,MAAA,CAAA4E,qBAAA,IAAA5E,MAAA,CAAA4E,qBAAA,IAAA/C,IAAA,CAAqB;QACxCQ,WAAS,EAAApB,MAAA,SAAAA,MAAA,OAzFzBe,cAAA,KAAAH,IAAA,KAyFmC7B,MAAA,CAAA6E,qBAAA,IAAA7E,MAAA,CAAA6E,qBAAA,IAAAhD,IAAA,CAAqB;WAChC7B,MAAA,CAAAK,IAAI,CAACoE,mBAAmB,I,cAApC5E,mBAAA,CAcM,OAdNiF,UAcM,EAAA7D,MAAA,SAAAA,MAAA,QAbJM,mBAAA,CAUM;QAVD7B,KAAK,EAAC;MAAgB,IACzB6B,mBAAA,CAGM;QAHDiB,KAAK,EAAC,4BAA4B;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,IAAI,EAAC,cAAc;QAACjD,KAAK,EAAC,aAAa;QAACkD,OAAO,EAAC;UAC7GrB,mBAAA,CAA0D;QAApDsB,CAAC,EAAC;MAAgD,IACxDtB,mBAAA,CAAgN;QAA1MsB,CAAC,EAAC;MAAsM,G,GAEhNtB,mBAAA,CAIM;QAJD7B,KAAK,EAAC;MAAc,IACvB6B,mBAAA,CAEM;QAFDiB,KAAK,EAAC,4BAA4B;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,IAAI,EAAC,cAAc;QAACjD,KAAK,EAAC,YAAY;QAACkD,OAAO,EAAC;UAC5GrB,mBAAA,CAA2K;QAArK,WAAS,EAAC,SAAS;QAACsB,CAAC,EAAC;kCAIlCtB,mBAAA,CAAgF;QAA7E7B,KAAK,EAAC;MAAc,IAtGrCoD,gBAAA,CAsGsC,gBAAc,GAAAvB,mBAAA,CAAsC;QAAhC7B,KAAK,EAAC;MAAc,GAAC,MAAI,E,qBACrE6B,mBAAA,CAAyC;QAAtC7B,KAAK,EAAC;MAAa,GAAC,gBAAc,oB,qBAEvCG,mBAAA,CAWM,OAXNkF,UAWM,GAVJrE,YAAA,CAAiDsC,kBAAA;QAAxCtD,KAAK,EAAC;MAAW;QA1GxCc,OAAA,EAAAC,QAAA,CA0GyC,MAAY,CAAZC,YAAA,CAAYuC,mBAAA,E;QA1GrD3B,CAAA;UA2GcC,mBAAA,CAA4D,KAA5DyD,WAA4D,EAAA7B,gBAAA,CAApCnD,MAAA,CAAAK,IAAI,CAACoE,mBAAmB,CAACrB,IAAI,kBACrD7B,mBAAA,CAAkF,QAAlF0D,WAAkF,EAAA9B,gBAAA,CAAvDnD,MAAA,CAAAsD,cAAc,CAACtD,MAAA,CAAAK,IAAI,CAACoE,mBAAmB,CAACtD,IAAI,mBACvET,YAAA,CAAwG6C,oBAAA;QAA7F7D,KAAK,EAAC,iBAAiB;QAACyB,IAAI,EAAC,OAAO;QAAES,OAAK,EA7GpEI,cAAA,CA6G2EhC,MAAA,CAAAkF,2BAA2B;;QA7GtG1E,OAAA,EAAAC,QAAA,CA6GwG,MAAEQ,MAAA,SAAAA,MAAA,QA7G1G6B,gBAAA,CA6GwG,IAAE,E;QA7G1GxB,CAAA;sCA+GsBtB,MAAA,CAAAmF,yBAAyB,I,cADjCtF,mBAAA,CAKS;QAnHvBF,GAAA;QAgHiB+D,GAAG,EAAE1D,MAAA,CAAAmF,yBAAyB;QAC/BxB,QAAQ,EAAR,EAAQ;QACRjE,KAAK,EAAC;8BAlHtB0F,WAAA,KAAArF,mBAAA,e,sCAuHUwB,mBAAA,CAME;QALAsC,IAAI,EAAC,MAAM;QACXtD,GAAG,EAAC,qBAAqB;QACzBuD,KAAsB,EAAtB;UAAA;QAAA,CAAsB;QACrBC,QAAM,EAAA9C,MAAA,SAAAA,MAAA,WAAAY,IAAA,KAAE7B,MAAA,CAAAqF,0BAAA,IAAArF,MAAA,CAAAqF,0BAAA,IAAAxD,IAAA,CAA0B;QACnCoC,MAAM,EAAC;;MA5HnB3C,CAAA;QAiIMZ,YAAA,CA8CeC,uBAAA;MA9CDC,KAAK,EAAC,MAAM;MAACC,IAAI,EAAC;;MAjItCL,OAAA,EAAAC,QAAA,CAkIQ,MA4CM,CA5CNc,mBAAA,CA4CM,OA5CN+D,WA4CM,GA3CJ/D,mBAAA,CAkCM;QAlCD7B,KAAK,EAnIpB+B,eAAA,EAmIqB,WAAW;UAAA,aACMzB,MAAA,CAAAuF,kBAAkB;UAAA,YAAcvF,MAAA,CAAAK,IAAI,CAACmF;QAAiB;QAC5E5D,OAAK,EAAAX,MAAA,SAAAA,MAAA,WAAAY,IAAA,KAAE7B,MAAA,CAAAyF,wBAAA,IAAAzF,MAAA,CAAAyF,wBAAA,IAAA5D,IAAA,CAAwB;QAC/BE,MAAI,EAAAd,MAAA,SAAAA,MAAA,OAtIpBe,cAAA,KAAAH,IAAA,KAsI8B7B,MAAA,CAAA0F,sBAAA,IAAA1F,MAAA,CAAA0F,sBAAA,IAAA7D,IAAA,CAAsB;QACpCK,UAAQ,EAAAjB,MAAA,SAAAA,MAAA,OAvIxBe,cAAA,CAuIe,QAAiB;QAChBG,WAAS,EAAAlB,MAAA,SAAAA,MAAA,OAxIzBe,cAAA,KAAAH,IAAA,KAwImC7B,MAAA,CAAA2F,mBAAA,IAAA3F,MAAA,CAAA2F,mBAAA,IAAA9D,IAAA,CAAmB;QACtCQ,WAAS,EAAApB,MAAA,SAAAA,MAAA,OAzIzBe,cAAA,KAAAH,IAAA,KAyImC7B,MAAA,CAAA4F,mBAAA,IAAA5F,MAAA,CAAA4F,mBAAA,IAAA/D,IAAA,CAAmB;WAC9B7B,MAAA,CAAAK,IAAI,CAACmF,iBAAiB,I,cAAlC3F,mBAAA,CAcM,OAdNgG,WAcM,EAAA5E,MAAA,SAAAA,MAAA,QAbJM,mBAAA,CAUM;QAVD7B,KAAK,EAAC;MAAgB,IACzB6B,mBAAA,CAGM;QAHDiB,KAAK,EAAC,4BAA4B;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,IAAI,EAAC,cAAc;QAACjD,KAAK,EAAC,aAAa;QAACkD,OAAO,EAAC;UAC7GrB,mBAAA,CAA0D;QAApDsB,CAAC,EAAC;MAAgD,IACxDtB,mBAAA,CAAgN;QAA1MsB,CAAC,EAAC;MAAsM,G,GAEhNtB,mBAAA,CAIM;QAJD7B,KAAK,EAAC;MAAc,IACvB6B,mBAAA,CAEM;QAFDiB,KAAK,EAAC,4BAA4B;QAACC,KAAK,EAAC,IAAI;QAACC,MAAM,EAAC,IAAI;QAACC,IAAI,EAAC,cAAc;QAACjD,KAAK,EAAC,YAAY;QAACkD,OAAO,EAAC;UAC5GrB,mBAAA,CAA2K;QAArK,WAAS,EAAC,SAAS;QAACsB,CAAC,EAAC;kCAIlCtB,mBAAA,CAAgF;QAA7E7B,KAAK,EAAC;MAAc,IAtJrCoD,gBAAA,CAsJsC,gBAAc,GAAAvB,mBAAA,CAAsC;QAAhC7B,KAAK,EAAC;MAAc,GAAC,MAAI,E,qBACrE6B,mBAAA,CAAyC;QAAtC7B,KAAK,EAAC;MAAa,GAAC,gBAAc,oB,qBAEvCG,mBAAA,CAWM,OAXNiG,WAWM,GAVJpF,YAAA,CAAiDsC,kBAAA;QAAxCtD,KAAK,EAAC;MAAW;QA1JxCc,OAAA,EAAAC,QAAA,CA0JyC,MAAY,CAAZC,YAAA,CAAYuC,mBAAA,E;QA1JrD3B,CAAA;UA2JcC,mBAAA,CAA0D,KAA1DwE,WAA0D,EAAA5C,gBAAA,CAAlCnD,MAAA,CAAAK,IAAI,CAACmF,iBAAiB,CAACpC,IAAI,kBACnD7B,mBAAA,CAAgF,QAAhFyE,WAAgF,EAAA7C,gBAAA,CAArDnD,MAAA,CAAAsD,cAAc,CAACtD,MAAA,CAAAK,IAAI,CAACmF,iBAAiB,CAACrE,IAAI,mBACrET,YAAA,CAAsG6C,oBAAA;QAA3F7D,KAAK,EAAC,iBAAiB;QAACyB,IAAI,EAAC,OAAO;QAAES,OAAK,EA7JpEI,cAAA,CA6J2EhC,MAAA,CAAAiG,yBAAyB;;QA7JpGzF,OAAA,EAAAC,QAAA,CA6JsG,MAAEQ,MAAA,SAAAA,MAAA,QA7JxG6B,gBAAA,CA6JsG,IAAE,E;QA7JxGxB,CAAA;sCA+JsBtB,MAAA,CAAAkG,uBAAuB,I,cAD/BrG,mBAAA,CAKS;QAnKvBF,GAAA;QAgKiB+D,GAAG,EAAE1D,MAAA,CAAAkG,uBAAuB;QAC7BvC,QAAQ,EAAR,EAAQ;QACRjE,KAAK,EAAC;8BAlKtByG,WAAA,KAAApG,mBAAA,e,sCAuKUwB,mBAAA,CAME;QALAsC,IAAI,EAAC,MAAM;QACXtD,GAAG,EAAC,mBAAmB;QACvBuD,KAAsB,EAAtB;UAAA;QAAA,CAAsB;QACrBC,QAAM,EAAA9C,MAAA,SAAAA,MAAA,WAAAY,IAAA,KAAE7B,MAAA,CAAAoG,wBAAA,IAAApG,MAAA,CAAAoG,wBAAA,IAAAvE,IAAA,CAAwB;QACjCoC,MAAM,EAAC;;MA5KnB3C,CAAA;sCAmL2BtB,MAAA,CAAAK,IAAI,CAACiE,gBAAgB,mB,cAA1CzE,mBAAA,CAmBWC,SAAA;MAtMjBH,GAAA;IAAA,IAkLMI,mBAAA,aAAgB,EAEdwB,mBAAA,CAWM,OAXN8E,WAWM,GAVJ3F,YAAA,CASW4F,mBAAA;MARTC,KAAK,EAAC,WAAW;MACjB1C,IAAI,EAAC,MAAM;MACV2C,QAAQ,EAAE,KAAK;MAChB,WAAS,EAAT;;MAEWhG,OAAO,EAAAC,QAAA,CAChB,MAA6CQ,MAAA,SAAAA,MAAA,QAA7CM,mBAAA,CAA6C,WAA1C,wCAAsC,oB;MA5LvDD,CAAA;UAiMQvB,mBAAA,eAAkB,EAClBW,YAAA,CAGE+F,6BAAA;MAFCC,eAAc,EAAE1G,MAAA,CAAA2G,0BAA0B;MAC1CC,aAAY,EAAE5G,MAAA,CAAA6G;iGApMzB9G,mBAAA,e,8BAyMIA,mBAAA,sBAAyB,E,EACHC,MAAA,CAAAK,IAAI,CAACW,QAAQ,uBAAuBhB,MAAA,CAAAK,IAAI,CAACiE,gBAAgB,oB,cAA/EpE,YAAA,CAGeS,uBAAA;MA7MnBhB,GAAA;IAAA;MAAAa,OAAA,EAAAC,QAAA,CA2MM,MAAqF,CAArFC,YAAA,CAAqF6C,oBAAA;QAA1EM,IAAI,EAAC,SAAS;QAAEjC,OAAK,EAAE5B,MAAA,CAAA8G,YAAY;QAAGC,OAAO,EAAE/G,MAAA,CAAAgH;;QA3MhExG,OAAA,EAAAC,QAAA,CA2M2E,MAAIQ,MAAA,SAAAA,MAAA,QA3M/E6B,gBAAA,CA2M2E,MAAI,E;QA3M/ExB,CAAA;iDA4MMZ,YAAA,CAA4C6C,oBAAA;QAAhC3B,OAAK,EAAE5B,MAAA,CAAAiH;MAAS;QA5MlCzG,OAAA,EAAAC,QAAA,CA4MoC,MAAEQ,MAAA,SAAAA,MAAA,QA5MtC6B,gBAAA,CA4MoC,IAAE,E;QA5MtCxB,CAAA;;MAAAA,CAAA;UAAAvB,mBAAA,e;IAAAuB,CAAA;0DAiNEzB,mBAAA,CAkPMC,SAAA;IAncRH,GAAA;EAAA,IAgNEI,mBAAA,wBAA2B,EAC3BwB,mBAAA,CAkPM,OAlPN2F,WAkPM,GAjPOlH,MAAA,CAAA+G,OAAO,I,cAAlBlH,mBAAA,CAEM,OAFNsH,WAEM,GADJzG,YAAA,CAAkC0G,sBAAA;IAApBC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAR;UAGTtH,MAAA,CAAAuH,WAAW,I,cAA3B1H,mBAAA,CAWM,OAXN2H,WAWM,GAVJ9G,YAAA,CAKE4F,mBAAA;IAJAC,KAAK,EAAC,UAAU;IAChB1C,IAAI,EAAC,OAAO;IACX4D,WAAW,EAAEzH,MAAA,CAAAuH,WAAW;IACzB,WAAS,EAAT;4CAEFhG,mBAAA,CAGM,OAHNmG,WAGM,GAFJhH,YAAA,CAA6D6C,oBAAA;IAAlDM,IAAI,EAAC,SAAS;IAAEjC,OAAK,EAAE5B,MAAA,CAAA2H;;IA9N1CnH,OAAA,EAAAC,QAAA,CA8NuD,MAAEQ,MAAA,SAAAA,MAAA,QA9NzD6B,gBAAA,CA8NuD,IAAE,E;IA9NzDxB,CAAA;kCA+NQZ,YAAA,CAA8C6C,oBAAA;IAAlC3B,OAAK,EAAE5B,MAAA,CAAA4H;EAAS;IA/NpCpH,OAAA,EAAAC,QAAA,CA+NsC,MAAIQ,MAAA,SAAAA,MAAA,QA/N1C6B,gBAAA,CA+NsC,MAAI,E;IA/N1CxB,CAAA;uDAmOIzB,mBAAA,CA+NM,OA/NNgI,WA+NM,GA9NJ9H,mBAAA,YAAe,EAEPC,MAAA,CAAA8H,QAAQ,CAACC,MAAM,iB,cADvB7H,YAAA,CA4BWoG,mBAAA;IAjQjB3G,GAAA;IAuOQkE,IAAI,EAAC,OAAO;IACX2C,QAAQ,EAAE,KAAK;IAChB,WAAS,EAAT,EAAS;IACT9G,KAAK,EAAC;;IAEK6G,KAAK,EAAA9F,QAAA,CACd,MAAqBQ,MAAA,SAAAA,MAAA,QAArBM,mBAAA,CAAqB,gBAAb,MAAI,qBA7OtBuB,gBAAA,CA6O+B,cACvB,E;IA9ORtC,OAAA,EAAAC,QAAA,CA+OQ,MAAyB,CAAzBc,mBAAA,CAAyB,WAAtB,QAAM,GAAA4B,gBAAA,CAAGnD,MAAA,CAAAC,MAAM,kBACTD,MAAA,CAAA8H,QAAQ,CAACE,SAAS,I,cAA3BnI,mBAAA,CAA0E,KAhPlFoI,WAAA,EAgPqC,QAAM,GAAA9E,gBAAA,CAAGnD,MAAA,CAAA8H,QAAQ,CAACE,SAAS,+BAhPhEjI,mBAAA,gBAiPiBC,MAAA,CAAA8H,QAAQ,CAACI,KAAK,I,cAAvBrI,mBAAA,CAA6E,KAA7EsI,WAA6E,EAA9B,QAAM,GAAAhF,gBAAA,CAAGnD,MAAA,CAAA8H,QAAQ,CAACI,KAAK,oBACxDlI,MAAA,CAAA8H,QAAQ,CAACM,OAAO,I,cAA9BvI,mBAAA,CAAgF,KAAhFwI,WAAgF,EAAAlF,gBAAA,CAAvBnD,MAAA,CAAA8H,QAAQ,CAACM,OAAO,qB,cACzEvI,mBAAA,CAA+C,KAA/CyI,WAA+C,EAAf,aAAW,I,4BAC3C/G,mBAAA,CAQM;MARD7B,KAAK,EAAC;IAAmB,IAC5B6B,mBAAA,CAA8B,YAA3BA,mBAAA,CAAuB,gBAAf,QAAM,E,GACjBA,mBAAA,CAKK,aAJHA,mBAAA,CAAoB,YAAhB,aAAW,GACfA,mBAAA,CAAqB,YAAjB,cAAY,GAChBA,mBAAA,CAAqB,YAAjB,cAAY,GAChBA,mBAAA,CAAe,YAAX,QAAM,E,wBAGdA,mBAAA,CAGM,OAHNgH,WAGM,GAFJ7H,YAAA,CAAiE6C,oBAAA;MAAtDM,IAAI,EAAC,SAAS;MAAEjC,OAAK,EAAE5B,MAAA,CAAA2H;;MA9P5CnH,OAAA,EAAAC,QAAA,CA8PyD,MAAMQ,MAAA,SAAAA,MAAA,QA9P/D6B,gBAAA,CA8PyD,QAAM,E;MA9P/DxB,CAAA;oCA+PUZ,YAAA,CAA8C6C,oBAAA;MAAlC3B,OAAK,EAAE5B,MAAA,CAAA4H;IAAS;MA/PtCpH,OAAA,EAAAC,QAAA,CA+PwC,MAAIQ,MAAA,SAAAA,MAAA,QA/P5C6B,gBAAA,CA+PwC,MAAI,E;MA/P5CxB,CAAA;;IAAAA,CAAA;QAqQmBtB,MAAA,CAAA8H,QAAQ,CAACC,MAAM,oB,cAD5BlI,mBAAA,CAcWC,SAAA;IAlRjBH,GAAA;EAAA,IAmQMI,mBAAA,WAAc,EACdW,YAAA,CAcW4F,mBAAA;IAZTzC,IAAI,EAAC,MAAM;IACV2C,QAAQ,EAAE,KAAK;IAChB,WAAS,EAAT,EAAS;IACT9G,KAAK,EAAC;;IAEK6G,KAAK,EAAA9F,QAAA,CACd,MAAoBQ,MAAA,SAAAA,MAAA,QAApBM,mBAAA,CAAoB,gBAAZ,KAAG,qBA5QrBuB,gBAAA,CA4Q8B,cACtB,E;IA7QRtC,OAAA,EAAAC,QAAA,CA8QQ,MAAyB,CAAzBc,mBAAA,CAAyB,WAAtB,QAAM,GAAA4B,gBAAA,CAAGnD,MAAA,CAAAC,MAAM,kBACTD,MAAA,CAAA8H,QAAQ,CAACE,SAAS,I,cAA3BnI,mBAAA,CAA0E,KA/QlF2I,WAAA,EA+QqC,QAAM,GAAArF,gBAAA,CAAGnD,MAAA,CAAA8H,QAAQ,CAACE,SAAS,+BA/QhEjI,mBAAA,gBAgRiBC,MAAA,CAAA8H,QAAQ,CAACM,OAAO,I,cAAzBvI,mBAAA,CAAqD,KAhR7D4I,WAAA,EAAAtF,gBAAA,CAgRsCnD,MAAA,CAAA8H,QAAQ,CAACM,OAAO,oBAhRtDrI,mBAAA,gBAiRiBC,MAAA,CAAA0I,qBAAqB,Q,cAA9B7I,mBAAA,CAAsF,KAjR9F8I,WAAA,EAiR4C,QAAM,GAAAxF,gBAAA,CAAGnD,MAAA,CAAA0I,qBAAqB,CAACE,OAAO,OAAM,IAAE,mBAjR1F7I,mBAAA,e;IAAAuB,CAAA;yDAAAvB,mBAAA,gBAoRMA,mBAAA,4BAA+B,EACpBC,MAAA,CAAA8H,QAAQ,CAACC,MAAM,oBAAoB/H,MAAA,CAAA8H,QAAQ,CAACC,MAAM,iBAAiB/H,MAAA,CAAA6I,mBAAmB,I,cAAjGhJ,mBAAA,CA2DM,OA3DNiJ,WA2DM,GAzDJ/I,mBAAA,eAAkB,EACPC,MAAA,CAAAK,IAAI,EAAEW,QAAQ,+B,cAAzBnB,mBAAA,CAuCM,OAvCNkJ,WAuCM,GAtCJxH,mBAAA,CAGK,MAHLyH,WAGK,GAFHtI,YAAA,CAAkCsC,kBAAA;IA1R9CxC,OAAA,EAAAC,QAAA,CA0RqB,MAAe,CAAfC,YAAA,CAAeuI,sBAAA,E;IA1RpC3H,CAAA;kCAAAwB,gBAAA,CA0R8C,YAEpC,G,GAEAvB,mBAAA,CAgCM,OAhCN2H,WAgCM,GA/BJ3H,mBAAA,CAcM,OAdN4H,WAcM,GAbJ5H,mBAAA,CAGM,OAHN6H,WAGM,G,4BAFJ7H,mBAAA,CAAe,YAAX,QAAM,sBACVb,YAAA,CAAkD2I,iBAAA;IAA1CxF,IAAI,EAAC,SAAS;IAAC1C,IAAI,EAAC;;IAlS5CX,OAAA,EAAAC,QAAA,CAkSoD,MAAKQ,MAAA,SAAAA,MAAA,QAlSzD6B,gBAAA,CAkSoD,OAAK,E;IAlSzDxB,CAAA;QAoScZ,YAAA,CAQE4I,8BAAA;IAPA/I,GAAG,EAAC,uBAAuB;IAC1B,SAAO,OAAOP,MAAA,CAAAC,MAAM;IACpB,YAAU,EAAE,IAAI;IAChB,mBAAiB,EAAE,EAAE;IACrBsG,KAAK,EAAE,MAAM;IACbgD,eAAc,EAAEvJ,MAAA,CAAAwJ,6BAA6B;IAC7CC,qBAAqB,EAAEzJ,MAAA,CAAA0J;sFAI5BnI,mBAAA,CAcM,OAdNoI,WAcM,GAbJpI,mBAAA,CAGM,OAHNqI,WAGM,G,4BAFJrI,mBAAA,CAAe,YAAX,QAAM,sBACVb,YAAA,CAAkD2I,iBAAA;IAA1CxF,IAAI,EAAC,SAAS;IAAC1C,IAAI,EAAC;;IAlT5CX,OAAA,EAAAC,QAAA,CAkToD,MAAKQ,MAAA,SAAAA,MAAA,QAlTzD6B,gBAAA,CAkToD,OAAK,E;IAlTzDxB,CAAA;QAoTcZ,YAAA,CAQE4I,8BAAA;IAPA/I,GAAG,EAAC,qBAAqB;IACxB,SAAO,OAAOP,MAAA,CAAAC,MAAM;IACpB,YAAU,EAAE,IAAI;IAChB,mBAAiB,EAAE,EAAE;IACrBsG,KAAK,EAAE,MAAM;IACbgD,eAAc,EAAEvJ,MAAA,CAAA6J,2BAA2B;IAC3CJ,qBAAqB,EAAEzJ,MAAA,CAAA0J;2GAOhC7J,mBAAA,CAaMC,SAAA;IA/UdH,GAAA;EAAA,IAiUQI,mBAAA,WAAc,EACdwB,mBAAA,CAaM,OAbNuI,WAaM,GAZJvI,mBAAA,CAGK,MAHLwI,WAGK,GAFHrJ,YAAA,CAAkCsC,kBAAA;IApU9CxC,OAAA,EAAAC,QAAA,CAoUqB,MAAe,CAAfC,YAAA,CAAeuI,sBAAA,E;IApUpC3H,CAAA;kCAAAwB,gBAAA,CAoU8C,YAEpC,G,GACApC,YAAA,CAOE4I,8BAAA;IANA/I,GAAG,EAAC,aAAa;IAChB,SAAO,EAAEP,MAAA,CAAAC,MAAM;IACf,YAAU,EAAE,IAAI;IAChB,mBAAiB,EAAE,EAAE;IACrBsJ,eAAc,EAAEvJ,MAAA,CAAAgK,mBAAmB;IACnCP,qBAAqB,EAAEzJ,MAAA,CAAA0J;2JAM9B7J,mBAAA,CAaWC,SAAA;IAhWjBH,GAAA;EAAA,IAkVMI,mBAAA,YAAe,EACfW,YAAA,CAaW4F,mBAAA;IAXTzC,IAAI,EAAC,SAAS;IACb2C,QAAQ,EAAE,KAAK;IAChB,WAAS,EAAT,EAAS;IACT9G,KAAK,EAAC;;IAEK6G,KAAK,EAAA9F,QAAA,CACd,MAAqBQ,MAAA,SAAAA,MAAA,QAArBM,mBAAA,CAAqB,gBAAb,MAAI,qBA3VtBuB,gBAAA,CA2V+B,eACvB,E;IA5VRtC,OAAA,EAAAC,QAAA,CA6VQ,MAAyB,CAAzBc,mBAAA,CAAyB,WAAtB,QAAM,GAAA4B,gBAAA,CAAGnD,MAAA,CAAAC,MAAM,kBACTD,MAAA,CAAA8H,QAAQ,CAACE,SAAS,I,cAA3BnI,mBAAA,CAA0E,KA9VlFoK,WAAA,EA8VqC,QAAM,GAAA9G,gBAAA,CAAGnD,MAAA,CAAA8H,QAAQ,CAACE,SAAS,+BA9VhEjI,mBAAA,gBA+ViBC,MAAA,CAAA0I,qBAAqB,Q,cAA9B7I,mBAAA,CAAuF,KA/V/FqK,WAAA,EA+V4C,SAAO,GAAA/G,gBAAA,CAAGnD,MAAA,CAAA0I,qBAAqB,CAACE,OAAO,OAAM,IAAE,mBA/V3F7I,mBAAA,e;IAAAuB,CAAA;wDAkWyCtB,MAAA,CAAA8H,QAAQ,CAACC,MAAM,iB,cAAlDlI,mBAAA,CAUM,OAVNsK,WAUM,G,4BATJ5I,mBAAA,CAAY,WAAT,OAAK,sBACRb,YAAA,CAOE0J,sBAAA;IANCC,UAAU,EAAErK,MAAA,CAAA8H,QAAQ,CAACwC,QAAQ;IAC7BvC,MAAM,EAAE/H,MAAA,CAAAuK,cAAc;IACtB,cAAY,EAAE,EAAE;IACjB,aAAW,EAAX,EAAW;IACV,WAAS,EAAE,IAAI;IAChB7K,KAAK,EAAC;wEAKVG,mBAAA,CAcMC,SAAA;IA7XZH,GAAA;EAAA,IA8WMI,mBAAA,cAAiB,EACjBwB,mBAAA,CAcM,OAdNiJ,WAcM,G,4BAbJjJ,mBAAA,CAAY,WAAT,OAAK,sBACRb,YAAA,CAWc0J,sBAAA;IAVXC,UAAU,EAAE,GAAG;IAChBtC,MAAM,EAAC,WAAW;IACjB,cAAY,EAAE,EAAE;IACjB,aAAW,EAAX,EAAW;IACV,WAAS,EAAE,IAAI;IAChBrI,KAAK,EAAC;;IAEKc,OAAO,EAAAC,QAAA,CAChB,CAAuC;MADnB4J;IAAU,MAAApJ,MAAA,SAAAA,MAAA,QAC9BM,mBAAA,CAAuC;MAAjC7B,KAAK,EAAC;IAAe,GAAC,MAAI,oB;IA1X5C4B,CAAA;0DA+XMvB,mBAAA,YAAe,EACJC,MAAA,CAAA0I,qBAAqB,Q,cAAhC7I,mBAAA,CAiBM,OAjBN4K,WAiBM,GAhBJlJ,mBAAA,CAGM,OAHNmJ,WAGM,GAFJhK,YAAA,CAA4BsC,kBAAA;IAlYtCxC,OAAA,EAAAC,QAAA,CAkYmB,MAAS,CAATC,YAAA,CAASiK,gBAAA,E;IAlY5BrJ,CAAA;kCAmYUC,mBAAA,CAAiB,cAAX,MAAI,qB,GAEZA,mBAAA,CAQM,OARNqJ,WAQM,GAPJrJ,mBAAA,CAAsE,QAAtEsJ,WAAsE,EAAA1H,gBAAA,CAA1CnD,MAAA,CAAA0I,qBAAqB,CAACE,OAAO,qB,4BACzDrH,mBAAA,CAAgC;IAA1B7B,KAAK,EAAC;EAAW,GAAC,GAAC,sBACdM,MAAA,CAAA8H,QAAQ,CAACC,MAAM,qB,cAA1BlI,mBAAA,CAIM,OAJNiL,WAIM,EAAA7J,MAAA,SAAAA,MAAA,QAHJM,mBAAA,CAAyB;IAAnB7B,KAAK,EAAC;EAAK,4BACjB6B,mBAAA,CAAyB;IAAnB7B,KAAK,EAAC;EAAK,4BACjB6B,mBAAA,CAAyB;IAAnB7B,KAAK,EAAC;EAAK,2B,MA3Y7BK,mBAAA,e,GA8YmBC,MAAA,CAAA8H,QAAQ,CAACC,MAAM,oB,cAA1BlI,mBAAA,CAEM,OAFNkL,WAEM,GADJrK,YAAA,CAA4CsC,kBAAA;IAAnCgI,KAAK,EAAC;EAAS;IA/YlCxK,OAAA,EAAAC,QAAA,CA+YmC,MAAS,CAATC,YAAA,CAASuK,gBAAA,E;IA/Y5C3J,CAAA;kCAAAwB,gBAAA,CA+YsD,QAC9C,G,KAhZR/C,mBAAA,e,KAAAA,mBAAA,gBAmZMwB,mBAAA,CA8CM,OA9CN2J,WA8CM,GA7COlL,MAAA,CAAA8H,QAAQ,CAACC,MAAM,oBAAoB/H,MAAA,CAAA8H,QAAQ,CAACqD,QAAQ,I,cAA/DtL,mBAAA,CAiBM,OAjBNuL,WAiBM,GAhBJ1K,YAAA,CAOY6C,oBAAA;IANVM,IAAI,EAAC,SAAS;IACdnE,KAAK,EAAC,iBAAiB;IACtBkC,OAAK,EAAE5B,MAAA,CAAAqL,UAAU;IAClBlK,IAAI,EAAC;;IAzZjBX,OAAA,EAAAC,QAAA,CA2ZY,MAAiD,CAAjDC,YAAA,CAAiDsC,kBAAA;MAAxCtD,KAAK,EAAC;IAAW;MA3ZtCc,OAAA,EAAAC,QAAA,CA2ZuC,MAAY,CAAZC,YAAA,CAAY4K,mBAAA,E;MA3ZnDhK,CAAA;oCAAAwB,gBAAA,CA2Z6D,QACnD,G;IA5ZVxB,CAAA;kCA8ZUZ,YAAA,CAMY6C,oBAAA;IALT3B,OAAK,EAAE5B,MAAA,CAAA4H,SAAS;IACjBlI,KAAK,EAAC,WAAW;IACjByB,IAAI,EAAC;;IAjajBX,OAAA,EAAAC,QAAA,CAkaW,MAEDQ,MAAA,SAAAA,MAAA,QApaV6B,gBAAA,CAkaW,QAED,E;IApaVxB,CAAA;sCAuawBtB,MAAA,CAAA8H,QAAQ,CAACC,MAAM,iB,cAA/BlI,mBAAA,CAgBM,OAhBN0L,WAgBM,GAfJ7K,YAAA,CAOY6C,oBAAA;IANVM,IAAI,EAAC,SAAS;IACbjC,OAAK,EAAE5B,MAAA,CAAAwL,aAAa;IACpBzE,OAAO,EAAE/G,MAAA,CAAAyL,QAAQ;IAClBtK,IAAI,EAAC;;IA5ajBX,OAAA,EAAAC,QAAA,CA6aW,MAEDQ,MAAA,SAAAA,MAAA,QA/aV6B,gBAAA,CA6aW,QAED,E;IA/aVxB,CAAA;6CAibUZ,YAAA,CAKY6C,oBAAA;IAJT3B,OAAK,EAAE5B,MAAA,CAAA4H,SAAS;IACjBzG,IAAI,EAAC;;IAnbjBX,OAAA,EAAAC,QAAA,CAobW,MAEDQ,MAAA,SAAAA,MAAA,QAtbV6B,gBAAA,CAobW,QAED,E;IAtbVxB,CAAA;qDAybQzB,mBAAA,CAOM,OAPN6L,WAOM,GANJhL,YAAA,CAKY6C,oBAAA;IAJT3B,OAAK,EAAE5B,MAAA,CAAA4H,SAAS;IACjBzG,IAAI,EAAC;;IA5bjBX,OAAA,EAAAC,QAAA,CA6bW,MAEDQ,MAAA,SAAAA,MAAA,QA/bV6B,gBAAA,CA6bW,QAED,E;IA/bVxB,CAAA;8FAqcEvB,mBAAA,uBAA0B,EACfC,MAAA,CAAA2L,cAAc,QAAQ3L,MAAA,CAAA2L,cAAc,WAAW3L,MAAA,CAAAC,MAAM,I,cAAhEJ,mBAAA,CAUM,OAVN+L,WAUM,G,4BATJrK,mBAAA,CAAY,WAAT,OAAK,sBACRb,YAAA,CAOe0J,sBAAA;IANZC,UAAU,EAAErK,MAAA,CAAA2L,cAAc;IAC1B5D,MAAM,EAAE/H,MAAA,CAAA2L,cAAc;IACtB,cAAY,EAAE,EAAE;IAChB,WAAS,EAAE,IAAI;IAChB,aAAW,EAAX,EAAW;IACXjM,KAAK,EAAC;yDA9cZK,mBAAA,gBAkdEA,mBAAA,aAAgB,EAChBW,YAAA,CAYYmL,oBAAA;IAXVtF,KAAK,EAAC,MAAM;IApdhBxF,UAAA,EAqdaf,MAAA,CAAA8L,oBAAoB;IArdjC,uBAAA7K,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAqdalB,MAAA,CAAA8L,oBAAoB,GAAA5K,MAAA;IAC7BuB,KAAK,EAAC,KAAK;IACX/C,KAAK,EAAC;;IAvdVc,OAAA,EAAAC,QAAA,CAouBS,MAYT,CAtRYT,MAAA,CAAA+L,iBAAiB,I,cADzBlM,mBAAA,CAKS;MA9dbF,GAAA;MA2dO+D,GAAG,EAAE1D,MAAA,CAAA+L,iBAAiB;MACvBpI,QAAQ,EAAR,EAAQ;MACRG,KAAoB,EAApB;QAAA;MAAA;4BA7dNkI,WAAA,KAAAjM,mBAAA,e;IAAAuB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}