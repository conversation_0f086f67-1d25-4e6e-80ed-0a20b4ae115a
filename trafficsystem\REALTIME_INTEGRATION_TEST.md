# 实时帧推送功能集成测试文档

## 概述

本文档描述了十字路口视频分析实时帧推送功能的集成测试方案和验证步骤。

## 功能架构

### 后端组件
1. **Python视频分析API** (`python_model/model_api.py`)
   - 智能帧选择算法
   - 自适应质量调整
   - 网络性能监控
   - WebSocket帧推送

2. **Java WebSocket控制器** (`VideoProgressWebSocketController.java`)
   - 帧数据接收和缓存
   - WebSocket消息推送
   - 任务资源管理

3. **视频分析服务** (`VideoAnalysisServiceImpl.java`)
   - 实时预览初始化
   - 资源清理管理

### 前端组件
1. **实时帧预览组件** (`RealTimeFrameViewer.vue`)
   - 帧数据接收和显示
   - 播放控制和缓冲管理
   - 网络状况监控

2. **WebSocket服务** (`stomp-service.js`)
   - 帧数据订阅和处理
   - 网络性能统计
   - 连接质量评估

3. **视频上传组件** (`VideoUploadForm.vue`)
   - 实时预览集成
   - 十字路口模式支持

4. **视频结果页面** (`VideoResult.vue`)
   - 预览模式切换
   - 实时/完整视频切换

## 测试计划

### 1. 单元测试

#### 1.1 Python API测试
```bash
# 运行实时帧推送测试
cd trafficsystem
python test_realtime_frames.py
```

**测试项目：**
- 智能帧选择算法效率
- 网络自适应质量调整
- 帧数据编码和压缩
- WebSocket推送性能

#### 1.2 前端组件测试
访问测试页面：`http://localhost:3000/realtime-test`

**测试项目：**
- WebSocket连接稳定性
- 帧数据接收和解码
- 组件渲染和交互
- 网络状况监控

### 2. 集成测试

#### 2.1 端到端流程测试

**测试步骤：**
1. 启动所有服务（MongoDB、Java后端、Python API、前端）
2. 上传十字路口视频
3. 验证实时预览功能
4. 检查完整视频生成

**验证点：**
- [ ] 视频上传成功
- [ ] 实时帧推送启动
- [ ] 帧数据正确显示
- [ ] 网络自适应工作
- [ ] 完整视频生成
- [ ] 资源正确清理

#### 2.2 性能测试

**测试场景：**
- 正常网络条件（延迟 < 100ms）
- 慢速网络条件（延迟 > 1s）
- 不稳定网络条件（丢包率 > 5%）

**性能指标：**
- 帧推送成功率 > 90%
- 平均响应时间 < 2s
- 内存使用稳定
- CPU使用率合理

### 3. 兼容性测试

#### 3.1 浏览器兼容性
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

#### 3.2 设备兼容性
- 桌面设备（1920x1080及以上）
- 平板设备（768x1024及以上）
- 移动设备（375x667及以上）

## 测试环境配置

### 环境要求
- MongoDB 4.4+
- Java 11+
- Python 3.8+
- Node.js 16+
- 内存 8GB+
- 磁盘空间 10GB+

### 服务启动顺序
1. MongoDB服务
2. Java后端服务（端口8080）
3. Python API服务（端口5000）
4. 前端开发服务器（端口3000）

### 配置检查清单
- [ ] MongoDB连接正常
- [ ] WebSocket端点可访问
- [ ] 文件上传路径配置
- [ ] 视频处理模型加载
- [ ] 网络端口开放

## 测试数据

### 测试视频要求
- 格式：MP4, AVI, MOV
- 分辨率：720p及以上
- 时长：30秒-5分钟
- 内容：包含车辆的十字路口场景

### 测试用例数据
```
test_videos/
├── intersection_normal.mp4      # 正常交通流量
├── intersection_heavy.mp4       # 高峰期交通
├── intersection_light.mp4       # 低峰期交通
└── intersection_complex.mp4     # 复杂场景
```

## 问题排查指南

### 常见问题

#### 1. WebSocket连接失败
**症状：** 前端无法接收实时帧数据
**排查步骤：**
1. 检查Java后端WebSocket端点
2. 验证STOMP配置
3. 检查防火墙设置
4. 查看浏览器控制台错误

#### 2. 帧推送延迟过高
**症状：** 实时预览明显滞后
**排查步骤：**
1. 检查网络延迟
2. 调整帧推送间隔
3. 优化图像压缩质量
4. 检查服务器性能

#### 3. 内存泄漏
**症状：** 长时间运行后内存持续增长
**排查步骤：**
1. 检查帧缓冲区清理
2. 验证WebSocket连接关闭
3. 检查定时器清理
4. 监控Blob URL释放

### 日志分析

#### Python API日志
```bash
# 查看实时推送日志
tail -f logs/model_api.log | grep "frame_push"
```

#### Java后端日志
```bash
# 查看WebSocket日志
tail -f logs/application.log | grep "WebSocket"
```

#### 前端控制台
- 检查WebSocket连接状态
- 监控帧数据接收
- 查看网络请求错误

## 性能基准

### 推荐配置下的性能指标
- **帧推送频率：** 0.5-2 fps
- **平均帧大小：** 50-150 KB
- **推送成功率：** > 95%
- **端到端延迟：** < 3秒
- **内存使用：** < 500MB（前端）
- **CPU使用：** < 30%（视频处理时）

### 性能优化建议
1. **网络优化**
   - 启用智能帧选择
   - 使用自适应质量
   - 配置合理的推送间隔

2. **内存优化**
   - 限制帧缓冲区大小
   - 及时清理过期数据
   - 使用对象池复用

3. **CPU优化**
   - 异步处理帧推送
   - 优化图像编码参数
   - 使用多线程处理

## 验收标准

### 功能验收
- [ ] 实时帧推送功能正常工作
- [ ] 智能帧选择算法有效
- [ ] 网络自适应调整正常
- [ ] 用户界面响应流畅
- [ ] 错误处理机制完善

### 性能验收
- [ ] 推送成功率 ≥ 90%
- [ ] 平均响应时间 ≤ 2秒
- [ ] 内存使用稳定
- [ ] 无明显内存泄漏
- [ ] 支持并发用户 ≥ 10

### 兼容性验收
- [ ] 主流浏览器支持
- [ ] 移动设备适配
- [ ] 不同网络环境适应
- [ ] 与现有功能兼容

## 部署检查清单

### 生产环境部署前
- [ ] 完成所有测试用例
- [ ] 性能测试通过
- [ ] 安全测试通过
- [ ] 文档更新完成
- [ ] 监控配置就绪

### 部署后验证
- [ ] 服务正常启动
- [ ] 功能基本验证
- [ ] 性能监控正常
- [ ] 错误日志检查
- [ ] 用户反馈收集

## 维护和监控

### 关键监控指标
- WebSocket连接数
- 帧推送成功率
- 平均响应时间
- 内存和CPU使用率
- 错误率和异常日志

### 定期维护任务
- 清理过期缓存数据
- 检查日志文件大小
- 更新性能基准数据
- 优化配置参数
- 备份重要数据

---

**文档版本：** 1.0  
**最后更新：** 2024-01-01  
**维护人员：** 开发团队
