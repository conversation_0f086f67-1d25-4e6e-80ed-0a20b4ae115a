#!/usr/bin/env python3
"""
测试帧推送功能的简单脚本
用于验证Python到Java的帧数据推送是否正常工作
"""

import requests
import json
import time
import base64
from datetime import datetime

# 配置
JAVA_BACKEND_URL = "http://localhost:8080"
TEST_TASK_ID = "7640209d-a9bf-499b-8f1f-bc7fe875abea"  # 使用您的实际任务ID

def create_test_frame_data():
    """创建测试帧数据"""
    # 创建一个简单的测试图像（1x1像素的透明PNG）
    test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    return {
        "type": "frame_update",
        "taskId": TEST_TASK_ID,
        "frameNumber": 1,
        "totalFrames": 100,
        "imageData": test_image_base64,
        "detectionCount": 3,
        "timestamp": datetime.now().isoformat(),
        "quality": 75,
        "networkStats": {
            "avg_response_time": 1.0,
            "success_rate": 1.0
        }
    }

def test_frame_push():
    """测试帧推送功能"""
    print(f"🚀 开始测试帧推送功能")
    print(f"📡 Java后端地址: {JAVA_BACKEND_URL}")
    print(f"🎯 测试任务ID: {TEST_TASK_ID}")
    print()
    
    # 测试端点URL
    url = f"{JAVA_BACKEND_URL}/api/video-progress/frame-update"
    
    # 创建测试数据
    test_data = create_test_frame_data()
    
    print(f"📦 测试数据:")
    print(f"  - 帧号: {test_data['frameNumber']}")
    print(f"  - 检测数量: {test_data['detectionCount']}")
    print(f"  - 图像数据大小: {len(test_data['imageData'])} 字符")
    print()
    
    try:
        print(f"📤 发送测试帧数据到: {url}")
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        start_time = time.time()
        response = requests.post(url, json=test_data, headers=headers, timeout=10)
        response_time = time.time() - start_time
        
        print(f"⏱️  响应时间: {response_time:.2f}秒")
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 帧推送测试成功!")
            print(f"📄 响应内容: {response.text}")
        else:
            print(f"❌ 帧推送测试失败!")
            print(f"📄 响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError as e:
        print(f"🔌 连接错误: 无法连接到Java后端")
        print(f"   请确保Java后端服务正在运行在 {JAVA_BACKEND_URL}")
        print(f"   错误详情: {e}")
        
    except requests.exceptions.Timeout:
        print(f"⏰ 请求超时: Java后端响应时间过长")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def test_health_check():
    """测试Java后端健康状态"""
    print(f"🏥 检查Java后端健康状态...")
    
    try:
        # 尝试访问根路径
        response = requests.get(f"{JAVA_BACKEND_URL}/", timeout=5)
        print(f"✅ Java后端服务正在运行 (HTTP {response.status_code})")
        return True
        
    except requests.exceptions.ConnectionError:
        print(f"❌ 无法连接到Java后端服务")
        print(f"   请确保服务正在运行在 {JAVA_BACKEND_URL}")
        return False
        
    except Exception as e:
        print(f"⚠️  健康检查异常: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🧪 帧推送功能测试")
    print("=" * 50)
    print()
    
    # 1. 健康检查
    if not test_health_check():
        print("\n❌ Java后端服务不可用，无法进行测试")
        return
    
    print()
    
    # 2. 帧推送测试
    test_frame_push()
    
    print()
    print("=" * 50)
    print("🏁 测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
