package com.traffic.analysis.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 四方向交通分析请求DTO
 */
@Data
public class FourWayAnalysisRequest {
    
    /**
     * 东方向视频文件
     */
    private MultipartFile eastVideo;

    /**
     * 南方向视频文件
     */
    private MultipartFile southVideo;

    /**
     * 西方向视频文件
     */
    private MultipartFile westVideo;

    /**
     * 北方向视频文件
     */
    private MultipartFile northVideo;
    
    /**
     * 分析类型
     */
    private String analysisType = "basic";

    /**
     * 检测敏感度
     */
    private Double sensitivityLevel = 0.5;

    /**
     * 是否启用预测分析
     */
    private Boolean enablePrediction = false;

    /**
     * 是否启用实时警报
     */
    private Boolean enableAlerts = false;

    /**
     * 分析描述
     */
    private String description;

    /**
     * 交叉口名称
     */
    private String intersectionName;

    /**
     * 分析时间段（小时）
     */
    private Integer analysisHours = 1;
    
    /**
     * 扩展配置参数
     */
    private Map<String, Object> config;
    
    /**
     * 验证所有视频文件是否都已上传
     */
    public boolean hasAllVideos() {
        return eastVideo != null && !eastVideo.isEmpty() &&
               southVideo != null && !southVideo.isEmpty() &&
               westVideo != null && !westVideo.isEmpty() &&
               northVideo != null && !northVideo.isEmpty();
    }
    
    /**
     * 获取视频文件总大小
     */
    public long getTotalVideoSize() {
        long totalSize = 0;
        if (eastVideo != null) totalSize += eastVideo.getSize();
        if (southVideo != null) totalSize += southVideo.getSize();
        if (westVideo != null) totalSize += westVideo.getSize();
        if (northVideo != null) totalSize += northVideo.getSize();
        return totalSize;
    }
    
    /**
     * 验证视频文件总大小是否超限
     */
    public boolean isVideoSizeExceeded(long maxTotalSize) {
        return getTotalVideoSize() > maxTotalSize;
    }
    
    /**
     * 获取分析配置摘要
     */
    public String getConfigSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("分析类型: ").append(analysisType);
        summary.append(", 敏感度: ").append(sensitivityLevel);
        summary.append(", 预测分析: ").append(enablePrediction ? "启用" : "禁用");
        summary.append(", 实时警报: ").append(enableAlerts ? "启用" : "禁用");
        summary.append(", 分析时长: ").append(analysisHours).append("小时");
        return summary.toString();
    }
}
