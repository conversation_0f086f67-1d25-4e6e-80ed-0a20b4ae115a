package com.traffic.analysis.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 信号灯优化建议实体类
 * 对应MongoDB的signal_optimizations集合
 */
@Data
@Document(collection = "signal_optimizations")
public class SignalOptimization {
    
    @Id
    private String id;
    
    /**
     * 关联的任务ID
     */
    @Field("task_id")
    private String taskId;
    
    /**
     * 关联的交通分析结果ID
     */
    @Field("analysis_result_id")
    private String analysisResultId;
    
    /**
     * 推荐信号周期（秒）
     */
    @Field("recommended_cycle")
    private int recommendedCycle;
    
    /**
     * 当前信号周期（秒）
     */
    @Field("current_cycle")
    private int currentCycle;
    
    /**
     * 各方向绿灯时间分配
     */
    @Field("green_time_allocation")
    private Map<String, Integer> greenTimeAllocation = new HashMap<>();
    
    /**
     * 当前绿灯时间分配
     */
    @Field("current_green_time_allocation")
    private Map<String, Integer> currentGreenTimeAllocation = new HashMap<>();
    
    /**
     * 优化理由
     */
    @Field("reason")
    private String reason;
    
    /**
     * 预期改善效果
     */
    @Field("expected_improvement")
    private String expectedImprovement;
    
    /**
     * 优化类型：timing, phase, adaptive
     */
    @Field("optimization_type")
    private String optimizationType;
    
    /**
     * 优化置信度 (0-1)
     */
    @Field("confidence")
    private double confidence;
    
    /**
     * 预期通行效率提升百分比
     */
    @Field("efficiency_improvement")
    private double efficiencyImprovement;
    
    /**
     * 预期拥堵缓解百分比
     */
    @Field("congestion_reduction")
    private double congestionReduction;
    
    /**
     * 优化状态：pending, applied, rejected
     */
    @Field("status")
    private String status = "pending";
    
    /**
     * 优化优先级：low, medium, high, urgent
     */
    @Field("priority")
    private String priority = "medium";
    
    /**
     * 适用时间段
     */
    @Field("applicable_time_periods")
    private List<TimePeriod> applicableTimePeriods = new ArrayList<>();
    
    /**
     * 创建时间
     */
    @Field("created_at")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Field("updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 应用时间
     */
    @Field("applied_at")
    private LocalDateTime appliedAt;
    
    /**
     * 应用者ID
     */
    @Field("applied_by")
    private String appliedBy;
    
    /**
     * 备注信息
     */
    @Field("notes")
    private String notes;
    
    /**
     * 时间段内部类
     */
    @Data
    public static class TimePeriod {
        /**
         * 开始时间（HH:mm格式）
         */
        @Field("start_time")
        private String startTime;
        
        /**
         * 结束时间（HH:mm格式）
         */
        @Field("end_time")
        private String endTime;
        
        /**
         * 适用的星期几（1-7，1为周一）
         */
        @Field("days_of_week")
        private List<Integer> daysOfWeek = new ArrayList<>();
        
        /**
         * 时间段描述
         */
        @Field("description")
        private String description;
    }
    
    /**
     * 计算总的预期改善效果
     */
    public double calculateTotalImprovement() {
        return (efficiencyImprovement + congestionReduction) / 2.0;
    }
    
    /**
     * 检查是否为高优先级优化
     */
    public boolean isHighPriority() {
        return "high".equals(priority) || "urgent".equals(priority);
    }
    
    /**
     * 检查优化是否已应用
     */
    public boolean isApplied() {
        return "applied".equals(status);
    }
    
    /**
     * 应用优化
     */
    public void applyOptimization(String appliedBy) {
        this.status = "applied";
        this.appliedBy = appliedBy;
        this.appliedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 拒绝优化
     */
    public void rejectOptimization(String reason) {
        this.status = "rejected";
        this.notes = reason;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 更新状态
     */
    public void updateStatus(String newStatus) {
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 验证绿灯时间分配是否合理
     */
    public boolean validateGreenTimeAllocation() {
        if (greenTimeAllocation == null || greenTimeAllocation.isEmpty()) {
            return false;
        }
        
        int totalGreenTime = greenTimeAllocation.values().stream()
                .mapToInt(Integer::intValue)
                .sum();
        
        // 绿灯总时间不应超过信号周期的80%
        return totalGreenTime <= (recommendedCycle * 0.8);
    }
    
    /**
     * 获取优化摘要
     */
    public String getOptimizationSummary() {
        return String.format("信号周期: %d秒 → %d秒, 预期效率提升: %.1f%%, 拥堵缓解: %.1f%%",
                currentCycle, recommendedCycle, efficiencyImprovement, congestionReduction);
    }
}
