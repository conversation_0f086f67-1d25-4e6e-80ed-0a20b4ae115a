/**
 * 全宽度优化器
 * 专门用于让双视频预览模块填满整个可用宽度
 */

// 日志防抖控制
const widthLogDebounce = {
  lastMessages: new Map(),
  debounceTime: 5000 // 5秒内相同消息只显示一次
};

// 智能日志函数
function widthSmartLog(level, message, data = null) {
  // 使用全局配置，如果不存在则使用默认值
  const config = window.GLOBAL_LOG_CONFIG || {
    enabled: false,
    level: 'ERROR',
    showWidthLogs: false,
    debounceTime: 5000
  };

  if (!config.enabled || !config.showWidthLogs) return;

  const levels = ['DEBUG', 'INFO', 'WARN', 'ERROR'];
  const currentLevelIndex = levels.indexOf(config.level);
  const messageLevelIndex = levels.indexOf(level);

  if (messageLevelIndex < currentLevelIndex) return;

  // 防抖检查
  const now = Date.now();
  const debounceTime = config.debounceTime || 5000;
  const lastTime = widthLogDebounce.lastMessages.get(message);
  if (lastTime && (now - lastTime) < debounceTime) {
    return;
  }
  widthLogDebounce.lastMessages.set(message, now);

  // 输出日志
  const logMethod = level === 'ERROR' ? console.error :
                   level === 'WARN' ? console.warn : console.log;
  logMethod(`[宽度优化] ${message}`, data || '');
}

document.addEventListener('DOMContentLoaded', function() {
  widthSmartLog('INFO', '📐 全宽度优化器已加载');

  // 等待Vue应用初始化
  setTimeout(() => {
    optimizeFullWidth();
    startWidthMonitoring();
  }, 2000);
});

function optimizeFullWidth() {
  widthSmartLog('DEBUG', '📐 开始全宽度优化...');

  // 查找预览容器
  const previewSection = document.querySelector('.realtime-preview-section');
  if (previewSection) {
    widthSmartLog('INFO', '✅ 找到预览容器，应用全宽度优化');
    applyFullWidthStyles(previewSection);
  } else {
    widthSmartLog('DEBUG', '⚠️ 未找到预览容器，等待创建...');
    setTimeout(optimizeFullWidth, 1000);
  }
}

function applyFullWidthStyles(container) {
  // 获取视口宽度
  const viewportWidth = window.innerWidth;
  
  // 根据屏幕尺寸应用不同的优化策略
  if (viewportWidth >= 1400) {
    // 大屏幕优化
    applyLargeScreenStyles(container);
  } else if (viewportWidth >= 1200) {
    // 中等屏幕优化
    applyMediumScreenStyles(container);
  } else if (viewportWidth >= 768) {
    // 小屏幕优化
    applySmallScreenStyles(container);
  } else {
    // 移动端优化
    applyMobileStyles(container);
  }
  
  widthSmartLog('INFO', `✅ 全宽度优化完成 (屏幕宽度: ${viewportWidth}px)`);
}

function applyLargeScreenStyles(container) {
  widthSmartLog('DEBUG', '🖥️ 应用大屏幕样式');
  
  // 容器样式
  Object.assign(container.style, {
    margin: '32px -60px',
    padding: '40px',
    width: 'calc(100vw - 120px)',
    maxWidth: '1600px',
    marginLeft: 'auto',
    marginRight: 'auto'
  });
  
  // 网格样式
  const grid = container.querySelector('.video-preview-grid');
  if (grid) {
    Object.assign(grid.style, {
      gap: '48px'
    });
  }
  
  // 视频容器样式
  const videoContainers = container.querySelectorAll('.video-preview-container');
  videoContainers.forEach(videoContainer => {
    Object.assign(videoContainer.style, {
      padding: '32px',
      minHeight: '480px'
    });
  });
  
  // 预览区域样式
  const previewAreas = container.querySelectorAll('.video-preview-area, .detection-frame-container');
  previewAreas.forEach(area => {
    Object.assign(area.style, {
      height: '400px',
      minHeight: '400px'
    });
  });
}

function applyMediumScreenStyles(container) {
  widthSmartLog('DEBUG', '💻 应用中等屏幕样式');
  
  // 容器样式
  Object.assign(container.style, {
    margin: '24px -40px',
    padding: '32px',
    width: 'calc(100vw - 80px)',
    maxWidth: '1400px',
    marginLeft: 'auto',
    marginRight: 'auto'
  });
  
  // 网格样式
  const grid = container.querySelector('.video-preview-grid');
  if (grid) {
    Object.assign(grid.style, {
      gap: '32px'
    });
  }
  
  // 视频容器样式
  const videoContainers = container.querySelectorAll('.video-preview-container');
  videoContainers.forEach(videoContainer => {
    Object.assign(videoContainer.style, {
      padding: '24px',
      minHeight: '420px'
    });
  });
  
  // 预览区域样式
  const previewAreas = container.querySelectorAll('.video-preview-area, .detection-frame-container');
  previewAreas.forEach(area => {
    Object.assign(area.style, {
      height: '340px',
      minHeight: '340px'
    });
  });
}

function applySmallScreenStyles(container) {
  widthSmartLog('DEBUG', '📱 应用小屏幕样式');
  
  // 容器样式
  Object.assign(container.style, {
    margin: '24px -20px',
    padding: '24px',
    width: 'calc(100vw - 40px)',
    maxWidth: '1200px',
    marginLeft: 'auto',
    marginRight: 'auto'
  });
  
  // 网格样式
  const grid = container.querySelector('.video-preview-grid');
  if (grid) {
    Object.assign(grid.style, {
      gap: '24px'
    });
  }
  
  // 视频容器样式
  const videoContainers = container.querySelectorAll('.video-preview-container');
  videoContainers.forEach(videoContainer => {
    Object.assign(videoContainer.style, {
      padding: '20px',
      minHeight: '380px'
    });
  });
  
  // 预览区域样式
  const previewAreas = container.querySelectorAll('.video-preview-area, .detection-frame-container');
  previewAreas.forEach(area => {
    Object.assign(area.style, {
      height: '300px',
      minHeight: '300px'
    });
  });
}

function applyMobileStyles(container) {
  widthSmartLog('DEBUG', '📱 应用移动端样式');
  
  // 容器样式
  Object.assign(container.style, {
    margin: '16px 0',
    padding: '16px',
    width: '100%',
    maxWidth: 'none',
    marginLeft: 'auto',
    marginRight: 'auto'
  });
  
  // 网格样式 - 移动端改为单列
  const grid = container.querySelector('.video-preview-grid');
  if (grid) {
    Object.assign(grid.style, {
      gridTemplateColumns: '1fr',
      gap: '16px'
    });
  }
  
  // 视频容器样式
  const videoContainers = container.querySelectorAll('.video-preview-container');
  videoContainers.forEach(videoContainer => {
    Object.assign(videoContainer.style, {
      padding: '16px',
      minHeight: '320px'
    });
  });
  
  // 预览区域样式
  const previewAreas = container.querySelectorAll('.video-preview-area, .detection-frame-container');
  previewAreas.forEach(area => {
    Object.assign(area.style, {
      height: '240px',
      minHeight: '240px'
    });
  });
}

function startWidthMonitoring() {
  widthSmartLog('INFO', '👀 开始宽度监控...');

  // 状态跟踪
  let lastResizeTime = 0;
  let widthCheckCount = 0;

  // 监听窗口大小变化
  let resizeTimeout;
  window.addEventListener('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      const now = Date.now();
      if (now - lastResizeTime > 1000) { // 1秒内只记录一次resize
        widthSmartLog('INFO', '🔄 窗口大小变化，重新优化宽度');
        lastResizeTime = now;
      }
      optimizeFullWidth();
    }, 300);
  });

  // 监控DOM变化，确保新添加的元素也能应用优化
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // 检查是否是预览相关元素
            if (node.classList && node.classList.contains('realtime-preview-section')) {
              widthSmartLog('INFO', '🔄 检测到新的预览元素，应用全宽度优化');
              setTimeout(() => applyFullWidthStyles(node), 100);
            }
          }
        });
      }
    });
  });

  // 开始观察
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // 定期检查和修复样式（减少频率和日志）
  setInterval(() => {
    const previewSection = document.querySelector('.realtime-preview-section');
    if (previewSection) {
      // 检查是否需要重新应用样式
      const currentWidth = previewSection.style.width;
      if (!currentWidth || currentWidth === '100%') {
        widthSmartLog('WARN', '🔧 检测到宽度样式丢失，重新应用');
        applyFullWidthStyles(previewSection);
      } else {
        // 只在前几次检查时显示正常状态
        widthCheckCount++;
        if (widthCheckCount <= 3) {
          widthSmartLog('DEBUG', '✅ 宽度样式状态正常');
        }
      }
    }
  }, 15000); // 增加检查间隔到15秒
}

// 添加CSS样式覆盖
function addFullWidthCSS() {
  const style = document.createElement('style');
  style.textContent = `
    /* 全宽度优化CSS */
    .realtime-preview-section {
      box-sizing: border-box !important;
    }
    
    .video-preview-grid {
      width: 100% !important;
      box-sizing: border-box !important;
    }
    
    .video-preview-container {
      box-sizing: border-box !important;
      width: 100% !important;
    }
    
    /* 确保父容器不限制宽度 */
    .status-section {
      max-width: none !important;
      width: 100% !important;
    }
    
    /* 大屏幕特殊优化 */
    @media (min-width: 1400px) {
      .realtime-preview-section {
        transform: translateX(0) !important;
      }
    }
  `;
  
  document.head.appendChild(style);
  widthSmartLog('INFO', '✅ 全宽度CSS样式已添加');
}

// 页面加载完成后添加CSS
document.addEventListener('DOMContentLoaded', function() {
  addFullWidthCSS();
});

// 导出函数供其他脚本使用
window.optimizeFullWidth = optimizeFullWidth;
window.applyFullWidthStyles = applyFullWidthStyles;
