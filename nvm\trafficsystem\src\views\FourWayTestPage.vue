<template>
  <div class="four-way-test-page">
    <div class="page-header">
      <h1>四方向实时检测测试页面</h1>
      <p>用于测试四方向实时检测功能</p>
    </div>

    <div class="test-controls">
      <el-card>
        <template #header>
          <span>测试控制</span>
        </template>
        
        <div class="control-group">
          <el-input
            v-model="testTaskId"
            placeholder="输入测试任务ID"
            style="width: 300px; margin-right: 16px;"
          />
          <el-button type="primary" @click="startTest" :loading="testing">
            开始测试
          </el-button>
          <el-button @click="stopTest" :disabled="!testing">
            停止测试
          </el-button>
        </div>
        
        <div class="test-status">
          <el-tag :type="connectionStatus === 'connected' ? 'success' : 'danger'">
            {{ connectionStatus === 'connected' ? '已连接' : '未连接' }}
          </el-tag>
          <span style="margin-left: 16px;">
            接收帧数: {{ frameCount }}
          </span>
        </div>
      </el-card>
    </div>

    <div v-if="testing" class="test-viewer">
      <FourWayRealtimeViewer
        ref="testViewer"
        :task-id="testTaskId"
        :auto-start="true"
        @detection-update="handleDetectionUpdate"
        @status-change="handleStatusChange"
      />
    </div>

    <div class="test-logs">
      <el-card>
        <template #header>
          <span>测试日志</span>
          <el-button size="small" @click="clearLogs" style="float: right;">
            清空日志
          </el-button>
        </template>
        
        <div class="logs-container">
          <div
            v-for="(log, index) in logs"
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue'

export default {
  name: 'FourWayTestPage',
  components: {
    FourWayRealtimeViewer
  },
  setup() {
    // 响应式数据
    const testTaskId = ref('test-task-' + Date.now())
    const testing = ref(false)
    const connectionStatus = ref('disconnected')
    const frameCount = ref(0)
    const testViewer = ref(null)
    const logs = ref([])
    
    // 方法
    const addLog = (type, message) => {
      logs.value.push({
        type,
        message,
        time: new Date().toLocaleTimeString()
      })
      
      // 限制日志数量
      if (logs.value.length > 100) {
        logs.value.shift()
      }
    }
    
    const clearLogs = () => {
      logs.value = []
    }
    
    const startTest = () => {
      if (!testTaskId.value) {
        ElMessage.warning('请输入测试任务ID')
        return
      }
      
      testing.value = true
      connectionStatus.value = 'connecting'
      frameCount.value = 0
      
      addLog('info', `开始测试任务: ${testTaskId.value}`)
      
      // 模拟连接成功
      setTimeout(() => {
        connectionStatus.value = 'connected'
        addLog('success', '连接成功')
      }, 1000)
    }
    
    const stopTest = () => {
      testing.value = false
      connectionStatus.value = 'disconnected'
      
      addLog('info', '测试已停止')
      
      // 清理查看器
      if (testViewer.value && testViewer.value.cleanup) {
        testViewer.value.cleanup()
      }
    }
    
    const handleDetectionUpdate = (updateData) => {
      frameCount.value++
      
      addLog('info', `收到检测更新: 方向=${updateData.direction}, 车辆=${updateData.frameData.detectionCount || 0}`)
      
      if (updateData.globalStats) {
        addLog('success', `全局统计: 总车辆=${updateData.globalStats.totalVehicles}, 峰值方向=${updateData.globalStats.peakDirection}`)
      }
    }
    
    const handleStatusChange = (statusData) => {
      addLog('info', `状态变化: ${JSON.stringify(statusData)}`)
    }
    
    // 生命周期
    onMounted(() => {
      addLog('info', '测试页面已加载')
    })
    
    onUnmounted(() => {
      if (testing.value) {
        stopTest()
      }
    })
    
    return {
      // 响应式数据
      testTaskId,
      testing,
      connectionStatus,
      frameCount,
      testViewer,
      logs,
      
      // 方法
      addLog,
      clearLogs,
      startTest,
      stopTest,
      handleDetectionUpdate,
      handleStatusChange
    }
  }
}
</script>

<style scoped>
.four-way-test-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  color: #1f2937;
  margin-bottom: 8px;
}

.page-header p {
  color: #6b7280;
  margin: 0;
}

.test-controls {
  margin-bottom: 24px;
}

.control-group {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.test-status {
  display: flex;
  align-items: center;
}

.test-viewer {
  margin-bottom: 24px;
}

.test-logs {
  max-height: 400px;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.log-item {
  padding: 4px 8px;
  border-left: 3px solid #e5e7eb;
  margin-bottom: 2px;
}

.log-item.info {
  border-left-color: #3b82f6;
  background: #eff6ff;
}

.log-item.success {
  border-left-color: #10b981;
  background: #ecfdf5;
}

.log-item.warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.log-item.error {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.log-time {
  color: #6b7280;
  margin-right: 12px;
}

.log-message {
  color: #1f2937;
}
</style>
