/**
 * 定格显示功能测试脚本
 * 用于测试检测帧的定格显示效果
 */

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
  console.log('🎯 定格显示测试脚本已加载');
  
  // 等待Vue应用初始化
  setTimeout(() => {
    setupFreezeFrameTest();
  }, 3000);
});

function setupFreezeFrameTest() {
  console.log('🧪 设置定格显示测试...');
  
  // 检查stompService是否可用
  if (typeof window.stompService === 'undefined') {
    console.log('⚠️ stompService未找到，等待加载...');
    setTimeout(setupFreezeFrameTest, 1000);
    return;
  }
  
  // 获取当前任务ID
  const taskId = getCurrentTaskId();
  if (!taskId) {
    console.log('⚠️ 未找到任务ID');
    return;
  }
  
  console.log(`🎯 当前任务ID: ${taskId}`);
  
  // 监听帧数据接收事件
  window.addEventListener('horizontalFrameReceived', (event) => {
    handleDetectionFrame('水平方向', event.detail.frameData);
  });
  
  window.addEventListener('verticalFrameReceived', (event) => {
    handleDetectionFrame('垂直方向', event.detail.frameData);
  });
  
  // 添加测试按钮
  addTestButtons(taskId);
}

function handleDetectionFrame(direction, frameData) {
  if (frameData.detectionCount > 0) {
    console.log(`🎯 ${direction}检测到车辆，定格显示: 帧${frameData.frameNumber}, 车辆${frameData.detectionCount}`);
    
    // 显示定格通知
    showFreezeNotification(direction, frameData);
  }
}

function showFreezeNotification(direction, frameData) {
  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = 'freeze-notification';
  notification.innerHTML = `
    <div class="freeze-content">
      <span class="freeze-icon">🎯</span>
      <div class="freeze-text">
        <strong>${direction}检测结果已定格</strong>
        <small>帧${frameData.frameNumber} - ${frameData.detectionCount}辆车</small>
      </div>
    </div>
  `;
  
  // 添加样式
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    z-index: 9999;
    animation: slideIn 0.3s ease-out;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;
  
  // 添加动画样式
  const style = document.createElement('style');
  style.textContent = `
    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    .freeze-content {
      display: flex;
      align-items: center;
      gap: 12px;
    }
    
    .freeze-icon {
      font-size: 24px;
    }
    
    .freeze-text {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }
    
    .freeze-text strong {
      font-size: 14px;
      font-weight: 600;
    }
    
    .freeze-text small {
      font-size: 12px;
      opacity: 0.9;
    }
  `;
  
  document.head.appendChild(style);
  document.body.appendChild(notification);
  
  // 3秒后自动移除
  setTimeout(() => {
    notification.style.animation = 'slideIn 0.3s ease-out reverse';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

function addTestButtons(taskId) {
  // 查找合适的位置添加测试按钮
  let buttonContainer = document.querySelector('.test-button-container');
  
  if (!buttonContainer) {
    buttonContainer = document.createElement('div');
    buttonContainer.className = 'test-button-container';
    buttonContainer.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      z-index: 9998;
    `;
    document.body.appendChild(buttonContainer);
  }
  
  // 添加测试按钮
  const testButton = document.createElement('button');
  testButton.textContent = '🧪 测试定格显示';
  testButton.style.cssText = `
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
  `;
  
  testButton.addEventListener('click', () => {
    sendTestDetectionFrame(taskId);
  });
  
  testButton.addEventListener('mouseenter', () => {
    testButton.style.background = '#2563eb';
    testButton.style.transform = 'translateY(-1px)';
  });
  
  testButton.addEventListener('mouseleave', () => {
    testButton.style.background = '#3b82f6';
    testButton.style.transform = 'translateY(0)';
  });
  
  buttonContainer.appendChild(testButton);
  
  // 添加清除按钮
  const clearButton = document.createElement('button');
  clearButton.textContent = '🧹 清除定格';
  clearButton.style.cssText = `
    background: #f59e0b;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
  `;
  
  clearButton.addEventListener('click', () => {
    clearAllDetectionFrames();
  });
  
  clearButton.addEventListener('mouseenter', () => {
    clearButton.style.background = '#d97706';
    clearButton.style.transform = 'translateY(-1px)';
  });
  
  clearButton.addEventListener('mouseleave', () => {
    clearButton.style.background = '#f59e0b';
    clearButton.style.transform = 'translateY(0)';
  });
  
  buttonContainer.appendChild(clearButton);
}

function sendTestDetectionFrame(taskId) {
  console.log('📤 发送测试检测帧...');
  
  // 创建测试帧数据
  const testFrameData = {
    type: "frame_update",
    taskId: taskId,
    frameNumber: 999,
    totalFrames: 1000,
    imageData: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
    detectionCount: Math.floor(Math.random() * 5) + 1, // 随机1-5辆车
    timestamp: new Date().toISOString(),
    quality: 75
  };
  
  // 发送到后端
  fetch('/api/video-progress/frame-update', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(testFrameData)
  }).then(response => {
    if (response.ok) {
      console.log('✅ 测试帧发送成功');
      showFreezeNotification('测试', testFrameData);
    } else {
      console.error('❌ 测试帧发送失败');
    }
  }).catch(error => {
    console.error('❌ 发送测试帧异常:', error);
  });
}

function clearAllDetectionFrames() {
  console.log('🧹 清除所有定格显示...');
  
  // 触发清除事件
  window.dispatchEvent(new CustomEvent('clearDetectionFrames'));
  
  // 显示清除通知
  const notification = document.createElement('div');
  notification.textContent = '🧹 已清除所有定格显示';
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #f59e0b;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    z-index: 9999;
    font-size: 14px;
    font-weight: 500;
  `;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 2000);
}

function getCurrentTaskId() {
  // 从URL参数获取
  const urlParams = new URLSearchParams(window.location.search);
  let taskId = urlParams.get('taskId');

  if (!taskId) {
    // 从sessionStorage获取
    try {
      const uploadState = sessionStorage.getItem('uploadState');
      if (uploadState) {
        const state = JSON.parse(uploadState);
        taskId = state.taskId;
      }
    } catch (e) {
      console.warn('无法从sessionStorage获取任务ID:', e);
    }
  }

  if (!taskId) {
    // 从页面元素获取
    const taskElements = document.querySelectorAll('[data-task-id]');
    if (taskElements.length > 0) {
      taskId = taskElements[0].getAttribute('data-task-id');
    }
  }

  return taskId;
}

// 导出函数供其他脚本使用
window.setupFreezeFrameTest = setupFreezeFrameTest;
window.sendTestDetectionFrame = sendTestDetectionFrame;
window.clearAllDetectionFrames = clearAllDetectionFrames;
