# 四方向智能交通分析系统 API 文档

## 概述

四方向智能交通分析系统提供了一套完整的RESTful API，用于处理十字路口四个方向的视频上传、分析和结果查询。

## 基础信息

- **基础URL**: `http://localhost:8080/api`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **文件上传**: multipart/form-data

## 认证

所有API请求都需要在请求头中包含有效的认证令牌：

```
Authorization: Bearer <your-token>
```

## API 端点

### 1. 四方向视频上传

#### 上传四方向视频文件

**端点**: `POST /video-analysis/four-way/upload`

**描述**: 上传十字路口四个方向的视频文件进行智能交通分析

**请求格式**: `multipart/form-data`

**请求参数**:
- `eastVideo` (file, required): 东向视频文件
- `southVideo` (file, required): 南向视频文件  
- `westVideo` (file, required): 西向视频文件
- `northVideo` (file, required): 北向视频文件

**文件要求**:
- 支持格式: MP4, AVI, MOV
- 单文件大小: 最大 500MB
- 总大小限制: 2GB
- 推荐分辨率: 720p 或更高

**响应示例**:
```json
{
  "success": true,
  "message": "四方向视频上传成功",
  "data": {
    "taskId": "four-way-task-20240119-001",
    "status": "queued",
    "createdAt": "2024-01-19T10:30:00Z",
    "directions": {
      "east": {
        "status": "queued",
        "fileId": "65a9b8c7d4e5f6789012345a"
      },
      "south": {
        "status": "queued", 
        "fileId": "65a9b8c7d4e5f6789012345b"
      },
      "west": {
        "status": "queued",
        "fileId": "65a9b8c7d4e5f6789012345c"
      },
      "north": {
        "status": "queued",
        "fileId": "65a9b8c7d4e5f6789012345d"
      }
    }
  }
}
```

### 2. 任务状态查询

#### 获取四方向分析任务状态

**端点**: `GET /video-analysis/four-way/{taskId}/status`

**描述**: 查询四方向分析任务的当前状态和进度

**路径参数**:
- `taskId` (string, required): 任务ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "taskId": "four-way-task-20240119-001",
    "status": "processing",
    "progress": 65,
    "createdAt": "2024-01-19T10:30:00Z",
    "updatedAt": "2024-01-19T10:35:00Z",
    "directions": {
      "east": {
        "status": "completed",
        "progress": 100,
        "vehicleCount": 25,
        "processingTime": 120
      },
      "south": {
        "status": "processing",
        "progress": 80,
        "vehicleCount": 18,
        "processingTime": 95
      },
      "west": {
        "status": "completed", 
        "progress": 100,
        "vehicleCount": 22,
        "processingTime": 110
      },
      "north": {
        "status": "queued",
        "progress": 0,
        "vehicleCount": 0,
        "processingTime": 0
      }
    }
  }
}
```

### 3. 分析结果查询

#### 获取四方向分析结果

**端点**: `GET /video-analysis/four-way/{taskId}/result`

**描述**: 获取完整的四方向交通分析结果

**路径参数**:
- `taskId` (string, required): 任务ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "taskId": "four-way-task-20240119-001",
    "status": "completed",
    "totalVehicleCount": 85,
    "processingDurationSeconds": 450,
    "directions": {
      "east": {
        "vehicleCount": 25,
        "vehicleTypes": {
          "car": 20,
          "truck": 3,
          "bus": 1,
          "motorcycle": 1
        },
        "averageFlowDensity": 2.5,
        "crowdLevel": "轻度拥堵"
      },
      "south": {
        "vehicleCount": 18,
        "vehicleTypes": {
          "car": 15,
          "truck": 2,
          "bus": 1,
          "motorcycle": 0
        },
        "averageFlowDensity": 1.8,
        "crowdLevel": "畅通"
      },
      "west": {
        "vehicleCount": 22,
        "vehicleTypes": {
          "car": 18,
          "truck": 2,
          "bus": 1,
          "motorcycle": 1
        },
        "averageFlowDensity": 2.2,
        "crowdLevel": "畅通"
      },
      "north": {
        "vehicleCount": 20,
        "vehicleTypes": {
          "car": 16,
          "truck": 3,
          "bus": 1,
          "motorcycle": 0
        },
        "averageFlowDensity": 2.0,
        "crowdLevel": "畅通"
      }
    },
    "trafficAnalysis": {
      "totalVehicleCount": 85,
      "peakDirection": "east",
      "trafficFlowBalance": 0.72,
      "congestionLevel": "轻度拥堵",
      "signalOptimization": {
        "recommendedCycle": 110,
        "greenTimeAllocation": {
          "east": 35,
          "south": 25,
          "west": 30,
          "north": 20
        },
        "expectedImprovement": "通行效率提升15%",
        "reason": "东向车流量较大，建议增加绿灯时间"
      }
    }
  }
}
```

### 4. 报告生成

#### 生成四方向分析报告

**端点**: `GET /video-analysis/four-way/{taskId}/report`

**描述**: 生成并返回HTML格式的四方向交通分析报告

**路径参数**:
- `taskId` (string, required): 任务ID

**响应**: HTML页面

#### 导出分析报告数据

**端点**: `GET /video-analysis/four-way/{taskId}/export`

**描述**: 导出JSON格式的分析报告数据

**路径参数**:
- `taskId` (string, required): 任务ID

**响应示例**:
```json
{
  "success": true,
  "data": {
    "taskId": "four-way-task-20240119-001",
    "title": "四方向智能交通分析报告",
    "generatedAt": "2024-01-19T11:00:00Z",
    "summary": {
      "totalVehicles": 85,
      "analysisTime": "2024-01-19T10:30:00Z",
      "processingDuration": 450,
      "peakDirection": "east"
    },
    "directions": {
      // 各方向详细数据
    },
    "intelligentAnalysis": {
      // 智能分析结果
    },
    "recommendations": [
      "建议在高峰时段增加东向绿灯时间",
      "考虑在南北方向设置右转专用道",
      "建议优化信号灯配时方案"
    ]
  }
}
```

### 5. WebSocket 实时数据

#### 连接实时帧数据推送

**端点**: `ws://localhost:8080/ws`

**主题订阅**:
- `/topic/four-way-frames/{taskId}` - 四方向帧数据
- `/topic/four-way-progress/{taskId}` - 四方向进度更新
- `/topic/four-way-frames/{taskId}/{direction}` - 特定方向帧数据

**消息格式**:
```json
{
  "type": "four_way_frame_update",
  "taskId": "four-way-task-20240119-001",
  "direction": "east",
  "frameNumber": 150,
  "totalFrames": 1800,
  "imageData": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "detectionCount": 3,
  "vehicleTypes": {
    "car": 2,
    "truck": 1
  },
  "timestamp": "2024-01-19T10:32:15Z"
}
```

## 错误处理

### 错误响应格式

```json
{
  "success": false,
  "errorCode": "FOUR_WAY_ANALYSIS_ERROR",
  "message": "错误描述信息",
  "timestamp": "2024-01-19T10:30:00Z",
  "path": "/api/video-analysis/four-way/upload",
  "details": {
    // 额外的错误详情
  }
}
```

### 常见错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `FOUR_WAY_ANALYSIS_ERROR` | 400 | 四方向分析业务错误 |
| `VIDEO_UPLOAD_ERROR` | 400 | 视频上传错误 |
| `VIDEO_PROCESSING_ERROR` | 500 | 视频处理错误 |
| `TASK_NOT_FOUND` | 404 | 任务未找到 |
| `FILE_SIZE_EXCEEDED` | 413 | 文件大小超限 |
| `MULTIPART_UPLOAD_ERROR` | 400 | 多部分上传错误 |
| `MODEL_SERVICE_ERROR` | 503 | AI模型服务不可用 |

## 使用示例

### JavaScript/Axios 示例

```javascript
// 上传四方向视频
const uploadFourWayVideos = async (files) => {
  const formData = new FormData()
  formData.append('eastVideo', files.east)
  formData.append('southVideo', files.south)
  formData.append('westVideo', files.west)
  formData.append('northVideo', files.north)
  
  try {
    const response = await axios.post('/api/video-analysis/four-way/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': `Bearer ${token}`
      },
      onUploadProgress: (progressEvent) => {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        console.log(`上传进度: ${progress}%`)
      }
    })
    
    return response.data
  } catch (error) {
    console.error('上传失败:', error.response?.data || error.message)
    throw error
  }
}

// 查询任务状态
const getTaskStatus = async (taskId) => {
  try {
    const response = await axios.get(`/api/video-analysis/four-way/${taskId}/status`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    return response.data
  } catch (error) {
    console.error('查询状态失败:', error.response?.data || error.message)
    throw error
  }
}
```

### cURL 示例

```bash
# 上传四方向视频
curl -X POST "http://localhost:8080/api/video-analysis/four-way/upload" \
  -H "Authorization: Bearer your-token" \
  -F "eastVideo=@east-video.mp4" \
  -F "southVideo=@south-video.mp4" \
  -F "westVideo=@west-video.mp4" \
  -F "northVideo=@north-video.mp4"

# 查询任务状态
curl -X GET "http://localhost:8080/api/video-analysis/four-way/task-id/status" \
  -H "Authorization: Bearer your-token"

# 获取分析结果
curl -X GET "http://localhost:8080/api/video-analysis/four-way/task-id/result" \
  -H "Authorization: Bearer your-token"
```

## 限制和注意事项

1. **文件大小限制**: 单个视频文件最大500MB，总上传大小不超过2GB
2. **并发限制**: 同一用户最多同时处理4个四方向分析任务
3. **处理时间**: 根据视频长度和复杂度，处理时间通常为5-30分钟
4. **数据保留**: 分析结果保留30天，原始视频文件保留7天
5. **API限流**: 每分钟最多100次API调用

## 版本信息

- **当前版本**: v1.0.0
- **最后更新**: 2024-01-19
- **兼容性**: 支持所有现代浏览器和移动设备
