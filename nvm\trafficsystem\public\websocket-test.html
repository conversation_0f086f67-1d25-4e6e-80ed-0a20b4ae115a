<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>WebSocket连接测试</h1>
    
    <div id="status" class="status info">准备测试WebSocket连接...</div>
    
    <div>
        <button id="testBtn" class="btn-primary" onclick="testWebSocket()">测试WebSocket连接</button>
        <button id="subscribeBtn" class="btn-success" onclick="subscribeToTopic()" disabled>订阅测试主题</button>
        <button id="clearBtn" class="btn-danger" onclick="clearLog()">清空日志</button>
    </div>
    
    <h3>连接日志</h3>
    <div id="log" class="log"></div>
    
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7.0.0/bundles/stomp.umd.min.js"></script>
    
    <script>
        let stompClient = null;
        let subscription = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function testWebSocket() {
            const testBtn = document.getElementById('testBtn');
            const subscribeBtn = document.getElementById('subscribeBtn');
            
            testBtn.disabled = true;
            testBtn.textContent = '连接中...';
            
            log('开始WebSocket连接测试...');
            updateStatus('正在连接WebSocket...', 'info');
            
            try {
                // 使用正确的端点地址
                const socketUrl = 'http://localhost:8080/api/ws';
                log(`连接地址: ${socketUrl}`);
                
                const socket = new SockJS(socketUrl);
                
                stompClient = new StompJs.Client({
                    webSocketFactory: () => socket,
                    debug: (str) => {
                        log(`[STOMP DEBUG] ${str}`);
                    },
                    reconnectDelay: 5000,
                    heartbeatIncoming: 4000,
                    heartbeatOutgoing: 4000
                });
                
                stompClient.onConnect = (frame) => {
                    log('✅ WebSocket连接成功!');
                    log(`连接帧信息: ${frame}`);
                    updateStatus('WebSocket连接成功', 'success');
                    
                    testBtn.textContent = '重新测试';
                    testBtn.disabled = false;
                    subscribeBtn.disabled = false;
                };
                
                stompClient.onStompError = (frame) => {
                    log(`❌ STOMP错误: ${frame.headers['message']}`);
                    log(`错误详情: ${frame.body}`);
                    updateStatus('STOMP连接错误', 'error');
                    
                    testBtn.textContent = '重新测试';
                    testBtn.disabled = false;
                };
                
                stompClient.onWebSocketError = (error) => {
                    log(`❌ WebSocket错误: ${error}`);
                    updateStatus('WebSocket连接错误', 'error');
                    
                    testBtn.textContent = '重新测试';
                    testBtn.disabled = false;
                };
                
                stompClient.onDisconnect = () => {
                    log('🔌 WebSocket连接已断开');
                    updateStatus('WebSocket连接已断开', 'info');
                    subscribeBtn.disabled = true;
                };
                
                stompClient.activate();
                
            } catch (error) {
                log(`❌ 连接异常: ${error.message}`);
                updateStatus('连接异常', 'error');
                
                testBtn.textContent = '重新测试';
                testBtn.disabled = false;
            }
        }
        
        function subscribeToTopic() {
            if (!stompClient || !stompClient.connected) {
                log('❌ WebSocket未连接，无法订阅');
                return;
            }
            
            const taskId = '0c497385-301d-48e6-a487-12453f3417e0';
            const topic = `/topic/four-way-frames/${taskId}`;
            
            log(`📡 订阅主题: ${topic}`);
            
            try {
                subscription = stompClient.subscribe(topic, (message) => {
                    log(`📨 收到消息: ${message.body}`);
                    try {
                        const data = JSON.parse(message.body);
                        log(`📊 解析数据: 方向=${data.direction}, 帧号=${data.frameNumber}, 检测数=${data.detectionCount}`);
                    } catch (e) {
                        log(`⚠️ 消息解析失败: ${e.message}`);
                    }
                });
                
                log('✅ 主题订阅成功');
                updateStatus('已订阅测试主题', 'success');
                
            } catch (error) {
                log(`❌ 订阅失败: ${error.message}`);
                updateStatus('订阅失败', 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        // 页面加载完成后自动测试
        window.onload = function() {
            log('页面加载完成，准备测试WebSocket连接');
        };
    </script>
</body>
</html>
