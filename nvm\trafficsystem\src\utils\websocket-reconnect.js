/**
 * WebSocket重连工具
 * 解决STOMP连接断开问题的即时修复方案
 */

import stompService from './stomp-service'

class WebSocketReconnector {
  constructor() {
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 2000
    this.isReconnecting = false
  }

  /**
   * 检查并修复WebSocket连接
   */
  async checkAndFixConnection() {
    console.log('🔍 检查WebSocket连接状态...')
    
    try {
      // 检查连接状态
      if (!stompService.connected || !stompService.client || !stompService.client.connected) {
        console.log('⚠️ WebSocket连接已断开，开始重连...')
        await this.reconnect()
      } else {
        console.log('✅ WebSocket连接正常')
        this.reconnectAttempts = 0
      }
    } catch (error) {
      console.error('❌ 检查连接状态失败:', error)
      await this.reconnect()
    }
  }

  /**
   * 重新连接WebSocket
   */
  async reconnect() {
    if (this.isReconnecting) {
      console.log('🔄 重连正在进行中，跳过...')
      return
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ 达到最大重连次数，停止重连')
      return
    }

    this.isReconnecting = true
    this.reconnectAttempts++

    console.log(`🔄 开始第 ${this.reconnectAttempts} 次重连尝试...`)

    try {
      // 强制断开现有连接
      if (stompService.client) {
        try {
          stompService.client.deactivate()
        } catch (e) {
          // 忽略断开连接时的错误
        }
      }

      // 重置连接状态
      stompService.connected = false
      stompService.connectionPromise = null

      // 重新初始化连接
      await stompService.init()

      console.log('✅ WebSocket重连成功')
      this.reconnectAttempts = 0
      this.isReconnecting = false

      // 重连成功后，重新订阅当前任务的数据
      this.resubscribeCurrentTask()

    } catch (error) {
      console.error(`❌ 第 ${this.reconnectAttempts} 次重连失败:`, error)
      this.isReconnecting = false

      // 等待一段时间后重试
      setTimeout(() => {
        this.reconnect()
      }, this.reconnectInterval * this.reconnectAttempts)
    }
  }

  /**
   * 重新订阅当前任务的数据
   */
  resubscribeCurrentTask() {
    const taskId = this.getCurrentTaskId()
    if (!taskId) {
      console.log('📝 没有找到当前任务ID，跳过重新订阅')
      return
    }

    console.log(`🔄 重新订阅任务 ${taskId} 的数据...`)

    try {
      // 重新订阅视频进度
      stompService.subscribe(`video-progress/${taskId}`, (data) => {
        console.log('📊 收到视频进度更新:', data)
        
        // 触发全局事件
        window.dispatchEvent(new CustomEvent('videoProgressUpdate', {
          detail: { taskId, data }
        }))
      })

      // 重新订阅帧数据
      stompService.subscribeFrameUpdates(taskId, (frameData) => {
        console.log('🎬 收到帧数据:', frameData)
        
        // 触发全局事件
        window.dispatchEvent(new CustomEvent('frameDataReceived', {
          detail: { taskId, frameData }
        }))
      })

      console.log('✅ 任务数据重新订阅成功')

    } catch (error) {
      console.error('❌ 重新订阅失败:', error)
    }
  }

  /**
   * 获取当前任务ID
   */
  getCurrentTaskId() {
    // 方法1: 从URL参数获取
    const urlParams = new URLSearchParams(window.location.search)
    let taskId = urlParams.get('taskId')

    if (!taskId) {
      // 方法2: 从路径获取
      const pathParts = window.location.pathname.split('/')
      const taskIndex = pathParts.indexOf('video-status')
      if (taskIndex !== -1 && pathParts[taskIndex + 1]) {
        taskId = pathParts[taskIndex + 1]
      }
    }

    if (!taskId) {
      // 方法3: 从sessionStorage获取
      try {
        const uploadState = sessionStorage.getItem('uploadState')
        if (uploadState) {
          const state = JSON.parse(uploadState)
          taskId = state.taskId
        }
      } catch (e) {
        console.warn('无法从sessionStorage获取任务ID:', e)
      }
    }

    if (!taskId) {
      // 方法4: 从Vue应用状态获取
      try {
        const app = document.querySelector('#app').__vue__
        if (app && app.$store && app.$store.state.currentTaskId) {
          taskId = app.$store.state.currentTaskId
        }
      } catch (e) {
        // 忽略错误
      }
    }

    return taskId
  }

  /**
   * 启动定期检查
   */
  startPeriodicCheck() {
    console.log('🔄 启动WebSocket连接定期检查...')
    
    // 每30秒检查一次连接状态
    setInterval(() => {
      this.checkAndFixConnection()
    }, 30000)

    // 立即检查一次
    this.checkAndFixConnection()
  }

  /**
   * 手动触发重连
   */
  async forceReconnect() {
    console.log('🔧 手动触发WebSocket重连...')
    this.reconnectAttempts = 0
    await this.reconnect()
  }
}

// 创建全局实例
const reconnector = new WebSocketReconnector()

// 导出实例和方法
export default reconnector

// 挂载到全局对象，方便调试
if (typeof window !== 'undefined') {
  window.wsReconnector = reconnector
  
  // 提供快捷方法
  window.fixWebSocket = () => reconnector.forceReconnect()
  window.checkWebSocket = () => reconnector.checkAndFixConnection()
}
