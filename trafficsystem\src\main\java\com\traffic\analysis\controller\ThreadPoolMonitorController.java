package com.traffic.analysis.controller;

import com.traffic.analysis.config.ThreadPoolMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 线程池监控控制器
 */
@RestController
@RequestMapping("/api/monitor")
public class ThreadPoolMonitorController {

    @Autowired
    private ThreadPoolMonitor threadPoolMonitor;

    /**
     * 获取所有线程池状态
     */
    @GetMapping("/thread-pools")
    public ResponseEntity<Map<String, Object>> getAllThreadPoolStatus() {
        Map<String, Object> status = threadPoolMonitor.getAllThreadPoolStatus();
        return ResponseEntity.ok(status);
    }

    /**
     * 获取四方向分析执行器状态
     */
    @GetMapping("/four-way-executor")
    public ResponseEntity<Map<String, Object>> getFourWayAnalysisExecutorStatus() {
        Map<String, Object> status = threadPoolMonitor.getFourWayAnalysisExecutorStatus();
        return ResponseEntity.ok(status);
    }

    /**
     * 检查四方向分析执行器是否可用
     */
    @GetMapping("/four-way-executor/available")
    public ResponseEntity<Map<String, Object>> isFourWayAnalysisExecutorAvailable() {
        boolean available = threadPoolMonitor.isFourWayAnalysisExecutorAvailable();
        Map<String, Object> result = Map.of(
            "available", available,
            "status", threadPoolMonitor.getFourWayAnalysisExecutorStatus()
        );
        return ResponseEntity.ok(result);
    }

    /**
     * 触发线程池状态日志记录
     */
    @GetMapping("/log-status")
    public ResponseEntity<String> logThreadPoolStatus() {
        threadPoolMonitor.logThreadPoolStatus();
        return ResponseEntity.ok("线程池状态已记录到日志");
    }
}
