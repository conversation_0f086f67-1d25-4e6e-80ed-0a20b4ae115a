{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, withK<PERSON>s as _withKeys, createBlock as _createBlock, Fragment as _Fragment, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"video-result-container\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"error-container\"\n};\nconst _hoisted_3 = {\n  class: \"video-info-card\"\n};\nconst _hoisted_4 = {\n  class: \"video-name-container\"\n};\nconst _hoisted_5 = {\n  key: 0\n};\nconst _hoisted_6 = {\n  class: \"summary-tab\"\n};\nconst _hoisted_7 = {\n  class: \"video-tab\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"video-mode-selector\"\n};\nconst _hoisted_9 = {\n  key: 1,\n  class: \"realtime-preview-container\"\n};\nconst _hoisted_10 = {\n  key: 0\n};\nconst _hoisted_11 = {\n  key: 1,\n  class: \"realtime-completed\"\n};\nconst _hoisted_12 = {\n  key: 2,\n  class: \"realtime-not-available\"\n};\nconst _hoisted_13 = {\n  class: \"complete-video-container\"\n};\nconst _hoisted_14 = {\n  key: 1,\n  class: \"video-not-available\"\n};\nconst _hoisted_15 = {\n  class: \"report-tab\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_edit = _resolveComponent(\"edit\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_check = _resolveComponent(\"check\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_VehicleStatsCard = _resolveComponent(\"VehicleStatsCard\");\n  const _component_VideoAnalyticsPanel = _resolveComponent(\"VideoAnalyticsPanel\");\n  const _component_el_tab_pane = _resolveComponent(\"el-tab-pane\");\n  const _component_el_radio_button = _resolveComponent(\"el-radio-button\");\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  const _component_RealTimeFrameViewer = _resolveComponent(\"RealTimeFrameViewer\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_VideoPlayerPanel = _resolveComponent(\"VideoPlayerPanel\");\n  const _component_VideoReportPanel = _resolveComponent(\"VideoReportPanel\");\n  const _component_el_tabs = _resolveComponent(\"el-tabs\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    class: \"page-header\"\n  }, [_createElementVNode(\"h2\", null, \"视频分析结果\"), _createElementVNode(\"p\", {\n    class: \"sub-title\"\n  }, \"查看交通视频分析详情\")], -1 /* HOISTED */)), _withDirectives((_openBlock(), _createBlock(_component_el_card, null, {\n    default: _withCtx(() => [$setup.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_empty, {\n      description: \"获取结果失败\",\n      \"image-size\": 120\n    }, {\n      description: _withCtx(() => [_createElementVNode(\"p\", null, _toDisplayString($setup.error), 1 /* TEXT */)]),\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/video-upload'))\n      }, {\n        default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"返回上传\")])),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])) : $setup.result ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createElementVNode(\"div\", _hoisted_3, [_cache[5] || (_cache[5] = _createElementVNode(\"h3\", null, \"视频信息\", -1 /* HOISTED */)), _createVNode(_component_el_descriptions, {\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"文件名\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [!$setup.editing ? (_openBlock(), _createElementBlock(\"span\", _hoisted_5, _toDisplayString($setup.result.video_info?.filename || $setup.result.video_filename), 1 /* TEXT */)) : (_openBlock(), _createBlock(_component_el_input, {\n          key: 1,\n          modelValue: $setup.editName,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.editName = $event),\n          size: \"small\",\n          onKeyup: _withKeys($setup.confirmRename, [\"enter\"])\n        }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])), !$setup.editing ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 2,\n          type: \"primary\",\n          size: \"small\",\n          circle: \"\",\n          onClick: $setup.startRename,\n          class: \"rename-button\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_edit)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\"])) : (_openBlock(), _createBlock(_component_el_button, {\n          key: 3,\n          type: \"success\",\n          size: \"small\",\n          circle: \"\",\n          onClick: $setup.confirmRename,\n          loading: $setup.renaming\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_check)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"onClick\", \"loading\"]))])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"方向\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.result.direction === 'horizontal' ? '横向 (东西方向)' : '纵向 (南北方向)'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"视频时长\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.result.video_info?.duration_seconds ? (Number($setup.result.video_info.duration_seconds.toFixed(1)) % 1 === 0 ? Math.floor($setup.result.video_info.duration_seconds) : $setup.result.video_info.duration_seconds.toFixed(1)) + ' 秒' : '计算中...'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"处理时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.result.processing_time ? $setup.result.processing_time.toFixed(2) + ' 秒' : '数据不可用'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"分析模式\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.result.mode === 'intersection' ? '十字路口分析' : '单向分析'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"车辆统计\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(\" 总计: \" + _toDisplayString($setup.calculateTotalVehicles()) + \" 辆 \", 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]), _createVNode(_component_el_tabs, {\n      modelValue: $setup.activeTab,\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.activeTab = $event),\n      class: \"result-tabs\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_tab_pane, {\n        label: \"分析摘要\",\n        name: \"summary\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createCommentVNode(\" 添加车辆类型统计卡片 \"), $setup.result && $setup.result.vehicle_type_stats ? (_openBlock(), _createBlock(_component_VehicleStatsCard, {\n          key: 0,\n          vehicleTypeStats: $setup.result.vehicle_type_stats\n        }, null, 8 /* PROPS */, [\"vehicleTypeStats\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 视频分析图表组件 \"), _createVNode(_component_VideoAnalyticsPanel, {\n          result: $setup.result\n        }, null, 8 /* PROPS */, [\"result\"])])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"分析视频\",\n        name: \"video\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createCommentVNode(\" 视频模式切换 \"), $setup.result.mode === 'intersection' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createVNode(_component_el_radio_group, {\n          modelValue: $setup.videoMode,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.videoMode = $event),\n          class: \"mode-radio-group\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_radio_button, {\n            label: \"realtime\"\n          }, {\n            default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"实时预览\")])),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_radio_button, {\n            label: \"complete\"\n          }, {\n            default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"完整视频\")])),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 实时预览模式 \"), $setup.videoMode === 'realtime' && $setup.result.mode === 'intersection' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [$setup.result.status === 'processing' || $setup.result.status === 'queued' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createVNode(_component_RealTimeFrameViewer, {\n          ref: \"frameViewer\",\n          \"task-id\": $setup.result.taskId || $setup.resultId,\n          \"auto-start\": true,\n          \"max-buffer-frames\": 30,\n          onFrameReceived: $setup.handleFrameReceived,\n          onPlaybackStateChange: $setup.handlePlaybackStateChange\n        }, null, 8 /* PROPS */, [\"task-id\", \"onFrameReceived\", \"onPlaybackStateChange\"])])) : $setup.result.status === 'completed' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createVNode(_component_el_alert, {\n          title: \"实时预览已结束\",\n          type: \"info\",\n          description: \"视频分析已完成，请切换到完整视频查看最终结果\",\n          \"show-icon\": \"\",\n          closable: false\n        })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createVNode(_component_el_empty, {\n          description: \"实时预览不可用\"\n        }, {\n          description: _withCtx(() => [_createElementVNode(\"p\", null, \"当前状态: \" + _toDisplayString($setup.getStatusText($setup.result.status)), 1 /* TEXT */), _cache[8] || (_cache[8] = _createElementVNode(\"p\", null, \"实时预览仅在视频分析过程中可用\", -1 /* HOISTED */))]),\n          _: 1 /* STABLE */\n        })]))])) : (_openBlock(), _createElementBlock(_Fragment, {\n          key: 2\n        }, [_createCommentVNode(\" 完整视频模式 \"), _createElementVNode(\"div\", _hoisted_13, [$setup.result.status === 'completed' && $setup.result.resultPath ? (_openBlock(), _createBlock(_component_VideoPlayerPanel, {\n          key: 0,\n          \"video-path\": $setup.getVideoUrl($setup.result.resultPath || $setup.result.result_path),\n          status: $setup.result.status,\n          progress: $setup.result.progress || 0,\n          onScreenshot: $setup.handleScreenshot\n        }, null, 8 /* PROPS */, [\"video-path\", \"status\", \"progress\", \"onScreenshot\"])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createVNode(_component_el_empty, {\n          description: \"视频分析未完成或视频不可用\"\n        }, {\n          description: _withCtx(() => [_createElementVNode(\"p\", null, \"当前状态: \" + _toDisplayString($setup.getStatusText($setup.result.status)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        })]))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_tab_pane, {\n        label: \"优化报告\",\n        name: \"report\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_15, [_createCommentVNode(\" 视频报告组件 \"), _createVNode(_component_VideoReportPanel, {\n          result: $setup.result,\n          \"report-url\": $setup.result.reportUrl || $setup.result.report_url || '',\n          \"video-path\": $setup.result.video_info?.filename || ''\n        }, null, 8 /* PROPS */, [\"result\", \"report-url\", \"video-path\"])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })), [[_directive_loading, $setup.loading]])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createBlock", "_component_el_card", "default", "_withCtx", "$setup", "error", "_hoisted_2", "_createVNode", "_component_el_empty", "description", "_toDisplayString", "_component_el_button", "type", "onClick", "_cache", "$event", "_ctx", "$router", "push", "_createTextVNode", "_", "result", "_Fragment", "_hoisted_3", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "label", "_hoisted_4", "editing", "_hoisted_5", "video_info", "filename", "video_filename", "_component_el_input", "modelValue", "editName", "size", "onKeyup", "_with<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "circle", "startRename", "_component_el_icon", "_component_edit", "loading", "renaming", "_component_check", "direction", "duration_seconds", "Number", "toFixed", "Math", "floor", "processing_time", "mode", "calculateTotalVehicles", "_component_el_tabs", "activeTab", "_component_el_tab_pane", "name", "_hoisted_6", "_createCommentVNode", "vehicle_type_stats", "_component_VehicleStatsCard", "vehicleTypeStats", "_component_VideoAnalyticsPanel", "_hoisted_7", "_hoisted_8", "_component_el_radio_group", "videoMode", "_component_el_radio_button", "_hoisted_9", "status", "_hoisted_10", "_component_RealTimeFrameViewer", "ref", "taskId", "resultId", "onFrameReceived", "handleFrameReceived", "onPlaybackStateChange", "handlePlaybackStateChange", "_hoisted_11", "_component_el_alert", "title", "closable", "_hoisted_12", "getStatusText", "_hoisted_13", "resultPath", "_component_VideoPlayerPanel", "getVideoUrl", "result_path", "progress", "onScreenshot", "handleScreenshot", "_hoisted_14", "_hoisted_15", "_component_VideoReportPanel", "reportUrl", "report_url"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\VideoResult.vue"], "sourcesContent": ["<template>\n  <div class=\"video-result-container\">\n    <div class=\"page-header\">\n      <h2>视频分析结果</h2>\n      <p class=\"sub-title\">查看交通视频分析详情</p>\n    </div>\n\n    <el-card v-loading=\"loading\">\n      <div v-if=\"error\" class=\"error-container\">\n        <el-empty \n          description=\"获取结果失败\" \n          :image-size=\"120\"\n        >\n          <template #description>\n            <p>{{ error }}</p>\n          </template>\n          <el-button type=\"primary\" @click=\"$router.push('/video-upload')\">返回上传</el-button>\n        </el-empty>\n      </div>\n\n      <template v-else-if=\"result\">\n        <div class=\"video-info-card\">\n          <h3>视频信息</h3>\n          <el-descriptions :column=\"2\" border>\n            <el-descriptions-item label=\"文件名\">\n              <div class=\"video-name-container\">\n                <span v-if=\"!editing\">{{ result.video_info?.filename || result.video_filename }}</span>\n                <el-input \n                  v-else \n                  v-model=\"editName\" \n                  size=\"small\" \n                  @keyup.enter=\"confirmRename\"\n                />\n                <el-button \n                  v-if=\"!editing\"\n                  type=\"primary\" \n                  size=\"small\" \n                  circle \n                  @click=\"startRename\"\n                  class=\"rename-button\"\n                >\n                  <el-icon><edit /></el-icon>\n                </el-button>\n                <el-button \n                  v-else \n                  type=\"success\" \n                  size=\"small\" \n                  circle\n                  @click=\"confirmRename\"\n                  :loading=\"renaming\"\n                >\n                  <el-icon><check /></el-icon>\n                </el-button>\n              </div>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"方向\">\n              {{ result.direction === 'horizontal' ? '横向 (东西方向)' : '纵向 (南北方向)' }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"视频时长\">\n              {{ result.video_info?.duration_seconds ? (Number(result.video_info.duration_seconds.toFixed(1)) % 1 === 0 ? Math.floor(result.video_info.duration_seconds) : result.video_info.duration_seconds.toFixed(1)) + ' 秒' : '计算中...' }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"处理时间\">\n              {{ result.processing_time ? result.processing_time.toFixed(2) + ' 秒' : '数据不可用' }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"分析模式\">\n              {{ result.mode === 'intersection' ? '十字路口分析' : '单向分析' }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"车辆统计\">\n              总计: {{ calculateTotalVehicles() }} 辆\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n\n        <el-tabs v-model=\"activeTab\" class=\"result-tabs\">\n          <el-tab-pane label=\"分析摘要\" name=\"summary\">\n            <div class=\"summary-tab\">\n              <!-- 添加车辆类型统计卡片 -->\n              <VehicleStatsCard \n                v-if=\"result && result.vehicle_type_stats\" \n                :vehicleTypeStats=\"result.vehicle_type_stats\" \n              />\n              \n              <!-- 视频分析图表组件 -->\n              <VideoAnalyticsPanel :result=\"result\" />\n            </div>\n          </el-tab-pane>\n          \n          <el-tab-pane label=\"分析视频\" name=\"video\">\n            <div class=\"video-tab\">\n              <!-- 视频模式切换 -->\n              <div class=\"video-mode-selector\" v-if=\"result.mode === 'intersection'\">\n                <el-radio-group v-model=\"videoMode\" class=\"mode-radio-group\">\n                  <el-radio-button label=\"realtime\">实时预览</el-radio-button>\n                  <el-radio-button label=\"complete\">完整视频</el-radio-button>\n                </el-radio-group>\n              </div>\n\n              <!-- 实时预览模式 -->\n              <div v-if=\"videoMode === 'realtime' && result.mode === 'intersection'\" class=\"realtime-preview-container\">\n                <div v-if=\"result.status === 'processing' || result.status === 'queued'\">\n                  <RealTimeFrameViewer\n                    ref=\"frameViewer\"\n                    :task-id=\"result.taskId || resultId\"\n                    :auto-start=\"true\"\n                    :max-buffer-frames=\"30\"\n                    @frame-received=\"handleFrameReceived\"\n                    @playback-state-change=\"handlePlaybackStateChange\"\n                  />\n                </div>\n                <div v-else-if=\"result.status === 'completed'\" class=\"realtime-completed\">\n                  <el-alert\n                    title=\"实时预览已结束\"\n                    type=\"info\"\n                    description=\"视频分析已完成，请切换到完整视频查看最终结果\"\n                    show-icon\n                    :closable=\"false\"\n                  />\n                </div>\n                <div v-else class=\"realtime-not-available\">\n                  <el-empty description=\"实时预览不可用\">\n                    <template #description>\n                      <p>当前状态: {{ getStatusText(result.status) }}</p>\n                      <p>实时预览仅在视频分析过程中可用</p>\n                    </template>\n                  </el-empty>\n                </div>\n              </div>\n\n              <!-- 完整视频模式 -->\n              <div v-else class=\"complete-video-container\">\n                <VideoPlayerPanel\n                  v-if=\"result.status === 'completed' && result.resultPath\"\n                  :video-path=\"getVideoUrl(result.resultPath || result.result_path)\"\n                  :status=\"result.status\"\n                  :progress=\"result.progress || 0\"\n                  @screenshot=\"handleScreenshot\"\n                />\n                <div v-else class=\"video-not-available\">\n                  <el-empty description=\"视频分析未完成或视频不可用\">\n                    <template #description>\n                      <p>当前状态: {{ getStatusText(result.status) }}</p>\n                    </template>\n                  </el-empty>\n                </div>\n              </div>\n            </div>\n          </el-tab-pane>\n          \n          <el-tab-pane label=\"优化报告\" name=\"report\">\n            <div class=\"report-tab\">\n              <!-- 视频报告组件 -->\n              <VideoReportPanel \n                :result=\"result\" \n                :report-url=\"result.reportUrl || result.report_url || ''\" \n                :video-path=\"result.video_info?.filename || ''\"\n              />\n            </div>\n          </el-tab-pane>\n        </el-tabs>\n      </template>\n    </el-card>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, onBeforeMount, onUnmounted, watch, nextTick } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { getVideoResult, updateVideoName, saveVideoProcessingTime } from '@/api/video'\nimport { refreshAuthToken } from '@/utils/http-common'\nimport { ElMessage } from 'element-plus'\nimport { \n  Edit,\n  Check\n} from '@element-plus/icons-vue'\nimport { STOMP_TOPIC_PREFIX } from '@/config'\nimport stompService from '@/utils/stomp-service'\n\n// 导入组件\nimport VideoPlayerPanel from '@/components/video/VideoPlayerPanel.vue'\nimport VideoAnalyticsPanel from '@/components/video/VideoAnalyticsPanel.vue'\nimport VideoReportPanel from '@/components/video/VideoReportPanel.vue'\nimport VehicleStatsCard from '@/components/video/VehicleStatsCard.vue'\nimport RealTimeFrameViewer from '@/components/video/RealTimeFrameViewer.vue'\n\nexport default {\n  name: 'VideoResult',\n  components: {\n    VideoPlayerPanel,\n    VideoAnalyticsPanel,\n    VideoReportPanel,\n    VehicleStatsCard,\n    RealTimeFrameViewer,\n    Edit,\n    Check\n  },\n  props: {\n    idType: {\n      type: String,\n      default: null\n    }\n  },\n  setup(props) {\n    const route = useRoute()\n    const resultId = ref(route.params.id)\n    const loading = ref(true)\n    const error = ref(null)\n    const result = ref(null)\n    const activeTab = ref('summary')\n    const editing = ref(false)\n    const editName = ref('')\n    const renaming = ref(false)\n\n    // 视频模式相关状态\n    const videoMode = ref('complete') // 'realtime' 或 'complete'\n    const frameViewer = ref(null)\n    const frameSubscription = ref(null)\n    \n    // 直接修改表格样式的函数\n    const forceTableStyles = () => {\n      // 给样式一些时间应用\n      setTimeout(() => {\n        // 获取所有表格标签单元格并应用样式\n        const labelCells = document.querySelectorAll('.el-descriptions__cell.el-descriptions__label');\n        labelCells.forEach(cell => {\n          cell.setAttribute('style', 'background-color: rgba(26, 32, 50, 0.8) !important; color: #e5e7eb !important; font-weight: 600 !important; border-color: rgba(255, 255, 255, 0.1) !important;');\n        });\n        \n        // 获取所有表格内容单元格并应用样式\n        const contentCells = document.querySelectorAll('.el-descriptions__cell.el-descriptions__content');\n        contentCells.forEach(cell => {\n          cell.setAttribute('style', 'background-color: rgba(31, 41, 55, 0.8) !important; color: #e5e7eb !important; border-color: rgba(255, 255, 255, 0.1) !important;');\n        });\n        \n\n      }, 500);\n    };\n    \n    // WebSocket相关\n    let reconnectTimer = null\n    let wsSubscription = null\n    let pollInterval = null // 轮询定时器\n    \n    // 添加Blob URL管理\n    const blobUrls = new Set() // 存储创建的所有Blob URLs\n    \n    // 添加最大重连次数计数器\n    const maxReconnectAttempts = ref(5); // 最大重连次数\n    const reconnectAttempts = ref(0); // 当前重连次数\n    \n    // 状态文本映射\n    const getStatusText = (status) => {\n      if (status === 'completed') return '已完成';\n      if (status === 'processing') return '处理中';\n      if (status === 'queued') return '排队中';\n      if (status === 'failed') return '失败';\n      return '未知';\n    };\n\n    // 清除所有状态数据\n    const resetState = () => {\n      // 清理WebSocket连接\n      cleanupWebSocket();\n      \n      // 清理轮询定时器\n      clearPolling();\n      \n      // 清理Blob URLs\n      clearBlobUrls();\n      \n      // 重置状态\n      result.value = null;\n      error.value = null;\n    };\n    \n    // 清理WebSocket资源\n    const cleanupWebSocket = () => {\n      if (wsSubscription) {\n        try {\n          // 使用stompService取消订阅\n          const taskId = result.value?.taskId || resultId.value;\n          if (taskId) {\n            stompService.unsubscribe(`task/${taskId}`);\n        }\n        wsSubscription = null;\n      } catch (err) {\n          console.warn('取消WebSocket订阅失败:', err);\n        }\n      }\n      \n      // 清除重连定时器\n      if (reconnectTimer) {\n        clearTimeout(reconnectTimer);\n        reconnectTimer = null;\n      }\n      \n      // 重置重连计数\n      reconnectAttempts.value = 0;\n    };\n    \n    // 检查STOMP连接状态\n    const checkStompConnection = () => {\n      // 检查STOMP服务是否已连接\n      if (!stompService.connected) {\n\n        \n        // 尝试初始化STOMP连接\n        stompService.init().catch(error => {\n          console.error('初始化STOMP连接失败:', error);\n          // 启动轮询作为备份\n          startPolling();\n        });\n      }\n    };\n    \n    // 清理轮询定时器\n    const clearPolling = () => {\n      if (pollInterval) {\n        clearInterval(pollInterval);\n        pollInterval = null;\n      }\n    };\n    \n    // 清理Blob URLs\n    const clearBlobUrls = () => {\n        blobUrls.forEach(url => {\n        try {\n          URL.revokeObjectURL(url);\n      } catch (err) {\n\n        }\n      });\n      blobUrls.clear();\n    };\n    \n    // 清理所有资源\n    const cleanupResources = () => {\n      // 清理WebSocket\n      cleanupWebSocket();\n      \n      // 清理轮询定时器\n      clearPolling();\n      \n      // 清理Blob URLs\n      clearBlobUrls();\n      \n      // 移除事件监听器\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\n    };\n    \n    // 启动轮询机制，作为WebSocket的备份\n    const startPolling = () => {\n      // 先清除可能存在的轮询\n      clearPolling();\n      \n      // 如果视频已完成处理，不需要轮询\n      if (result.value && result.value.status === 'completed') {\n\n        return;\n      }\n      \n\n      \n      // 设置轮询间隔（每10秒）\n      pollInterval = setInterval(() => {\n\n        fetchResult(false); // 传入false表示这是轮询调用，减少日志输出\n      }, 10000);\n    };\n    \n    // 初始化WebSocket连接\n    const initWebSocket = async (taskId) => {\n      if (!taskId) {\n        console.warn('任务ID为空，无法初始化WebSocket');\n        return;\n      }\n      \n      // 先清理现有连接\n      cleanupWebSocket();\n      \n      try {\n        console.log(`初始化视频结果WebSocket连接，订阅任务: ${taskId}`);\n        \n        // 检查STOMP连接状态，确保连接已建立\n        if (!stompService.connected) {\n          console.log('STOMP连接未建立，尝试初始化连接...');\n          try {\n            await stompService.init();\n            console.log('STOMP连接已成功初始化');\n          } catch (connError) {\n            console.error('STOMP连接初始化失败:', connError);\n            // 连接失败，启动轮询作为备份\n            startPolling();\n            return; // 退出订阅尝试\n          }\n        }\n            \n        // 使用STOMP服务初始化并订阅\n        const topic = `task/${taskId}`;\n        console.log(`订阅主题: ${STOMP_TOPIC_PREFIX}/${topic}`);\n            \n        // 订阅主题\n        stompService.subscribe(topic, (data) => {\n          handleWebSocketMessage({ body: JSON.stringify(data) });\n        }).then(subscription => {\n          console.log('WebSocket订阅成功');\n          wsSubscription = subscription;\n          reconnectAttempts.value = 0; // 重置重连计数\n          \n          // WebSocket连接成功后，仍然启动轮询作为备份\n          startPolling();\n        }).catch(error => {\n          console.error('WebSocket订阅失败:', error);\n          // 失败后尝试重连，但要检查重连次数\n          reconnectWebSocket(taskId);\n          \n          // 无论如何都启动轮询作为备份\n          startPolling();\n        });\n      } catch (err) {\n        console.error('初始化WebSocket失败:', err);\n        reconnectWebSocket(taskId);\n        \n        // 启动轮询作为备份\n        startPolling();\n      }\n    };\n    \n    // 重新连接WebSocket\n    const reconnectWebSocket = (taskId) => {\n      // 检查重连次数\n      if (reconnectAttempts.value >= maxReconnectAttempts.value) {\n        console.warn(`已达到最大重连次数(${maxReconnectAttempts.value})，停止重连WebSocket`);\n        return;\n      }\n      \n      // 增加重连计数\n      reconnectAttempts.value++;\n      \n      // 清除现有定时器\n      if (reconnectTimer) {\n        clearTimeout(reconnectTimer);\n      }\n      \n      console.log(`WebSocket重连尝试 ${reconnectAttempts.value}/${maxReconnectAttempts.value}`);\n      \n      // 设置重连定时器\n      reconnectTimer = setTimeout(() => {\n        console.log('尝试重新连接WebSocket...');\n        initWebSocket(taskId);\n      }, 5000); // 5秒后重试\n    };\n    \n    // 处理WebSocket消息\n    const handleWebSocketMessage = (message) => {\n      try {\n        const data = JSON.parse(message.body);\n        console.log('收到WebSocket消息:', data);\n        \n        if (data.status) {\n          // 更新状态信息\n          if (result.value) {\n            result.value.status = data.status;\n            result.value.progress = data.progress || result.value.progress;\n            \n            // 如果任务完成，刷新完整数据\n            if (data.status === 'completed') {\n              ElMessage.success('视频分析已完成');\n              fetchResult();\n            } else if (data.status === 'failed') {\n              ElMessage.error('视频分析失败: ' + (data.error || '未知错误'));\n              result.value.error = data.error;\n            }\n          }\n        }\n      } catch (err) {\n        console.error('处理WebSocket消息失败:', err);\n      }\n    };\n    \n    // 页面可见性变化处理\n    const handleVisibilityChange = () => {\n      if (!document.hidden && activeTab.value === 'summary') {\n        // 当页面变为可见且当前在summary标签页时\n        if (result.value) {\n          console.log('页面变为可见，刷新数据');\n          fetchResult();\n        }\n      }\n    };\n    \n    // 截图处理函数\n    const handleScreenshot = (dataURL) => {\n      // 创建下载链接\n      const link = document.createElement('a');\n      link.download = `视频截图_${new Date().getTime()}.png`;\n      link.href = dataURL;\n      \n      // 添加到DOM并触发下载\n      document.body.appendChild(link);\n      link.click();\n      \n      // 清理DOM元素\n      setTimeout(() => {\n        if (document.body.contains(link)) {\n          document.body.removeChild(link);\n        }\n      }, 100);\n      \n      ElMessage.success('截图已保存');\n    };\n    \n    // 获取结果数据\n    const fetchResult = async (showLoading = true) => {\n      if (!resultId.value) {\n        error.value = '未提供结果ID，无法获取分析结果';\n        loading.value = false;\n        return;\n      }\n      \n      error.value = null;\n      \n      // 仅当showLoading为true时显示加载状态\n      if (showLoading) {\n      loading.value = true;\n      }\n      \n      try {\n\n        \n        // 检测ID类型\n        const isMongoObjectId = resultId.value && /^[0-9a-f]{24}$/i.test(resultId.value);\n        const isUuid = resultId.value && resultId.value.includes('-');\n        \n\n        \n        // 添加用户信息到请求URL参数\n        let userInfo = null;\n        try {\n          const userStr = localStorage.getItem('user');\n          if (userStr) {\n            userInfo = JSON.parse(userStr);\n          }\n        } catch (e) {\n\n        }\n        \n        // 构造带有用户信息的请求选项\n        const requestOptions = {\n          retryWithUuid: isMongoObjectId // 如果是MongoDB ID，允许API尝试UUID查询\n        };\n        \n        if (userInfo) {\n          requestOptions.params = {\n            userId: userInfo.id || '',\n            username: userInfo.username || '',\n            role: userInfo.role || ''\n          };\n        }\n        \n        // 如果路由参数中有type，添加到请求选项\n        if (route.params.type) {\n          requestOptions.idType = route.params.type;\n        }\n        \n        // 使用props中的idType参数(来自路由定义中的props)\n        if (props.idType) {\n          console.log(`使用props中的idType: ${props.idType}`);\n          requestOptions.idType = props.idType;\n        }\n        \n        // 添加额外的调试信息\n        console.log('发起API请求，选项:', requestOptions);\n        \n        const data = await getVideoResult(resultId.value, requestOptions);\n        \n        if (!data) {\n          console.error('getVideoResult返回空值');\n          error.value = '获取分析结果失败: API返回空值';\n          return;\n        }\n        \n        if (data && data.data) {\n          console.log('获取分析结果成功:', data.data);\n          console.log('VideoResult - 车辆类型统计数据:', data.data.vehicle_type_stats);\n          result.value = data.data;\n          \n          // 初始化编辑名称\n          editName.value = \n            result.value.video_info?.filename || \n            result.value.video_filename || \n            '未命名视频';\n            \n          // 检查视频是否已完成处理但没有记录处理时间\n          if (result.value.status === 'completed' && (!result.value.processing_time || result.value.processing_time <= 0)) {\n            console.log('检测到视频已完成但缺少处理时间记录，尝试更新处理时间');\n            \n            // 使用任务ID或结果ID\n            const taskId = result.value.taskId || resultId.value;\n            \n            try {\n              // 如果有完成时间戳但没有开始时间戳，计算估计处理时间\n              const estimatedTime = 60; // 默认估计60秒\n              await saveVideoProcessingTime(taskId, estimatedTime);\n              console.log('已更新缺失的视频处理时间为估计值:', estimatedTime);\n              result.value.processing_time = estimatedTime;\n              ElMessage.info('已更新视频处理时间');\n            } catch (err) {\n              console.warn('更新处理时间失败:', err);\n            }\n          }\n            \n          // 如果视频正在处理中，初始化WebSocket连接实时接收处理状态\n          if (result.value.status === 'processing' || result.value.status === 'queued') {\n            const taskId = result.value.taskId || resultId.value;\n            if (taskId) {\n              console.log('视频正在处理中，初始化WebSocket连接以获取实时状态...');\n              initWebSocket(taskId);\n            }\n          }\n            \n          // 应用样式\n          nextTick(() => {\n            forceTableStyles();\n          });\n        } else {\n          console.warn('获取分析结果响应格式不正确:', data);\n          error.value = '响应格式不正确，无法显示结果';\n        }\n      } catch (err) {\n\n        \n        // 如果是认证错误，尝试刷新令牌\n        if (err.authError || err.status === 'auth_error') {\n          try {\n            if (typeof refreshAuthToken === 'function') {\n              const refreshed = await refreshAuthToken();\n              if (refreshed) {\n\n                return fetchResult();  // 递归调用自身重试\n              }\n            } else {\n\n            }\n          } catch (refreshErr) {\n\n          }\n          \n          // 刷新失败时显示认证错误\n          error.value = '认证失败或会话已过期，请重新登录';\n          ElMessage({\n            message: '认证失败或会话已过期，请重新登录',\n            type: 'error',\n            duration: 5000\n          });\n        } else {\n          // 如果是轮询请求，错误处理更加宽容\n          if (showLoading) {\n          error.value = err.message || '获取分析结果失败';\n          } else {\n\n          }\n        }\n      } finally {\n        if (showLoading) {\n        loading.value = false;\n        }\n      }\n    };\n    \n    // 获取视频URL\n    const getVideoUrl = (path) => {\n      if (!path) return ''\n      \n      // 检查是否为Base64数据\n      if (isBase64(path)) {\n        return path\n      }\n      \n      // 检查是否为GridFS ID（24位十六进制字符串）\n      if (/^[0-9a-f]{24}$/i.test(path)) {\n        return `/api/media/video/${path}`;\n      }\n      \n      // 检查是否为Blob URL\n      if (path.startsWith('blob:')) {\n        // 确保这个Blob URL被跟踪\n        blobUrls.add(path) // 只需追踪，不需要重新创建\n        return path\n      }\n      \n      // 处理视频URL - 支持绝对路径和相对路径\n      if (path.startsWith('/')) {\n        // 已经是以/开头的路径，直接返回\n        return path;\n      } else if (path.includes(':\\\\') || path.includes(':/')) {\n        // 处理Windows绝对路径\n        const filename = path.split('\\\\').pop().split('/').pop();\n        // 如果文件名看起来像MongoDB ID，使用media API\n        if (/^[0-9a-f]{24}$/i.test(filename)) {\n          return `/api/media/video/${filename}`;\n        }\n        return `/api/static/videos/${filename}`;\n      }\n      \n      // 处理其他格式的路径\n      const filename = path.split('/').pop();\n      // 如果文件名看起来像MongoDB ID，使用media API\n      if (/^[0-9a-f]{24}$/i.test(filename)) {\n        return `/api/media/video/${filename}`;\n      }\n      return `/api/static/videos/${filename}`;\n    }\n    \n    // 获取图像URL\n    const getImageUrl = (imageId) => {\n      // 检查是否为Base64数据\n      if (imageId && imageId.startsWith('data:image')) {\n        return imageId;\n      }\n      \n      // 检查是否为GridFS ID（24位十六进制字符串）\n      if (imageId && /^[0-9a-f]{24}$/i.test(imageId)) {\n        return `/api/media/image/${imageId}`;\n      }\n      \n      // 兼容旧版URL\n      return imageId;\n    }\n    \n    // 检查URL是否为Base64数据\n    const isBase64 = (url) => {\n      return url && (url.startsWith('data:image') || url.startsWith('data:video'));\n    }\n    \n    // 开始重命名\n    const startRename = () => {\n      editing.value = true;\n      editName.value = result.value?.video_info?.filename || '';\n    };\n    \n    // 确认重命名\n    const confirmRename = async () => {\n      if (!editName.value.trim()) {\n        ElMessage.warning('视频名称不能为空');\n        return;\n      }\n      \n      renaming.value = true;\n      try {\n        // 获取任务ID\n        const taskId = result.value?.taskId || resultId.value;\n        \n        if (!taskId) {\n          throw new Error('任务ID不存在');\n        }\n        \n        const response = await updateVideoName(taskId, editName.value);\n        \n        if (response && response.data && response.data.success) {\n          // 更新本地数据\n          result.value.video_info.filename = editName.value;\n          editing.value = false;\n          ElMessage.success('视频重命名成功');\n        } else {\n          throw new Error(response?.data?.message || '重命名失败');\n        }\n      } catch (err) {\n        ElMessage.error('视频重命名失败: ' + (err.message || '未知错误'));\n        console.error('重命名错误:', err);\n      } finally {\n        renaming.value = false;\n          }\n    };\n    \n    // 监听result变化，在获取到taskId后初始化WebSocket\n    watch(() => result.value?.taskId, (newTaskId) => {\n      if (newTaskId) {\n\n        initWebSocket(newTaskId);\n      }\n    });\n    \n    // 计算车辆总数\n    const calculateTotalVehicles = () => {\n      if (!result.value || !result.value.vehicle_type_stats) {\n        return result.value?.vehicle_count || 0;\n      }\n      \n      // 计算所有车辆类型数量之和\n      let total = 0;\n      Object.values(result.value.vehicle_type_stats).forEach(count => {\n        total += count;\n      });\n      \n      // 如果计算结果与vehicle_count不同，使用计算结果\n      return total;\n    };\n    \n    onBeforeMount(() => {\n      // 在挂载前就清除先前的状态\n      resetState();\n      \n      // 移除可能存在的会话存储数据\n      try {\n        sessionStorage.removeItem('videoResultState');\n      } catch (err) {\n\n      }\n    })\n    \n\n\n    onMounted(() => {\n      // 重置状态并获取新数据\n      resetState();\n      fetchResult();\n\n\n\n      // 监听页面可见性变化\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // 应用强制样式\n      forceTableStyles();\n\n      // 检查STOMP连接状态\n      checkStompConnection();\n      \n      // 如果已经有resultId，尝试获取任务ID并初始化WebSocket\n      if (resultId.value) {\n\n        // 使用resultId作为初始taskId进行连接\n        // 在fetchResult后会通过watch更新为正确的taskId\n        setTimeout(() => {\n          if (result.value && result.value.taskId) {\n            initWebSocket(result.value.taskId);\n          } else {\n\n            initWebSocket(resultId.value);\n          }\n        }, 1000);\n      }\n    })\n    \n    // 监听标签页切换，应用样式\n    watch(activeTab, () => {\n        nextTick(() => {\n          forceTableStyles();\n        });\n    });\n    \n    // 监听结果变化，每次更新时重新应用样式\n    watch(result, () => {\n      if (result.value) {\n      nextTick(() => {\n        forceTableStyles();\n      });\n      }\n    });\n    \n    // 实时预览相关方法\n    const handleFrameReceived = (frameData) => {\n      console.log(`接收到帧数据: 帧${frameData.frameNumber}, 车辆数${frameData.detectionCount}`);\n    };\n\n    const handlePlaybackStateChange = (state) => {\n      console.log('播放状态变化:', state);\n    };\n\n    // 初始化实时预览功能\n    const initRealtimePreview = async () => {\n      try {\n        if (result.value && result.value.mode === 'intersection' &&\n            (result.value.status === 'processing' || result.value.status === 'queued')) {\n\n          const taskId = result.value.taskId || resultId.value;\n          if (taskId) {\n            console.log(`为任务 ${taskId} 初始化实时预览功能`);\n\n            // 订阅帧数据更新\n            frameSubscription.value = await stompService.subscribeFrameUpdates(taskId, (frameData) => {\n              if (frameViewer.value && frameViewer.value.addFrameData) {\n                frameViewer.value.addFrameData(frameData);\n              }\n            });\n          }\n        }\n      } catch (error) {\n        console.error('初始化实时预览失败:', error);\n      }\n    };\n\n    // 清理实时预览资源\n    const cleanupRealtimePreview = () => {\n      try {\n        if (frameSubscription.value) {\n          const taskId = result.value?.taskId || resultId.value;\n          if (taskId) {\n            stompService.clearFrameBuffer(taskId);\n          }\n          frameSubscription.value = null;\n        }\n\n        if (frameViewer.value && frameViewer.value.clearFrameData) {\n          frameViewer.value.clearFrameData();\n        }\n      } catch (error) {\n        console.error('清理实时预览资源失败:', error);\n      }\n    };\n\n    // 监听视频模式变化\n    watch(videoMode, (newMode) => {\n      if (newMode === 'realtime') {\n        nextTick(() => {\n          initRealtimePreview();\n        });\n      } else {\n        cleanupRealtimePreview();\n      }\n    });\n\n    // 监听结果状态变化，自动初始化实时预览\n    watch(() => result.value?.status, (newStatus, oldStatus) => {\n      if (newStatus === 'processing' && oldStatus !== 'processing' &&\n          result.value?.mode === 'intersection' && videoMode.value === 'realtime') {\n        nextTick(() => {\n          initRealtimePreview();\n        });\n      } else if (newStatus === 'completed' || newStatus === 'failed') {\n        cleanupRealtimePreview();\n      }\n    });\n\n    onUnmounted(() => {\n      // 清理实时预览资源\n      cleanupRealtimePreview();\n\n      // 使用清理函数释放所有资源\n      cleanupResources();\n    })\n    \n    return {\n      resultId,\n      loading,\n      error,\n      result,\n      activeTab,\n      editing,\n      editName,\n      renaming,\n      startRename,\n      confirmRename,\n      getVideoUrl,\n      getImageUrl,\n      isBase64,\n      getStatusText,\n      handleScreenshot,\n      forceTableStyles,\n      calculateTotalVehicles,\n      // 实时预览相关\n      videoMode,\n      frameViewer,\n      handleFrameReceived,\n      handlePlaybackStateChange\n    }\n  }\n}\n</script>\n\n<style scoped>\n.video-result-container {\n  min-height: 100vh;\n  background-color: #111827;\n  color: #e5e7eb;\n  padding: 2.5rem;\n  width: 100%;\n  max-width: 100%;\n  margin: 0;\n  overflow-x: hidden;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n  text-align: left;\n  max-width: 1400px;\n  margin-left: auto;\n  margin-right: auto;\n}\n\n.page-header h2 {\n  font-size: 2.5rem;\n  font-weight: 800;\n  background: linear-gradient(90deg, #6366f1, #8b5cf6);\n  -webkit-background-clip: text;\n  background-clip: text;\n  color: transparent;\n  margin-bottom: 0.5rem;\n}\n\n.sub-title {\n  color: #d1d5db;\n  font-size: 1.1rem;\n}\n\n.el-card {\n  max-width: 1400px;\n  margin-left: auto;\n  margin-right: auto;\n  margin-bottom: 1.5rem;\n  background: rgba(255, 255, 255, 0.03) !important;\n  border: 1px solid rgba(255, 255, 255, 0.06) !important;\n  border-radius: 1rem !important;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;\n}\n\n:deep(.el-card__header) {\n  background-color: rgba(26, 32, 50, 0.8);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 1rem 1.5rem;\n}\n\n:deep(.el-card__body) {\n  padding: 1.5rem;\n}\n\n.video-info-card {\n  margin-bottom: 2rem;\n}\n\n.video-info-card h3 {\n  color: #e5e7eb;\n  font-weight: 600;\n  margin-bottom: 1rem;\n}\n\n.video-name-container {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.rename-button {\n  margin-left: 10px;\n}\n\n/* 表格样式强制覆盖 */\n:deep(.el-descriptions) {\n  --el-descriptions-item-bordered-label-background: rgba(26, 32, 50, 0.8) !important;\n  background-color: rgba(31, 41, 55, 0.5) !important;\n  border-radius: 8px !important;\n  overflow: hidden !important;\n}\n\n:deep(.el-descriptions .el-descriptions__label),\n:deep(.el-descriptions .el-descriptions__cell.el-descriptions__label),\n:deep(.el-descriptions__label.is-bordered-label),\n:deep(.el-descriptions__cell.el-descriptions__label.is-bordered-label),\n:deep(.el-descriptions__table .el-descriptions__cell.el-descriptions__label),\n:deep(td.el-descriptions__cell.el-descriptions__label) {\n  color: #e5e7eb !important;\n  background-color: rgba(26, 32, 50, 0.8) !important;\n  font-weight: 600 !important;\n  border-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n:deep(.el-descriptions .el-descriptions__content),\n:deep(.el-descriptions .el-descriptions__cell.el-descriptions__content),\n:deep(.el-descriptions__content.is-bordered-content),\n:deep(.el-descriptions__cell.el-descriptions__content.is-bordered-content),\n:deep(.el-descriptions__table .el-descriptions__cell.el-descriptions__content),\n:deep(td.el-descriptions__cell.el-descriptions__content) {\n  color: #e5e7eb !important;\n  background-color: rgba(255, 255, 255, 0.03) !important;\n  border-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n/* 全局覆盖Element Plus的描述列表样式 */\n:deep(.el-descriptions__table) {\n  background-color: transparent !important;\n}\n\n:deep(.el-descriptions__table td) {\n  background-color: rgba(255, 255, 255, 0.03) !important;\n}\n\n:deep(.el-descriptions__table td.el-descriptions__label) {\n  background-color: rgba(26, 32, 50, 0.8) !important;\n}\n\n:deep(.el-tabs__item) {\n  color: #d1d5db;\n  font-size: 1.1rem;\n  padding: 0 20px;\n}\n\n:deep(.el-tabs__item.is-active) {\n  color: #6366f1;\n}\n\n:deep(.el-tabs__active-bar) {\n  background-color: #6366f1;\n}\n\n:deep(.el-tabs__nav-wrap::after) {\n  background-color: rgba(255, 255, 255, 0.08);\n}\n\n.result-tabs {\n  margin-top: 1rem;\n}\n\n.summary-tab,\n.video-tab,\n.report-tab {\n  padding: 1rem 0;\n}\n\n.video-not-available {\n  background: rgba(17, 24, 39, 0.5);\n  border-radius: 8px;\n  padding: 30px;\n  text-align: center;\n  margin-top: 20px;\n}\n\n/* 视频模式选择器样式 */\n.video-mode-selector {\n  margin-bottom: 20px;\n  display: flex;\n  justify-content: center;\n  padding: 16px;\n  background: rgba(31, 41, 55, 0.5);\n  border-radius: 8px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.mode-radio-group {\n  background: rgba(17, 24, 39, 0.8);\n  border-radius: 6px;\n  padding: 4px;\n}\n\n:deep(.mode-radio-group .el-radio-button__inner) {\n  background: transparent;\n  border: none;\n  color: #d1d5db;\n  padding: 8px 16px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n:deep(.mode-radio-group .el-radio-button__original-radio:checked + .el-radio-button__inner) {\n  background: #6366f1;\n  color: white;\n  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);\n}\n\n:deep(.mode-radio-group .el-radio-button__inner:hover) {\n  background: rgba(99, 102, 241, 0.2);\n  color: #e5e7eb;\n}\n\n/* 实时预览容器样式 */\n.realtime-preview-container {\n  background: rgba(17, 24, 39, 0.5);\n  border-radius: 8px;\n  padding: 20px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.realtime-completed {\n  padding: 20px;\n  text-align: center;\n}\n\n.realtime-not-available {\n  padding: 30px;\n  text-align: center;\n}\n\n/* 完整视频容器样式 */\n.complete-video-container {\n  background: rgba(17, 24, 39, 0.3);\n  border-radius: 8px;\n  padding: 16px;\n  border: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n.error-container {\n  padding: 2rem;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n    justify-content: center;\n  min-height: 300px;\n}\n\n:deep(.el-empty__description) {\n  color: #e5e7eb !important;\n}\n\n:deep(.el-empty__image) {\n  opacity: 0.6;\n}\n</style> \n\n<!-- 全局样式覆盖 -->\n<style>\n/* 直接对表格单元格应用样式，不使用scoped确保全局应用 */\n.el-descriptions__table {\n  background-color: transparent !important;\n}\n\n.el-descriptions__table .el-descriptions__cell.el-descriptions__label,\ntd.el-descriptions__cell.el-descriptions__label,\n.el-descriptions__label.is-bordered-label {\n  background-color: rgba(26, 32, 50, 0.8) !important;\n  color: #e5e7eb !important;\n  font-weight: 600 !important;\n  border-color: rgba(255, 255, 255, 0.1) !important;\n}\n\n.el-descriptions__table .el-descriptions__cell.el-descriptions__content,\ntd.el-descriptions__cell.el-descriptions__content,\n.el-descriptions__content.is-bordered-content {\n  background-color: rgba(255, 255, 255, 0.03) !important;\n  color: #e5e7eb !important;\n  border-color: rgba(255, 255, 255, 0.1) !important;\n}\n</style> "], "mappings": ";;;EACOA,KAAK,EAAC;AAAwB;;EADrCC,GAAA;EAQwBD,KAAK,EAAC;;;EAajBA,KAAK,EAAC;AAAiB;;EAIjBA,KAAK,EAAC;AAAsB;;EAzB/CC,GAAA;AAAA;;EA2EiBD,KAAK,EAAC;AAAa;;EAanBA,KAAK,EAAC;AAAW;;EAxFlCC,GAAA;EA0FmBD,KAAK,EAAC;;;EA1FzBC,GAAA;EAkGqFD,KAAK,EAAC;;;EAlG3FC,GAAA;AAAA;;EAAAA,GAAA;EA6G+DD,KAAK,EAAC;;;EA7GrEC,GAAA;EAsH4BD,KAAK,EAAC;;;EAWRA,KAAK,EAAC;AAA0B;;EAjI1DC,GAAA;EAyI4BD,KAAK,EAAC;;;EAYjBA,KAAK,EAAC;AAAY;;;;;;;;;;;;;;;;;;;;;;uBApJjCE,mBAAA,CAgKM,OAhKNC,UAgKM,G,0BA/JJC,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAmC;IAAhCJ,KAAK,EAAC;EAAW,GAAC,YAAU,E,qDAGjCK,YAAA,CAyJUC,kBAAA;IAhKdC,OAAA,EAAAC,QAAA,CAMsB,MAYd,CAVSC,MAAA,CAAAC,KAAK,I,cAAhBR,mBAAA,CAUM,OAVNS,UAUM,GATJC,YAAA,CAQWC,mBAAA;MAPTC,WAAW,EAAC,QAAQ;MACnB,YAAU,EAAE;;MAEFA,WAAW,EAAAN,QAAA,CACpB,MAAkB,CAAlBJ,mBAAA,CAAkB,WAAAW,gBAAA,CAAZN,MAAA,CAAAC,KAAK,iB;MAdvBH,OAAA,EAAAC,QAAA,CAgBU,MAAiF,CAAjFI,YAAA,CAAiFI,oBAAA;QAAtEC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;QAhBxDhB,OAAA,EAAAC,QAAA,CAgB2E,MAAIW,MAAA,QAAAA,MAAA,OAhB/EK,gBAAA,CAgB2E,MAAI,E;QAhB/EC,CAAA;;MAAAA,CAAA;YAoB2BhB,MAAA,CAAAiB,MAAM,I,cAA3BxB,mBAAA,CA2IWyB,SAAA;MA/JjB1B,GAAA;IAAA,IAqBQG,mBAAA,CAkDM,OAlDNwB,UAkDM,G,0BAjDJxB,mBAAA,CAAa,YAAT,MAAI,sBACRQ,YAAA,CA+CkBiB,0BAAA;MA/CAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;MAvBvCxB,OAAA,EAAAC,QAAA,CAwBY,MA8BuB,CA9BvBI,YAAA,CA8BuBoB,+BAAA;QA9BDC,KAAK,EAAC;MAAK;QAxB7C1B,OAAA,EAAAC,QAAA,CAyBc,MA4BM,CA5BNJ,mBAAA,CA4BM,OA5BN8B,UA4BM,G,CA3BSzB,MAAA,CAAA0B,OAAO,I,cAApBjC,mBAAA,CAAuF,QA1BvGkC,UAAA,EAAArB,gBAAA,CA0ByCN,MAAA,CAAAiB,MAAM,CAACW,UAAU,EAAEC,QAAQ,IAAI7B,MAAA,CAAAiB,MAAM,CAACa,cAAc,qB,cAC7ElC,YAAA,CAKEmC,mBAAA;UAhClBvC,GAAA;UAAAwC,UAAA,EA6B2BhC,MAAA,CAAAiC,QAAQ;UA7BnC,uBAAAvB,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA6B2BX,MAAA,CAAAiC,QAAQ,GAAAtB,MAAA;UACjBuB,IAAI,EAAC,OAAO;UACXC,OAAK,EA/BxBC,SAAA,CA+BgCpC,MAAA,CAAAqC,aAAa;8DAGpBrC,MAAA,CAAA0B,OAAO,I,cADhB9B,YAAA,CASYW,oBAAA;UA1C5Bf,GAAA;UAmCkBgB,IAAI,EAAC,SAAS;UACd0B,IAAI,EAAC,OAAO;UACZI,MAAM,EAAN,EAAM;UACL7B,OAAK,EAAET,MAAA,CAAAuC,WAAW;UACnBhD,KAAK,EAAC;;UAvCxBO,OAAA,EAAAC,QAAA,CAyCkB,MAA2B,CAA3BI,YAAA,CAA2BqC,kBAAA;YAzC7C1C,OAAA,EAAAC,QAAA,CAyC2B,MAAQ,CAARI,YAAA,CAAQsC,eAAA,E;YAzCnCzB,CAAA;;UAAAA,CAAA;yDA2CgBpB,YAAA,CASYW,oBAAA;UApD5Bf,GAAA;UA6CkBgB,IAAI,EAAC,SAAS;UACd0B,IAAI,EAAC,OAAO;UACZI,MAAM,EAAN,EAAM;UACL7B,OAAK,EAAET,MAAA,CAAAqC,aAAa;UACpBK,OAAO,EAAE1C,MAAA,CAAA2C;;UAjD5B7C,OAAA,EAAAC,QAAA,CAmDkB,MAA4B,CAA5BI,YAAA,CAA4BqC,kBAAA;YAnD9C1C,OAAA,EAAAC,QAAA,CAmD2B,MAAS,CAATI,YAAA,CAASyC,gBAAA,E;YAnDpC5B,CAAA;;UAAAA,CAAA;;QAAAA,CAAA;UAuDYb,YAAA,CAEuBoB,+BAAA;QAFDC,KAAK,EAAC;MAAI;QAvD5C1B,OAAA,EAAAC,QAAA,CAwDc,MAAmE,CAxDjFgB,gBAAA,CAAAT,gBAAA,CAwDiBN,MAAA,CAAAiB,MAAM,CAAC4B,SAAS,8D;QAxDjC7B,CAAA;UA0DYb,YAAA,CAEuBoB,+BAAA;QAFDC,KAAK,EAAC;MAAM;QA1D9C1B,OAAA,EAAAC,QAAA,CA2Dc,MAAgO,CA3D9OgB,gBAAA,CAAAT,gBAAA,CA2DiBN,MAAA,CAAAiB,MAAM,CAACW,UAAU,EAAEkB,gBAAgB,IAAIC,MAAM,CAAC/C,MAAA,CAAAiB,MAAM,CAACW,UAAU,CAACkB,gBAAgB,CAACE,OAAO,iBAAiBC,IAAI,CAACC,KAAK,CAAClD,MAAA,CAAAiB,MAAM,CAACW,UAAU,CAACkB,gBAAgB,IAAI9C,MAAA,CAAAiB,MAAM,CAACW,UAAU,CAACkB,gBAAgB,CAACE,OAAO,uC;QA3DrNhC,CAAA;UA6DYb,YAAA,CAEuBoB,+BAAA;QAFDC,KAAK,EAAC;MAAM;QA7D9C1B,OAAA,EAAAC,QAAA,CA8Dc,MAAiF,CA9D/FgB,gBAAA,CAAAT,gBAAA,CA8DiBN,MAAA,CAAAiB,MAAM,CAACkC,eAAe,GAAGnD,MAAA,CAAAiB,MAAM,CAACkC,eAAe,CAACH,OAAO,qC;QA9DxEhC,CAAA;UAgEYb,YAAA,CAEuBoB,+BAAA;QAFDC,KAAK,EAAC;MAAM;QAhE9C1B,OAAA,EAAAC,QAAA,CAiEc,MAAwD,CAjEtEgB,gBAAA,CAAAT,gBAAA,CAiEiBN,MAAA,CAAAiB,MAAM,CAACmC,IAAI,wD;QAjE5BpC,CAAA;UAmEYb,YAAA,CAEuBoB,+BAAA;QAFDC,KAAK,EAAC;MAAM;QAnE9C1B,OAAA,EAAAC,QAAA,CAmE+C,MAC7B,CApElBgB,gBAAA,CAmE+C,OAC7B,GAAAT,gBAAA,CAAGN,MAAA,CAAAqD,sBAAsB,MAAK,KACpC,gB;QArEZrC,CAAA;;MAAAA,CAAA;UAyEQb,YAAA,CAqFUmD,kBAAA;MA9JlBtB,UAAA,EAyE0BhC,MAAA,CAAAuD,SAAS;MAzEnC,uBAAA7C,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAyE0BX,MAAA,CAAAuD,SAAS,GAAA5C,MAAA;MAAEpB,KAAK,EAAC;;MAzE3CO,OAAA,EAAAC,QAAA,CA0EU,MAWc,CAXdI,YAAA,CAWcqD,sBAAA;QAXDhC,KAAK,EAAC,MAAM;QAACiC,IAAI,EAAC;;QA1EzC3D,OAAA,EAAAC,QAAA,CA2EY,MASM,CATNJ,mBAAA,CASM,OATN+D,UASM,GARJC,mBAAA,gBAAmB,EAEX3D,MAAA,CAAAiB,MAAM,IAAIjB,MAAA,CAAAiB,MAAM,CAAC2C,kBAAkB,I,cAD3ChE,YAAA,CAGEiE,2BAAA;UAhFhBrE,GAAA;UA+EiBsE,gBAAgB,EAAE9D,MAAA,CAAAiB,MAAM,CAAC2C;yDA/E1CD,mBAAA,gBAkFcA,mBAAA,cAAiB,EACjBxD,YAAA,CAAwC4D,8BAAA;UAAlB9C,MAAM,EAAEjB,MAAA,CAAAiB;QAAM,oC;QAnFlDD,CAAA;UAuFUb,YAAA,CA2DcqD,sBAAA;QA3DDhC,KAAK,EAAC,MAAM;QAACiC,IAAI,EAAC;;QAvFzC3D,OAAA,EAAAC,QAAA,CAwFY,MAyDM,CAzDNJ,mBAAA,CAyDM,OAzDNqE,UAyDM,GAxDJL,mBAAA,YAAe,EACwB3D,MAAA,CAAAiB,MAAM,CAACmC,IAAI,uB,cAAlD3D,mBAAA,CAKM,OALNwE,UAKM,GAJJ9D,YAAA,CAGiB+D,yBAAA;UA9FjClC,UAAA,EA2FyChC,MAAA,CAAAmE,SAAS;UA3FlD,uBAAAzD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IA2FyCX,MAAA,CAAAmE,SAAS,GAAAxD,MAAA;UAAEpB,KAAK,EAAC;;UA3F1DO,OAAA,EAAAC,QAAA,CA4FkB,MAAwD,CAAxDI,YAAA,CAAwDiE,0BAAA;YAAvC5C,KAAK,EAAC;UAAU;YA5FnD1B,OAAA,EAAAC,QAAA,CA4FoD,MAAIW,MAAA,QAAAA,MAAA,OA5FxDK,gBAAA,CA4FoD,MAAI,E;YA5FxDC,CAAA;cA6FkBb,YAAA,CAAwDiE,0BAAA;YAAvC5C,KAAK,EAAC;UAAU;YA7FnD1B,OAAA,EAAAC,QAAA,CA6FoD,MAAIW,MAAA,QAAAA,MAAA,OA7FxDK,gBAAA,CA6FoD,MAAI,E;YA7FxDC,CAAA;;UAAAA,CAAA;+CAAA2C,mBAAA,gBAiGcA,mBAAA,YAAe,EACJ3D,MAAA,CAAAmE,SAAS,mBAAmBnE,MAAA,CAAAiB,MAAM,CAACmC,IAAI,uB,cAAlD3D,mBAAA,CA4BM,OA5BN4E,UA4BM,GA3BOrE,MAAA,CAAAiB,MAAM,CAACqD,MAAM,qBAAqBtE,MAAA,CAAAiB,MAAM,CAACqD,MAAM,iB,cAA1D7E,mBAAA,CASM,OA5GtB8E,WAAA,GAoGkBpE,YAAA,CAOEqE,8BAAA;UANAC,GAAG,EAAC,aAAa;UAChB,SAAO,EAAEzE,MAAA,CAAAiB,MAAM,CAACyD,MAAM,IAAI1E,MAAA,CAAA2E,QAAQ;UAClC,YAAU,EAAE,IAAI;UAChB,mBAAiB,EAAE,EAAE;UACrBC,eAAc,EAAE5E,MAAA,CAAA6E,mBAAmB;UACnCC,qBAAqB,EAAE9E,MAAA,CAAA+E;8FAGZ/E,MAAA,CAAAiB,MAAM,CAACqD,MAAM,oB,cAA7B7E,mBAAA,CAQM,OARNuF,WAQM,GAPJ7E,YAAA,CAME8E,mBAAA;UALAC,KAAK,EAAC,SAAS;UACf1E,IAAI,EAAC,MAAM;UACXH,WAAW,EAAC,wBAAwB;UACpC,WAAS,EAAT,EAAS;UACR8E,QAAQ,EAAE;+BAGf1F,mBAAA,CAOM,OAPN2F,WAOM,GANJjF,YAAA,CAKWC,mBAAA;UALDC,WAAW,EAAC;QAAS;UAClBA,WAAW,EAAAN,QAAA,CACpB,MAA+C,CAA/CJ,mBAAA,CAA+C,WAA5C,QAAM,GAAAW,gBAAA,CAAGN,MAAA,CAAAqF,aAAa,CAACrF,MAAA,CAAAiB,MAAM,CAACqD,MAAM,mB,0BACvC3E,mBAAA,CAAsB,WAAnB,iBAAe,qB;UA1HxCqB,CAAA;kCAiIcvB,mBAAA,CAeMyB,SAAA;UAhJpB1B,GAAA;QAAA,IAgIcmE,mBAAA,YAAe,EACfhE,mBAAA,CAeM,OAfN2F,WAeM,GAbItF,MAAA,CAAAiB,MAAM,CAACqD,MAAM,oBAAoBtE,MAAA,CAAAiB,MAAM,CAACsE,UAAU,I,cAD1D3F,YAAA,CAME4F,2BAAA;UAxIlBhG,GAAA;UAoImB,YAAU,EAAEQ,MAAA,CAAAyF,WAAW,CAACzF,MAAA,CAAAiB,MAAM,CAACsE,UAAU,IAAIvF,MAAA,CAAAiB,MAAM,CAACyE,WAAW;UAC/DpB,MAAM,EAAEtE,MAAA,CAAAiB,MAAM,CAACqD,MAAM;UACrBqB,QAAQ,EAAE3F,MAAA,CAAAiB,MAAM,CAAC0E,QAAQ;UACzBC,YAAU,EAAE5F,MAAA,CAAA6F;wGAEfpG,mBAAA,CAMM,OANNqG,WAMM,GALJ3F,YAAA,CAIWC,mBAAA;UAJDC,WAAW,EAAC;QAAe;UACxBA,WAAW,EAAAN,QAAA,CACpB,MAA+C,CAA/CJ,mBAAA,CAA+C,WAA5C,QAAM,GAAAW,gBAAA,CAAGN,MAAA,CAAAqF,aAAa,CAACrF,MAAA,CAAAiB,MAAM,CAACqD,MAAM,kB;UA5I7DtD,CAAA;;QAAAA,CAAA;UAoJUb,YAAA,CAScqD,sBAAA;QATDhC,KAAK,EAAC,MAAM;QAACiC,IAAI,EAAC;;QApJzC3D,OAAA,EAAAC,QAAA,CAqJY,MAOM,CAPNJ,mBAAA,CAOM,OAPNoG,WAOM,GANJpC,mBAAA,YAAe,EACfxD,YAAA,CAIE6F,2BAAA;UAHC/E,MAAM,EAAEjB,MAAA,CAAAiB,MAAM;UACd,YAAU,EAAEjB,MAAA,CAAAiB,MAAM,CAACgF,SAAS,IAAIjG,MAAA,CAAAiB,MAAM,CAACiF,UAAU;UACjD,YAAU,EAAElG,MAAA,CAAAiB,MAAM,CAACW,UAAU,EAAEC,QAAQ;;QA1JxDb,CAAA;;MAAAA,CAAA;qEAAA2C,mBAAA,e;IAAA3C,CAAA;6BAOwBhB,MAAA,CAAA0C,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}