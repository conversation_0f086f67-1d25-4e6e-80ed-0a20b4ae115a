<template>
  <div class="vehicle-chart">
    <div class="chart-container" ref="chartRef"></div>
  </div>
</template>

<script setup>
/* eslint-disable no-undef */
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts/core';
import { Bar<PERSON><PERSON>, PieChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, GridComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([TitleComponent, TooltipComponent, GridComponent, LegendComponent, <PERSON><PERSON><PERSON>, Pie<PERSON><PERSON>, CanvasRenderer]);

const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  chartType: {
    type: String,
    default: 'bar', // 'bar' 或 'pie'
    validator: (value) => ['bar', 'pie'].includes(value)
  }
});

const chartRef = ref(null);
let chart = null;

// 处理数据格式
const chartData = computed(() => {
  console.log('VehicleChart - 接收到的数据:', props.data);
  console.log('VehicleChart - 图表类型:', props.chartType);

  if (!props.data) {
    console.log('VehicleChart - 没有数据');
    return { categories: [], counts: [] };
  }

  // 处理 vehicleTypeStats 格式的数据
  if (props.data.vehicleTypeStats) {
    const categories = Object.keys(props.data.vehicleTypeStats).filter(cat => isValidVehicleType(cat));
    const counts = categories.map(cat => props.data.vehicleTypeStats[cat]);
    console.log('VehicleChart - 使用vehicleTypeStats格式:', { categories, counts });
    return { categories, counts };
  }

  // 处理传统格式数据
  if (props.data.categories && props.data.counts) {
    console.log('VehicleChart - 使用categories/counts格式:', props.data);
    return props.data;
  }

  // 如果是单纯的对象 {car: 5, truck: 3} 格式
  if (typeof props.data === 'object' && !Array.isArray(props.data)) {
    const categories = [];
    const counts = [];

    for (const [key, value] of Object.entries(props.data)) {
      if (key !== 'count' && key !== 'vehicles' && key !== 'imageUrl' &&
          typeof value === 'number' && isValidVehicleType(key)) {
        categories.push(key);
        counts.push(value);
      }
    }

    if (categories.length > 0) {
      console.log('VehicleChart - 从对象格式生成数据:', { categories, counts });
      return { categories, counts };
    }
  }

  // 处理 detections 格式数据
  if (props.data.detections && Array.isArray(props.data.detections)) {
    const typeCounter = {};
    props.data.detections.forEach(det => {
      const type = det.className || det.class_name || '未知';
      // 只统计有效的车辆类型
      if (isValidVehicleType(type)) {
        typeCounter[type] = (typeCounter[type] || 0) + 1;
      }
    });

    const categories = Object.keys(typeCounter);
    const counts = categories.map(cat => typeCounter[cat]);
    console.log('VehicleChart - 从detections生成数据:', { categories, counts });
    return { categories, counts };
  }

  console.log('VehicleChart - 无法处理的数据格式，返回空数据');
  return { categories: [], counts: [] };
});

// 判断是否为有效的车辆类型（排除交通设施）
const isValidVehicleType = (type) => {
  if (!type || typeof type !== 'string') {
    console.log(`VehicleChart isValidVehicleType - 无效类型: ${type} (${typeof type})`);
    return false;
  }

  const lowerType = type.toLowerCase().trim();
  console.log(`VehicleChart isValidVehicleType - 检查类型: "${type}" -> "${lowerType}"`);

  // 英文车辆类型
  const validEnglishTypes = [
    'car', 'truck', 'bus', 'motorcycle', 'bicycle', 'person', 'unknown',
    'vehicle', 'auto', 'motorbike', 'bike', 'pedestrian', 'human'
  ];

  // 中文车辆类型
  const validChineseTypes = [
    '小汽车', '汽车', '轿车', '卡车', '货车', '公交车', '客车', '大巴',
    '摩托车', '自行车', '单车', '行人', '人', '未知', '车辆'
  ];

  // 排除交通设施
  const excludeTypes = [
    'stop sign', 'traffic light', 'traffic_light', 'stop_sign',
    '停车标志', '交通信号灯', '红绿灯', '信号灯'
  ];

  if (excludeTypes.includes(lowerType) || excludeTypes.includes(type)) {
    console.log(`VehicleChart isValidVehicleType - 排除交通设施: ${type}`);
    return false;
  }

  const isValid = validEnglishTypes.includes(lowerType) ||
                 validChineseTypes.includes(type) ||
                 validChineseTypes.includes(lowerType);

  console.log(`VehicleChart isValidVehicleType - "${type}" 是否有效: ${isValid}`);
  return isValid;
};

// 格式化车辆类型显示名称
const formatVehicleType = (type) => {
  if (!type || typeof type !== 'string') {
    return '未知';
  }

  const lowerType = type.toLowerCase().trim();

  // 英文到中文的映射
  const englishToChineseMap = {
    'car': '汽车',
    'auto': '汽车',
    'vehicle': '汽车',
    'truck': '卡车',
    'bus': '公交车',
    'motorcycle': '摩托车',
    'motorbike': '摩托车',
    'bicycle': '自行车',
    'bike': '自行车',
    'person': '行人',
    'pedestrian': '行人',
    'human': '行人',
    'unknown': '未知'
  };

  // 中文类型标准化
  const chineseNormalizationMap = {
    '小汽车': '汽车',
    '轿车': '汽车',
    '货车': '卡车',
    '客车': '公交车',
    '大巴': '公交车',
    '单车': '自行车',
    '人': '行人'
  };

  // 先检查是否是英文类型
  if (englishToChineseMap[lowerType]) {
    return englishToChineseMap[lowerType];
  }

  // 检查是否是中文类型需要标准化
  if (chineseNormalizationMap[type]) {
    return chineseNormalizationMap[type];
  }

  // 如果已经是标准中文名称，直接返回
  const standardChineseTypes = ['汽车', '卡车', '公交车', '摩托车', '自行车', '行人', '未知'];
  if (standardChineseTypes.includes(type)) {
    return type;
  }

  // 默认返回原始类型
  return type;
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
    updateChart();
  }
};

// 更新图表
const updateChart = () => {
  if (!chart) {
    console.log('VehicleChart - 图表未初始化');
    return;
  }

  const { categories, counts } = chartData.value;
  console.log('VehicleChart - 更新图表数据:', { categories, counts });

  if (!categories || categories.length === 0) {
    console.log('VehicleChart - 没有类别数据，跳过图表更新');
    return;
  }

  // 格式化车辆类型名称
  const formattedCategories = categories.map(cat => formatVehicleType(cat));
  console.log('VehicleChart - 格式化后的类别:', formattedCategories);
  
  // 颜色配置
  const colorMap = {
    '汽车': '#5470C6',
    '卡车': '#91CC75',
    '公交车': '#FAC858',
    '摩托车': '#EE6666',
    '自行车': '#73C0DE',
    '行人': '#3BA272',
    '未知': '#9A60B4'
  };
  
  // 根据类别名称获取颜色，如果没有预定义则使用默认颜色
  const colors = formattedCategories.map(category => colorMap[category] || null);
  
  // 根据图表类型构建不同的配置
  let option;
  
  if (props.chartType === 'pie') {
    option = {
      title: {
        text: '车辆类型分布',
        left: 'center',
        textStyle: {
          color: '#ffffff',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
        backgroundColor: 'rgba(25, 32, 50, 0.9)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: '#ffffff'
        }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: formattedCategories,
        textStyle: {
          color: '#ffffff',
          fontSize: 14
        }
      },
      series: [
        {
          name: '类型分布',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '{b}: {c} ({d}%)',
            color: '#ffffff',
            fontSize: 14,
            fontWeight: 'bold'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold',
              color: '#ffffff'
            }
          },
          labelLine: {
            show: true
          },
          data: formattedCategories.map((category, index) => ({
            value: counts[index] || 0,
            name: category,
            itemStyle: {
              color: colors[index]
            }
          }))
        }
      ]
    };
  } else {
    // 柱状图配置
    option = {
      title: {
        text: '车辆类型分布',
        left: 'center',
        textStyle: {
          color: '#ffffff',
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          const data = params[0];
          return `${data.name}: ${data.value} 辆`;
        },
        backgroundColor: 'rgba(25, 32, 50, 0.9)',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        textStyle: {
          color: '#ffffff'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: formattedCategories,
        axisLabel: {
          interval: 0,
          rotate: 30,
          color: '#ffffff',
          fontSize: 12,
          fontWeight: 'bold'
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisTick: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '数量',
        minInterval: 1,
        nameTextStyle: {
          color: '#ffffff',
          fontSize: 14,
          fontWeight: 'bold'
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 12,
          fontWeight: 'bold'
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisTick: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      series: [{
        name: '车辆数量',
        type: 'bar',
        data: counts.map((value, index) => ({
          value: value,
          itemStyle: {
            color: colors[index]
          }
        })),
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#ffffff',
          fontSize: 12,
          fontWeight: 'bold'
        }
      }]
    };
  }

  console.log('VehicleChart - 设置图表配置:', option);
  chart.setOption(option);
  console.log('VehicleChart - 图表更新完成');
};

// 监听窗口大小变化
window.addEventListener('resize', () => {
  if (chart) {
    chart.resize();
  }
});

// 监听数据变化
watch(() => props.data, () => {
  if (chart) {
    updateChart();
  }
}, { deep: true });

// 监听图表类型变化
watch(() => props.chartType, () => {
  if (chart) {
    updateChart();
  }
});

// 监听加载状态
watch(() => props.loading, (newVal) => {
  if (!newVal && chart) {
    updateChart();
  }
});

onMounted(() => {
  initChart();
});

// 在组件卸载时销毁图表
onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', () => {
    if (chart) {
      chart.resize();
    }
  });
});
</script>

<style scoped>
.vehicle-chart {
  width: 100%;
  height: 100%;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style> 