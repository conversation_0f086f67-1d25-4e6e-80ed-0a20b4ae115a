{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createBlock as _createBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"video-analytics-panel\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"summary-cards\"\n};\nconst _hoisted_3 = {\n  class: \"card-value\"\n};\nconst _hoisted_4 = {\n  class: \"card-value\"\n};\nconst _hoisted_5 = {\n  class: \"card-value\"\n};\nconst _hoisted_6 = {\n  class: \"vehicle-count\"\n};\nconst _hoisted_7 = {\n  class: \"vehicle-count\"\n};\nconst _hoisted_8 = {\n  class: \"vehicle-count\"\n};\nconst _hoisted_9 = {\n  class: \"vehicle-count\"\n};\nconst _hoisted_10 = {\n  class: \"vehicle-count\"\n};\nconst _hoisted_11 = {\n  class: \"charts-actions\"\n};\nconst _hoisted_12 = {\n  class: \"intersection-view\"\n};\nconst _hoisted_13 = {\n  class: \"traffic-flow-card\"\n};\nconst _hoisted_14 = {\n  class: \"chart-mini-container\"\n};\nconst _hoisted_15 = {\n  ref: \"horizontalFlowChart\",\n  style: {\n    \"width\": \"100%\",\n    \"height\": \"280px\"\n  }\n};\nconst _hoisted_16 = {\n  class: \"traffic-flow-card\"\n};\nconst _hoisted_17 = {\n  class: \"chart-mini-container\"\n};\nconst _hoisted_18 = {\n  ref: \"verticalFlowChart\",\n  style: {\n    \"width\": \"100%\",\n    \"height\": \"280px\"\n  }\n};\nconst _hoisted_19 = {\n  class: \"traffic-comparison-card\"\n};\nconst _hoisted_20 = {\n  class: \"chart-container\"\n};\nconst _hoisted_21 = {\n  ref: \"combinedFlowChart\",\n  style: {\n    \"width\": \"100%\",\n    \"height\": \"350px\"\n  }\n};\nconst _hoisted_22 = {\n  class: \"chart-container\"\n};\nconst _hoisted_23 = {\n  ref: \"lineChart\",\n  style: {\n    \"width\": \"100%\",\n    \"height\": \"350px\"\n  }\n};\nconst _hoisted_24 = {\n  class: \"chart-container\"\n};\nconst _hoisted_25 = {\n  ref: \"pieChart\",\n  style: {\n    \"width\": \"100%\",\n    \"height\": \"350px\"\n  }\n};\nconst _hoisted_26 = {\n  class: \"chart-container\"\n};\nconst _hoisted_27 = {\n  ref: \"barChart\",\n  style: {\n    \"width\": \"100%\",\n    \"height\": \"350px\"\n  }\n};\nconst _hoisted_28 = {\n  key: 2,\n  class: \"solution-section\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_download = _resolveComponent(\"download\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [$setup.analysisSummary ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_row, {\n    gutter: 20\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 8\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stat-card\"\n      }, {\n        header: _withCtx(() => _cache[0] || (_cache[0] = [_createElementVNode(\"div\", {\n          class: \"card-header\"\n        }, [_createElementVNode(\"span\", null, \"总车辆数\")], -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.vehicleCount || 0), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      span: 8\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stat-card\"\n      }, {\n        header: _withCtx(() => _cache[1] || (_cache[1] = [_createElementVNode(\"div\", {\n          class: \"card-header\"\n        }, [_createElementVNode(\"span\", null, \"处理时间\")], -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.processingTime ? $setup.processingTime.toFixed(2) + '秒' : '数据不可用'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      span: 8\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"stat-card\"\n      }, {\n        header: _withCtx(() => _cache[2] || (_cache[2] = [_createElementVNode(\"div\", {\n          class: \"card-header\"\n        }, [_createElementVNode(\"span\", null, \"拥堵等级\")], -1 /* HOISTED */)])),\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.getCongestionLevel($setup.vehicleCount)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 添加车辆类型统计卡片 \"), _cache[13] || (_cache[13] = _createElementVNode(\"h3\", {\n    class: \"section-title\"\n  }, \"车辆类型统计\", -1 /* HOISTED */)), _createVNode(_component_el_row, {\n    gutter: 20,\n    class: \"vehicle-type-cards\"\n  }, {\n    default: _withCtx(() => [$setup.vehicleTypeStats && $setup.vehicleTypeStats.car !== undefined ? (_openBlock(), _createBlock(_component_el_col, {\n      key: 0,\n      span: 4\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"vehicle-type-card\"\n      }, {\n        default: _withCtx(() => [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n          class: \"vehicle-icon\"\n        }, \"🚗\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.vehicleTypeStats.car), 1 /* TEXT */), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n          class: \"vehicle-label\"\n        }, \"小汽车\", -1 /* HOISTED */))]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.vehicleTypeStats && $setup.vehicleTypeStats.motorcycle !== undefined ? (_openBlock(), _createBlock(_component_el_col, {\n      key: 1,\n      span: 4\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"vehicle-type-card\"\n      }, {\n        default: _withCtx(() => [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n          class: \"vehicle-icon\"\n        }, \"🏍️\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_7, _toDisplayString($setup.vehicleTypeStats.motorcycle), 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n          class: \"vehicle-label\"\n        }, \"摩托车\", -1 /* HOISTED */))]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.vehicleTypeStats && $setup.vehicleTypeStats.truck !== undefined ? (_openBlock(), _createBlock(_component_el_col, {\n      key: 2,\n      span: 4\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"vehicle-type-card\"\n      }, {\n        default: _withCtx(() => [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n          class: \"vehicle-icon\"\n        }, \"🚚\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.vehicleTypeStats.truck), 1 /* TEXT */), _cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n          class: \"vehicle-label\"\n        }, \"卡车\", -1 /* HOISTED */))]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.vehicleTypeStats && $setup.vehicleTypeStats.bus !== undefined ? (_openBlock(), _createBlock(_component_el_col, {\n      key: 3,\n      span: 4\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"vehicle-type-card\"\n      }, {\n        default: _withCtx(() => [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n          class: \"vehicle-icon\"\n        }, \"🚌\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.vehicleTypeStats.bus), 1 /* TEXT */), _cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n          class: \"vehicle-label\"\n        }, \"公交车\", -1 /* HOISTED */))]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.vehicleTypeStats && $setup.vehicleTypeStats.bicycle !== undefined ? (_openBlock(), _createBlock(_component_el_col, {\n      key: 4,\n      span: 4\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_card, {\n        class: \"vehicle-type-card\"\n      }, {\n        default: _withCtx(() => [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n          class: \"vehicle-icon\"\n        }, \"🚲\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.vehicleTypeStats.bicycle), 1 /* TEXT */), _cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n          class: \"vehicle-label\"\n        }, \"自行车\", -1 /* HOISTED */))]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true), $setup.isIntersection ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_button, {\n    size: \"small\",\n    onClick: $setup.exportAllCharts,\n    type: \"success\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_download)]),\n      _: 1 /* STABLE */\n    }), _cache[14] || (_cache[14] = _createTextVNode(\" 导出所有图表 \"))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\"])]), _cache[18] || (_cache[18] = _createElementVNode(\"h3\", null, \"十字路口综合分析\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_row, {\n    gutter: 20\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 12\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_cache[15] || (_cache[15] = _createElementVNode(\"h4\", null, \"东西方向车流量\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, null, 512 /* NEED_PATCH */)])])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      span: 12\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_16, [_cache[16] || (_cache[16] = _createElementVNode(\"h4\", null, \"南北方向车流量\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, null, 512 /* NEED_PATCH */)])])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_row, {\n    style: {\n      \"margin-top\": \"30px\"\n    }\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      span: 24\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_cache[17] || (_cache[17] = _createElementVNode(\"h4\", null, \"方向车流量对比\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, null, 512 /* NEED_PATCH */)])])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })]), _createVNode(_component_el_divider)], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _cache[22] || (_cache[22] = _createElementVNode(\"h3\", {\n    class: \"traffic-trend-title\"\n  }, \"车流量趋势\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, null, 512 /* NEED_PATCH */)]), _createVNode(_component_el_divider), _createVNode(_component_el_row, {\n    gutter: 20\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_col, {\n      md: 12\n    }, {\n      default: _withCtx(() => [_cache[19] || (_cache[19] = _createElementVNode(\"h3\", null, \"车型分布\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, null, 512 /* NEED_PATCH */)])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_col, {\n      md: 12\n    }, {\n      default: _withCtx(() => [_cache[20] || (_cache[20] = _createElementVNode(\"h3\", null, \"方向分布\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, null, 512 /* NEED_PATCH */)])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 添加交通解决方案建议 \"), $setup.vehicleCount > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, [_cache[21] || (_cache[21] = _createElementVNode(\"h3\", null, \"交通解决方案\", -1 /* HOISTED */)), _createVNode(_component_el_alert, {\n    type: $setup.getSolutionAlertType($setup.vehicleCount),\n    title: $setup.getSolutionTitle($setup.vehicleCount),\n    description: $setup.getSolutionDescription($setup.vehicleCount),\n    \"show-icon\": \"\",\n    closable: false,\n    class: \"solution-alert\"\n  }, null, 8 /* PROPS */, [\"type\", \"title\", \"description\"])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "ref", "style", "_createElementBlock", "_hoisted_1", "$setup", "analysisSummary", "_hoisted_2", "_createVNode", "_component_el_row", "gutter", "default", "_withCtx", "_component_el_col", "span", "_component_el_card", "header", "_cache", "_createElementVNode", "_hoisted_3", "_toDisplayString", "vehicleCount", "_", "_hoisted_4", "processingTime", "toFixed", "_hoisted_5", "getCongestionLevel", "_createCommentVNode", "vehicleTypeStats", "car", "undefined", "_createBlock", "_hoisted_6", "motorcycle", "_hoisted_7", "truck", "_hoisted_8", "bus", "_hoisted_9", "bicycle", "_hoisted_10", "isIntersection", "_Fragment", "_hoisted_11", "_component_el_button", "size", "onClick", "exportAllCharts", "type", "_component_el_icon", "_component_download", "_createTextVNode", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_el_divider", "_hoisted_22", "_hoisted_23", "md", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_component_el_alert", "getSolutionAlertType", "title", "getSolutionTitle", "description", "getSolutionDescription", "closable"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\video\\VideoAnalyticsPanel.vue"], "sourcesContent": ["<template>\n  <div class=\"video-analytics-panel\">\n    <div class=\"summary-cards\" v-if=\"analysisSummary\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"8\">\n          <el-card class=\"stat-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>总车辆数</span>\n              </div>\n            </template>\n            <div class=\"card-value\">\n              {{ vehicleCount || 0 }}\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-card class=\"stat-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>处理时间</span>\n              </div>\n            </template>\n            <div class=\"card-value\">\n              {{ processingTime ? processingTime.toFixed(2) + '秒' : '数据不可用' }}\n            </div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-card class=\"stat-card\">\n            <template #header>\n              <div class=\"card-header\">\n                <span>拥堵等级</span>\n              </div>\n            </template>\n            <div class=\"card-value\">\n              {{ getCongestionLevel(vehicleCount) }}\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n      \n      <!-- 添加车辆类型统计卡片 -->\n      <h3 class=\"section-title\">车辆类型统计</h3>\n      <el-row :gutter=\"20\" class=\"vehicle-type-cards\">\n        <el-col :span=\"4\" v-if=\"vehicleTypeStats && vehicleTypeStats.car !== undefined\">\n          <el-card class=\"vehicle-type-card\">\n            <div class=\"vehicle-icon\">🚗</div>\n            <div class=\"vehicle-count\">{{ vehicleTypeStats.car }}</div>\n            <div class=\"vehicle-label\">小汽车</div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"4\" v-if=\"vehicleTypeStats && vehicleTypeStats.motorcycle !== undefined\">\n          <el-card class=\"vehicle-type-card\">\n            <div class=\"vehicle-icon\">🏍️</div>\n            <div class=\"vehicle-count\">{{ vehicleTypeStats.motorcycle }}</div>\n            <div class=\"vehicle-label\">摩托车</div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"4\" v-if=\"vehicleTypeStats && vehicleTypeStats.truck !== undefined\">\n          <el-card class=\"vehicle-type-card\">\n            <div class=\"vehicle-icon\">🚚</div>\n            <div class=\"vehicle-count\">{{ vehicleTypeStats.truck }}</div>\n            <div class=\"vehicle-label\">卡车</div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"4\" v-if=\"vehicleTypeStats && vehicleTypeStats.bus !== undefined\">\n          <el-card class=\"vehicle-type-card\">\n            <div class=\"vehicle-icon\">🚌</div>\n            <div class=\"vehicle-count\">{{ vehicleTypeStats.bus }}</div>\n            <div class=\"vehicle-label\">公交车</div>\n          </el-card>\n        </el-col>\n        <el-col :span=\"4\" v-if=\"vehicleTypeStats && vehicleTypeStats.bicycle !== undefined\">\n          <el-card class=\"vehicle-type-card\">\n            <div class=\"vehicle-icon\">🚲</div>\n            <div class=\"vehicle-count\">{{ vehicleTypeStats.bicycle }}</div>\n            <div class=\"vehicle-label\">自行车</div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n\n    <template v-if=\"isIntersection\">\n      <div class=\"charts-actions\">\n        <el-button size=\"small\" @click=\"exportAllCharts\" type=\"success\">\n          <el-icon><download /></el-icon> 导出所有图表\n        </el-button>\n      </div>\n      <h3>十字路口综合分析</h3>\n      <div class=\"intersection-view\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"12\">\n            <div class=\"traffic-flow-card\">\n              <h4>东西方向车流量</h4>\n              <div class=\"chart-mini-container\">\n                <div ref=\"horizontalFlowChart\" style=\"width: 100%; height: 280px;\"></div>\n              </div>\n            </div>\n          </el-col>\n          <el-col :span=\"12\">\n            <div class=\"traffic-flow-card\">\n              <h4>南北方向车流量</h4>\n              <div class=\"chart-mini-container\">\n                <div ref=\"verticalFlowChart\" style=\"width: 100%; height: 280px;\"></div>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n        \n        <el-row style=\"margin-top: 30px\">\n          <el-col :span=\"24\">\n            <div class=\"traffic-comparison-card\">\n              <h4>方向车流量对比</h4>\n              <div class=\"chart-container\">\n                <div ref=\"combinedFlowChart\" style=\"width: 100%; height: 350px;\"></div>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n      \n      <el-divider></el-divider>\n    </template>\n  \n    <h3 class=\"traffic-trend-title\">车流量趋势</h3>\n    <div class=\"chart-container\">\n      <div ref=\"lineChart\" style=\"width: 100%; height: 350px;\"></div>\n    </div>\n    \n    <el-divider></el-divider>\n    \n    <el-row :gutter=\"20\">\n      <el-col :md=\"12\">\n        <h3>车型分布</h3>\n        <div class=\"chart-container\">\n          <div ref=\"pieChart\" style=\"width: 100%; height: 350px;\"></div>\n        </div>\n      </el-col>\n      <el-col :md=\"12\">\n        <h3>方向分布</h3>\n        <div class=\"chart-container\">\n          <div ref=\"barChart\" style=\"width: 100%; height: 350px;\"></div>\n        </div>\n      </el-col>\n    </el-row>\n    \n    <!-- 添加交通解决方案建议 -->\n    <div class=\"solution-section\" v-if=\"vehicleCount > 0\">\n      <h3>交通解决方案</h3>\n      <el-alert\n        :type=\"getSolutionAlertType(vehicleCount)\"\n        :title=\"getSolutionTitle(vehicleCount)\"\n        :description=\"getSolutionDescription(vehicleCount)\"\n        show-icon\n        :closable=\"false\"\n        class=\"solution-alert\"\n      ></el-alert>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, onMounted, nextTick, watch } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport * as echarts from 'echarts';\nimport { Download } from '@element-plus/icons-vue';\n\nexport default {\n  name: 'VideoAnalyticsPanel',\n  components: {\n    Download\n  },\n  props: {\n    // 分析结果数据\n    result: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  setup(props) {\n    // 图表引用\n    const horizontalFlowChart = ref(null);\n    const verticalFlowChart = ref(null);\n    const combinedFlowChart = ref(null);\n    const lineChart = ref(null);\n    const pieChart = ref(null);\n    const barChart = ref(null);\n    \n    // 图表实例\n    let horizontalFlowChartInstance = null;\n    let verticalFlowChartInstance = null;\n    let combinedFlowChartInstance = null;\n    let lineChartInstance = null;\n    let pieChartInstance = null;\n    let barChartInstance = null;\n    \n    // 计算属性\n    const analysisSummary = computed(() => props.result?.analysis_summary || null);\n    const isIntersection = computed(() => props.result?.mode === 'intersection');\n    \n    // 新增计算属性\n    const vehicleCount = computed(() => {\n      if (!props.result || !props.result.vehicle_type_stats) {\n        return props.result?.vehicle_count || 0;\n      }\n      \n      // 计算所有车辆类型数量之和\n      let total = 0;\n      Object.values(props.result.vehicle_type_stats).forEach(count => {\n        total += count;\n      });\n      \n      return total;\n    });\n    const processingTime = computed(() => props.result?.processing_time || 0);\n    const vehicleTypeStats = computed(() => props.result?.vehicle_type_stats || {});\n    \n    // 格式化时间\n    const formatTime = (seconds) => {\n      const mins = Math.floor(seconds / 60);\n      const secs = Math.floor(seconds % 60);\n      return `${mins}分${secs.toString().padStart(2, '0')}秒`;\n    };\n    \n    // 根据车辆数量获取拥堵等级\n    const getCongestionLevel = (count) => {\n      if (count <= 5) return '不拥挤';\n      if (count <= 10) return '一般';\n      if (count <= 20) return '较拥挤';\n      return '拥挤';\n    };\n    \n    // 获取解决方案的标题\n    const getSolutionTitle = (count) => {\n      if (count <= 5) return '方案一：正常红绿灯交换';\n      if (count <= 10) return '方案二：延长横向绿灯时间';\n      if (count <= 20) return '方案三：延长纵向绿灯时间';\n      return '方案四：发出提醒（需人为干预）';\n    };\n    \n    // 获取解决方案的描述\n    const getSolutionDescription = (count) => {\n      if (count <= 5) return '当前车流量正常，可维持默认信号灯配置。';\n      if (count <= 10) return '当前车流量略大，建议延长东西方向绿灯时间，确保车辆顺畅通行。';\n      if (count <= 20) return '当前车流量较大，建议延长南北方向绿灯时间，减少车辆积压。';\n      return '当前车流量大，可能出现拥堵，建议启动交通应急预案，必要时派遣交警现场指挥。';\n    };\n    \n    // 获取解决方案的警告类型\n    const getSolutionAlertType = (count) => {\n      if (count <= 5) return 'success';\n      if (count <= 10) return 'info';\n      if (count <= 20) return 'warning';\n      return 'error';\n    };\n    \n    // 图表配置\n    const lineChartOption = computed(() => {\n      if (!props.result || !props.result.time_series_data) {\n        return {};\n      }\n      \n      const timeData = props.result.time_series_data.timestamps.map(formatTime);\n      const countData = props.result.time_series_data.vehicle_counts;\n      \n      return {\n        title: {\n          text: '车流量时间趋势'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: timeData,\n          axisLabel: {\n            rotate: 45\n          },\n          name: '时间'\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数'\n        },\n        series: [\n          {\n            name: '车辆数',\n            type: 'line',\n            data: countData,\n            smooth: true\n          }\n        ]\n      };\n    });\n    \n    // 判断是否为有效的车辆类型（排除交通设施）\n    const isValidVehicleType = (type) => {\n      if (!type || typeof type !== 'string') {\n        console.log(`VideoAnalyticsPanel isValidVehicleType - 无效类型: ${type} (${typeof type})`);\n        return false;\n      }\n\n      const lowerType = type.toLowerCase().trim();\n      console.log(`VideoAnalyticsPanel isValidVehicleType - 检查类型: \"${type}\" -> \"${lowerType}\"`);\n\n      // 英文车辆类型\n      const validEnglishTypes = [\n        'car', 'truck', 'bus', 'motorcycle', 'bicycle', 'person', 'unknown',\n        'vehicle', 'auto', 'motorbike', 'bike', 'pedestrian', 'human'\n      ];\n\n      // 中文车辆类型\n      const validChineseTypes = [\n        '汽车', '小汽车', '轿车', '卡车', '货车', '公交车', '客车', '大巴',\n        '摩托车', '自行车', '单车', '行人', '人', '未知', '车辆'\n      ];\n\n      // 排除交通设施\n      const excludeTypes = [\n        'stop sign', 'traffic light', 'traffic_light', 'stop_sign',\n        '停车标志', '交通信号灯', '红绿灯', '信号灯'\n      ];\n\n      if (excludeTypes.includes(lowerType) || excludeTypes.includes(type)) {\n        console.log(`VideoAnalyticsPanel isValidVehicleType - 排除交通设施: ${type}`);\n        return false;\n      }\n\n      const isValid = validEnglishTypes.includes(lowerType) ||\n                     validChineseTypes.includes(type) ||\n                     validChineseTypes.includes(lowerType);\n\n      console.log(`VideoAnalyticsPanel isValidVehicleType - \"${type}\" 是否有效: ${isValid}`);\n      return isValid;\n    };\n\n    const pieChartOption = computed(() => {\n      if (!props.result || !props.result.vehicle_type_stats) {\n        return {};\n      }\n\n      // 获取车辆类型中文名称\n      const getVehicleTypeName = (type) => {\n        if (!type || typeof type !== 'string') {\n          return '未知';\n        }\n\n        const lowerType = type.toLowerCase().trim();\n\n        // 英文到中文的映射\n        const englishToChineseMap = {\n          'car': '汽车',\n          'auto': '汽车',\n          'vehicle': '汽车',\n          'truck': '卡车',\n          'bus': '公交车',\n          'motorcycle': '摩托车',\n          'motorbike': '摩托车',\n          'bicycle': '自行车',\n          'bike': '自行车',\n          'person': '行人',\n          'pedestrian': '行人',\n          'human': '行人',\n          'unknown': '未知'\n        };\n\n        // 中文类型标准化\n        const chineseNormalizationMap = {\n          '小汽车': '汽车',\n          '轿车': '汽车',\n          '货车': '卡车',\n          '客车': '公交车',\n          '大巴': '公交车',\n          '单车': '自行车',\n          '人': '行人'\n        };\n\n        // 先检查是否是英文类型\n        if (englishToChineseMap[lowerType]) {\n          return englishToChineseMap[lowerType];\n        }\n\n        // 检查是否是中文类型需要标准化\n        if (chineseNormalizationMap[type]) {\n          return chineseNormalizationMap[type];\n        }\n\n        // 如果已经是标准中文名称，直接返回\n        const standardChineseTypes = ['汽车', '卡车', '公交车', '摩托车', '自行车', '行人', '未知'];\n        if (standardChineseTypes.includes(type)) {\n          return type;\n        }\n\n        // 默认返回原始类型\n        return type;\n      };\n\n      console.log('VideoAnalyticsPanel - 原始车辆类型数据:', props.result.vehicle_type_stats);\n\n      // 将车辆类型数据转换为饼图所需格式，过滤掉非车辆类型\n      const seriesData = Object.entries(props.result.vehicle_type_stats)\n        .filter(([type]) => {\n          const isValid = isValidVehicleType(type);\n          console.log(`VideoAnalyticsPanel - 过滤车辆类型: \"${type}\" -> ${isValid}`);\n          return isValid;\n        })\n        .map(([type, count]) => {\n          const chineseName = getVehicleTypeName(type);\n          console.log(`VideoAnalyticsPanel - 映射车辆类型: \"${type}\" -> \"${chineseName}\"`);\n          return {\n            name: chineseName,\n            value: count\n          };\n        });\n\n      console.log('VideoAnalyticsPanel - 饼图数据:', seriesData);\n      \n      return {\n        title: {\n          text: '车型分布',\n          textStyle: {\n            fontSize: 16,\n            fontWeight: 'bold',\n            color: '#ffffff',\n            fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif'\n          }\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          textStyle: {\n            color: '#e5e7eb',\n            fontSize: 12,\n            fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif'\n          }\n        },\n        series: [\n          {\n            name: '车型',\n            type: 'pie',\n            radius: ['40%', '70%'],\n            avoidLabelOverlap: false,\n            label: {\n              show: false,\n              position: 'center'\n            },\n            emphasis: {\n              label: {\n                show: true,\n                fontSize: 14,\n                fontWeight: 'bold',\n                color: '#ffffff'\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            data: seriesData\n          }\n        ],\n        color: ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#9A60B4']\n      };\n    });\n    \n    const barChartOption = computed(() => {\n      if (!props.result || !props.result.analysis_summary || !props.result.analysis_summary.direction_distribution) {\n        return {};\n      }\n      \n      const directionMap = {\n        'eastbound': '东向西',\n        'westbound': '西向东',\n        'northbound': '北向南',\n        'southbound': '南向北'\n      };\n      \n      const categories = [];\n      const data = [];\n      \n      Object.entries(props.result.analysis_summary.direction_distribution).forEach(([dir, count]) => {\n        categories.push(directionMap[dir] || dir);\n        data.push(count);\n      });\n      \n      return {\n        title: {\n          text: '方向分布',\n          textStyle: {\n            fontSize: 16,\n            fontWeight: 'bold',\n            color: '#ffffff',\n            fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif'\n          }\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories,\n          name: '行驶方向',\n          nameTextStyle: {\n            fontSize: 14,\n            fontWeight: 'bold',\n            color: '#e5e7eb',\n            padding: [5, 0, 0, 0],\n            fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif'\n          },\n          axisLabel: {\n            fontSize: 13,\n            fontWeight: 'bold',\n            color: '#e5e7eb',\n            fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif'\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数',\n          nameTextStyle: {\n            fontSize: 14,\n            fontWeight: 'bold',\n            color: '#e5e7eb',\n            padding: [0, 0, 5, 0],\n            fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif'\n          },\n          axisLabel: {\n            fontSize: 13,\n            fontWeight: 'bold',\n            color: '#e5e7eb',\n            fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif'\n          }\n        },\n        series: [\n          {\n            name: '车辆数',\n            type: 'bar',\n            data: data,\n            barWidth: '40%',\n            itemStyle: {\n              color: '#5470c6'\n            },\n            label: {\n              show: true,\n              position: 'top',\n              fontSize: 14,\n              fontWeight: 'bold',\n              color: '#ffffff',\n              fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif'\n            }\n          }\n        ]\n      };\n    });\n    \n    // 添加十字路口分析相关的图表选项\n    const horizontalFlowOption = computed(() => {\n      if (!props.result || !props.result.horizontal_data || !props.result.horizontal_data.time_series_data) {\n        return {};\n      }\n      \n      const timeData = props.result.horizontal_data.time_series_data.timestamps.map(formatTime);\n      const countData = props.result.horizontal_data.time_series_data.vehicle_counts;\n      \n      return {\n        title: {\n          text: '东西方向车流量'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: timeData,\n          axisLabel: {\n            rotate: 45\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数'\n        },\n        series: [\n          {\n            name: '车辆数',\n            type: 'line',\n            data: countData,\n            smooth: true,\n            itemStyle: {\n              color: '#409EFF'\n            }\n          }\n        ]\n      };\n    });\n    \n    const verticalFlowOption = computed(() => {\n      if (!props.result || !props.result.vertical_data || !props.result.vertical_data.time_series_data) {\n        return {};\n      }\n      \n      const timeData = props.result.vertical_data.time_series_data.timestamps.map(formatTime);\n      const countData = props.result.vertical_data.time_series_data.vehicle_counts;\n      \n      return {\n        title: {\n          text: '南北方向车流量'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: timeData,\n          axisLabel: {\n            rotate: 45\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数'\n        },\n        series: [\n          {\n            name: '车辆数',\n            type: 'line',\n            data: countData,\n            smooth: true,\n            itemStyle: {\n              color: '#67C23A'\n            }\n          }\n        ]\n      };\n    });\n    \n    const combinedFlowOption = computed(() => {\n      if (!props.result || !props.result.horizontal_data || !props.result.vertical_data) {\n        return {};\n      }\n      \n      // 获取横向数据\n      const horizontalTimeData = props.result.horizontal_data.time_series_data?.timestamps || [];\n      const horizontalCountData = props.result.horizontal_data.time_series_data?.vehicle_counts || [];\n      \n      // 获取纵向数据\n      const verticalTimeData = props.result.vertical_data.time_series_data?.timestamps || [];\n      const verticalCountData = props.result.vertical_data.time_series_data?.vehicle_counts || [];\n      \n      // 合并时间数据，确保时间点一致\n      const timePoints = [...new Set([...horizontalTimeData, ...verticalTimeData])].sort((a, b) => a - b);\n      const formattedTimePoints = timePoints.map(formatTime);\n      \n      // 为时间点找到对应的数据，如果没有则为0\n      const getCountAtTime = (times, counts, timePoint) => {\n        const index = times.indexOf(timePoint);\n        return index !== -1 ? counts[index] : 0;\n      };\n      \n      // 为每个时间点构建横向和纵向数据\n      const horizontalData = timePoints.map(t => getCountAtTime(horizontalTimeData, horizontalCountData, t));\n      const verticalData = timePoints.map(t => getCountAtTime(verticalTimeData, verticalCountData, t));\n      \n      return {\n        title: {\n          text: '方向车流量对比'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: ['东西方向', '南北方向']\n        },\n        xAxis: {\n          type: 'category',\n          data: formattedTimePoints,\n          axisLabel: {\n            rotate: 45\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '车辆数'\n        },\n        series: [\n          {\n            name: '东西方向',\n            type: 'line',\n            data: horizontalData,\n            smooth: true,\n            itemStyle: {\n              color: '#409EFF'\n            }\n          },\n          {\n            name: '南北方向',\n            type: 'line',\n            data: verticalData,\n            smooth: true,\n            itemStyle: {\n              color: '#67C23A'\n            }\n          }\n        ]\n      };\n    });\n    \n    // 初始化图表\n    const initCharts = async () => {\n      // 清除旧的图表实例\n      disposeCharts();\n      \n      // 如果结果为空或页面不可见，取消初始化\n      if (!props.result || document.hidden) {\n        return;\n      }\n      \n      // 使用Promise.all等待所有图表初始化完成\n      await new Promise(resolve => setTimeout(resolve, 200)); // 等待200ms确保DOM布局完成\n      \n      // 定义全局字体设置 - 修改字体族\n      const textStyle = {\n        fontSize: 14,\n        fontWeight: 'bold',\n        fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n        color: '#ffffff'\n      };\n\n      // 坐标轴标签样式 - 使用更好的中文字体\n      const axisLabelStyle = {\n        fontSize: 13,\n        fontWeight: 'bold',\n        color: '#e5e7eb',\n        fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif'\n      };\n\n      // 轴线样式\n      const axisLineStyle = {\n        lineStyle: {\n          color: 'rgba(255, 255, 255, 0.2)'\n        }\n      };\n      \n      // 更安全的图表初始化函数\n      const safeInitChart = (element, option, name) => {\n        return new Promise((resolve) => {\n          if (!element) {\n            console.log(`图表DOM引用不存在: ${name}`);\n            resolve(null);\n            return;\n          }\n          \n          // 验证DOM元素已渲染且可见\n          if (element.offsetWidth <= 0 || element.offsetHeight <= 0) {\n            console.log(`图表DOM元素尺寸无效: ${name} (${element.offsetWidth}x${element.offsetHeight})`);\n            resolve(null);\n            return;\n          }\n          \n          try {\n            console.log(`初始化图表: ${name} (${element.offsetWidth}x${element.offsetHeight})`);\n            \n            // 设置设备像素比，提高清晰度\n            const devicePixelRatio = window.devicePixelRatio || 2;\n            \n            // 应用全局字体设置\n            option.textStyle = textStyle;\n            \n            // 增强轴标签清晰度\n            if (option.xAxis) {\n              if (Array.isArray(option.xAxis)) {\n                option.xAxis.forEach(axis => {\n                  axis.axisLabel = { \n                    ...axis.axisLabel, \n                    ...axisLabelStyle,\n                    fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                    fontSize: 13,\n                    fontWeight: 'bold',\n                    textShadow: '0 0 2px rgba(0,0,0,0.3)',\n                    rich: {\n                      a: {\n                        fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                        fontWeight: 'bold'\n                      }\n                    }\n                  };\n                  axis.axisLine = { ...axis.axisLine, ...axisLineStyle };\n                  // 添加轴名称样式\n                  if (axis.name) {\n                    axis.nameTextStyle = {\n                      fontSize: 14,\n                      fontWeight: 'bold',\n                      color: '#e5e7eb',\n                      padding: [5, 0, 0, 0],\n                      fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                      textShadow: '0 0 2px rgba(0,0,0,0.3)'\n                    };\n                  }\n                });\n              } else {\n                option.xAxis.axisLabel = { \n                  ...option.xAxis.axisLabel, \n                  ...axisLabelStyle,\n                  fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                  fontSize: 13, \n                  fontWeight: 'bold',\n                  textShadow: '0 0 2px rgba(0,0,0,0.3)',\n                  rich: {\n                    a: {\n                      fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                      fontWeight: 'bold'\n                    }\n                  }\n                };\n                option.xAxis.axisLine = { ...option.xAxis.axisLine, ...axisLineStyle };\n                // 添加轴名称样式\n                if (option.xAxis.name) {\n                  option.xAxis.nameTextStyle = {\n                    fontSize: 14,\n                    fontWeight: 'bold',\n                    color: '#e5e7eb',\n                    padding: [5, 0, 0, 0],\n                    fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                    textShadow: '0 0 2px rgba(0,0,0,0.3)'\n                  };\n                }\n              }\n            }\n            \n            if (option.yAxis) {\n              if (Array.isArray(option.yAxis)) {\n                option.yAxis.forEach(axis => {\n                  axis.axisLabel = { \n                    ...axis.axisLabel, \n                    ...axisLabelStyle,\n                    fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                    fontSize: 13,\n                    fontWeight: 'bold',\n                    textShadow: '0 0 2px rgba(0,0,0,0.3)',\n                    rich: {\n                      a: {\n                        fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                        fontWeight: 'bold'\n                      }\n                    }\n                  };\n                  axis.axisLine = { ...axis.axisLine, ...axisLineStyle };\n                  // 添加轴名称样式\n                  if (axis.name) {\n                    axis.nameTextStyle = {\n                      fontSize: 14,\n                      fontWeight: 'bold',\n                      color: '#e5e7eb',\n                      padding: [0, 0, 5, 0],\n                      fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                      textShadow: '0 0 2px rgba(0,0,0,0.3)'\n                    };\n                  }\n                });\n              } else {\n                option.yAxis.axisLabel = { \n                  ...option.yAxis.axisLabel, \n                  ...axisLabelStyle,\n                  fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                  fontSize: 13,\n                  fontWeight: 'bold',\n                  textShadow: '0 0 2px rgba(0,0,0,0.3)',\n                  rich: {\n                    a: {\n                      fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                      fontWeight: 'bold'\n                    }\n                  }\n                };\n                option.yAxis.axisLine = { ...option.yAxis.axisLine, ...axisLineStyle };\n                // 添加轴名称样式\n                if (option.yAxis.name) {\n                  option.yAxis.nameTextStyle = {\n                    fontSize: 14,\n                    fontWeight: 'bold',\n                    color: '#e5e7eb',\n                    padding: [0, 0, 5, 0],\n                    fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                    textShadow: '0 0 2px rgba(0,0,0,0.3)'\n                  };\n                }\n              }\n            }\n            \n            // 增强标题清晰度\n            if (option.title) {\n              option.title.textStyle = {\n                fontSize: 16,\n                fontWeight: 'bold',\n                color: '#ffffff',\n                fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                textShadow: '0 0 2px rgba(0,0,0,0.3)'\n              };\n            }\n            \n            // 增强提示框清晰度\n            if (option.tooltip) {\n              option.tooltip.textStyle = {\n                fontSize: 14,\n                fontWeight: 'normal',\n                fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif'\n              };\n            }\n            \n            // 增强图例清晰度\n            if (option.legend) {\n              option.legend.textStyle = {\n                fontSize: 13,\n                fontWeight: 'bold',\n                color: '#e5e7eb',\n                fontFamily: '\"Microsoft YaHei\", \"PingFang SC\", \"Hiragino Sans GB\", sans-serif',\n                textShadow: '0 0 2px rgba(0,0,0,0.3)'\n              };\n            }\n            \n            const instance = echarts.init(element, null, {\n              devicePixelRatio: devicePixelRatio,\n              renderer: 'canvas'  // 显式指定渲染器\n            });\n            instance.setOption(option);\n            resolve(instance);\n          } catch (err) {\n            console.error(`初始化图表失败 ${name}:`, err);\n            resolve(null);\n          }\n        });\n      };\n      \n      const chartsToInit = [];\n      \n      // 初始化东西方向车流量图\n      if (horizontalFlowChart.value && props.result?.horizontal_data) {\n        chartsToInit.push(\n          safeInitChart(horizontalFlowChart.value, horizontalFlowOption.value, \"东西方向车流量\")\n            .then(instance => { if (instance) horizontalFlowChartInstance = instance; })\n        );\n      }\n      \n      // 初始化南北方向车流量图\n      if (verticalFlowChart.value && props.result?.vertical_data) {\n        chartsToInit.push(\n          safeInitChart(verticalFlowChart.value, verticalFlowOption.value, \"南北方向车流量\")\n            .then(instance => { if (instance) verticalFlowChartInstance = instance; })\n        );\n      }\n      \n      // 初始化方向车流量对比图\n      if (combinedFlowChart.value && props.result?.horizontal_data && props.result?.vertical_data) {\n        chartsToInit.push(\n          safeInitChart(combinedFlowChart.value, combinedFlowOption.value, \"方向车流量对比\")\n            .then(instance => { if (instance) combinedFlowChartInstance = instance; })\n        );\n      }\n      \n      // 初始化车流量时间分布图\n      if (lineChart.value && props.result?.time_series_data) {\n        chartsToInit.push(\n          safeInitChart(lineChart.value, lineChartOption.value, \"车流量时间趋势\")\n            .then(instance => { if (instance) lineChartInstance = instance; })\n        );\n      }\n      \n      // 初始化车型分布图\n      if (pieChart.value && props.result?.vehicle_type_stats) {\n        chartsToInit.push(\n          safeInitChart(pieChart.value, pieChartOption.value, \"车型分布\")\n            .then(instance => { if (instance) pieChartInstance = instance; })\n        );\n      }\n      \n      // 初始化方向分布图\n      if (barChart.value && props.result?.analysis_summary?.direction_distribution) {\n        chartsToInit.push(\n          safeInitChart(barChart.value, barChartOption.value, \"方向分布\")\n            .then(instance => { if (instance) barChartInstance = instance; })\n        );\n      }\n      \n      // 等待所有图表初始化完成\n      try {\n        await Promise.all(chartsToInit);\n        console.log('所有图表初始化完成');\n      } catch (err) {\n        console.error('图表初始化过程中出错:', err);\n      }\n    };\n    \n    // 清除图表实例\n    const disposeCharts = () => {\n      horizontalFlowChartInstance && horizontalFlowChartInstance.dispose();\n      verticalFlowChartInstance && verticalFlowChartInstance.dispose();\n      combinedFlowChartInstance && combinedFlowChartInstance.dispose();\n      lineChartInstance && lineChartInstance.dispose();\n      pieChartInstance && pieChartInstance.dispose();\n      barChartInstance && barChartInstance.dispose();\n      \n      horizontalFlowChartInstance = null;\n      verticalFlowChartInstance = null;\n      combinedFlowChartInstance = null;\n      lineChartInstance = null;\n      pieChartInstance = null;\n      barChartInstance = null;\n    };\n    \n    // 导出图表为图片\n    const exportChart = (chartInstance, fileName) => {\n      if (!chartInstance) return;\n      \n      try {\n        const dataURL = chartInstance.getDataURL({\n          type: 'png',\n          pixelRatio: 2,\n          backgroundColor: '#fff'\n        });\n        \n        const link = document.createElement('a');\n        link.download = fileName;\n        link.href = dataURL;\n        \n        // 添加到DOM并触发下载\n        document.body.appendChild(link);\n        link.click();\n        \n        // 清理DOM元素\n        setTimeout(() => {\n          if (document.body.contains(link)) {\n            document.body.removeChild(link);\n          }\n        }, 100);\n      } catch (err) {\n        ElMessage.error('导出图表失败: ' + err.message);\n      }\n    };\n    \n    // 导出所有图表\n    const exportAllCharts = () => {\n      if (lineChartInstance) {\n        exportChart(lineChartInstance, '车流量时间趋势.png');\n      }\n      \n      if (pieChartInstance) {\n        exportChart(pieChartInstance, '车型分布.png');\n      }\n      \n      if (barChartInstance) {\n        exportChart(barChartInstance, '方向分布.png');\n      }\n      \n      if (props.result?.mode === 'intersection') {\n        if (horizontalFlowChartInstance) {\n          exportChart(horizontalFlowChartInstance, '东西方向车流量.png');\n        }\n        \n        if (verticalFlowChartInstance) {\n          exportChart(verticalFlowChartInstance, '南北方向车流量.png');\n        }\n        \n        if (combinedFlowChartInstance) {\n          exportChart(combinedFlowChartInstance, '方向车流量对比.png');\n        }\n      }\n      \n      ElMessage.success('图表导出完成');\n    };\n    \n    // 监听窗口大小变化，重新调整图表大小\n    const handleResize = () => {\n      // 对每个图表实例进行空检查并重绘\n      if (horizontalFlowChartInstance) {\n        try {\n          horizontalFlowChartInstance.resize();\n        } catch (err) {}\n      }\n      \n      if (verticalFlowChartInstance) {\n        try {\n          verticalFlowChartInstance.resize();\n        } catch (err) {}\n      }\n      \n      if (combinedFlowChartInstance) {\n        try {\n          combinedFlowChartInstance.resize();\n        } catch (err) {}\n      }\n      \n      if (lineChartInstance) {\n        try {\n          lineChartInstance.resize();\n        } catch (err) {}\n      }\n      \n      if (pieChartInstance) {\n        try {\n          pieChartInstance.resize();\n        } catch (err) {}\n      }\n      \n      if (barChartInstance) {\n        try {\n          barChartInstance.resize();\n        } catch (err) {}\n      }\n    };\n    \n    // 监听结果变化\n    watch(() => props.result, () => {\n      // 当结果发生变化时，重新初始化图表\n      nextTick(() => {\n        setTimeout(() => {\n          initCharts();\n        }, 300);\n      });\n    }, { deep: true });\n    \n    onMounted(() => {\n      // 延迟初始化图表，确保DOM已完全渲染\n      nextTick(() => {\n        setTimeout(() => {\n          initCharts();\n        }, 300);\n      });\n      \n      // 添加窗口大小变化监听\n      window.addEventListener('resize', handleResize);\n    });\n    \n    return {\n      analysisSummary,\n      isIntersection,\n      vehicleCount,\n      processingTime,\n      vehicleTypeStats,\n      horizontalFlowChart,\n      verticalFlowChart,\n      combinedFlowChart,\n      lineChart,\n      pieChart,\n      barChart,\n      formatTime,\n      getCongestionLevel,\n      getSolutionTitle,\n      getSolutionDescription,\n      getSolutionAlertType,\n      exportAllCharts,\n      initCharts,\n      disposeCharts\n    };\n  },\n  beforeUnmount() {\n    // 卸载组件前清理资源\n    this.disposeCharts();\n    window.removeEventListener('resize', this.handleResize);\n  }\n};\n</script>\n\n<style scoped>\n.video-analytics-panel {\n  width: 100%;\n}\n\n.summary-cards {\n  margin-top: 20px;\n}\n\n.stat-card {\n  text-align: center;\n  background: rgba(255, 255, 255, 0.05) !important;\n  border: 1px solid rgba(255, 255, 255, 0.06) !important;\n}\n\n:deep(.stat-card .el-card__header) {\n  background-color: rgba(26, 32, 50, 0.8);\n}\n\n.card-header {\n  font-weight: bold;\n  color: #ffffff;\n}\n\n.card-value {\n  font-size: 28px !important;\n  font-weight: 700 !important;\n  color: #6366f1 !important;\n  padding: 20px 0 !important;\n}\n\n.section-title {\n  margin-top: 30px;\n  margin-bottom: 15px;\n  color: #ffffff;\n  font-weight: 600;\n}\n\n.vehicle-type-cards {\n  margin-bottom: 30px;\n}\n\n.vehicle-type-card {\n  text-align: center;\n  background: rgba(255, 255, 255, 0.05) !important;\n  border: 1px solid rgba(255, 255, 255, 0.06) !important;\n  padding: 15px 0;\n  transition: all 0.3s ease;\n  height: 100%;\n}\n\n.vehicle-type-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n}\n\n.vehicle-icon {\n  font-size: 30px;\n  margin-bottom: 10px;\n}\n\n.vehicle-count {\n  font-size: 24px;\n  font-weight: 700;\n  color: #6366f1;\n  margin-bottom: 5px;\n}\n\n.vehicle-label {\n  color: #d1d5db;\n  font-size: 14px;\n}\n\n.chart-container {\n  height: 350px;\n  margin-top: 20px;\n  margin-bottom: 30px;\n  background: rgba(17, 24, 39, 0.5);\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.chart-mini-container {\n  height: 280px;\n  margin-top: 15px;\n  margin-bottom: 15px;\n  background: rgba(17, 24, 39, 0.5);\n  border-radius: 8px;\n  padding: 10px;\n}\n\n.traffic-flow-card,\n.traffic-comparison-card {\n  padding: 15px;\n  background-color: rgba(17, 24, 39, 0.5);\n  border-radius: 8px;\n}\n\n.traffic-comparison-card {\n  padding: 20px;\n}\n\n.traffic-flow-card h4,\n.traffic-comparison-card h4 {\n  color: #e5e7eb;\n  margin-bottom: 10px;\n}\n\nh3 {\n  color: #ffffff !important;\n  font-weight: 600 !important;\n  margin-bottom: 15px;\n}\n\n/* 增大车流量趋势标题 */\nh3.traffic-trend-title {\n  font-size: 24px !important;\n  margin-top: 30px !important;\n  margin-bottom: 20px !important;\n  font-weight: 700 !important;\n}\n\n.charts-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-bottom: 15px;\n}\n\n.solution-section {\n  margin-top: 30px;\n  margin-bottom: 30px;\n}\n\n.solution-alert {\n  background: rgba(17, 24, 39, 0.5) !important;\n  border: 1px solid rgba(255, 255, 255, 0.06) !important;\n  margin-top: 15px;\n}\n\n:deep(.solution-alert .el-alert__title) {\n  font-size: 16px !important;\n  font-weight: 600 !important;\n}\n\n:deep(.solution-alert .el-alert__description) {\n  font-size: 14px !important;\n  margin-top: 8px !important;\n}\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EADpCC,GAAA;EAESD,KAAK,EAAC;;;EASEA,KAAK,EAAC;AAAY;;EAYlBA,KAAK,EAAC;AAAY;;EAYlBA,KAAK,EAAC;AAAY;;EAalBA,KAAK,EAAC;AAAe;;EAOrBA,KAAK,EAAC;AAAe;;EAOrBA,KAAK,EAAC;AAAe;;EAOrBA,KAAK,EAAC;AAAe;;EAOrBA,KAAK,EAAC;AAAe;;EAQ3BA,KAAK,EAAC;AAAgB;;EAMtBA,KAAK,EAAC;AAAmB;;EAGnBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAsB;;EAC1BE,GAAG,EAAC,qBAAqB;EAACC,KAAmC,EAAnC;IAAA;IAAA;EAAA;;;EAK9BH,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAsB;;EAC1BE,GAAG,EAAC,mBAAmB;EAACC,KAAmC,EAAnC;IAAA;IAAA;EAAA;;;EAQ5BH,KAAK,EAAC;AAAyB;;EAE7BA,KAAK,EAAC;AAAiB;;EACrBE,GAAG,EAAC,mBAAmB;EAACC,KAAmC,EAAnC;IAAA;IAAA;EAAA;;;EAWpCH,KAAK,EAAC;AAAiB;;EACrBE,GAAG,EAAC,WAAW;EAACC,KAAmC,EAAnC;IAAA;IAAA;EAAA;;;EAQdH,KAAK,EAAC;AAAiB;;EACrBE,GAAG,EAAC,UAAU;EAACC,KAAmC,EAAnC;IAAA;IAAA;EAAA;;;EAKjBH,KAAK,EAAC;AAAiB;;EACrBE,GAAG,EAAC,UAAU;EAACC,KAAmC,EAAnC;IAAA;IAAA;EAAA;;;EA9I9BF,GAAA;EAoJSD,KAAK,EAAC;;;;;;;;;;;uBAnJbI,mBAAA,CA8JM,OA9JNC,UA8JM,GA7J6BC,MAAA,CAAAC,eAAe,I,cAAhDH,mBAAA,CA+EM,OA/ENI,UA+EM,GA9EJC,YAAA,CAqCSC,iBAAA;IArCAC,MAAM,EAAE;EAAE;IAHzBC,OAAA,EAAAC,QAAA,CAIQ,MAWS,CAXTJ,YAAA,CAWSK,iBAAA;MAXAC,IAAI,EAAE;IAAC;MAJxBH,OAAA,EAAAC,QAAA,CAKU,MASU,CATVJ,YAAA,CASUO,kBAAA;QATDhB,KAAK,EAAC;MAAW;QACbiB,MAAM,EAAAJ,QAAA,CACf,MAEMK,MAAA,QAAAA,MAAA,OAFNC,mBAAA,CAEM;UAFDnB,KAAK,EAAC;QAAa,IACtBmB,mBAAA,CAAiB,cAAX,MAAI,E;QAR1BP,OAAA,EAAAC,QAAA,CAWY,MAEM,CAFNM,mBAAA,CAEM,OAFNC,UAEM,EAAAC,gBAAA,CADDf,MAAA,CAAAgB,YAAY,sB;QAZ7BC,CAAA;;MAAAA,CAAA;QAgBQd,YAAA,CAWSK,iBAAA;MAXAC,IAAI,EAAE;IAAC;MAhBxBH,OAAA,EAAAC,QAAA,CAiBU,MASU,CATVJ,YAAA,CASUO,kBAAA;QATDhB,KAAK,EAAC;MAAW;QACbiB,MAAM,EAAAJ,QAAA,CACf,MAEMK,MAAA,QAAAA,MAAA,OAFNC,mBAAA,CAEM;UAFDnB,KAAK,EAAC;QAAa,IACtBmB,mBAAA,CAAiB,cAAX,MAAI,E;QApB1BP,OAAA,EAAAC,QAAA,CAuBY,MAEM,CAFNM,mBAAA,CAEM,OAFNK,UAEM,EAAAH,gBAAA,CADDf,MAAA,CAAAmB,cAAc,GAAGnB,MAAA,CAAAmB,cAAc,CAACC,OAAO,oC;QAxBxDH,CAAA;;MAAAA,CAAA;QA4BQd,YAAA,CAWSK,iBAAA;MAXAC,IAAI,EAAE;IAAC;MA5BxBH,OAAA,EAAAC,QAAA,CA6BU,MASU,CATVJ,YAAA,CASUO,kBAAA;QATDhB,KAAK,EAAC;MAAW;QACbiB,MAAM,EAAAJ,QAAA,CACf,MAEMK,MAAA,QAAAA,MAAA,OAFNC,mBAAA,CAEM;UAFDnB,KAAK,EAAC;QAAa,IACtBmB,mBAAA,CAAiB,cAAX,MAAI,E;QAhC1BP,OAAA,EAAAC,QAAA,CAmCY,MAEM,CAFNM,mBAAA,CAEM,OAFNQ,UAEM,EAAAN,gBAAA,CADDf,MAAA,CAAAsB,kBAAkB,CAACtB,MAAA,CAAAgB,YAAY,kB;QApChDC,CAAA;;MAAAA,CAAA;;IAAAA,CAAA;MA0CMM,mBAAA,gBAAmB,E,4BACnBV,mBAAA,CAAqC;IAAjCnB,KAAK,EAAC;EAAe,GAAC,QAAM,sBAChCS,YAAA,CAoCSC,iBAAA;IApCAC,MAAM,EAAE,EAAE;IAAEX,KAAK,EAAC;;IA5CjCY,OAAA,EAAAC,QAAA,CA8CU,MASS,CAVaP,MAAA,CAAAwB,gBAAgB,IAAIxB,MAAA,CAAAwB,gBAAgB,CAACC,GAAG,KAAKC,SAAS,I,cAA9EC,YAAA,CAMSnB,iBAAA;MAnDjBb,GAAA;MA6CiBc,IAAI,EAAE;;MA7CvBH,OAAA,EAAAC,QAAA,CA8CU,MAIU,CAJVJ,YAAA,CAIUO,kBAAA;QAJDhB,KAAK,EAAC;MAAmB;QA9C5CY,OAAA,EAAAC,QAAA,CA+CY,MAAkC,C,0BAAlCM,mBAAA,CAAkC;UAA7BnB,KAAK,EAAC;QAAc,GAAC,IAAE,sBAC5BmB,mBAAA,CAA2D,OAA3De,UAA2D,EAAAb,gBAAA,CAA7Bf,MAAA,CAAAwB,gBAAgB,CAACC,GAAG,kB,0BAClDZ,mBAAA,CAAoC;UAA/BnB,KAAK,EAAC;QAAe,GAAC,KAAG,qB;QAjD1CuB,CAAA;;MAAAA,CAAA;UAAAM,mBAAA,gBAoDgCvB,MAAA,CAAAwB,gBAAgB,IAAIxB,MAAA,CAAAwB,gBAAgB,CAACK,UAAU,KAAKH,SAAS,I,cAArFC,YAAA,CAMSnB,iBAAA;MA1DjBb,GAAA;MAoDiBc,IAAI,EAAE;;MApDvBH,OAAA,EAAAC,QAAA,CAqDU,MAIU,CAJVJ,YAAA,CAIUO,kBAAA;QAJDhB,KAAK,EAAC;MAAmB;QArD5CY,OAAA,EAAAC,QAAA,CAsDY,MAAmC,C,0BAAnCM,mBAAA,CAAmC;UAA9BnB,KAAK,EAAC;QAAc,GAAC,KAAG,sBAC7BmB,mBAAA,CAAkE,OAAlEiB,UAAkE,EAAAf,gBAAA,CAApCf,MAAA,CAAAwB,gBAAgB,CAACK,UAAU,kB,0BACzDhB,mBAAA,CAAoC;UAA/BnB,KAAK,EAAC;QAAe,GAAC,KAAG,qB;QAxD1CuB,CAAA;;MAAAA,CAAA;UAAAM,mBAAA,gBA2DgCvB,MAAA,CAAAwB,gBAAgB,IAAIxB,MAAA,CAAAwB,gBAAgB,CAACO,KAAK,KAAKL,SAAS,I,cAAhFC,YAAA,CAMSnB,iBAAA;MAjEjBb,GAAA;MA2DiBc,IAAI,EAAE;;MA3DvBH,OAAA,EAAAC,QAAA,CA4DU,MAIU,CAJVJ,YAAA,CAIUO,kBAAA;QAJDhB,KAAK,EAAC;MAAmB;QA5D5CY,OAAA,EAAAC,QAAA,CA6DY,MAAkC,C,0BAAlCM,mBAAA,CAAkC;UAA7BnB,KAAK,EAAC;QAAc,GAAC,IAAE,sBAC5BmB,mBAAA,CAA6D,OAA7DmB,UAA6D,EAAAjB,gBAAA,CAA/Bf,MAAA,CAAAwB,gBAAgB,CAACO,KAAK,kB,0BACpDlB,mBAAA,CAAmC;UAA9BnB,KAAK,EAAC;QAAe,GAAC,IAAE,qB;QA/DzCuB,CAAA;;MAAAA,CAAA;UAAAM,mBAAA,gBAkEgCvB,MAAA,CAAAwB,gBAAgB,IAAIxB,MAAA,CAAAwB,gBAAgB,CAACS,GAAG,KAAKP,SAAS,I,cAA9EC,YAAA,CAMSnB,iBAAA;MAxEjBb,GAAA;MAkEiBc,IAAI,EAAE;;MAlEvBH,OAAA,EAAAC,QAAA,CAmEU,MAIU,CAJVJ,YAAA,CAIUO,kBAAA;QAJDhB,KAAK,EAAC;MAAmB;QAnE5CY,OAAA,EAAAC,QAAA,CAoEY,MAAkC,C,0BAAlCM,mBAAA,CAAkC;UAA7BnB,KAAK,EAAC;QAAc,GAAC,IAAE,sBAC5BmB,mBAAA,CAA2D,OAA3DqB,UAA2D,EAAAnB,gBAAA,CAA7Bf,MAAA,CAAAwB,gBAAgB,CAACS,GAAG,kB,4BAClDpB,mBAAA,CAAoC;UAA/BnB,KAAK,EAAC;QAAe,GAAC,KAAG,qB;QAtE1CuB,CAAA;;MAAAA,CAAA;UAAAM,mBAAA,gBAyEgCvB,MAAA,CAAAwB,gBAAgB,IAAIxB,MAAA,CAAAwB,gBAAgB,CAACW,OAAO,KAAKT,SAAS,I,cAAlFC,YAAA,CAMSnB,iBAAA;MA/EjBb,GAAA;MAyEiBc,IAAI,EAAE;;MAzEvBH,OAAA,EAAAC,QAAA,CA0EU,MAIU,CAJVJ,YAAA,CAIUO,kBAAA;QAJDhB,KAAK,EAAC;MAAmB;QA1E5CY,OAAA,EAAAC,QAAA,CA2EY,MAAkC,C,4BAAlCM,mBAAA,CAAkC;UAA7BnB,KAAK,EAAC;QAAc,GAAC,IAAE,sBAC5BmB,mBAAA,CAA+D,OAA/DuB,WAA+D,EAAArB,gBAAA,CAAjCf,MAAA,CAAAwB,gBAAgB,CAACW,OAAO,kB,4BACtDtB,mBAAA,CAAoC;UAA/BnB,KAAK,EAAC;QAAe,GAAC,KAAG,qB;QA7E1CuB,CAAA;;MAAAA,CAAA;UAAAM,mBAAA,e;IAAAN,CAAA;UAAAM,mBAAA,gBAmFoBvB,MAAA,CAAAqC,cAAc,I,cAA9BvC,mBAAA,CAwCWwC,SAAA;IA3Hf3C,GAAA;EAAA,IAoFMkB,mBAAA,CAIM,OAJN0B,WAIM,GAHJpC,YAAA,CAEYqC,oBAAA;IAFDC,IAAI,EAAC,OAAO;IAAEC,OAAK,EAAE1C,MAAA,CAAA2C,eAAe;IAAEC,IAAI,EAAC;;IArF9DtC,OAAA,EAAAC,QAAA,CAsFU,MAA+B,CAA/BJ,YAAA,CAA+B0C,kBAAA;MAtFzCvC,OAAA,EAAAC,QAAA,CAsFmB,MAAY,CAAZJ,YAAA,CAAY2C,mBAAA,E;MAtF/B7B,CAAA;oCAAA8B,gBAAA,CAsFyC,UACjC,G;IAvFR9B,CAAA;gEAyFMJ,mBAAA,CAAiB,YAAb,UAAQ,sBACZA,mBAAA,CA8BM,OA9BNmC,WA8BM,GA7BJ7C,YAAA,CAiBSC,iBAAA;IAjBAC,MAAM,EAAE;EAAE;IA3F3BC,OAAA,EAAAC,QAAA,CA4FU,MAOS,CAPTJ,YAAA,CAOSK,iBAAA;MAPAC,IAAI,EAAE;IAAE;MA5F3BH,OAAA,EAAAC,QAAA,CA6FY,MAKM,CALNM,mBAAA,CAKM,OALNoC,WAKM,G,4BAJJpC,mBAAA,CAAgB,YAAZ,SAAO,sBACXA,mBAAA,CAEM,OAFNqC,WAEM,GADJrC,mBAAA,CAAyE,OAAzEsC,WAAyE,8B;MAhGzFlC,CAAA;QAoGUd,YAAA,CAOSK,iBAAA;MAPAC,IAAI,EAAE;IAAE;MApG3BH,OAAA,EAAAC,QAAA,CAqGY,MAKM,CALNM,mBAAA,CAKM,OALNuC,WAKM,G,4BAJJvC,mBAAA,CAAgB,YAAZ,SAAO,sBACXA,mBAAA,CAEM,OAFNwC,WAEM,GADJxC,mBAAA,CAAuE,OAAvEyC,WAAuE,8B;MAxGvFrC,CAAA;;IAAAA,CAAA;MA8GQd,YAAA,CASSC,iBAAA;IATDP,KAAwB,EAAxB;MAAA;IAAA;EAAwB;IA9GxCS,OAAA,EAAAC,QAAA,CA+GU,MAOS,CAPTJ,YAAA,CAOSK,iBAAA;MAPAC,IAAI,EAAE;IAAE;MA/G3BH,OAAA,EAAAC,QAAA,CAgHY,MAKM,CALNM,mBAAA,CAKM,OALN0C,WAKM,G,4BAJJ1C,mBAAA,CAAgB,YAAZ,SAAO,sBACXA,mBAAA,CAEM,OAFN2C,WAEM,GADJ3C,mBAAA,CAAuE,OAAvE4C,WAAuE,8B;MAnHvFxC,CAAA;;IAAAA,CAAA;QA0HMd,YAAA,CAAyBuD,qBAAA,E,+BA1H/BnC,mBAAA,gB,4BA6HIV,mBAAA,CAA0C;IAAtCnB,KAAK,EAAC;EAAqB,GAAC,OAAK,sBACrCmB,mBAAA,CAEM,OAFN8C,WAEM,GADJ9C,mBAAA,CAA+D,OAA/D+C,WAA+D,8B,GAGjEzD,YAAA,CAAyBuD,qBAAA,GAEzBvD,YAAA,CAaSC,iBAAA;IAbAC,MAAM,EAAE;EAAE;IApIvBC,OAAA,EAAAC,QAAA,CAqIM,MAKS,CALTJ,YAAA,CAKSK,iBAAA;MALAqD,EAAE,EAAE;IAAE;MArIrBvD,OAAA,EAAAC,QAAA,CAsIQ,MAAa,C,4BAAbM,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAEM,OAFNiD,WAEM,GADJjD,mBAAA,CAA8D,OAA9DkD,WAA8D,8B;MAxIxE9C,CAAA;QA2IMd,YAAA,CAKSK,iBAAA;MALAqD,EAAE,EAAE;IAAE;MA3IrBvD,OAAA,EAAAC,QAAA,CA4IQ,MAAa,C,4BAAbM,mBAAA,CAAa,YAAT,MAAI,sBACRA,mBAAA,CAEM,OAFNmD,WAEM,GADJnD,mBAAA,CAA8D,OAA9DoD,WAA8D,8B;MA9IxEhD,CAAA;;IAAAA,CAAA;MAmJIM,mBAAA,gBAAmB,EACiBvB,MAAA,CAAAgB,YAAY,Q,cAAhDlB,mBAAA,CAUM,OAVNoE,WAUM,G,4BATJrD,mBAAA,CAAe,YAAX,QAAM,sBACVV,YAAA,CAOYgE,mBAAA;IANTvB,IAAI,EAAE5C,MAAA,CAAAoE,oBAAoB,CAACpE,MAAA,CAAAgB,YAAY;IACvCqD,KAAK,EAAErE,MAAA,CAAAsE,gBAAgB,CAACtE,MAAA,CAAAgB,YAAY;IACpCuD,WAAW,EAAEvE,MAAA,CAAAwE,sBAAsB,CAACxE,MAAA,CAAAgB,YAAY;IACjD,WAAS,EAAT,EAAS;IACRyD,QAAQ,EAAE,KAAK;IAChB/E,KAAK,EAAC;iEA5Jd6B,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}