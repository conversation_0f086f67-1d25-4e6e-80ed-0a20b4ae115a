package com.traffic.analysis.exception;

import lombok.Getter;

/**
 * 资源未找到异常
 */
@Getter
public class ResourceNotFoundException extends RuntimeException {
    
    private final String resourceType;
    private final String resourceId;
    private final String errorCode;
    
    public ResourceNotFoundException(String message) {
        super(message);
        this.resourceType = null;
        this.resourceId = null;
        this.errorCode = "RESOURCE_NOT_FOUND";
    }
    
    public ResourceNotFoundException(String resourceType, String resourceId) {
        super(String.format("%s未找到: %s", resourceType, resourceId));
        this.resourceType = resourceType;
        this.resourceId = resourceId;
        this.errorCode = "RESOURCE_NOT_FOUND";
    }
    
    public ResourceNotFoundException(String message, String resourceType, String resourceId) {
        super(message);
        this.resourceType = resourceType;
        this.resourceId = resourceId;
        this.errorCode = "RESOURCE_NOT_FOUND";
    }
    
    public ResourceNotFoundException(String message, String resourceType, String resourceId, String errorCode) {
        super(message);
        this.resourceType = resourceType;
        this.resourceId = resourceId;
        this.errorCode = errorCode;
    }
    
    public ResourceNotFoundException(String message, Throwable cause) {
        super(message, cause);
        this.resourceType = null;
        this.resourceId = null;
        this.errorCode = "RESOURCE_NOT_FOUND";
    }
    
    // 静态工厂方法
    public static ResourceNotFoundException user(String userId) {
        return new ResourceNotFoundException("用户", userId);
    }
    
    public static ResourceNotFoundException task(String taskId) {
        return new ResourceNotFoundException("任务", taskId);
    }
    
    public static ResourceNotFoundException video(String videoId) {
        return new ResourceNotFoundException("视频", videoId);
    }
    
    public static ResourceNotFoundException analysis(String analysisId) {
        return new ResourceNotFoundException("分析结果", analysisId);
    }
    
    public static ResourceNotFoundException report(String reportId) {
        return new ResourceNotFoundException("报告", reportId);
    }
    
    public static ResourceNotFoundException file(String filePath) {
        return new ResourceNotFoundException("文件", filePath);
    }
    
    public static ResourceNotFoundException fourWayTask(String taskId) {
        return new ResourceNotFoundException("四方向分析任务", taskId);
    }
    
    public static ResourceNotFoundException signalOptimization(String optimizationId) {
        return new ResourceNotFoundException("信号优化方案", optimizationId);
    }
}
