package com.traffic.analysis.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 智能交通分析结果数据结构
 * 对应MongoDB的traffic_analysis_results集合
 */
@Data
@Document(collection = "traffic_analysis_results")
public class TrafficAnalysisResult {

    @Id
    private String id;

    /**
     * 关联的任务ID
     */
    @Field("task_id")
    private String taskId;

    /**
     * 分析类型：single_video, intersection, four_way
     */
    @Field("analysis_type")
    private String analysisType;

    /**
     * 总车辆数量
     */
    @Field("total_vehicle_count")
    private int totalVehicleCount;

    /**
     * 车流量最大的方向
     */
    @Field("peak_direction")
    private Direction peakDirection;

    /**
     * 交通流量平衡度 (0-1，1表示完全平衡)
     */
    @Field("traffic_flow_balance")
    private double trafficFlowBalance;

    /**
     * 整体拥堵等级
     */
    @Field("congestion_level")
    private String congestionLevel;

    /**
     * 拥堵指数 (0-1)
     */
    @Field("congestion_index")
    private double congestionIndex;

    /**
     * 信号灯优化建议
     */
    @Field("signal_optimization")
    private SignalOptimization signalOptimization;

    /**
     * 方向间流量关联度
     */
    @Field("flow_correlation")
    private Map<String, Double> flowCorrelation = new HashMap<>();

    /**
     * 交通流向分析
     */
    @Field("traffic_flows")
    private List<TrafficFlow> trafficFlows = new ArrayList<>();

    /**
     * 拥堵预测
     */
    @Field("congestion_prediction")
    private CongestionPrediction congestionPrediction;

    /**
     * 智能分析建议
     */
    @Field("recommendations")
    private List<String> recommendations = new ArrayList<>();

    /**
     * 分析创建时间
     */
    @Field("created_at")
    private LocalDateTime createdAt;

    /**
     * 分析更新时间
     */
    @Field("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 分析完成时间
     */
    @Field("completed_at")
    private LocalDateTime completedAt;

    /**
     * 处理耗时（秒）
     */
    @Field("processing_duration")
    private long processingDuration;
    
    /**
     * 信号灯优化建议内部类
     */
    @Data
    public static class SignalOptimization {
        /**
         * 推荐信号周期（秒）
         */
        @Field("recommended_cycle")
        private int recommendedCycle;

        /**
         * 各方向绿灯时间分配
         */
        @Field("green_time_allocation")
        private Map<String, Integer> greenTimeAllocation = new HashMap<>();

        /**
         * 优化理由
         */
        @Field("reason")
        private String reason;

        /**
         * 预期改善效果
         */
        @Field("expected_improvement")
        private String expectedImprovement;

        /**
         * 优化类型：timing, phase, adaptive
         */
        @Field("optimization_type")
        private String optimizationType;

        /**
         * 优化置信度 (0-1)
         */
        @Field("confidence")
        private double confidence;

        /**
         * 预期通行效率提升百分比
         */
        @Field("efficiency_improvement")
        private double efficiencyImprovement;
    }
    
    /**
     * 交通流向数据内部类
     */
    @Data
    public static class TrafficFlow {
        /**
         * 起始方向
         */
        @Field("from_direction")
        private String fromDirection;

        /**
         * 目标方向
         */
        @Field("to_direction")
        private String toDirection;

        /**
         * 流量强度 (0-1)
         */
        @Field("intensity")
        private double intensity;

        /**
         * 车辆数量
         */
        @Field("vehicle_count")
        private int vehicleCount;

        /**
         * 流向类型：直行、左转、右转
         */
        @Field("flow_type")
        private String flowType;

        /**
         * 平均速度 (km/h)
         */
        @Field("average_speed")
        private double averageSpeed;

        /**
         * 流量密度 (车辆/km)
         */
        @Field("density")
        private double density;
    }
    
    /**
     * 拥堵预测内部类
     */
    @Data
    public static class CongestionPrediction {
        /**
         * 预测拥堵方向
         */
        @Field("predicted_congestion_directions")
        private List<String> predictedCongestionDirections = new ArrayList<>();

        /**
         * 拥堵概率 (0-1)
         */
        @Field("congestion_probability")
        private double congestionProbability;

        /**
         * 预测时间范围（分钟）
         */
        @Field("prediction_time_range")
        private int predictionTimeRange;

        /**
         * 建议疏导措施
         */
        @Field("recommended_actions")
        private List<String> recommendedActions = new ArrayList<>();

        /**
         * 预测准确度 (0-1)
         */
        @Field("prediction_accuracy")
        private double predictionAccuracy;

        /**
         * 预测生成时间
         */
        @Field("prediction_time")
        private LocalDateTime predictionTime;
    }
    
    /**
     * 计算交通流量平衡度
     */
    public void calculateTrafficFlowBalance(Map<Direction, Integer> directionCounts) {
        if (directionCounts == null || directionCounts.isEmpty()) {
            this.trafficFlowBalance = 0.0;
            return;
        }
        
        int total = directionCounts.values().stream().mapToInt(Integer::intValue).sum();
        if (total == 0) {
            this.trafficFlowBalance = 1.0;
            return;
        }
        
        double idealRatio = 1.0 / directionCounts.size();
        double variance = 0.0;
        
        for (int count : directionCounts.values()) {
            double actualRatio = (double) count / total;
            variance += Math.pow(actualRatio - idealRatio, 2);
        }
        
        // 平衡度 = 1 - 标准化方差
        this.trafficFlowBalance = Math.max(0.0, 1.0 - (variance / idealRatio));
    }
    
    /**
     * 确定拥堵等级
     */
    public void determineCongestionLevel(int totalVehicles, double averageVehiclesPerDirection) {
        if (averageVehiclesPerDirection < 10) {
            this.congestionLevel = "畅通";
        } else if (averageVehiclesPerDirection < 20) {
            this.congestionLevel = "轻度拥堵";
        } else if (averageVehiclesPerDirection < 35) {
            this.congestionLevel = "中度拥堵";
        } else {
            this.congestionLevel = "严重拥堵";
        }
    }
}
