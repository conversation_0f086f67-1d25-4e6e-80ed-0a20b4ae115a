# 修复Maven settings.xml文件中的XML标签错误
$settingsPath = "D:\apache-maven-3.9.8\conf\settings.xml"

if (Test-Path $settingsPath) {
    Write-Host "正在修复Maven settings.xml文件..."
    
    # 读取文件内容
    $content = Get-Content $settingsPath -Raw
    
    # 修复错误的标签：将 <mirror0f> 替换为 <mirrorOf>
    $fixedContent = $content -replace '<mirror0f>', '<mirrorOf>'
    
    # 备份原文件
    $backupPath = $settingsPath + ".backup"
    Copy-Item $settingsPath $backupPath
    Write-Host "已备份原文件到: $backupPath"
    
    # 写入修复后的内容
    Set-Content $settingsPath $fixedContent -Encoding UTF8
    Write-Host "已修复Maven settings.xml文件"
    
    # 验证修复
    $lines = Get-Content $settingsPath
    $line163 = $lines[162]  # 数组索引从0开始，所以第163行是索引162
    Write-Host "第163行内容: $line163"
    
} else {
    Write-Host "Maven settings.xml文件不存在: $settingsPath"
}
