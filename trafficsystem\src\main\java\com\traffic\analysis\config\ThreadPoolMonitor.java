package com.traffic.analysis.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池监控器
 */
@Component
public class ThreadPoolMonitor {

    private static final Logger log = LoggerFactory.getLogger(ThreadPoolMonitor.class);

    @Autowired
    @Qualifier("taskExecutor")
    private Executor taskExecutor;

    @Autowired
    @Qualifier("videoProcessingExecutor")
    private Executor videoProcessingExecutor;

    @Autowired
    @Qualifier("fourWayAnalysisExecutor")
    private Executor fourWayAnalysisExecutor;

    /**
     * 获取所有线程池的状态信息
     */
    public Map<String, Object> getAllThreadPoolStatus() {
        Map<String, Object> status = new HashMap<>();
        
        status.put("taskExecutor", getThreadPoolStatus("taskExecutor", taskExecutor));
        status.put("videoProcessingExecutor", getThreadPoolStatus("videoProcessingExecutor", videoProcessingExecutor));
        status.put("fourWayAnalysisExecutor", getThreadPoolStatus("fourWayAnalysisExecutor", fourWayAnalysisExecutor));
        
        return status;
    }

    /**
     * 获取单个线程池的状态信息
     */
    public Map<String, Object> getThreadPoolStatus(String name, Executor executor) {
        Map<String, Object> status = new HashMap<>();
        
        if (executor instanceof ThreadPoolTaskExecutor) {
            ThreadPoolTaskExecutor taskExecutor = (ThreadPoolTaskExecutor) executor;
            ThreadPoolExecutor threadPoolExecutor = taskExecutor.getThreadPoolExecutor();
            
            status.put("name", name);
            status.put("corePoolSize", threadPoolExecutor.getCorePoolSize());
            status.put("maximumPoolSize", threadPoolExecutor.getMaximumPoolSize());
            status.put("activeCount", threadPoolExecutor.getActiveCount());
            status.put("poolSize", threadPoolExecutor.getPoolSize());
            status.put("queueSize", threadPoolExecutor.getQueue().size());
            status.put("completedTaskCount", threadPoolExecutor.getCompletedTaskCount());
            status.put("taskCount", threadPoolExecutor.getTaskCount());
            status.put("largestPoolSize", threadPoolExecutor.getLargestPoolSize());
            
            // 计算使用率
            double utilization = (double) threadPoolExecutor.getActiveCount() / threadPoolExecutor.getMaximumPoolSize() * 100;
            status.put("utilization", String.format("%.2f%%", utilization));
            
        } else {
            status.put("name", name);
            status.put("type", executor.getClass().getSimpleName());
            status.put("status", "无法获取详细信息");
        }
        
        return status;
    }

    /**
     * 记录线程池状态到日志
     */
    public void logThreadPoolStatus() {
        Map<String, Object> allStatus = getAllThreadPoolStatus();
        
        log.info("=== 线程池状态监控 ===");
        for (Map.Entry<String, Object> entry : allStatus.entrySet()) {
            @SuppressWarnings("unchecked")
            Map<String, Object> poolStatus = (Map<String, Object>) entry.getValue();
            
            log.info("线程池: {} | 活跃线程: {}/{} | 队列: {} | 使用率: {} | 已完成: {}", 
                poolStatus.get("name"),
                poolStatus.get("activeCount"),
                poolStatus.get("maximumPoolSize"),
                poolStatus.get("queueSize"),
                poolStatus.get("utilization"),
                poolStatus.get("completedTaskCount")
            );
        }
        log.info("=== 线程池状态监控结束 ===");
    }

    /**
     * 获取四方向分析执行器的详细状态
     */
    public Map<String, Object> getFourWayAnalysisExecutorStatus() {
        return getThreadPoolStatus("fourWayAnalysisExecutor", fourWayAnalysisExecutor);
    }

    /**
     * 检查四方向分析执行器是否有足够的可用线程
     */
    public boolean isFourWayAnalysisExecutorAvailable() {
        Map<String, Object> status = getFourWayAnalysisExecutorStatus();
        
        if (status.containsKey("activeCount") && status.containsKey("maximumPoolSize")) {
            int activeCount = (Integer) status.get("activeCount");
            int maximumPoolSize = (Integer) status.get("maximumPoolSize");
            
            // 如果活跃线程数少于最大线程数，说明还有可用线程
            return activeCount < maximumPoolSize;
        }
        
        return false;
    }
}
