package com.traffic.analysis.exception;

import lombok.Getter;

/**
 * 视频分析异常
 */
@Getter
public class VideoAnalysisException extends RuntimeException {
    
    private final String errorCode;
    private final Object details;
    
    public VideoAnalysisException(String message) {
        super(message);
        this.errorCode = "VIDEO_ANALYSIS_ERROR";
        this.details = null;
    }
    
    public VideoAnalysisException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
        this.details = null;
    }
    
    public VideoAnalysisException(String message, Object details) {
        super(message);
        this.errorCode = "VIDEO_ANALYSIS_ERROR";
        this.details = details;
    }
    
    public VideoAnalysisException(String message, String errorCode, Object details) {
        super(message);
        this.errorCode = errorCode;
        this.details = details;
    }
    
    public VideoAnalysisException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "VIDEO_ANALYSIS_ERROR";
        this.details = null;
    }
    
    public VideoAnalysisException(String message, Throwable cause, String errorCode) {
        super(message, cause);
        this.errorCode = errorCode;
        this.details = null;
    }
    
    public VideoAnalysisException(String message, Throwable cause, String errorCode, Object details) {
        super(message, cause);
        this.errorCode = errorCode;
        this.details = details;
    }
    
    // 静态工厂方法
    public static VideoAnalysisException uploadFailed(String message) {
        return new VideoAnalysisException(message, "VIDEO_UPLOAD_FAILED");
    }
    
    public static VideoAnalysisException processingFailed(String message) {
        return new VideoAnalysisException(message, "VIDEO_PROCESSING_FAILED");
    }
    
    public static VideoAnalysisException analysisTimeout(String message) {
        return new VideoAnalysisException(message, "VIDEO_ANALYSIS_TIMEOUT");
    }
    
    public static VideoAnalysisException invalidFormat(String message) {
        return new VideoAnalysisException(message, "INVALID_VIDEO_FORMAT");
    }
    
    public static VideoAnalysisException fileTooLarge(String message) {
        return new VideoAnalysisException(message, "VIDEO_FILE_TOO_LARGE");
    }
    
    public static VideoAnalysisException modelError(String message) {
        return new VideoAnalysisException(message, "AI_MODEL_ERROR");
    }
    
    public static VideoAnalysisException storageError(String message) {
        return new VideoAnalysisException(message, "VIDEO_STORAGE_ERROR");
    }
    
    public static VideoAnalysisException taskNotFound(String taskId) {
        return new VideoAnalysisException("视频分析任务未找到: " + taskId, "TASK_NOT_FOUND");
    }
    
    public static VideoAnalysisException resultNotFound(String resultId) {
        return new VideoAnalysisException("视频分析结果未找到: " + resultId, "RESULT_NOT_FOUND");
    }
}
