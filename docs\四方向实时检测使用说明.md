# 四方向实时检测功能使用说明

## 概述

四方向实时检测功能是交通分析系统的核心功能，允许用户实时查看十字路口四个方向（东、南、西、北）的车辆检测情况。系统采用先进的YOLOv12x深度学习模型，支持同时分析多个视频流，提供高精度的车辆识别、流量统计和智能交通分析。每个方向的视频会同时进行分析，并实时显示检测到的车辆信息，同时生成详细的交通流量报告和优化建议。

## 功能特点

### 1. 实时四方向显示
- **2x2网格布局**：四个方向同时显示在一个页面上，支持全屏和响应式布局
- **实时帧更新**：每检测一帧就立即更新对应方向的显示，支持帧冻结模式
- **定格显示**：检测到车辆的帧会保持显示，直到下一次检测结果到达
- **高清显示**：支持高分辨率视频显示，自动适配屏幕尺寸

### 2. 智能分析功能
- **车辆识别**：精确识别各类车辆（轿车、卡车、公交车、摩托车等）
- **流量统计**：实时计算各方向车流量、密度和速度
- **拥堵检测**：自动识别交通拥堵状况并生成警报
- **流向分析**：分析车辆转向和流向模式

### 3. 统计信息
- **方向统计**：每个方向的车辆计数、帧率和检测精度
- **全局统计**：总车辆数、最繁忙方向、平均处理速度、拥堵指数
- **实时更新**：统计数据随检测结果实时更新，支持历史趋势图
- **数据导出**：支持统计数据的CSV、Excel格式导出

### 4. 连接状态监控
- **连接指示器**：显示WebSocket连接状态和网络质量
- **最后更新时间**：显示最近一次接收数据的时间
- **进度条**：显示整体检测进度和各方向处理状态
- **错误恢复**：自动重连机制和错误提示

### 5. 报告生成
- **实时报告**：自动生成交通流量分析报告
- **智能建议**：基于AI分析的交通优化建议
- **多格式导出**：支持PDF、Word、Excel格式的报告导出
- **历史对比**：支持不同时间段的数据对比分析

## 使用步骤

### 1. 上传四方向视频
1. 访问 **四方向上传** 页面 (`/four-way-upload`)
2. 分别上传东、南、西、北四个方向的视频文件
   - 支持格式：MP4、AVI、MOV、WMV、FLV、WebM、MKV
   - 文件大小：单个文件最大500MB，总大小不超过2GB
   - 分辨率：建议1080p或以上，最低720p
3. 配置分析参数：
   - **分析类型**：基础分析、高级分析、智能分析
   - **检测敏感度**：0.1-1.0，默认0.5
   - **分析时长**：1-24小时，默认1小时
   - **启用预测**：是否启用交通流量预测
4. 点击 **开始上传** 按钮
5. 记录返回的 **任务ID**

### 2. 开始实时检测
1. 访问 **四方向实时检测** 页面 (`/four-way-realtime`)
2. 在任务选择区域输入上一步获得的 **任务ID**
3. 选择显示模式：
   - **实时模式**：连续显示检测帧
   - **冻结模式**：检测帧保持显示直到下一帧
   - **对比模式**：显示原始帧和检测帧对比
4. 点击 **开始检测** 按钮
5. 系统会自动连接WebSocket并开始接收实时数据

### 3. 查看检测结果
- **四方向网格**：查看每个方向的实时检测画面
- **方向统计**：查看每个方向的车辆数、帧率和检测精度
- **全局统计**：查看整体的交通流量信息、拥堵指数
- **连接状态**：确认数据连接正常和网络质量
- **实时图表**：查看流量趋势图和统计图表

### 4. 分析和报告
1. 等待分析完成或手动停止分析
2. 查看 **智能分析结果**：
   - 交通流量平衡度分析
   - 拥堵热点识别
   - 信号灯优化建议
3. 生成和下载报告：
   - 点击 **生成报告** 按钮
   - 选择报告格式（PDF、Word、Excel）
   - 下载详细分析报告

### 5. 历史记录管理
1. 访问 **分析历史** 页面查看所有分析记录
2. 支持的操作：
   - 查看任务详情
   - 重新分析
   - 删除任务
   - 批量操作
   - 数据导出

## 页面布局说明

### 四方向网格布局
```
┌─────────┬─────────┬─────────┐
│         │  北向   │         │
│         │ (North) │         │
├─────────┼─────────┼─────────┤
│  西向   │ 十字路口 │  东向   │
│ (West)  │  中心   │ (East)  │
├─────────┼─────────┼─────────┤
│         │  南向   │         │
│         │ (South) │         │
└─────────┴─────────┴─────────┘
```

### 信息显示
- **方向标题**：显示方向名称和状态标签
- **检测画面**：显示最新的检测帧图像
- **统计信息**：显示车辆数和帧率
- **中心区域**：显示总车辆数和十字路口图标

## 技术实现

### 数据流
1. **视频上传** → GridFS存储（cross存储桶）
2. **Python模型分析** → 逐帧检测车辆
3. **WebSocket推送** → 实时帧数据传输
4. **前端显示** → 四方向同步更新

### WebSocket主题
- **四方向帧数据**：`/topic/four-way-frames/{taskId}`
- **四方向进度**：`/topic/four-way-progress/{taskId}`
- **特定方向帧数据**：`/topic/four-way-frames/{taskId}/{direction}`

### 数据存储
- **视频文件**：GridFS cross存储桶
- **分析结果**：intersection_analysis_four_way集合
- **实时帧数据**：内存缓存（临时存储）

## 故障排除

### 常见问题

1. **连接失败**
   - 检查任务ID是否正确
   - 确认视频上传是否成功
   - 检查WebSocket连接状态

2. **没有数据更新**
   - 确认Python模型服务正在运行
   - 检查视频分析是否已开始
   - 查看浏览器控制台错误信息

3. **显示异常**
   - 刷新页面重新连接
   - 检查网络连接稳定性
   - 确认浏览器支持WebSocket

### 调试工具

1. **浏览器控制台**
   ```javascript
   // 启用样式日志（如果需要调试样式问题）
   enableStyleLogs()
   
   // 检查WebSocket连接
   window.stompService.connected
   
   // 查看帧缓冲状态
   window.stompService.getFourWayFrameStats('your-task-id')
   ```

2. **测试页面**
   - 访问 `/four-way-test` 进行功能测试
   - 查看详细的连接和数据接收日志

## 性能优化

### 网络优化
- 帧数据自动压缩
- 智能帧选择（只推送有检测结果的帧）
- 连接质量自适应调整

### 显示优化
- 帧缓冲管理（最多缓存30帧）
- 定格显示减少闪烁
- 响应式布局适配不同屏幕

## 注意事项

1. **浏览器兼容性**：建议使用Chrome、Firefox、Edge等现代浏览器
2. **网络要求**：需要稳定的网络连接以确保实时数据传输
3. **性能要求**：四方向同时显示对设备性能有一定要求
4. **数据量**：实时传输会产生较大的网络流量

## 更新日志

### v1.0.0 (2024-01-19)
- 初始版本发布
- 支持四方向实时检测显示
- 实现WebSocket数据订阅
- 添加统计信息和状态监控
