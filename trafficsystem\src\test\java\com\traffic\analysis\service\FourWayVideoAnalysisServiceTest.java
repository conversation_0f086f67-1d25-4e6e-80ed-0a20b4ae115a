package com.traffic.analysis.service;

import com.traffic.analysis.model.Direction;
import com.traffic.analysis.model.DirectionVideoData;
import com.traffic.analysis.model.FourWayIntersectionAnalysis;
import com.traffic.analysis.model.TrafficAnalysisResult;
import com.traffic.analysis.service.impl.VideoAnalysisServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 四方向视频分析服务测试类
 */
@ExtendWith(MockitoExtension.class)
class FourWayVideoAnalysisServiceTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private GridFsTemplate gridFsTemplate;

    @InjectMocks
    private VideoAnalysisServiceImpl videoAnalysisService;

    private FourWayIntersectionAnalysis testAnalysis;
    private MockMultipartFile testVideoFile;

    @BeforeEach
    void setUp() {
        // 创建测试用的四方向分析对象
        testAnalysis = new FourWayIntersectionAnalysis();
        testAnalysis.setTaskId("test-task-001");
        testAnalysis.setUserId("test-user");
        testAnalysis.setUsername("testuser");
        testAnalysis.setRole("admin");
        testAnalysis.setStatus("queued");
        testAnalysis.setProgress(0);
        testAnalysis.setCreatedAt(LocalDateTime.now());
        testAnalysis.setUpdatedAt(LocalDateTime.now());
        testAnalysis.initializeDirections();

        // 创建测试视频文件
        testVideoFile = new MockMultipartFile(
            "video",
            "test-video.mp4",
            "video/mp4",
            "test video content".getBytes()
        );
    }

    @Test
    void testProcessFourWayIntersectionVideos_Success() throws IOException {
        // 准备测试数据
        MockMultipartFile eastFile = new MockMultipartFile("east", "east.mp4", "video/mp4", "east video".getBytes());
        MockMultipartFile southFile = new MockMultipartFile("south", "south.mp4", "video/mp4", "south video".getBytes());
        MockMultipartFile westFile = new MockMultipartFile("west", "west.mp4", "video/mp4", "west video".getBytes());
        MockMultipartFile northFile = new MockMultipartFile("north", "north.mp4", "video/mp4", "north video".getBytes());

        // Mock MongoDB保存操作
        when(mongoTemplate.save(any(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way")))
            .thenReturn(testAnalysis);

        // 执行测试
        FourWayIntersectionAnalysis result = videoAnalysisService.processFourWayIntersectionVideos(
            eastFile, southFile, westFile, northFile,
            "test-user", "testuser", "admin"
        );

        // 验证结果
        assertNotNull(result);
        assertEquals("test-user", result.getUserId());
        assertEquals("testuser", result.getUsername());
        assertEquals("admin", result.getRole());
        assertEquals("queued", result.getStatus());
        assertEquals(4, result.getDirections().size());

        // 验证所有方向都已初始化
        for (Direction direction : Direction.values()) {
            assertNotNull(result.getDirectionData(direction));
            assertEquals("queued", result.getDirectionData(direction).getStatus());
        }

        // 验证MongoDB保存操作被调用
        verify(mongoTemplate, times(1)).save(any(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
    }

    @Test
    void testFindFourWayAnalysisByTaskId_Found() {
        // 准备测试数据
        String taskId = "test-task-001";
        when(mongoTemplate.findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way")))
            .thenReturn(testAnalysis);

        // 执行测试
        Optional<FourWayIntersectionAnalysis> result = videoAnalysisService.findFourWayAnalysisByTaskId(taskId);

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(taskId, result.get().getTaskId());

        // 验证MongoDB查询操作被调用
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
    }

    @Test
    void testFindFourWayAnalysisByTaskId_NotFound() {
        // 准备测试数据
        String taskId = "non-existent-task";
        when(mongoTemplate.findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way")))
            .thenReturn(null);

        // 执行测试
        Optional<FourWayIntersectionAnalysis> result = videoAnalysisService.findFourWayAnalysisByTaskId(taskId);

        // 验证结果
        assertFalse(result.isPresent());

        // 验证MongoDB查询操作被调用
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
    }

    @Test
    void testUpdateFourWayAnalysisStatus_Success() {
        // 准备测试数据
        String taskId = "test-task-001";
        String newStatus = "processing";
        String message = "开始处理四方向视频";

        when(mongoTemplate.findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way")))
            .thenReturn(testAnalysis);

        // 执行测试
        FourWayIntersectionAnalysis result = videoAnalysisService.updateFourWayAnalysisStatus(taskId, newStatus, message);

        // 验证结果
        assertNotNull(result);
        assertEquals(taskId, result.getTaskId());

        // 验证MongoDB更新操作被调用
        verify(mongoTemplate, times(1)).updateFirst(any(Query.class), any(), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
    }

    @Test
    void testUpdateDirectionProgress_Success() {
        // 准备测试数据
        String taskId = "test-task-001";
        Direction direction = Direction.EAST;
        int progress = 50;
        String status = "processing";

        when(mongoTemplate.findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way")))
            .thenReturn(testAnalysis);
        when(mongoTemplate.save(any(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way")))
            .thenReturn(testAnalysis);

        // 执行测试
        FourWayIntersectionAnalysis result = videoAnalysisService.updateDirectionProgress(taskId, direction, progress, status);

        // 验证结果
        assertNotNull(result);
        assertEquals(taskId, result.getTaskId());

        // 验证MongoDB操作被调用
        verify(mongoTemplate, times(1)).updateFirst(any(Query.class), any(), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
        verify(mongoTemplate, times(1)).save(any(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
    }

    @Test
    void testCompleteFourWayAnalysis_Success() {
        // 准备测试数据
        String taskId = "test-task-001";
        Map<Direction, Map<String, Object>> directionsResults = new HashMap<>();
        
        for (Direction direction : Direction.values()) {
            Map<String, Object> result = new HashMap<>();
            result.put("vehicle_count", 25);
            result.put("vehicle_types", new HashMap<String, Integer>());
            directionsResults.put(direction, result);
        }

        // 设置测试分析对象的方向数据
        for (Direction direction : Direction.values()) {
            DirectionVideoData directionData = new DirectionVideoData();
            directionData.setStatus("processing");
            directionData.setProgress(50);
            testAnalysis.setDirectionData(direction, directionData);
        }

        when(mongoTemplate.findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way")))
            .thenReturn(testAnalysis);
        when(mongoTemplate.save(any(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way")))
            .thenReturn(testAnalysis);

        // 执行测试
        FourWayIntersectionAnalysis result = videoAnalysisService.completeFourWayAnalysis(taskId, directionsResults);

        // 验证结果
        assertNotNull(result);
        assertEquals(taskId, result.getTaskId());
        assertEquals("completed", result.getStatus());
        assertEquals(100, result.getProgress());
        assertNotNull(result.getTrafficAnalysis());

        // 验证所有方向都已完成
        for (Direction direction : Direction.values()) {
            DirectionVideoData directionData = result.getDirectionData(direction);
            assertNotNull(directionData);
            assertEquals("completed", directionData.getStatus());
            assertEquals(100, directionData.getProgress());
            assertEquals(25, directionData.getVehicleCount());
        }

        // 验证MongoDB操作被调用
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
        verify(mongoTemplate, times(1)).save(any(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
    }

    @Test
    void testGetFourWayAnalysisResult_Success() {
        // 准备测试数据
        String taskId = "test-task-001";
        
        // 设置交通分析结果
        TrafficAnalysisResult trafficAnalysis = new TrafficAnalysisResult();
        trafficAnalysis.setTotalVehicleCount(100);
        trafficAnalysis.setPeakDirection(Direction.EAST);
        trafficAnalysis.setTrafficFlowBalance(0.75);
        trafficAnalysis.setCongestionLevel("轻度拥堵");
        testAnalysis.setTrafficAnalysis(trafficAnalysis);

        when(mongoTemplate.findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way")))
            .thenReturn(testAnalysis);

        // 执行测试
        Map<String, Object> result = videoAnalysisService.getFourWayAnalysisResult(taskId);

        // 验证结果
        assertNotNull(result);
        assertEquals(taskId, result.get("taskId"));
        assertEquals("queued", result.get("status"));
        assertEquals(0, result.get("progress"));
        assertNotNull(result.get("directions"));
        assertNotNull(result.get("trafficAnalysis"));

        // 验证MongoDB查询操作被调用
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
    }

    @Test
    void testGenerateFourWayTrafficReport_Success() {
        // 准备测试数据
        String taskId = "test-task-001";
        testAnalysis.setStatus("completed");
        
        // 设置交通分析结果
        TrafficAnalysisResult trafficAnalysis = new TrafficAnalysisResult();
        trafficAnalysis.setTotalVehicleCount(100);
        trafficAnalysis.setPeakDirection(Direction.EAST);
        trafficAnalysis.setTrafficFlowBalance(0.75);
        trafficAnalysis.setCongestionLevel("轻度拥堵");
        testAnalysis.setTrafficAnalysis(trafficAnalysis);

        when(mongoTemplate.findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way")))
            .thenReturn(testAnalysis);

        // 执行测试
        Map<String, Object> result = videoAnalysisService.generateFourWayTrafficReport(taskId);

        // 验证结果
        assertNotNull(result);
        assertEquals(taskId, result.get("taskId"));
        assertEquals("四方向智能交通分析报告", result.get("title"));
        assertNotNull(result.get("generatedAt"));
        assertNotNull(result.get("summary"));
        assertNotNull(result.get("directions"));
        assertNotNull(result.get("intelligentAnalysis"));
        assertNotNull(result.get("recommendations"));

        // 验证MongoDB查询操作被调用
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(FourWayIntersectionAnalysis.class), eq("intersection_analysis_four_way"));
    }

    @Test
    void testProcessFourWayIntersectionVideos_NullFile() {
        // 测试空文件处理
        assertThrows(IllegalArgumentException.class, () -> {
            videoAnalysisService.processFourWayIntersectionVideos(
                null, testVideoFile, testVideoFile, testVideoFile,
                "test-user", "testuser", "admin"
            );
        });
    }

    @Test
    void testFourWayAnalysisTaskValidation() {
        // 测试任务验证方法
        assertTrue(testAnalysis.isAllDirectionsCompleted() == false);
        assertFalse(testAnalysis.hasAnyDirectionFailed());
        assertEquals(0, testAnalysis.getCompletedDirectionsCount());
        assertEquals(100, testAnalysis.getTotalVehicleCount());
        
        // 设置一个方向为完成状态
        DirectionVideoData eastData = testAnalysis.getDirectionData(Direction.EAST);
        eastData.setStatus("completed");
        eastData.setVehicleCount(25);
        
        assertEquals(1, testAnalysis.getCompletedDirectionsCount());
        assertEquals(25, testAnalysis.getTotalVehicleCount());
    }
}
