<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <!-- 禁用ResizeObserver错误显示 -->
    <script>
      (function() {
        // 等待webpack-dev-server的overlay模块加载完成
        const originalCreateOverlay = window.createOverlay;
        if (originalCreateOverlay) {
          // 如果函数已经存在，立即修补
          patchOverlay();
        } else {
          // 否则等待函数加载完成
          Object.defineProperty(window, 'createOverlay', {
            configurable: true,
            enumerable: true,
            get: function() {
              return originalCreateOverlay;
            },
            set: function(newCreateOverlay) {
              originalCreateOverlay = newCreateOverlay;
              patchOverlay();
            }
          });
        }


      })();
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- WebSocket连接修复脚本 -->
    <script src="<%= BASE_URL %>websocket-fix.js"></script>
    <!-- 双视频预览修复脚本 -->
    <script src="<%= BASE_URL %>dual-video-fix.js"></script>
    <!-- 定格显示测试脚本 -->
    <script src="<%= BASE_URL %>freeze-frame-test.js"></script>
    <!-- 增强预览样式 -->
    <link rel="stylesheet" href="<%= BASE_URL %>enhanced-preview-styles.css">
    <!-- 日志配置管理器（必须在其他样式脚本之前加载） -->
    <script src="<%= BASE_URL %>log-config.js"></script>
    <!-- 样式增强器 -->
    <script src="<%= BASE_URL %>style-enhancer.js"></script>
    <!-- 全宽度优化器 -->
    <script src="<%= BASE_URL %>fullwidth-optimizer.js"></script>
    <!-- built files will be auto injected -->
  </body>
</html>
