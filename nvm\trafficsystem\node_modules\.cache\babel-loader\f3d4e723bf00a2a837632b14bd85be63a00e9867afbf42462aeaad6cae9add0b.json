{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"four-way-detection-test\"\n};\nconst _hoisted_2 = {\n  class: \"test-controls\"\n};\nconst _hoisted_3 = {\n  class: \"control-row\"\n};\nconst _hoisted_4 = {\n  class: \"status-info\",\n  style: {\n    \"margin-top\": \"16px\"\n  }\n};\nconst _hoisted_5 = {\n  style: {\n    \"margin-left\": \"16px\"\n  }\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"test-content\"\n};\nconst _hoisted_7 = {\n  key: 1,\n  class: \"test-logs\"\n};\nconst _hoisted_8 = {\n  class: \"log-container\"\n};\nconst _hoisted_9 = {\n  class: \"log-time\"\n};\nconst _hoisted_10 = {\n  class: \"log-message\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_FourWayRealtimeViewer = _resolveComponent(\"FourWayRealtimeViewer\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n    class: \"test-header\"\n  }, [_createElementVNode(\"h1\", null, \"四方向检测测试页面\"), _createElementVNode(\"p\", null, \"直接测试四方向实时检测功能\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => _cache[1] || (_cache[1] = [_createElementVNode(\"span\", null, \"测试控制\", -1 /* HOISTED */)])),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_input, {\n      modelValue: $setup.testTaskId,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.testTaskId = $event),\n      placeholder: \"输入测试任务ID\",\n      style: {\n        \"width\": \"300px\",\n        \"margin-right\": \"10px\"\n      }\n    }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.startTest,\n      disabled: !$setup.testTaskId\n    }, {\n      default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\" 开始测试 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n      onClick: $setup.stopTest,\n      disabled: !$setup.isTestRunning\n    }, {\n      default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\" 停止测试 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"disabled\"])]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_tag, {\n      type: $setup.isTestRunning ? 'success' : 'info'\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 状态: \" + _toDisplayString($setup.isTestRunning ? '测试运行中' : '测试未开始'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"]), _createElementVNode(\"span\", _hoisted_5, \"任务ID: \" + _toDisplayString($setup.currentTaskId || '未设置'), 1 /* TEXT */)])]),\n    _: 1 /* STABLE */\n  })]), $setup.isTestRunning ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_FourWayRealtimeViewer, {\n    \"task-id\": $setup.currentTaskId,\n    \"auto-start\": true,\n    onDetectionUpdate: $setup.handleDetectionUpdate,\n    onStatusChange: $setup.handleStatusChange\n  }, null, 8 /* PROPS */, [\"task-id\", \"onDetectionUpdate\", \"onStatusChange\"])])) : _createCommentVNode(\"v-if\", true), $setup.logs.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createVNode(_component_el_card, null, {\n    header: _withCtx(() => [_cache[5] || (_cache[5] = _createElementVNode(\"span\", null, \"测试日志\", -1 /* HOISTED */)), _createVNode(_component_el_button, {\n      size: \"small\",\n      onClick: $setup.clearLogs,\n      style: {\n        \"float\": \"right\"\n      }\n    }, {\n      default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\" 清空日志 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.logs, (log, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: _normalizeClass([\"log-item\", log.type])\n      }, [_createElementVNode(\"span\", _hoisted_9, _toDisplayString(log.time), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_10, _toDisplayString(log.message), 1 /* TEXT */)], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))])]),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "style", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_card", "header", "_withCtx", "_cache", "default", "_hoisted_3", "_component_el_input", "modelValue", "$setup", "testTaskId", "$event", "placeholder", "_component_el_button", "type", "onClick", "startTest", "disabled", "_createTextVNode", "_", "stopTest", "isTestRunning", "_hoisted_4", "_component_el_tag", "_toDisplayString", "_hoisted_5", "currentTaskId", "_hoisted_6", "_component_FourWayRealtimeViewer", "onDetectionUpdate", "handleDetectionUpdate", "onStatusChange", "handleStatusChange", "_createCommentVNode", "logs", "length", "_hoisted_7", "size", "clearLogs", "_hoisted_8", "_Fragment", "_renderList", "log", "index", "_normalizeClass", "_hoisted_9", "time", "_hoisted_10", "message"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\FourWayDetectionTest.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-detection-test\">\n    <div class=\"test-header\">\n      <h1>四方向检测测试页面</h1>\n      <p>直接测试四方向实时检测功能</p>\n    </div>\n\n    <div class=\"test-controls\">\n      <el-card>\n        <template #header>\n          <span>测试控制</span>\n        </template>\n        \n        <div class=\"control-row\">\n          <el-input \n            v-model=\"testTaskId\" \n            placeholder=\"输入测试任务ID\"\n            style=\"width: 300px; margin-right: 10px;\"\n          />\n          <el-button \n            type=\"primary\" \n            @click=\"startTest\"\n            :disabled=\"!testTaskId\"\n          >\n            开始测试\n          </el-button>\n          <el-button \n            @click=\"stopTest\"\n            :disabled=\"!isTestRunning\"\n          >\n            停止测试\n          </el-button>\n        </div>\n\n        <div class=\"status-info\" style=\"margin-top: 16px;\">\n          <el-tag :type=\"isTestRunning ? 'success' : 'info'\">\n            状态: {{ isTestRunning ? '测试运行中' : '测试未开始' }}\n          </el-tag>\n          <span style=\"margin-left: 16px;\">任务ID: {{ currentTaskId || '未设置' }}</span>\n        </div>\n      </el-card>\n    </div>\n\n    <div class=\"test-content\" v-if=\"isTestRunning\">\n      <FourWayRealtimeViewer\n        :task-id=\"currentTaskId\"\n        :auto-start=\"true\"\n        @detection-update=\"handleDetectionUpdate\"\n        @status-change=\"handleStatusChange\"\n      />\n    </div>\n\n    <div class=\"test-logs\" v-if=\"logs.length > 0\">\n      <el-card>\n        <template #header>\n          <span>测试日志</span>\n          <el-button \n            size=\"small\" \n            @click=\"clearLogs\"\n            style=\"float: right;\"\n          >\n            清空日志\n          </el-button>\n        </template>\n        \n        <div class=\"log-container\">\n          <div \n            v-for=\"(log, index) in logs\" \n            :key=\"index\"\n            class=\"log-item\"\n            :class=\"log.type\"\n          >\n            <span class=\"log-time\">{{ log.time }}</span>\n            <span class=\"log-message\">{{ log.message }}</span>\n          </div>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue'\n\nexport default {\n  name: 'FourWayDetectionTest',\n  components: {\n    FourWayRealtimeViewer\n  },\n  setup() {\n    // 响应式数据\n    const testTaskId = ref('8689ed66-1063-4d52-a83e-6c0cd54ea37d')\n    const currentTaskId = ref('')\n    const isTestRunning = ref(false)\n    const logs = ref([])\n\n    // 方法\n    const addLog = (message, type = 'info') => {\n      logs.value.push({\n        time: new Date().toLocaleTimeString(),\n        message,\n        type\n      })\n    }\n\n    const startTest = () => {\n      if (!testTaskId.value) {\n        ElMessage.warning('请输入测试任务ID')\n        return\n      }\n\n      currentTaskId.value = testTaskId.value\n      isTestRunning.value = true\n      addLog(`开始测试，任务ID: ${testTaskId.value}`, 'success')\n      ElMessage.success('测试已开始')\n    }\n\n    const stopTest = () => {\n      isTestRunning.value = false\n      currentTaskId.value = ''\n      addLog('测试已停止', 'warning')\n      ElMessage.info('测试已停止')\n    }\n\n    const clearLogs = () => {\n      logs.value = []\n    }\n\n    const handleDetectionUpdate = (data) => {\n      addLog(`检测更新: ${data.direction}方向, 车辆${data.directionStats.vehicleCount}辆`, 'success')\n    }\n\n    const handleStatusChange = (data) => {\n      addLog(`状态变化: ${data.direction}方向, 状态${data.status}`, 'info')\n    }\n\n    return {\n      testTaskId,\n      currentTaskId,\n      isTestRunning,\n      logs,\n      startTest,\n      stopTest,\n      clearLogs,\n      handleDetectionUpdate,\n      handleStatusChange\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-detection-test {\n  padding: 24px;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.test-header {\n  text-align: center;\n  margin-bottom: 24px;\n}\n\n.test-header h1 {\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n.test-header p {\n  color: #606266;\n  margin: 0;\n}\n\n.test-controls {\n  margin-bottom: 24px;\n}\n\n.control-row {\n  display: flex;\n  align-items: center;\n}\n\n.test-content {\n  margin-bottom: 24px;\n}\n\n.test-logs {\n  margin-top: 24px;\n}\n\n.log-container {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.log-item {\n  padding: 8px 0;\n  border-bottom: 1px solid #f0f0f0;\n  display: flex;\n  align-items: center;\n}\n\n.log-item:last-child {\n  border-bottom: none;\n}\n\n.log-time {\n  color: #909399;\n  font-size: 12px;\n  margin-right: 12px;\n  min-width: 80px;\n}\n\n.log-message {\n  flex: 1;\n}\n\n.log-item.success .log-message {\n  color: #67c23a;\n}\n\n.log-item.warning .log-message {\n  color: #e6a23c;\n}\n\n.log-item.error .log-message {\n  color: #f56c6c;\n}\n\n.log-item.info .log-message {\n  color: #409eff;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAyB;;EAM7BA,KAAK,EAAC;AAAe;;EAMjBA,KAAK,EAAC;AAAa;;EAqBnBA,KAAK,EAAC,aAAa;EAACC,KAAyB,EAAzB;IAAA;EAAA;;;EAIjBA,KAA0B,EAA1B;IAAA;EAAA;AAA0B;;EAtC1CC,GAAA;EA2CSF,KAAK,EAAC;;;EA3CfE,GAAA;EAoDSF,KAAK,EAAC;;;EAaFA,KAAK,EAAC;AAAe;;EAOhBA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC;AAAa;;;;;;;uBAxEnCG,mBAAA,CA6EM,OA7ENC,UA6EM,G,0BA5EJC,mBAAA,CAGM;IAHDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAAkB,YAAd,WAAS,GACbA,mBAAA,CAAoB,WAAjB,eAAa,E,sBAGlBA,mBAAA,CAkCM,OAlCNC,UAkCM,GAjCJC,YAAA,CAgCUC,kBAAA;IA/BGC,MAAM,EAAAC,QAAA,CACf,MAAiBC,MAAA,QAAAA,MAAA,OAAjBN,mBAAA,CAAiB,cAAX,MAAI,oB;IAVpBO,OAAA,EAAAF,QAAA,CAaQ,MAmBM,CAnBNL,mBAAA,CAmBM,OAnBNQ,UAmBM,GAlBJN,YAAA,CAIEO,mBAAA;MAlBZC,UAAA,EAeqBC,MAAA,CAAAC,UAAU;MAf/B,uBAAAN,MAAA,QAAAA,MAAA,MAAAO,MAAA,IAeqBF,MAAA,CAAAC,UAAU,GAAAC,MAAA;MACnBC,WAAW,EAAC,UAAU;MACtBlB,KAAyC,EAAzC;QAAA;QAAA;MAAA;6CAEFM,YAAA,CAMYa,oBAAA;MALVC,IAAI,EAAC,SAAS;MACbC,OAAK,EAAEN,MAAA,CAAAO,SAAS;MAChBC,QAAQ,GAAGR,MAAA,CAAAC;;MAtBxBL,OAAA,EAAAF,QAAA,CAuBW,MAEDC,MAAA,QAAAA,MAAA,OAzBVc,gBAAA,CAuBW,QAED,E;MAzBVC,CAAA;gDA0BUnB,YAAA,CAKYa,oBAAA;MAJTE,OAAK,EAAEN,MAAA,CAAAW,QAAQ;MACfH,QAAQ,GAAGR,MAAA,CAAAY;;MA5BxBhB,OAAA,EAAAF,QAAA,CA6BW,MAEDC,MAAA,QAAAA,MAAA,OA/BVc,gBAAA,CA6BW,QAED,E;MA/BVC,CAAA;kDAkCQrB,mBAAA,CAKM,OALNwB,UAKM,GAJJtB,YAAA,CAESuB,iBAAA;MAFAT,IAAI,EAAEL,MAAA,CAAAY,aAAa;;MAnCtChB,OAAA,EAAAF,QAAA,CAmC6D,MAC7C,CApChBe,gBAAA,CAmC6D,OAC7C,GAAAM,gBAAA,CAAGf,MAAA,CAAAY,aAAa,qC;MApChCF,CAAA;iCAsCUrB,mBAAA,CAA0E,QAA1E2B,UAA0E,EAAzC,QAAM,GAAAD,gBAAA,CAAGf,MAAA,CAAAiB,aAAa,0B;IAtCjEP,CAAA;QA2CoCV,MAAA,CAAAY,aAAa,I,cAA7CzB,mBAAA,CAOM,OAPN+B,UAOM,GANJ3B,YAAA,CAKE4B,gCAAA;IAJC,SAAO,EAAEnB,MAAA,CAAAiB,aAAa;IACtB,YAAU,EAAE,IAAI;IAChBG,iBAAgB,EAAEpB,MAAA,CAAAqB,qBAAqB;IACvCC,cAAa,EAAEtB,MAAA,CAAAuB;mFAhDxBC,mBAAA,gBAoDiCxB,MAAA,CAAAyB,IAAI,CAACC,MAAM,Q,cAAxCvC,mBAAA,CAyBM,OAzBNwC,UAyBM,GAxBJpC,YAAA,CAuBUC,kBAAA;IAtBGC,MAAM,EAAAC,QAAA,CACf,MAAiB,C,0BAAjBL,mBAAA,CAAiB,cAAX,MAAI,sBACVE,YAAA,CAMYa,oBAAA;MALVwB,IAAI,EAAC,OAAO;MACXtB,OAAK,EAAEN,MAAA,CAAA6B,SAAS;MACjB5C,KAAqB,EAArB;QAAA;MAAA;;MA3DZW,OAAA,EAAAF,QAAA,CA4DW,MAEDC,MAAA,QAAAA,MAAA,OA9DVc,gBAAA,CA4DW,QAED,E;MA9DVC,CAAA;;IAAAd,OAAA,EAAAF,QAAA,CAiEQ,MAUM,CAVNL,mBAAA,CAUM,OAVNyC,UAUM,I,kBATJ3C,mBAAA,CAQM4C,SAAA,QA1EhBC,WAAA,CAmEmChC,MAAA,CAAAyB,IAAI,EAnEvC,CAmEoBQ,GAAG,EAAEC,KAAK;2BADpB/C,mBAAA,CAQM;QANHD,GAAG,EAAEgD,KAAK;QACXlD,KAAK,EArEjBmD,eAAA,EAqEkB,UAAU,EACRF,GAAG,CAAC5B,IAAI;UAEhBhB,mBAAA,CAA4C,QAA5C+C,UAA4C,EAAArB,gBAAA,CAAlBkB,GAAG,CAACI,IAAI,kBAClChD,mBAAA,CAAkD,QAAlDiD,WAAkD,EAAAvB,gBAAA,CAArBkB,GAAG,CAACM,OAAO,iB;;IAzEpD7B,CAAA;UAAAc,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}