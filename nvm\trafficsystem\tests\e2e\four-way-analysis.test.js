/**
 * 四方向交通分析端到端测试
 */

import { test, expect } from '@playwright/test';
import path from 'path';

// 测试配置
const TEST_CONFIG = {
  baseURL: 'http://localhost:3000',
  timeout: 30000,
  videoTimeout: 60000
};

// 测试用户信息
const TEST_USER = {
  username: 'testuser',
  password: 'testpass123'
};

// 测试文件路径
const TEST_FILES = {
  eastVideo: path.join(__dirname, '../fixtures/videos/east.mp4'),
  southVideo: path.join(__dirname, '../fixtures/videos/south.mp4'),
  westVideo: path.join(__dirname, '../fixtures/videos/west.mp4'),
  northVideo: path.join(__dirname, '../fixtures/videos/north.mp4'),
  invalidFile: path.join(__dirname, '../fixtures/files/test.txt')
};

test.describe('四方向交通分析', () => {
  
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/login');
    await page.fill('[data-testid="username"]', TEST_USER.username);
    await page.fill('[data-testid="password"]', TEST_USER.password);
    await page.click('[data-testid="login-button"]');
    
    // 等待登录成功
    await expect(page).toHaveURL('/dashboard');
    
    // 导航到四方向分析页面
    await page.click('[data-testid="four-way-analysis-menu"]');
    await expect(page).toHaveURL('/four-way-analysis');
  });

  test('成功上传四方向视频并开始分析', async ({ page }) => {
    // 上传东方向视频
    await page.setInputFiles('[data-testid="east-video-input"]', TEST_FILES.eastVideo);
    await expect(page.locator('[data-testid="east-video-preview"]')).toBeVisible();

    // 上传南方向视频
    await page.setInputFiles('[data-testid="south-video-input"]', TEST_FILES.southVideo);
    await expect(page.locator('[data-testid="south-video-preview"]')).toBeVisible();

    // 上传西方向视频
    await page.setInputFiles('[data-testid="west-video-input"]', TEST_FILES.westVideo);
    await expect(page.locator('[data-testid="west-video-preview"]')).toBeVisible();

    // 上传北方向视频
    await page.setInputFiles('[data-testid="north-video-input"]', TEST_FILES.northVideo);
    await expect(page.locator('[data-testid="north-video-preview"]')).toBeVisible();

    // 配置分析参数
    await page.selectOption('[data-testid="analysis-type"]', 'basic');
    await page.fill('[data-testid="sensitivity-level"]', '0.7');
    await page.fill('[data-testid="description"]', '端到端测试分析');

    // 开始分析
    await page.click('[data-testid="start-analysis-button"]');

    // 验证分析开始
    await expect(page.locator('[data-testid="analysis-status"]')).toContainText('分析中');
    await expect(page.locator('[data-testid="progress-bar"]')).toBeVisible();

    // 等待分析完成或超时
    await page.waitForSelector('[data-testid="analysis-completed"]', { 
      timeout: TEST_CONFIG.videoTimeout 
    });

    // 验证分析结果
    await expect(page.locator('[data-testid="analysis-results"]')).toBeVisible();
    await expect(page.locator('[data-testid="traffic-summary"]')).toBeVisible();
  });

  test('验证文件格式限制', async ({ page }) => {
    // 尝试上传非视频文件
    await page.setInputFiles('[data-testid="east-video-input"]', TEST_FILES.invalidFile);

    // 验证错误提示
    await expect(page.locator('[data-testid="file-error-message"]')).toContainText('不支持的文件格式');
    
    // 验证上传按钮仍然禁用
    await expect(page.locator('[data-testid="start-analysis-button"]')).toBeDisabled();
  });

  test('验证必须上传所有四个方向的视频', async ({ page }) => {
    // 只上传三个方向的视频
    await page.setInputFiles('[data-testid="east-video-input"]', TEST_FILES.eastVideo);
    await page.setInputFiles('[data-testid="south-video-input"]', TEST_FILES.southVideo);
    await page.setInputFiles('[data-testid="west-video-input"]', TEST_FILES.westVideo);

    // 验证开始分析按钮仍然禁用
    await expect(page.locator('[data-testid="start-analysis-button"]')).toBeDisabled();

    // 验证提示信息
    await expect(page.locator('[data-testid="upload-status"]')).toContainText('请上传所有四个方向的视频');
  });

  test('实时进度更新', async ({ page }) => {
    // 上传所有视频
    await uploadAllVideos(page);

    // 开始分析
    await page.click('[data-testid="start-analysis-button"]');

    // 监听进度更新
    let progressUpdates = [];
    page.on('websocket', ws => {
      ws.on('framereceived', event => {
        const data = JSON.parse(event.payload);
        if (data.type === 'progress') {
          progressUpdates.push(data.progress);
        }
      });
    });

    // 等待进度更新
    await page.waitForFunction(() => {
      const progressText = document.querySelector('[data-testid="progress-percentage"]')?.textContent;
      return progressText && parseInt(progressText) > 0;
    });

    // 验证进度条更新
    const progressBar = page.locator('[data-testid="progress-bar"]');
    await expect(progressBar).toHaveAttribute('aria-valuenow', /[1-9]\d*/);
  });

  test('查看分析历史记录', async ({ page }) => {
    // 导航到历史记录页面
    await page.click('[data-testid="analysis-history-tab"]');

    // 验证历史记录列表
    await expect(page.locator('[data-testid="history-list"]')).toBeVisible();

    // 如果有历史记录，验证记录项
    const historyItems = page.locator('[data-testid="history-item"]');
    const count = await historyItems.count();
    
    if (count > 0) {
      // 验证第一个记录项的内容
      const firstItem = historyItems.first();
      await expect(firstItem.locator('[data-testid="task-id"]')).toBeVisible();
      await expect(firstItem.locator('[data-testid="created-time"]')).toBeVisible();
      await expect(firstItem.locator('[data-testid="status"]')).toBeVisible();

      // 点击查看详情
      await firstItem.click();
      await expect(page.locator('[data-testid="task-details"]')).toBeVisible();
    }
  });

  test('删除分析任务', async ({ page }) => {
    // 导航到历史记录页面
    await page.click('[data-testid="analysis-history-tab"]');

    // 等待历史记录加载
    await page.waitForSelector('[data-testid="history-list"]');

    const historyItems = page.locator('[data-testid="history-item"]');
    const count = await historyItems.count();

    if (count > 0) {
      // 选择第一个任务进行删除
      const firstItem = historyItems.first();
      await firstItem.locator('[data-testid="delete-button"]').click();

      // 确认删除
      await page.click('[data-testid="confirm-delete-button"]');

      // 验证删除成功提示
      await expect(page.locator('[data-testid="success-message"]')).toContainText('删除成功');

      // 验证任务从列表中移除
      const newCount = await historyItems.count();
      expect(newCount).toBe(count - 1);
    }
  });

  test('系统状态监控', async ({ page }) => {
    // 导航到系统状态页面
    await page.click('[data-testid="system-status-tab"]');

    // 验证系统状态信息
    await expect(page.locator('[data-testid="system-health"]')).toBeVisible();
    await expect(page.locator('[data-testid="task-statistics"]')).toBeVisible();
    await expect(page.locator('[data-testid="performance-metrics"]')).toBeVisible();

    // 验证实时数据更新
    const initialTaskCount = await page.locator('[data-testid="total-tasks"]').textContent();
    
    // 等待数据刷新
    await page.waitForTimeout(5000);
    
    // 验证数据可能已更新（或至少显示正确）
    const currentTaskCount = await page.locator('[data-testid="total-tasks"]').textContent();
    expect(currentTaskCount).toBeTruthy();
  });

  test('响应式设计验证', async ({ page }) => {
    // 测试移动端视图
    await page.setViewportSize({ width: 375, height: 667 });

    // 验证移动端布局
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
    
    // 验证视频上传区域在移动端的显示
    await expect(page.locator('[data-testid="video-upload-grid"]')).toHaveClass(/mobile-grid/);

    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 });
    
    // 验证平板端布局
    await expect(page.locator('[data-testid="video-upload-grid"]')).toHaveClass(/tablet-grid/);

    // 恢复桌面视图
    await page.setViewportSize({ width: 1920, height: 1080 });
  });

  test('错误处理和恢复', async ({ page }) => {
    // 模拟网络错误
    await page.route('**/api/video-analysis/four-way/upload', route => {
      route.abort('failed');
    });

    // 尝试上传视频
    await uploadAllVideos(page);
    await page.click('[data-testid="start-analysis-button"]');

    // 验证错误提示
    await expect(page.locator('[data-testid="error-message"]')).toContainText('网络错误');

    // 恢复网络连接
    await page.unroute('**/api/video-analysis/four-way/upload');

    // 重试上传
    await page.click('[data-testid="retry-button"]');

    // 验证重试成功
    await expect(page.locator('[data-testid="analysis-status"]')).toContainText('分析中');
  });

  // 辅助函数
  async function uploadAllVideos(page) {
    await page.setInputFiles('[data-testid="east-video-input"]', TEST_FILES.eastVideo);
    await page.setInputFiles('[data-testid="south-video-input"]', TEST_FILES.southVideo);
    await page.setInputFiles('[data-testid="west-video-input"]', TEST_FILES.westVideo);
    await page.setInputFiles('[data-testid="north-video-input"]', TEST_FILES.northVideo);

    // 等待所有视频预览加载
    await expect(page.locator('[data-testid="east-video-preview"]')).toBeVisible();
    await expect(page.locator('[data-testid="south-video-preview"]')).toBeVisible();
    await expect(page.locator('[data-testid="west-video-preview"]')).toBeVisible();
    await expect(page.locator('[data-testid="north-video-preview"]')).toBeVisible();
  }
});

test.describe('性能测试', () => {
  
  test('页面加载性能', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/four-way-analysis');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // 验证页面加载时间不超过3秒
    expect(loadTime).toBeLessThan(3000);
  });

  test('大文件上传性能', async ({ page }) => {
    // 这里可以测试大视频文件的上传性能
    // 由于测试环境限制，这里只做基本验证
    
    await page.goto('/four-way-analysis');
    
    const startTime = Date.now();
    await page.setInputFiles('[data-testid="east-video-input"]', TEST_FILES.eastVideo);
    
    // 等待文件上传完成
    await expect(page.locator('[data-testid="east-video-preview"]')).toBeVisible();
    
    const uploadTime = Date.now() - startTime;
    
    // 验证文件上传响应时间合理
    expect(uploadTime).toBeLessThan(10000);
  });
});
