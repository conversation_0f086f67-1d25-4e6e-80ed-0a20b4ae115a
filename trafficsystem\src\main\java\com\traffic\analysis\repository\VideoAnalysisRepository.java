package com.traffic.analysis.repository;

import com.traffic.analysis.model.VideoAnalysis;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 视频分析数据访问层接口
 * 提供对analysis_videos集合的CRUD操作
 */
@Repository
public interface VideoAnalysisRepository extends MongoRepository<VideoAnalysis, String> {
    
    // 根据任务ID查找视频分析记录
    Optional<VideoAnalysis> findByTaskId(String taskId);
    
    // 根据用户ID查找视频分析记录
    List<VideoAnalysis> findByUserId(String userId);
    
    // 分页查询用户的视频分析记录
    Page<VideoAnalysis> findByUserId(String userId, Pageable pageable);
    
    // 根据角色查找视频分析记录
    List<VideoAnalysis> findByRole(String role);
    
    // 分页查询特定角色的视频分析记录
    Page<VideoAnalysis> findByRole(String role, Pageable pageable);
    
    // 根据方向查找视频分析记录
    List<VideoAnalysis> findByDirection(String direction);
    
    // 根据状态查找视频分析记录
    List<VideoAnalysis> findByStatus(String status);
    
    // 根据用户ID和方向查找视频分析记录
    List<VideoAnalysis> findByUserIdAndDirection(String userId, String direction);
    
    // 根据用户ID和状态查找视频分析记录
    List<VideoAnalysis> findByUserIdAndStatus(String userId, String status);
    
    // 根据结果ID查找视频分析记录
    Optional<VideoAnalysis> findByResultId(String resultId);
    
    // 根据用户ID和角色查找视频分析记录
    List<VideoAnalysis> findByUserIdAndRole(String userId, String role);
    
    // 分页查询用户特定角色的视频分析记录
    Page<VideoAnalysis> findByUserIdAndRole(String userId, String role, Pageable pageable);
    
    // 根据方向和角色查找视频分析记录
    List<VideoAnalysis> findByDirectionAndRole(String direction, String role);
    
    // 分页查询方向和角色的视频分析记录
    Page<VideoAnalysis> findByDirectionAndRole(String direction, String role, Pageable pageable);
    
    // 根据用户ID、方向和角色查找视频分析记录
    List<VideoAnalysis> findByUserIdAndDirectionAndRole(String userId, String direction, String role);
    
    // 分页查询用户ID、方向和角色的视频分析记录
    Page<VideoAnalysis> findByUserIdAndDirectionAndRole(String userId, String direction, String role, Pageable pageable);

    // ==================== 扩展查询方法 ====================

    /**
     * 根据创建时间范围查找视频分析记录
     */
    List<VideoAnalysis> findByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据用户ID和创建时间范围查找视频分析记录
     */
    List<VideoAnalysis> findByUserIdAndCreatedAtBetween(String userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找正在处理的任务
     */
    @Query("{'status': 'processing'}")
    List<VideoAnalysis> findProcessingTasks();

    /**
     * 查找已完成的任务
     */
    @Query("{'status': 'completed'}")
    List<VideoAnalysis> findCompletedTasks();

    /**
     * 查找失败的任务
     */
    @Query("{'status': 'failed'}")
    List<VideoAnalysis> findFailedTasks();

    /**
     * 查找排队中的任务
     */
    @Query("{'status': 'queued'}")
    List<VideoAnalysis> findQueuedTasks();

    /**
     * 统计用户的分析任务数量
     */
    long countByUserId(String userId);

    /**
     * 统计指定状态的任务数量
     */
    long countByStatus(String status);

    /**
     * 统计用户指定状态的任务数量
     */
    long countByUserIdAndStatus(String userId, String status);

    /**
     * 统计指定时间范围内的任务数量
     */
    long countByCreatedAtBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找最近的分析记录
     */
    @Query(value = "{}", sort = "{'createdAt': -1}")
    List<VideoAnalysis> findRecentAnalyses(Pageable pageable);

    /**
     * 查找用户最近的分析记录
     */
    @Query(value = "{'userId': ?0}", sort = "{'createdAt': -1}")
    List<VideoAnalysis> findRecentAnalysesByUser(String userId, Pageable pageable);

    /**
     * 查找指定时间之前创建的记录
     */
    List<VideoAnalysis> findByCreatedAtBefore(LocalDateTime dateTime);

    /**
     * 查找指定时间之后创建的记录
     */
    List<VideoAnalysis> findByCreatedAtAfter(LocalDateTime dateTime);

    /**
     * 查找处理时间超过指定时长的任务
     */
    @Query("{'processingStartTime': {$ne: null}, 'processingEndTime': null, 'processingStartTime': {$lt: ?0}}")
    List<VideoAnalysis> findLongRunningTasks(LocalDateTime cutoffTime);

    /**
     * 删除指定时间之前的记录
     */
    void deleteByCreatedAtBefore(LocalDateTime dateTime);

    /**
     * 删除指定用户的记录
     */
    void deleteByUserId(String userId);

    /**
     * 删除指定状态的记录
     */
    void deleteByStatus(String status);

    /**
     * 查找需要清理的临时记录
     */
    @Query("{'status': {$in: ['queued', 'processing']}, 'createdAt': {$lt: ?0}}")
    List<VideoAnalysis> findStaleRecords(LocalDateTime cutoffTime);
}