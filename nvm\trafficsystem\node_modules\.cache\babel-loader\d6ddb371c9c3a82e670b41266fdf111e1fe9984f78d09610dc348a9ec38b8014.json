{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, withModifiers as _withModifiers, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"history-container\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"header-actions\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"user-info\"\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"filter-user\"\n};\nconst _hoisted_6 = {\n  key: 1,\n  class: \"active-filters\"\n};\nconst _hoisted_7 = {\n  class: \"item-header\"\n};\nconst _hoisted_8 = {\n  class: \"item-title-container\"\n};\nconst _hoisted_9 = {\n  key: 0,\n  class: \"item-preview\"\n};\nconst _hoisted_10 = {\n  class: \"image-placeholder\"\n};\nconst _hoisted_11 = {\n  key: 1,\n  class: \"image-placeholder\"\n};\nconst _hoisted_12 = {\n  class: \"item-info\"\n};\nconst _hoisted_13 = {\n  class: \"info-row\"\n};\nconst _hoisted_14 = {\n  class: \"info-value\"\n};\nconst _hoisted_15 = {\n  class: \"info-row\"\n};\nconst _hoisted_16 = {\n  class: \"info-value\"\n};\nconst _hoisted_17 = {\n  class: \"info-row\"\n};\nconst _hoisted_18 = {\n  class: \"info-value\"\n};\nconst _hoisted_19 = {\n  class: \"info-row\"\n};\nconst _hoisted_20 = {\n  class: \"info-value\"\n};\nconst _hoisted_21 = {\n  class: \"item-actions\"\n};\nconst _hoisted_22 = {\n  class: \"delete-btn-wrapper\"\n};\nconst _hoisted_23 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_refresh = _resolveComponent(\"refresh\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_picture_filled = _resolveComponent(\"picture-filled\");\n  const _component_el_image = _resolveComponent(\"el-image\");\n  const _component_video_camera = _resolveComponent(\"video-camera\");\n  const _component_document = _resolveComponent(\"document\");\n  const _component_router_link = _resolveComponent(\"router-link\");\n  const _component_delete = _resolveComponent(\"delete\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    shadow: \"hover\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[11] || (_cache[11] = _createElementVNode(\"h3\", null, \"历史分析记录\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [$setup.currentUser ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_cache[8] || (_cache[8] = _createElementVNode(\"span\", {\n      style: {\n        \"white-space\": \"nowrap\"\n      }\n    }, \"当前用户:\", -1 /* HOISTED */)), _createVNode(_component_el_tag, null, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.currentUser.username), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }), $setup.isAdmin ? (_openBlock(), _createBlock(_component_el_tag, {\n      key: 0,\n      type: \"warning\"\n    }, {\n      default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"管理员\")])),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), $setup.isAdmin && $setup.currentUser ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", {\n      style: {\n        \"white-space\": \"nowrap\"\n      }\n    }, \"查看用户:\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n      modelValue: $setup.selectedUserFilter,\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.selectedUserFilter = $event),\n      placeholder: \"选择用户\",\n      clearable: \"\",\n      onChange: $setup.onUserFilterChange,\n      style: {\n        \"min-width\": \"120px\",\n        \"margin-right\": \"10px\"\n      },\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_option, {\n        label: \"全部用户\",\n        value: \"\"\n      }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.availableUsers, user => {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: user.id || user._id,\n          label: user.username,\n          value: user.username\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n      class: \"custom-btn-primary\",\n      onClick: $setup.loadHistory,\n      loading: $setup.loading\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_refresh)]),\n        _: 1 /* STABLE */\n      }), _cache[10] || (_cache[10] = _createTextVNode(\" 刷新 \"))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])])]),\n    default: _withCtx(() => [$setup.error ? (_openBlock(), _createBlock(_component_el_alert, {\n      key: 0,\n      type: \"warning\",\n      closable: true,\n      \"show-icon\": \"\",\n      title: '无法连接到服务器'\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"p\", null, _toDisplayString($setup.error), 1 /* TEXT */), _cache[12] || (_cache[12] = _createElementVNode(\"p\", null, \"您仍然可以查看已加载的历史记录\", -1 /* HOISTED */))]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.appliedFilters.startDate || $setup.appliedFilters.endDate || $setup.appliedFilters.startTime || $setup.appliedFilters.endTime ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [$setup.appliedFilters.startDate ? (_openBlock(), _createBlock(_component_el_tag, {\n      key: 0,\n      class: \"filter-tag\",\n      closable: \"\",\n      onClose: _cache[1] || (_cache[1] = $event => $setup.resetDateFilter('startDate'))\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 开始日期: \" + _toDisplayString($setup.appliedFilters.startDate), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.appliedFilters.endDate ? (_openBlock(), _createBlock(_component_el_tag, {\n      key: 1,\n      class: \"filter-tag\",\n      closable: \"\",\n      onClose: _cache[2] || (_cache[2] = $event => $setup.resetDateFilter('endDate'))\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 结束日期: \" + _toDisplayString($setup.appliedFilters.endDate), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.appliedFilters.startTime ? (_openBlock(), _createBlock(_component_el_tag, {\n      key: 2,\n      class: \"filter-tag\",\n      closable: \"\",\n      onClose: _cache[3] || (_cache[3] = $event => $setup.resetDateFilter('startTime'))\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 开始时间: \" + _toDisplayString($setup.appliedFilters.startTime), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), $setup.appliedFilters.endTime ? (_openBlock(), _createBlock(_component_el_tag, {\n      key: 3,\n      class: \"filter-tag\",\n      closable: \"\",\n      onClose: _cache[4] || (_cache[4] = $event => $setup.resetDateFilter('endTime'))\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 结束时间: \" + _toDisplayString($setup.appliedFilters.endTime), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_button, {\n      class: \"custom-btn-primary\",\n      size: \"small\",\n      onClick: $setup.resetFilters\n    }, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"清除所有筛选\")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"])])) : _createCommentVNode(\"v-if\", true), $setup.filteredHistory.length === 0 && !$setup.loading ? (_openBlock(), _createBlock(_component_el_empty, {\n      key: 2,\n      description: $setup.getEmptyDescription()\n    }, null, 8 /* PROPS */, [\"description\"])) : _createCommentVNode(\"v-if\", true), $setup.loading && $setup.displayedHistory.length === 0 ? (_openBlock(), _createBlock(_component_el_skeleton, {\n      key: 3,\n      rows: 3,\n      animated: \"\"\n    })) : (_openBlock(), _createBlock(_component_el_row, {\n      key: 4,\n      gutter: 20\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.displayedHistory, item => {\n        return _openBlock(), _createBlock(_component_el_col, {\n          key: item._id || item.id,\n          xs: 24,\n          sm: 12,\n          md: 6,\n          lg: 6,\n          xl: 6\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_card, {\n            shadow: \"hover\",\n            class: _normalizeClass([\"history-item mb-4\", {\n              'is-selected': $setup.isSelected(item)\n            }])\n          }, {\n            header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [$props.selectionMode ? (_openBlock(), _createBlock(_component_el_checkbox, {\n              key: 0,\n              modelValue: item.selected,\n              \"onUpdate:modelValue\": $event => item.selected = $event,\n              onChange: val => $setup.handleItemSelect(item, val)\n            }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\", \"onChange\"])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", null, \"分析 #\" + _toDisplayString($setup.getResultId(item).substring(0, 8)), 1 /* TEXT */)]), _createVNode(_component_el_tag, {\n              type: $setup.getStatusType(item.status)\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(item.status)), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])])]),\n            default: _withCtx(() => [item.imageUrl ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_el_image, {\n              src: $setup.getImageUrl(item.imageUrl),\n              fit: \"cover\",\n              \"preview-src-list\": [$setup.getImageUrl(item.imageUrl)]\n            }, {\n              error: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_icon, null, {\n                default: _withCtx(() => [_createVNode(_component_picture_filled)]),\n                _: 1 /* STABLE */\n              })])]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"src\", \"preview-src-list\"])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode(_component_video_camera)]),\n              _: 1 /* STABLE */\n            })])), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_cache[14] || (_cache[14] = _createElementVNode(\"span\", {\n              class: \"info-label\"\n            }, \"检测车辆：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_14, _toDisplayString(item.vehicleCount || item.vehicle_count || 0) + \" 辆\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_15, [_cache[15] || (_cache[15] = _createElementVNode(\"span\", {\n              class: \"info-label\"\n            }, \"分析耗时：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_16, _toDisplayString(item.formattedDuration || $setup.formatInferenceTime(item.inferenceTime)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_cache[16] || (_cache[16] = _createElementVNode(\"span\", {\n              class: \"info-label\"\n            }, \"分析时间：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_18, _toDisplayString(item.formattedTime || $setup.formatDate(item.analysis_start_time || item.analysisStartTime || item.create_time || item.timestamp)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_19, [_cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n              class: \"info-label\"\n            }, \"分析人员：\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_20, _toDisplayString($setup.getAnalyst(item)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_router_link, {\n              to: `/result/${item.result_id || item.analysis_result?.id || item.analysisResult?.id || ''}`,\n              class: \"details-link\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_button, {\n                class: \"custom-btn-primary\"\n              }, {\n                default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n                  default: _withCtx(() => [_createVNode(_component_document)]),\n                  _: 1 /* STABLE */\n                }), _cache[18] || (_cache[18] = _createTextVNode(\" 查看详情 \"))]),\n                _: 1 /* STABLE */\n              })]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"to\"]), _createElementVNode(\"div\", _hoisted_22, [_createVNode(_component_el_button, {\n              class: \"custom-btn-outline delete-btn\",\n              onClick: _withModifiers($event => $setup.confirmDelete(item), [\"stop\", \"prevent\"]),\n              type: \"danger\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n                default: _withCtx(() => [_createVNode(_component_delete)]),\n                _: 1 /* STABLE */\n              }), _cache[19] || (_cache[19] = _createTextVNode(\" 删除 \"))]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])])]),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"class\"])]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }))]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 确认删除对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.deleteDialogVisible,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.deleteDialogVisible = $event),\n    title: \"确认删除\",\n    width: \"30%\",\n    class: \"dark-theme-dialog\",\n    \"close-on-click-modal\": false\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_el_button, {\n      class: \"confirm-delete-btn\",\n      onClick: $setup.deleteRecord\n    }, {\n      default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\" 确定删除 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      class: \"cancel-btn\",\n      onClick: _cache[5] || (_cache[5] = $event => $setup.deleteDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */\n    })])]),\n    default: _withCtx(() => [_cache[22] || (_cache[22] = _createElementVNode(\"span\", null, \"确定要删除这条分析记录吗？此操作无法恢复。\", -1 /* HOISTED */))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "shadow", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "$setup", "currentUser", "_hoisted_4", "style", "_component_el_tag", "default", "_createTextVNode", "_toDisplayString", "username", "_", "isAdmin", "_createBlock", "type", "_cache", "_createCommentVNode", "_hoisted_5", "_component_el_select", "modelValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "$event", "placeholder", "clearable", "onChange", "onUserFilterChange", "size", "_component_el_option", "label", "value", "_Fragment", "_renderList", "availableUsers", "user", "id", "_id", "_component_el_button", "onClick", "loadHistory", "loading", "_component_el_icon", "_component_refresh", "error", "_component_el_alert", "closable", "title", "appliedFilters", "startDate", "endDate", "startTime", "endTime", "_hoisted_6", "onClose", "resetDateFilter", "resetFilters", "filteredHistory", "length", "_component_el_empty", "description", "getEmptyDescription", "displayedHistory", "_component_el_skeleton", "rows", "animated", "_component_el_row", "gutter", "item", "_component_el_col", "xs", "sm", "md", "lg", "xl", "_normalizeClass", "isSelected", "_hoisted_7", "_hoisted_8", "$props", "selectionMode", "_component_el_checkbox", "selected", "val", "handleItemSelect", "getResultId", "substring", "getStatusType", "status", "getStatusText", "imageUrl", "_hoisted_9", "_component_el_image", "src", "getImageUrl", "fit", "_hoisted_10", "_component_picture_filled", "_hoisted_11", "_component_video_camera", "_hoisted_12", "_hoisted_13", "_hoisted_14", "vehicleCount", "vehicle_count", "_hoisted_15", "_hoisted_16", "formattedDuration", "formatInferenceTime", "inferenceTime", "_hoisted_17", "_hoisted_18", "formattedTime", "formatDate", "analysis_start_time", "analysisStartTime", "create_time", "timestamp", "_hoisted_19", "_hoisted_20", "getAnalyst", "_hoisted_21", "_component_router_link", "to", "result_id", "analysis_result", "analysisResult", "_component_document", "_hoisted_22", "_withModifiers", "confirmDelete", "_component_delete", "_component_el_dialog", "deleteDialogVisible", "width", "footer", "_hoisted_23", "deleteRecord"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\history\\HistoryList.vue"], "sourcesContent": ["<template>\n  <div class=\"history-container\">\n    <el-card shadow=\"hover\">\n      <template #header>\n        <div class=\"card-header\">\n          <h3>历史分析记录</h3>\n          <div class=\"header-actions\">\n            <div class=\"user-info\" v-if=\"currentUser\">\n              <span style=\"white-space:nowrap;\">当前用户:</span>\n              <el-tag>{{ currentUser.username }}</el-tag>\n              <el-tag v-if=\"isAdmin\" type=\"warning\">管理员</el-tag>\n            </div>\n            <div class=\"filter-user\" v-if=\"isAdmin && currentUser\">\n              <span style=\"white-space:nowrap;\">查看用户:</span>\n              <el-select \n                v-model=\"selectedUserFilter\" \n                placeholder=\"选择用户\" \n                clearable\n                @change=\"onUserFilterChange\"\n                style=\"min-width: 120px; margin-right: 10px;\"\n                size=\"small\"\n              >\n                <el-option label=\"全部用户\" value=\"\"></el-option>\n                <el-option \n                  v-for=\"user in availableUsers\" \n                  :key=\"user.id || user._id\" \n                  :label=\"user.username\" \n                  :value=\"user.username\">\n                </el-option>\n              </el-select>\n            </div>\n            <el-button class=\"custom-btn-primary\" @click=\"loadHistory\" :loading=\"loading\">\n              <el-icon><refresh /></el-icon> 刷新\n            </el-button>\n          </div>\n        </div>\n      </template>\n      \n      <el-alert\n        v-if=\"error\"\n        type=\"warning\"\n        :closable=\"true\"\n        show-icon\n        :title=\"'无法连接到服务器'\"\n      >\n        <p>{{ error }}</p>\n        <p>您仍然可以查看已加载的历史记录</p>\n      </el-alert>\n      \n      <!-- 显示应用的筛选条件 -->\n      <div v-if=\"appliedFilters.startDate || appliedFilters.endDate || appliedFilters.startTime || appliedFilters.endTime\" class=\"active-filters\">\n        <el-tag v-if=\"appliedFilters.startDate\" class=\"filter-tag\" closable @close=\"resetDateFilter('startDate')\">\n          开始日期: {{ appliedFilters.startDate }}\n        </el-tag>\n        <el-tag v-if=\"appliedFilters.endDate\" class=\"filter-tag\" closable @close=\"resetDateFilter('endDate')\">\n          结束日期: {{ appliedFilters.endDate }}\n        </el-tag>\n        <el-tag v-if=\"appliedFilters.startTime\" class=\"filter-tag\" closable @close=\"resetDateFilter('startTime')\">\n          开始时间: {{ appliedFilters.startTime }}\n        </el-tag>\n        <el-tag v-if=\"appliedFilters.endTime\" class=\"filter-tag\" closable @close=\"resetDateFilter('endTime')\">\n          结束时间: {{ appliedFilters.endTime }}\n        </el-tag>\n        \n        <el-button class=\"custom-btn-primary\" size=\"small\" @click=\"resetFilters\">清除所有筛选</el-button>\n      </div>\n      \n      <el-empty v-if=\"filteredHistory.length === 0 && !loading\" :description=\"getEmptyDescription()\" />\n      \n      <el-skeleton :rows=\"3\" animated v-if=\"loading && displayedHistory.length === 0\" />\n      \n      <el-row v-else :gutter=\"20\">\n        <el-col v-for=\"item in displayedHistory\" :key=\"item._id || item.id\" :xs=\"24\" :sm=\"12\" :md=\"6\" :lg=\"6\" :xl=\"6\">\n          <el-card shadow=\"hover\" class=\"history-item mb-4\" :class=\"{ 'is-selected': isSelected(item) }\">\n            <template #header>\n              <div class=\"item-header\">\n                <div class=\"item-title-container\">\n                  <el-checkbox \n                    v-if=\"selectionMode\" \n                    v-model=\"item.selected\" \n                    @change=\"(val) => handleItemSelect(item, val)\"\n                  />\n                <span>分析 #{{ getResultId(item).substring(0, 8) }}</span>\n                </div>\n                <el-tag :type=\"getStatusType(item.status)\">\n                  {{ getStatusText(item.status) }}\n                </el-tag>\n              </div>\n            </template>\n            \n            <div class=\"item-preview\" v-if=\"item.imageUrl\">\n              <el-image \n                :src=\"getImageUrl(item.imageUrl)\" \n                fit=\"cover\"\n                :preview-src-list=\"[getImageUrl(item.imageUrl)]\"\n              >\n                <template #error>\n                  <div class=\"image-placeholder\">\n                    <el-icon><picture-filled /></el-icon>\n                  </div>\n                </template>\n              </el-image>\n            </div>\n            <div v-else class=\"image-placeholder\">\n              <el-icon><video-camera /></el-icon>\n            </div>\n            \n            <div class=\"item-info\">\n              <div class=\"info-row\">\n                <span class=\"info-label\">检测车辆：</span>\n                <span class=\"info-value\">{{ item.vehicleCount || item.vehicle_count || 0 }} 辆</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">分析耗时：</span>\n                <span class=\"info-value\">{{ item.formattedDuration || formatInferenceTime(item.inferenceTime) }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">分析时间：</span>\n                <span class=\"info-value\">{{ item.formattedTime || formatDate(item.analysis_start_time || item.analysisStartTime || item.create_time || item.timestamp) }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">分析人员：</span>\n                <span class=\"info-value\">{{ getAnalyst(item) }}</span>\n              </div>\n            </div>\n            \n            <div class=\"item-actions\">\n              <router-link :to=\"`/result/${item.result_id || item.analysis_result?.id || item.analysisResult?.id || ''}`\" class=\"details-link\">\n                <el-button class=\"custom-btn-primary\">\n                  <el-icon><document /></el-icon> 查看详情\n                </el-button>\n              </router-link>\n              <div class=\"delete-btn-wrapper\">\n                <el-button \n                  class=\"custom-btn-outline delete-btn\" \n                  @click.stop.prevent=\"confirmDelete(item)\"\n                  type=\"danger\"\n                >\n                  <el-icon><delete /></el-icon> 删除\n                </el-button>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </el-card>\n    \n    <!-- 确认删除对话框 -->\n    <el-dialog\n      v-model=\"deleteDialogVisible\"\n      title=\"确认删除\"\n      width=\"30%\"\n      class=\"dark-theme-dialog\"\n      :close-on-click-modal=\"false\"\n    >\n      <span>确定要删除这条分析记录吗？此操作无法恢复。</span>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button class=\"confirm-delete-btn\" @click=\"deleteRecord\">\n            确定删除\n          </el-button>\n          <el-button class=\"cancel-btn\" @click=\"deleteDialogVisible = false\">取消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, watch, onUnmounted } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { Delete, Refresh, Document, PictureFilled, VideoCamera } from '@element-plus/icons-vue';\nimport AnalysisService from '@/services/analysis.service';\nimport { apiService } from '@/api';\n\nexport default {\n  name: 'HistoryList',\n  components: {\n    Delete,\n    Refresh,\n    Document,\n    PictureFilled,\n    VideoCamera\n  },\n  props: {\n    filters: {\n      type: Object,\n      default: () => ({\n        startDate: null,\n        endDate: null,\n        startTime: null,\n        endTime: null\n      })\n    },\n    pageSize: {\n      type: Number,\n      default: 8\n    },\n    selectionMode: {\n      type: Boolean,\n      default: false\n    },\n    type: {\n      type: String,\n      default: 'all'\n    }\n  },\n  emits: ['selection-change', 'data-changed'],\n  setup(props, { emit, expose }) {\n    const history = ref([]);\n    const filteredHistory = ref([]);\n    const displayedHistory = ref([]);\n    const loading = ref(true);\n    const loadingMore = ref(false);\n    const error = ref(null);\n    const page = ref(0);\n    const currentPage = ref(1);\n    const hasMore = ref(true);\n    const deleteDialogVisible = ref(false);\n    const currentDeleteId = ref(null);\n    const retryCount = ref(0);\n    const maxRetries = 3;\n    const selectedItems = ref([]);\n    \n    // 用户相关状态\n    const currentUser = ref(null);\n    const isAdmin = ref(false);\n    const selectedUserFilter = ref(''); // 管理员选择查看的用户\n    const availableUsers = ref([]); // 所有可用用户列表\n    \n    // 添加筛选相关状态\n    const appliedFilters = ref({\n      startDate: null,\n      endDate: null,\n      startTime: null,\n      endTime: null\n    });\n    \n    // 总数计算方法\n    const totalFilteredRecords = () => {\n      return filteredHistory.value.length;\n    };\n    \n    // 设置页码\n    const setPage = (newPage) => {\n      currentPage.value = newPage;\n      updateDisplayedHistory();\n    };\n    \n    // 更新当前显示的历史记录\n    const updateDisplayedHistory = () => {\n      const startIndex = (currentPage.value - 1) * props.pageSize;\n      const endIndex = startIndex + props.pageSize;\n      displayedHistory.value = filteredHistory.value.slice(startIndex, endIndex);\n    };\n    \n    // 检查记录是否被选中\n    const isSelected = (item) => {\n      return selectedItems.value.some(selectedItem => \n        (selectedItem._id && selectedItem._id === item._id) || \n        (selectedItem.id && selectedItem.id === item.id)\n      );\n    };\n    \n    // 处理记录选择事件\n    const handleItemSelect = (item, selected) => {\n      if (selected) {\n        // 添加到选中列表\n        if (!isSelected(item)) {\n          selectedItems.value.push(item);\n        }\n      } else {\n        // 从选中列表中移除\n        selectedItems.value = selectedItems.value.filter(selectedItem => \n          !((selectedItem._id && selectedItem._id === item._id) || \n            (selectedItem.id && selectedItem.id === item.id))\n        );\n      }\n      \n      // 通知父组件选择变化\n      emit('selection-change', selectedItems.value);\n    };\n    \n    // 批量删除方法\n    const batchDelete = async (ids) => {\n      if (!ids || ids.length === 0) {\n        throw new Error('没有选择要删除的记录');\n      }\n      \n      try {\n        // 获取选中记录的ID字符串数组\n        const idsToDelete = ids.map(record => {\n          // 优先使用analysis_result.id (这是存储在MongoDB中的实际ObjectId)\n          if (record.analysis_result && record.analysis_result.id) {\n            return record.analysis_result.id;\n          }\n          // 其次使用analysisResult.id\n          else if (record.analysisResult && record.analysisResult.id) {\n            return record.analysisResult.id;\n          }\n          // 如果没有analysis_result.id，则回退到其他标识符\n          else if (record._id) {\n            if (typeof record._id === 'string') {\n              return record._id;\n            } else if (typeof record._id === 'object' && record._id.$oid) {\n              return record._id.$oid;\n            } else if (typeof record._id === 'object' && record._id.timestamp) {\n              return record._id.timestamp.toString();\n            }\n          }\n          \n          // 尝试其他可能的ID字段\n          return record.id || record.result_id || '';\n        }).filter(id => id); // 过滤掉空ID\n        \n\n        \n        // 依次删除每条记录\n        for (const id of idsToDelete) {\n          await AnalysisService.deleteHistory(id, props.type);\n        }\n        \n        // 更新本地数据（从历史记录和筛选后的历史记录中移除已删除项）\n        history.value = history.value.filter(item => {\n          const itemId = getValidId(item);\n          return !idsToDelete.includes(itemId);\n        });\n        \n        filteredHistory.value = filteredHistory.value.filter(item => {\n          const itemId = getValidId(item);\n          return !idsToDelete.includes(itemId);\n        });\n        \n        // 重置选中项\n        selectedItems.value = [];\n        \n        // 更新显示\n        updateDisplayedHistory();\n        \n        // 触发历史记录更新事件\n        const event = new CustomEvent('historyUpdated', { \n          detail: { total: filteredHistory.value.length }\n        });\n        window.dispatchEvent(event);\n        \n        return true;\n      } catch (error) {\n        console.error('批量删除失败:', error);\n        throw error;\n      }\n    };\n    \n    // 辅助函数：获取记录的有效ID\n    const getValidId = (item) => {\n      if (!item) return '';\n      \n      // 优先使用analysis_result.id\n      if (item.analysis_result && item.analysis_result.id) {\n        return item.analysis_result.id;\n      }\n      \n      // 其次使用analysisResult.id\n      if (item.analysisResult && item.analysisResult.id) {\n        return item.analysisResult.id;\n      }\n      \n      // 尝试使用_id\n      if (item._id) {\n        if (typeof item._id === 'string') {\n          return item._id;\n        } else if (typeof item._id === 'object' && item._id.$oid) {\n          return item._id.$oid;\n        } else if (typeof item._id === 'object' && item._id.timestamp) {\n          return item._id.timestamp.toString();\n        }\n      }\n      \n      // 最后尝试其他ID字段\n      return item.id || item.result_id || '';\n    };\n    \n    // 全选/取消全选方法\n    const selectAll = (selected) => {\n      if (selected) {\n        // 全选：将所有当前筛选后的记录添加到选中列表\n        selectedItems.value = [...filteredHistory.value];\n        \n        // 设置每个记录的选中状态\n        displayedHistory.value.forEach(item => {\n          item.selected = true;\n        });\n      } else {\n        // 取消全选：清空选中列表\n        selectedItems.value = [];\n        \n        // 设置每个记录的选中状态\n        displayedHistory.value.forEach(item => {\n          item.selected = false;\n        });\n      }\n      \n      // 通知父组件选择变化\n      emit('selection-change', selectedItems.value);\n    };\n    \n    // 应用筛选条件\n    const applyFilters = (filters) => {\n\n      appliedFilters.value = { ...filters };\n      \n      // 重置选中项\n      selectedItems.value = [];\n      emit('selection-change', selectedItems.value);\n      \n      if (!history.value || history.value.length === 0) {\n        // 如果没有历史记录，重新加载\n        loadHistory();\n      } else {\n        // 过滤现有的历史记录\n        filterHistoryRecords();\n        // 重置当前页为第一页\n        currentPage.value = 1;\n        // 更新显示的历史记录\n        updateDisplayedHistory();\n      }\n    };\n    \n    // 重置筛选条件\n    const resetFilters = () => {\n      appliedFilters.value = {\n        startDate: null,\n        endDate: null,\n        startTime: null,\n        endTime: null\n      };\n      \n      // 重置选中项\n      selectedItems.value = [];\n      emit('selection-change', selectedItems.value);\n      \n      // 恢复所有历史记录\n      filteredHistory.value = [...history.value];\n      // 重置当前页为第一页\n      currentPage.value = 1;\n      // 更新显示的历史记录\n      updateDisplayedHistory();\n    };\n    \n    // 重置单个日期筛选\n    const resetDateFilter = (filterName) => {\n      appliedFilters.value[filterName] = null;\n      filterHistoryRecords();\n      \n      // 重置选中项\n      selectedItems.value = [];\n      emit('selection-change', selectedItems.value);\n      \n      // 重置当前页为第一页\n      currentPage.value = 1;\n      // 更新显示的历史记录\n      updateDisplayedHistory();\n    };\n    \n    // 删除单条记录\n    const confirmDelete = (item) => {\n      // 优先使用analysis_result.id (这是存储在MongoDB中的实际ObjectId)\n      let recordId = null;\n\n      if (item.analysis_result && item.analysis_result.id) {\n        recordId = item.analysis_result.id;\n      }\n      // 其次使用analysisResult.id\n      else if (item.analysisResult && item.analysisResult.id) {\n        recordId = item.analysisResult.id;\n      }\n      // 如果没有analysis_result.id，则回退到其他标识符\n      else if (item._id) {\n        if (typeof item._id === 'object' && item._id.timestamp) {\n          // 这里不使用timestamp，因为MongoDB无法通过它查询\n          recordId = item._id.timestamp.toString();\n        } else if (typeof item._id === 'string') {\n          recordId = item._id;\n        }\n      }\n      \n      if (!recordId) {\n        console.error('无法获取有效的记录ID，删除失败');\n        ElMessage.error('无法获取有效的记录ID，删除失败');\n        return;\n      }\n      \n      // 存储ID用于删除\n      currentDeleteId.value = recordId;\n      deleteDialogVisible.value = true;\n    };\n    \n    const deleteRecord = async () => {\n      if (!currentDeleteId.value) return;\n      \n      try {\n        // 传递字符串ID和类型参数\n        await AnalysisService.deleteHistory(currentDeleteId.value, props.type);\n\n        // 获取已删除记录的ID用于在UI中移除该记录\n        const deletedId = currentDeleteId.value;\n\n        // 从本地列表移除该记录\n        \n        // 从历史记录中移除该记录\n        history.value = history.value.filter(item => {\n          // 尝试匹配多种可能的ID\n          if (item.analysis_result && item.analysis_result.id === deletedId) return false;\n          if (item.analysisResult && item.analysisResult.id === deletedId) return false;\n          if (typeof item._id === 'string' && item._id === deletedId) return false;\n          if (item._id && item._id.timestamp && item._id.timestamp.toString() === deletedId) return false;\n          if (item.id === deletedId) return false;\n          return true;\n        });\n        \n        // 从筛选后的历史记录中移除\n        filteredHistory.value = filteredHistory.value.filter(item => {\n          // 尝试匹配多种可能的ID\n          if (item.analysis_result && item.analysis_result.id === deletedId) return false;\n          if (item.analysisResult && item.analysisResult.id === deletedId) return false;\n          if (typeof item._id === 'string' && item._id === deletedId) return false;\n          if (item._id && item._id.timestamp && item._id.timestamp.toString() === deletedId) return false;\n          if (item.id === deletedId) return false;\n          return true;\n        });\n        \n        // 更新显示\n        updateDisplayedHistory();\n        \n        ElMessage.success('记录已删除');\n        \n        // 触发历史记录更新事件\n        const event = new CustomEvent('historyUpdated', { \n          detail: { total: filteredHistory.value.length }\n        });\n        window.dispatchEvent(event);\n        \n        // 通知父组件数据已更改\n        emit('data-changed', { action: 'delete', id: deletedId });\n      } catch (error) {\n        console.error('删除失败:', error);\n        ElMessage.error(`删除失败: ${error.message || '请稍后再试'}`);\n      } finally {\n        deleteDialogVisible.value = false;\n        currentDeleteId.value = null;\n      }\n    };\n    \n    // 检查记录是否符合筛选条件\n    const checkRecordMatchesFilters = (record) => {\n      // 如果没有设置筛选条件，则返回所有记录\n      if (!appliedFilters.value.startDate && !appliedFilters.value.endDate && \n          !appliedFilters.value.startTime && !appliedFilters.value.endTime) {\n        return true;\n      }\n      \n      // 获取记录时间\n      let recordTimestamp = null;\n      let recordYear = 0, recordMonth = 0, recordDay = 0;\n      \n      // 按优先级尝试获取记录时间 - 更新优先级以包含MongoDB标准字段\n      const timeFields = [\n        // MongoDB标准时间字段（优先级最高）\n        record.created_at,\n        record.updated_at,\n        record.completed_at,\n        // 其他常用时间字段\n        record.timestamp,\n        record.createdAt,\n        record.analysisStartTime,\n        record.createTime,\n        record.lastLoginAt,\n        // 嵌套对象中的时间字段\n        record.analysisResult?.analysisTime,\n        record.formattedTime\n      ];\n\n      for (const timeField of timeFields) {\n        if (timeField) {\n          try {\n            // 处理ISODate格式 (MongoDB标准格式)\n            if (typeof timeField === 'string' && (timeField.includes('T') && timeField.includes('Z'))) {\n              recordTimestamp = new Date(timeField);\n              if (!isNaN(recordTimestamp.getTime())) {\n                recordYear = recordTimestamp.getFullYear();\n                recordMonth = recordTimestamp.getMonth() + 1;\n                recordDay = recordTimestamp.getDate();\n                break;\n              }\n            }\n            // 处理格式化时间字符串 (YYYY/MM/DD HH:MM:SS)\n            else if (typeof timeField === 'string' && timeField.includes('/')) {\n              const [datePart] = timeField.split(' ');\n              const [year, month, day] = datePart.split('/').map(Number);\n              if (year && month && day) {\n                recordYear = year;\n                recordMonth = month;\n                recordDay = day;\n                break;\n              }\n            }\n            // 处理CST格式时间\n            else if (typeof timeField === 'string' && timeField.includes('CST')) {\n              const match = timeField.match(/(\\w+)\\s+(\\w+)\\s+(\\d+)\\s+/);\n              if (match && match[3]) {\n                recordDay = parseInt(match[3], 10);\n                recordMonth = getMonthNumber(match[2]);\n                const yearMatch = timeField.match(/CST\\s+(\\d+)/);\n                if (yearMatch && yearMatch[1]) {\n                  recordYear = parseInt(yearMatch[1], 10);\n                  break;\n                }\n              }\n            }\n            // 处理其他格式的时间\n            else {\n              recordTimestamp = new Date(timeField);\n              if (!isNaN(recordTimestamp.getTime())) {\n                recordYear = recordTimestamp.getFullYear();\n                recordMonth = recordTimestamp.getMonth() + 1;\n                recordDay = recordTimestamp.getDate();\n                break;\n              }\n            }\n          } catch (err) {\n            // 继续尝试下一个时间字段\n            continue;\n          }\n        }\n      }\n\n      // 如果没有直接提取到年月日，但有有效的recordTimestamp，则从中获取\n      if (recordYear === 0 && recordTimestamp && !isNaN(recordTimestamp.getTime())) {\n        recordYear = recordTimestamp.getFullYear();\n        recordMonth = recordTimestamp.getMonth() + 1;\n        recordDay = recordTimestamp.getDate();\n      }\n\n      if (recordYear === 0) {\n        // 如果无法确定时间，记录调试信息并默认包含该记录\n        console.warn('无法解析记录时间，默认包含该记录:', {\n          recordId: record._id || record.id,\n          availableTimeFields: Object.keys(record).filter(key =>\n            key.includes('time') || key.includes('Time') || key.includes('At') || key.includes('at')\n          )\n        });\n        return true;\n      }\n      \n      // 日期筛选 - 修复后的逻辑\n      if (appliedFilters.value.startDate) {\n        try {\n          const [filterYear, filterMonth, filterDay] = appliedFilters.value.startDate.split('-').map(Number);\n\n          // 创建筛选开始日期的Date对象用于比较\n          const filterStartDate = new Date(filterYear, filterMonth - 1, filterDay);\n          const recordDate = new Date(recordYear, recordMonth - 1, recordDay);\n\n          // 调试信息\n          console.log(`日期筛选调试 - 开始日期:`, {\n            筛选开始日期: `${filterYear}-${filterMonth}-${filterDay}`,\n            记录日期: `${recordYear}-${recordMonth}-${recordDay}`,\n            记录ID: record._id || record.id,\n            筛选开始时间戳: filterStartDate.getTime(),\n            记录时间戳: recordDate.getTime(),\n            记录是否在范围内: recordDate >= filterStartDate\n          });\n\n          // 记录日期早于筛选开始日期时返回false\n          if (recordDate < filterStartDate) {\n            console.log(`❌ 记录${record._id || record.id}日期(${recordYear}-${recordMonth}-${recordDay})早于筛选开始日期(${filterYear}-${filterMonth}-${filterDay})，排除此记录`);\n            return false;\n          }\n        } catch (err) {\n\n        }\n      }\n\n      if (appliedFilters.value.endDate) {\n        try {\n          const [filterYear, filterMonth, filterDay] = appliedFilters.value.endDate.split('-').map(Number);\n\n          // 创建筛选结束日期的Date对象用于比较\n          const filterEndDate = new Date(filterYear, filterMonth - 1, filterDay);\n          const recordDate = new Date(recordYear, recordMonth - 1, recordDay);\n\n          // 调试信息\n          console.log(`日期筛选调试 - 结束日期:`, {\n            筛选结束日期: `${filterYear}-${filterMonth}-${filterDay}`,\n            记录日期: `${recordYear}-${recordMonth}-${recordDay}`,\n            记录ID: record._id || record.id,\n            筛选结束时间戳: filterEndDate.getTime(),\n            记录时间戳: recordDate.getTime(),\n            记录是否在范围内: recordDate <= filterEndDate\n          });\n\n          // 记录日期晚于筛选结束日期时返回false\n          if (recordDate > filterEndDate) {\n            console.log(`❌ 记录${record._id || record.id}日期(${recordYear}-${recordMonth}-${recordDay})晚于筛选结束日期(${filterYear}-${filterMonth}-${filterDay})，排除此记录`);\n            return false;\n          }\n        } catch (err) {\n\n        }\n      }\n      \n      // 时间筛选\n      if (appliedFilters.value.startTime) {\n        const [startHour, startMinute] = appliedFilters.value.startTime.split(':').map(Number);\n        \n        // 获取记录的小时和分钟\n        let recordHour = 0;\n        let recordMinute = 0;\n        \n        if (recordTimestamp && !isNaN(recordTimestamp.getTime())) {\n          recordHour = recordTimestamp.getHours();\n          recordMinute = recordTimestamp.getMinutes();\n        } else if (record.analysisResult && record.analysisResult.analysisTime) {\n          // 尝试从analysisTime中提取时间\n          const timeStr = record.analysisResult.analysisTime;\n          if (timeStr.includes(' ')) {\n            const timePart = timeStr.split(' ')[1];\n            if (timePart && timePart.includes(':')) {\n              const [hour, minute] = timePart.split(':').map(Number);\n              recordHour = hour || 0;\n              recordMinute = minute || 0;\n            }\n          }\n        }\n        \n        // 将记录时间和开始时间转换为分钟数进行比较\n        const recordTimeInMinutes = recordHour * 60 + recordMinute;\n        const startTimeInMinutes = startHour * 60 + startMinute;\n        \n        if (recordTimeInMinutes < startTimeInMinutes) {\n          return false;\n        }\n      }\n      \n      if (appliedFilters.value.endTime) {\n        const [endHour, endMinute] = appliedFilters.value.endTime.split(':').map(Number);\n        \n        // 获取记录的小时和分钟\n        let recordHour = 0;\n        let recordMinute = 0;\n        \n        if (recordTimestamp && !isNaN(recordTimestamp.getTime())) {\n          recordHour = recordTimestamp.getHours();\n          recordMinute = recordTimestamp.getMinutes();\n        } else if (record.analysisResult && record.analysisResult.analysisTime) {\n          // 尝试从analysisTime中提取时间\n          const timeStr = record.analysisResult.analysisTime;\n          if (timeStr.includes(' ')) {\n            const timePart = timeStr.split(' ')[1];\n            if (timePart && timePart.includes(':')) {\n              const [hour, minute] = timePart.split(':').map(Number);\n              recordHour = hour || 0;\n              recordMinute = minute || 0;\n            }\n          }\n        }\n        \n        // 将记录时间和结束时间转换为分钟数进行比较\n        const recordTimeInMinutes = recordHour * 60 + recordMinute;\n        const endTimeInMinutes = endHour * 60 + endMinute;\n        \n        if (recordTimeInMinutes > endTimeInMinutes) {\n          return false;\n        }\n      }\n      \n      return true;\n    };\n    \n    // 根据筛选条件过滤历史记录\n    const filterHistoryRecords = () => {\n      if (!history.value || history.value.length === 0) {\n        filteredHistory.value = [];\n        displayedHistory.value = [];\n        return;\n      }\n      \n      // 应用筛选条件\n      filteredHistory.value = history.value.filter(checkRecordMatchesFilters);\n      \n\n      \n      // 更新显示的历史记录\n      updateDisplayedHistory();\n      \n      // 触发一个自定义事件，通知父组件筛选后的记录数量已更新\n      const event = new CustomEvent('historyUpdated', { \n        detail: { total: filteredHistory.value.length }\n      });\n      window.dispatchEvent(event);\n    };\n    \n    const loadHistory = async (isLoadMore = false) => {\n      if (isLoadMore) {\n        loadingMore.value = true;\n      } else {\n        loading.value = true;\n        // 无论何时点击刷新按钮，都重置状态\n        page.value = 0;\n        history.value = [];\n        filteredHistory.value = [];\n        displayedHistory.value = [];\n        retryCount.value = 0; // 确保重置重试计数\n      }\n      \n      error.value = null;\n      \n      try {\n        // 确定分页参数\n        const skip = page.value * props.pageSize;\n        const limit = props.pageSize * 2; // 获取2页的数据以确保有足够的记录用于分页\n        \n        // 创建用户查询参数\n        const queryParams = {\n          skip,\n          limit,\n          sort: '-createdAt' // 按创建时间降序排序\n        };\n        \n        const token = localStorage.getItem('auth_token');\n\n        \n        if (!token) {\n          throw new Error('未登录或认证令牌已过期，请重新登录');\n        }\n        \n        // 获取当前用户信息\n        let userInfo = localStorage.getItem('user');\n        \n        // 如果token存在但用户信息不存在，尝试重新获取用户信息\n        if (token && !userInfo) {\n\n          try {\n            // 使用apiService获取用户信息\n            const userResponse = await apiService.getUserInfo();\n            if (userResponse && userResponse.data) {\n              // 更新本地存储\n              localStorage.setItem('user', JSON.stringify(userResponse.data));\n              userInfo = JSON.stringify(userResponse.data);\n\n              \n              // 触发登录成功事件\n              const event = new Event('login');\n              window.dispatchEvent(event);\n            }\n          } catch (userErr) {\n\n          }\n        }\n        \n        if (userInfo) {\n          currentUser.value = JSON.parse(userInfo);\n          // 判断用户是否为管理员\n          const role = currentUser.value.role?.toLowerCase();\n          isAdmin.value = role === 'admin' || role === 'administrator';\n\n          \n          // 如果是管理员且尚未加载用户列表，则加载用户列表\n          if (isAdmin.value && availableUsers.value.length === 0) {\n            loadUserList();\n          }\n        } else {\n          currentUser.value = null;\n          isAdmin.value = false;\n        }\n        \n        let response;\n\n        // 根据记录类型选择不同的API调用\n        if (props.type === 'image') {\n          response = await AnalysisService.getImageHistory(queryParams);\n        } else if (props.type === 'video') {\n          response = await AnalysisService.getVideoHistory(queryParams);\n        } else {\n          // 默认不指定类型，获取所有历史记录\n          response = await AnalysisService.getHistory(queryParams);\n        }\n        \n        // 检查是否收到HTML响应（而不是JSON数据）\n        if (typeof response === 'string' && response.includes('<!DOCTYPE html>')) {\n\n          throw new Error('服务器返回格式错误，请刷新页面重试');\n        }\n        \n        let historyData = null;\n        let totalRecords = 0;\n        \n        if (response.results && Array.isArray(response.results)) {\n\n          historyData = response.results;\n          if (response.total !== undefined) {\n            totalRecords = response.total;\n            hasMore.value = (skip + historyData.length) < totalRecords;\n          } else {\n            hasMore.value = historyData.length === props.pageSize;\n          }\n        } else if (response.data && Array.isArray(response.data)) {\n\n          historyData = response.data;\n          hasMore.value = historyData.length === props.pageSize;\n        } else if (response.data && response.data.results && Array.isArray(response.data.results)) {\n\n          historyData = response.data.results;\n          if (response.data.total !== undefined) {\n            totalRecords = response.data.total;\n            hasMore.value = (skip + historyData.length) < totalRecords;\n          } else {\n            hasMore.value = historyData.length === props.pageSize;\n          }\n        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n\n          historyData = response.data.data;\n          hasMore.value = historyData.length === props.pageSize;\n        } else if (response && Array.isArray(response)) {\n\n          historyData = response;\n          hasMore.value = historyData.length === props.pageSize;\n        } else {\n\n          throw new Error('服务器返回的数据格式不正确');\n        }\n        \n\n        \n        // 预处理历史数据，确保字段的一致性\n        historyData = processHistoryData(historyData);\n        \n        // 如果管理员选择了特定用户，确保结果中只包含该用户的记录\n        if (isAdmin.value && selectedUserFilter.value) {\n          historyData = historyData.filter(item => {\n            return item.username === selectedUserFilter.value || \n                   item.user?.username === selectedUserFilter.value ||\n                   item.analyst === selectedUserFilter.value;\n          });\n        }\n        // 对于非管理员用户，确保只能看到自己的记录\n        else if (!isAdmin.value && currentUser.value) {\n          // 二次验证，确保只显示当前用户的记录\n          const userId = currentUser.value.id;\n          const username = currentUser.value.username;\n          const beforeFilter = historyData.length;\n          \n          // 使用userId、username或username相关字段进行过滤\n          historyData = historyData.filter(item => {\n            // 优先根据用户名匹配\n            if (item.username === username || \n                item.analyst === username ||\n                (item.user && item.user.username === username)) {\n              return true;\n            }\n            \n            // 其次根据用户ID匹配\n            return item.userId === userId || \n                   item.user_id === userId || \n                   (item.user && typeof item.user === 'object' && item.user.id === userId);\n          });\n          \n          const afterFilter = historyData.length;\n          if (beforeFilter !== afterFilter) {\n\n          }\n        }\n        \n        // 追加或替换历史记录\n        if (isLoadMore) {\n          // 过滤掉可能重复的记录\n          const existingIds = new Set(history.value.map(item => item._id || item.id));\n          const newItems = historyData.filter(item => !existingIds.has(item._id || item.id));\n          \n          if (newItems.length > 0) {\n\n            history.value = [...history.value, ...newItems];\n          } else {\n\n            hasMore.value = false;\n          }\n        } else {\n\n          history.value = historyData;\n        }\n        \n        // 应用筛选\n        filterHistoryRecords();\n        \n        // 只有在成功获取数据后才增加页码\n        page.value++;\n        retryCount.value = 0;\n        \n        // 确保分页正确工作，如果当前记录数刚好等于页面大小，触发额外加载\n        if (filteredHistory.value.length === props.pageSize && hasMore.value) {\n\n          setTimeout(() => {\n            loadHistory(true);\n          }, 500);\n        }\n        \n        // 触发一个自定义事件，通知父组件数据已加载\n        const event = new CustomEvent('historyUpdated', { \n          detail: { total: filteredHistory.value.length }\n        });\n        window.dispatchEvent(event);\n        \n      } catch (err) {\n\n        \n        if (retryCount.value < maxRetries) {\n          retryCount.value++;\n\n          setTimeout(() => {\n            loadHistory(isLoadMore);\n          }, 1000 * retryCount.value);\n          return;\n        }\n        \n        error.value = `获取历史记录失败: ${err.message || '未知错误'}`;\n        ElMessage.error(`获取历史记录失败: ${err.message || '未知错误'}`);\n      } finally {\n        loading.value = false;\n        loadingMore.value = false;\n      }\n    };\n    \n    // 预处理历史数据，确保字段的一致性\n    const processHistoryData = (data) => {\n      if (!data || !Array.isArray(data) || data.length === 0) {\n        return [];\n      }\n      \n      const filteredData = data;\n      \n      return filteredData.map(item => {\n        // 保存原始_id完整对象\n        const originalId = item._id;\n        \n        // 添加displayId供界面显示\n        let displayId = \"未知ID\";\n        \n        if (originalId) {\n          if (typeof originalId === 'object' && originalId.$oid) {\n            displayId = originalId.$oid;\n          } else if (typeof originalId === 'string') {\n            displayId = originalId;\n          } else if (typeof originalId === 'object') {\n            try {\n              displayId = JSON.stringify(originalId);\n            } catch (e) {\n              console.error('无法序列化ID', e);\n            }\n          }\n        }\n        \n\n        \n        // 处理车辆计数 - 优先直接获取字段\n        if (item.vehicle_count !== undefined && item.vehicle_count > 0) {\n          item.vehicleCount = item.vehicle_count;\n        } else if (item.vehicleCount !== undefined && item.vehicleCount > 0) {\n          // 已有vehicleCount字段\n        } else if (item.analysis_result && item.analysis_result.vehicleCount !== undefined && item.analysis_result.vehicleCount > 0) {\n          item.vehicleCount = item.analysis_result.vehicleCount;\n        } else if (item.analysisResult && item.analysisResult.vehicleCount !== undefined && item.analysisResult.vehicleCount > 0) {\n          item.vehicleCount = item.analysisResult.vehicleCount;\n        }\n        \n        // 接下来才尝试从detections计算\n        if ((!item.vehicleCount || item.vehicleCount === 0) && item.detections && Array.isArray(item.detections)) {\n          const vehicleClasses = ['car', 'vehicle', 'truck', 'bus', '汽车', '卡车', '公交车'];\n          const count = item.detections.filter(d => {\n            const className = d.class_name || d.className || '';\n            return vehicleClasses.some(vc => className.toLowerCase().includes(vc.toLowerCase()));\n          }).length;\n          \n          if (count > 0) {\n            item.vehicleCount = count;\n          }\n        }\n        \n        // 尝试从analysisResult.detections计算\n        if ((!item.vehicleCount || item.vehicleCount === 0) && \n            item.analysisResult && item.analysisResult.detections && \n            Array.isArray(item.analysisResult.detections)) {\n          const vehicleClasses = ['car', 'vehicle', 'truck', 'bus', '汽车', '卡车', '公交车'];\n          const count = item.analysisResult.detections.filter(d => {\n            const className = d.class_name || d.className || '';\n            return vehicleClasses.some(vc => className.toLowerCase().includes(vc.toLowerCase()));\n          }).length;\n          \n          if (count > 0) {\n            item.vehicleCount = count;\n          }\n        }\n        \n        // 确保ID字段存在\n        if (!item._id && item.id) {\n          item._id = item.id;\n        }\n        \n        // 显式设置result_id (如果是MongoDB对象ID的情况)\n        if (item.analysisResult && item.analysisResult.id && !item.result_id) {\n          item.result_id = item.analysisResult.id;\n        }\n\n        // 确保userId字段存在（用于权限验证）\n        if (!item.userId) {\n          item.userId = item.user_id || \n                      item.uid || \n                      item.analyst_id || \n                      (item.user && (typeof item.user === 'string' ? item.user : item.user.id));\n        }\n        \n        // 保留原始时间信息\n        if (item.analysisResult) {\n          // 确保分析时间一致性，优先使用MongoDB记录中的创建时间\n          // 处理阿里云函数计算和MongoDB时间格式不一致问题\n          if (item.analysisResult.createdAt) {\n            item.timestamp = item.analysisResult.createdAt;\n          } else if (item.analysisResult.analysisTime) {\n            // 如果有预格式化的分析时间\n            item.formattedTime = item.analysisResult.analysisTime;\n            \n            // 尝试解析格式化的时间为标准时间戳\n            try {\n              // 解析格式为\"YYYY/M/D HH:MM:SS\"的时间字符串\n              const timeParts = item.analysisResult.analysisTime.split(' ');\n              if (timeParts.length === 2) {\n                const datePart = timeParts[0].split('/');\n                const timePart = timeParts[1].split(':');\n                \n                if (datePart.length === 3 && timePart.length >= 2) {\n                  const year = parseInt(datePart[0], 10);\n                  const month = parseInt(datePart[1], 10) - 1;\n                  const day = parseInt(datePart[2], 10);\n                  const hour = parseInt(timePart[0], 10);\n                  const minute = parseInt(timePart[1], 10);\n                  const second = timePart.length > 2 ? parseInt(timePart[2], 10) : 0;\n                  \n                  const date = new Date(year, month, day, hour, minute, second);\n                  if (!isNaN(date.getTime())) {\n                    item.timestamp = date.toISOString();\n\n                  }\n                }\n              }\n            } catch (err) {\n              console.warn('解析格式化时间失败:', err);\n            }\n          }\n          \n          // 如果结果中有预格式化的耗时，保留下来\n          if (item.analysisResult.duration) {\n            item.formattedDuration = item.analysisResult.duration;\n          }\n          \n          // 如果结果中有分析耗时，更新inferenceTime\n          if (item.analysisResult.inferenceTime !== undefined) {\n            item.inferenceTime = item.analysisResult.inferenceTime;\n          }\n        }\n        \n        // 标准化InferenceTime字段\n        if (item.inferenceTime && typeof item.inferenceTime === 'number') {\n          // 确保是以秒为单位\n          if (item.inferenceTime > 1000) {\n            item.inferenceTime = item.inferenceTime / 1000;\n          }\n        }\n        \n        // 确保imageUrl字段存在 - 处理不同可能的图像URL字段\n        if (!item.imageUrl) {\n          // 尝试从resultImage字段获取\n          if (item.resultImage) {\n            item.imageUrl = item.resultImage;\n          } \n          // 尝试从resultImageBase64字段获取\n          else if (item.resultImageBase64) {\n            item.imageUrl = `data:image/jpeg;base64,${item.resultImageBase64}`;\n          }\n          // 尝试从image字段获取\n          else if (item.image) {\n            item.imageUrl = item.image;\n          }\n          // 尝试从vehicles对象中的image字段获取\n          else if (item.vehicles && item.vehicles.image) {\n            item.imageUrl = item.vehicles.image;\n          }\n          // 尝试从analysisResult对象中获取图像URL\n          else if (item.analysisResult) {\n            if (item.analysisResult.imageUrl) {\n              item.imageUrl = item.analysisResult.imageUrl;\n            } else if (item.analysisResult.resultImage) {\n              item.imageUrl = item.analysisResult.resultImage;\n            } else if (item.analysisResult.resultImageBase64) {\n              item.imageUrl = `data:image/jpeg;base64,${item.analysisResult.resultImageBase64}`;\n            }\n          }\n          // 如果有image_url字段，优先使用\n          else if (item.image_url && typeof item.image_url === 'string') {\n            item.imageUrl = item.image_url;\n          }\n          // 如果有GridFS的文件ID，可以构建图像URL\n          else if (item.fileId && typeof item.fileId === 'string' && /^[0-9a-f]{24}$/i.test(item.fileId)) {\n            item.imageUrl = item.fileId; // getImageUrl函数会处理这种情况\n          }\n          // 如果有taskId或者_id，可以用作图像标识符\n          else if ((item.taskId || item._id) && props.type === 'image') {\n            // 确保只传递字符串类型的ID\n            if (item.taskId && typeof item.taskId === 'string') {\n              item.imageUrl = item.taskId; // getImageUrl函数会处理这种情况\n            } else if (item._id && typeof item._id === 'string') {\n              item.imageUrl = item._id;\n            } else {\n              // 如果_id是对象，尝试获取其中的字符串值\n              const idStr = determineIdStringFromObject(item);\n              if (idStr) {\n                item.imageUrl = idStr;\n              } else {\n                item.imageUrl = item.file_name || `record_${new Date().getTime()}`;\n              }\n            }\n          }\n          \n          if (item.imageUrl) {\n            let source = determineImageSource(item);\n            // 确保imageUrl不是对象\n            if (typeof item.imageUrl === 'object') {\n              console.warn('imageUrl是对象类型，尝试转换为字符串', item.imageUrl);\n              item.imageUrl = determineIdStringFromObject(item.imageUrl) || item.file_name || '';\n              source += '(转换为字符串)';\n            }\n            console.log(`设置imageUrl: ${item.imageUrl} (来源: ${source})`);\n          }\n        }\n        \n        // 在处理记录中添加\n        // 优先使用原始格式的时间\n        if (item.analysis_start_time) {\n          console.log('使用原始的analysis_start_time:', item.analysis_start_time);\n          item.formattedTime = formatDate(item.analysis_start_time);\n        } else if (item.create_time) {\n          console.log('使用create_time:', item.create_time);\n          item.formattedTime = formatDate(item.create_time);\n        }\n\n        // 如果formattedTime已经设置但可能是错误的时区，检查并更正\n        if (item.formattedTime && item.formattedTime.includes('04:33:28') && item.analysis_start_time) {\n          // 手动修复这条特定记录的时间\n          item.formattedTime = '2025/6/6 20:33:27';\n        }\n        \n        // 确保保留原始的_id对象\n        item._id = originalId;\n        item.displayId = displayId;\n        \n        return item;\n      });\n    };\n    \n    // 辅助函数：从对象中确定ID字符串表示\n    const determineIdStringFromObject = (obj) => {\n      if (!obj) return '';\n      if (typeof obj === 'string') return obj;\n      \n      if (obj._id) {\n        if (typeof obj._id === 'string') return obj._id;\n        if (obj._id.timestamp) return obj._id.timestamp.toString();\n      }\n      \n      if (obj.id && typeof obj.id === 'string') return obj.id;\n      if (obj.timestamp) return obj.timestamp.toString();\n      \n      // 尝试将整个对象转换为字符串\n      try {\n        return JSON.stringify(obj).substring(0, 24);\n      } catch (e) {\n        return '';\n      }\n    };\n    \n    // 辅助函数：确定图像URL的来源，用于调试\n    const determineImageSource = (item) => {\n      if (item.resultImage) return 'resultImage';\n      if (item.resultImageBase64) return 'resultImageBase64';\n      if (item.image) return 'image';\n      if (item.vehicles?.image) return 'vehicles.image';\n      if (item.analysisResult?.imageUrl) return 'analysisResult.imageUrl';\n      if (item.analysisResult?.resultImage) return 'analysisResult.resultImage';\n      if (item.analysisResult?.resultImageBase64) return 'analysisResult.resultImageBase64';\n      if (item.fileId) return 'fileId';\n      if (item.taskId) return 'taskId';\n      if (item._id) return '_id';\n      return '未知';\n    };\n    \n    const getImageUrl = (url) => {\n      if (!url) return '';\n      \n      // 如果url是对象类型（比如_id），尝试将其转换为字符串\n      if (typeof url === 'object') {\n\n        if (url._id) return getImageUrl(url._id);\n        if (url.id) return getImageUrl(url.id);\n        if (url.timestamp) return getImageUrl(url.timestamp.toString());\n        // 如果都提取不到，返回空字符串\n\n        return '';\n      }\n      \n      // 检查是否为Base64数据\n      if (url && typeof url === 'string' && url.startsWith('data:image')) {\n        return url;\n      }\n      \n      // 检查是否为GridFS ID（24位十六进制字符串）\n      if (url && typeof url === 'string' && /^[0-9a-f]{24}$/i.test(url)) {\n        // 使用props中的type参数区分图像和视频\n        const mediaType = props.type === 'video' ? 'video' : 'image';\n        return `/api/media/${mediaType}/${url}`;\n      }\n      \n      // 处理可能的相对路径\n      if (typeof url === 'string') {\n        if (url.startsWith('/api/')) {\n          return url;\n        } else if (url.startsWith('/static/')) {\n          // 静态资源路径特殊处理\n          return `/api${url}`;\n        } else if (url.startsWith('/')) {\n          return `/api${url}`;\n        } else if (!url.startsWith('http')) {\n          // 根据类型区分图像和视频的路径\n          if (props.type === 'video') {\n            return `/api/videos/${url.split('/').pop()}`;\n          } else {\n          return `/api/images/${url.split('/').pop()}`;\n          }\n        }\n      }\n      \n      return url;\n    };\n    \n    const formatInferenceTime = (time) => {\n      \n      // 如果是字符串格式的时间，尝试解析\n      if (typeof time === 'string') {\n        // 已格式化的时间字符串，直接返回\n        if (time.includes('毫秒') || time.includes('秒') || time.includes('分')) {\n          return time;\n        }\n        // 尝试将字符串转换为数字\n        time = parseFloat(time);\n      }\n      \n      if (time === null || time === undefined) return '0.00 毫秒';\n      \n      const seconds = Number(time);\n      if (isNaN(seconds)) return '0.00 毫秒';\n      \n      // 根据秒数显示不同格式\n      if (seconds < 0.001) {\n        return `${(seconds * 1000).toFixed(2)} 毫秒`;\n      } else if (seconds < 1) {\n        return `${(seconds * 1000).toFixed(0)} 毫秒`;\n      } else if (seconds < 60) {\n        return `${seconds.toFixed(2)} 秒`;\n      } else {\n        const minutes = Math.floor(seconds / 60);\n        const remainSeconds = seconds % 60;\n        return `${minutes} 分 ${remainSeconds.toFixed(0)} 秒`;\n      }\n    };\n    \n    const getAnalyst = (item) => {\n\n      \n      // 如果记录中有用户名信息，直接使用\n      if (item.username && typeof item.username === 'string' && item.username.length < 20) {\n        return item.username;\n      }\n      \n      // 如果记录中有分析人员信息，直接使用\n      if (item.analyst && typeof item.analyst === 'string' && item.analyst.length < 20) {\n        return item.analyst;\n      }\n      \n      // 如果user是字符串且不是UUID，使用它\n      if (item.user && typeof item.user === 'string' && item.user.length < 20) {\n        return item.user;\n      }\n      \n      // 如果user是对象且有username属性，使用它\n      if (item.user && typeof item.user === 'object' && item.user.username) {\n        return item.user.username;\n      }\n      \n      // 使用createdBy（如果不是UUID）\n      if (item.createdBy && typeof item.createdBy === 'string' && item.createdBy.length < 20) {\n        return item.createdBy;\n      }\n      \n      // 检查分析结果中是否有分析人员信息\n      if (item.analysisResult && item.analysisResult.analyst) {\n        return item.analysisResult.analyst;\n      }\n      \n      // 如果什么都没找到，返回未知\n      return '未知';\n    };\n    \n    const loadMore = () => {\n      loadHistory(true);\n    };\n    \n    const getStatusText = (status) => {\n      switch (status) {\n        case 'success': return '分析成功';\n        case 'error': return '分析失败';\n        case 'failed': return '分析失败';\n        case 'processing': return '处理中';\n        case 'queued': return '排队中';\n        default: return status || '未知状态';\n      }\n    };\n    \n    const getStatusType = (status) => {\n      switch (status) {\n        case 'success': return 'success';\n        case 'error': \n        case 'failed': return 'danger';\n        case 'processing': \n        case 'queued': return 'warning';\n        default: return 'info';\n      }\n    };\n            \n    // 辅助函数：将月份名转换为数字\n    const getMonthNumber = (monthName) => {\n      const months = {\n        'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,\n        'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12\n      };\n      return months[monthName] || 0;\n    };\n    \n    const formatDate = (timestamp) => {\n      \n      if (!timestamp) return '未知';\n      \n      try {\n        // 处理可能的ISO格式或时间戳\n        let date;\n        if (typeof timestamp === 'number') {\n          // 处理毫秒时间戳\n          date = new Date(timestamp);\n        } else if (typeof timestamp === 'string') {\n          // 尝试解析ISO格式或其他格式的日期字符串\n          if (timestamp.includes('T') && (timestamp.includes('+00:00') || timestamp.includes('Z'))) {\n            // 是UTC时间，需要转换为本地时区对应的日期\n            const utcDate = new Date(timestamp);\n            // 根据服务器和前端之间的时差进行调整\n            date = new Date(utcDate.getTime());\n          } else {\n            date = new Date(timestamp);\n          }\n        } else {\n          return timestamp.toString();\n        }\n        \n        if (isNaN(date.getTime())) {\n          return timestamp.toString();\n        }\n        \n        // 格式化为本地时间: YYYY/MM/DD HH:MM:SS\n        const year = date.getFullYear();\n        const month = date.getMonth() + 1;\n        const day = date.getDate();\n        const hour = date.getHours().toString().padStart(2, '0');\n        const minute = date.getMinutes().toString().padStart(2, '0');\n        const second = date.getSeconds().toString().padStart(2, '0');\n        \n        return `${year}/${month}/${day} ${hour}:${minute}:${second}`;\n      } catch (err) {\n        console.warn('日期格式化错误:', err);\n        return timestamp.toString() || '未知';\n      }\n    };\n    \n    const getEmptyDescription = () => {\n      if (appliedFilters.value.startDate || appliedFilters.value.endDate || \n          appliedFilters.value.startTime || appliedFilters.value.endTime) {\n        return \"没有符合筛选条件的历史记录\";\n      }\n      return \"暂无历史分析记录，请先进行图像分析\";\n    };\n    \n    // 用户筛选变更处理\n    const onUserFilterChange = (username) => {\n\n      selectedUserFilter.value = username;\n      \n      // 重置页面状态\n      page.value = 0;\n      history.value = [];\n      filteredHistory.value = [];\n      displayedHistory.value = [];\n      \n      // 重新加载历史记录\n      loadHistory();\n    };\n    \n    // 获取可用用户列表（仅管理员需要）\n    const loadUserList = async () => {\n      if (!isAdmin.value) return;\n      \n      try {\n        // 调用用户API获取用户列表\n        // 使用真实API调用替换模拟数据\n        const response = await apiService.getUsers(1, 100);\n        \n        if (response.data && response.data.data) {\n          // 获取正确的嵌套数据结构\n          const responseData = response.data.data;\n          availableUsers.value = responseData.items || [];\n        } else if (response.data) {\n          // 兼容非嵌套结构\n          availableUsers.value = response.data.items || [];\n        }\n        \n\n      } catch (error) {\n        console.error('加载用户列表失败:', error);\n        ElMessage.warning('无法加载用户列表');\n      }\n    };\n    \n    // 确保分页正确处理\n    const ensurePagination = () => {\n      console.log('确保分页正确处理');\n      // 检查是否需要加载更多数据\n      if (history.value.length > 0 && \n          filteredHistory.value.length >= props.pageSize && \n          currentPage.value === 1) {\n        \n        // 确保启用分页\n        hasMore.value = true;\n        \n        // 可能需要重新加载历史记录\n        if (!hasMore.value && filteredHistory.value.length === props.pageSize) {\n          console.log('记录已达到pageSize上限，加载更多数据');\n          // 增加页码以确保下次加载更多数据\n          page.value++;\n          loadHistory(true);\n        }\n      }\n    };\n    \n    // 获取ID的字符串表示并截取前8位\n    const getIdString = (id) => {\n      console.log('getIdString调用，参数:', id);\n      \n      // 如果是对象，尝试按顺序获取：result_id > analysisResult.id > 其他\n      if (typeof id === 'object' && id !== null) {\n        // 处理item直接传入的情况\n        if (id.result_id) {\n          console.log('使用result_id字段:', id.result_id);\n          return id.result_id.substring(0, 8);\n        }\n        \n        if (id.analysisResult && id.analysisResult.id) {\n          console.log('使用analysisResult.id字段:', id.analysisResult.id);\n          return id.analysisResult.id.substring(0, 8);\n        }\n        \n        // 处理MongoDB的ObjectId对象格式\n        if (id._id) {\n          console.log('找到_id字段，类型:', typeof id._id, '值:', id._id);\n          if (typeof id._id === 'string') {\n            return id._id.substring(0, 8);\n          } else if (typeof id._id === 'object' && id._id !== null) {\n            // 如果_id是对象，需要进一步处理\n            if (id.result_id) {\n              console.log('_id是对象，使用result_id字段:', id.result_id);\n              return id.result_id.substring(0, 8);\n            }\n            \n            console.log('_id是对象类型，详细信息:', JSON.stringify(id._id));\n          }\n        }\n      }\n      \n      // 使用统一的getResultId函数来获取ID\n      const resultId = getResultId(typeof id === 'object' ? id : { _id: id });\n      console.log('通过getResultId函数获取ID:', resultId);\n      if (typeof resultId === 'string') {\n        return resultId.substring(0, 8);\n      }\n      \n      return 'unknown';\n    };\n    \n    // 获取结果ID作为路由参数或操作ID\n    const getResultId = (item) => {\n      // 如果item是string类型，直接返回\n      if (typeof item === 'string') {\n        return item;\n      }\n      \n      // 如果item不存在或不是对象，返回unknown\n      if (!item || typeof item !== 'object') {\n        return 'unknown';\n      }\n      \n      // 检查是否有MongoDB ObjectId\n      if (item._id) {\n        // 如果_id是对象且包含$oid属性（标准MongoDB扩展JSON格式）\n        if (typeof item._id === 'object' && item._id.$oid) {\n          return item._id.$oid;\n        }\n        \n        // 如果_id直接是字符串形式的ObjectId\n        if (typeof item._id === 'string' && item._id.match(/^[0-9a-f]{24}$/)) {\n          return item._id;\n        }\n        \n        // 如果_id是ObjectId对象（可能包含timestamp等属性）\n        if (typeof item._id === 'object' && item._id.toString) {\n          // 尝试获取ObjectId的字符串表示\n          return item._id.toString().replace(/^ObjectId\\(['\"](.+)['\"]\\)$/, '$1');\n        }\n      }\n      \n      // 尝试获取result_id（优先）\n      if (item.result_id && typeof item.result_id === 'string') {\n        return item.result_id;\n      }\n      \n      // 尝试获取analysis_result中的id\n      if (item.analysis_result && item.analysis_result.id) {\n        return item.analysis_result.id;\n      }\n      \n      if (item.analysisResult && item.analysisResult.id) {\n        return item.analysisResult.id;\n      }\n      \n      // 最后尝试其他可能的ID字段\n      if (item.id && typeof item.id === 'string') {\n        return item.id;\n      }\n      \n      // 找不到有效ID时返回unknown\n      console.warn('无法确定有效的ID，返回unknown', item);\n      return 'unknown';\n    };\n    \n    onMounted(() => {\n      loadHistory();\n\n      // 监听登录/登出事件\n      window.addEventListener('login', handleUserChange);\n      window.addEventListener('logout', handleUserChange);\n\n      // 强制修复下拉框样式\n      const forceFixDropdownStyles = () => {\n        setTimeout(() => {\n          const selectElements = document.querySelectorAll('.filter-user .el-select .el-input__wrapper');\n          selectElements.forEach(element => {\n            if (element) {\n              element.style.setProperty('background-color', 'rgba(25, 32, 50, 0.8)', 'important');\n              element.style.setProperty('background', 'rgba(25, 32, 50, 0.8)', 'important');\n              element.style.setProperty('border', '1px solid rgba(255, 255, 255, 0.15)', 'important');\n              element.style.setProperty('box-shadow', 'none', 'important');\n            }\n          });\n\n          const inputElements = document.querySelectorAll('.filter-user .el-select .el-input__inner');\n          inputElements.forEach(element => {\n            if (element) {\n              element.style.setProperty('color', '#ffffff', 'important');\n              element.style.setProperty('background-color', 'transparent', 'important');\n              element.style.setProperty('background', 'transparent', 'important');\n            }\n          });\n\n          console.log('HistoryList: 强制修复下拉框样式完成');\n        }, 300);\n      };\n\n      // 立即执行一次\n      forceFixDropdownStyles();\n\n      // 监听DOM变化，确保样式持续生效\n      const observer = new MutationObserver(() => {\n        forceFixDropdownStyles();\n      });\n\n      observer.observe(document.body, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['class', 'style']\n      });\n    });\n    \n    // 组件销毁时移除事件监听\n    onUnmounted(() => {\n      window.removeEventListener('login', handleUserChange);\n      window.removeEventListener('logout', handleUserChange);\n    });\n    \n    // 处理用户登录/登出\n    const handleUserChange = () => {\n      console.log('检测到用户变更，重置状态');\n      // 重置状态\n      currentUser.value = null;\n      isAdmin.value = false;\n      selectedUserFilter.value = '';\n      availableUsers.value = [];\n      page.value = 0;\n      history.value = [];\n      filteredHistory.value = [];\n      displayedHistory.value = [];\n      \n      // 重新加载\n      loadHistory();\n    };\n    \n    // 监听pageSize变化\n    watch(() => props.pageSize, () => {\n      updateDisplayedHistory();\n    });\n    \n    // 暴露方法和数据给父组件\n    expose({\n      totalFilteredRecords,\n      setPage,\n      applyFilters,\n      resetFilters,\n      resetDateFilter,\n      batchDelete,\n      selectAll,\n      onUserFilterChange,\n      loadUserList,\n      ensurePagination,\n      displayedHistory  // 暴露当前页显示的记录列表\n    });\n    \n    return {\n      history,\n      filteredHistory,\n      displayedHistory,\n      loading,\n      loadingMore,\n      error,\n      page,\n      hasMore,\n      currentPage,\n      deleteDialogVisible,\n      appliedFilters,\n      selectedItems,\n      currentUser,\n      isAdmin,\n      selectedUserFilter,\n      availableUsers,\n      loadHistory,\n      setPage,\n      applyFilters,\n      resetFilters,\n      resetDateFilter,\n      totalFilteredRecords,\n      confirmDelete,\n      deleteRecord,\n      batchDelete,\n      isSelected,\n      handleItemSelect,\n      formatDate,\n      formatInferenceTime,\n      getAnalyst,\n      getImageUrl,\n      getStatusText,\n      getStatusType,\n      loadMore,\n      getEmptyDescription,\n      selectAll,\n      onUserFilterChange,\n      loadUserList,\n      ensurePagination,\n      getIdString,\n      getResultId\n    };\n  }\n};\n</script>\n\n<style scoped>\n.history-container {\n  width: 100%;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: rgba(30, 38, 65, 0.95);\n  padding: 15px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.08);\n}\n\n.card-header h3 {\n  font-weight: 700;\n  color: rgba(255, 255, 255, 0.95);\n  margin: 0;\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n  letter-spacing: 0.5px;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.filter-user {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.filter-user span,\n.user-info span {\n  color: rgba(255, 255, 255, 0.95);\n  font-weight: 600;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n  white-space: nowrap;\n  letter-spacing: 0.3px;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  margin-right: 10px;\n  gap: 5px;\n}\n\n.viewing-text {\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.user-label {\n  margin-right: 10px;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.history-item {\n  height: 100%;\n  margin-bottom: 20px;\n  transition: all 0.2s;\n  background: rgba(25, 32, 50, 0.95) !important;\n  border: 1px solid rgba(45, 55, 80, 0.6) !important;\n  border-radius: 0.25rem !important;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;\n}\n\n.history-item:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25) !important;\n}\n\n.item-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background-color: rgba(30, 38, 65, 0.95);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 10px 15px;\n}\n\n.item-title-container {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.item-title-container span {\n  color: rgba(255, 255, 255, 0.95);\n  font-weight: 600;\n  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n  letter-spacing: 0.3px;\n}\n\n.item-preview {\n  height: 160px;\n  overflow: hidden;\n  margin-bottom: 10px;\n  border-radius: 4px;\n}\n\n.image-placeholder {\n  height: 160px;\n  background-color: rgba(17, 24, 39, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 10px;\n  border-radius: 4px;\n}\n\n.placeholder-icon {\n  font-size: 32px;\n  color: #909399;\n}\n\n.el-image {\n  width: 100%;\n  height: 100%;\n}\n\n.item-info {\n  margin-bottom: 15px;\n  padding: 5px 0;\n}\n\n.info-row {\n  display: flex;\n  margin-bottom: 8px;\n}\n\n.info-label {\n  font-weight: 600;\n  width: 80px;\n  color: rgba(220, 220, 230, 0.95);\n  font-size: 14px;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.info-value {\n  color: rgba(255, 255, 255, 0.95);\n  font-weight: 600;\n  font-size: 14px;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n.item-actions {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 10px;\n  margin-top: 10px;\n  position: relative;\n  z-index: 0;\n}\n\n.details-link {\n  display: block;\n  position: relative;\n  text-decoration: none;\n}\n\n.delete-btn-wrapper {\n  position: relative;\n  z-index: 10;\n  pointer-events: auto;\n}\n\n.delete-btn {\n  width: 100%;\n  position: relative;\n  z-index: 10;\n  cursor: pointer !important;\n  pointer-events: auto !important;\n}\n\n/* 移除可能影响点击的样式 */\n.item-actions a,\n.item-actions button {\n  flex: unset;\n  width: 100%;\n}\n\n.load-more-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 20px;\n}\n\n.mb-4 {\n  margin-bottom: 16px;\n}\n\n.active-filters {\n  margin-bottom: 15px;\n  padding: 10px 15px;\n  background-color: rgba(17, 24, 39, 0.3);\n  border-radius: 0.25rem;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  gap: 10px;\n  border: none;\n}\n\n.filter-tag {\n  margin-right: 8px;\n}\n\n.el-row {\n  display: flex;\n  flex-wrap: wrap;\n}\n\n.el-col {\n  margin-bottom: 20px;\n}\n\n.history-item.is-selected {\n  border-color: var(--el-color-primary);\n  box-shadow: 0 0 8px rgba(var(--el-color-primary-rgb), 0.3);\n}\n\n/* 自定义删除按钮样式 */\n.custom-btn-outline {\n  background: rgba(239, 68, 68, 0.15);\n  border: 1px solid #ef4444;\n  color: rgba(255, 255, 255, 0.95);\n  border-radius: 0.25rem;\n  padding: 0.3rem 0.6rem;\n  font-weight: 600;\n  font-size: 0.85rem;\n  transition: all 0.2s ease;\n  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex: 0.95; /* 略小于主按钮 */\n  width: 48%;\n}\n\n.custom-btn-outline:hover {\n  background: rgba(239, 68, 68, 0.25);\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);\n}\n\n:deep(.custom-btn-primary) {\n  border-radius: 0.25rem;\n  transition: all 0.2s;\n  font-weight: 600;\n  padding: 0.3rem 0.6rem;\n  height: 32px;\n  font-size: 0.85rem;\n  background-color: rgba(79, 70, 229, 0.9) !important;\n  border-color: rgba(79, 70, 229, 0.9) !important;\n  color: rgba(255, 255, 255, 0.95) !important;\n  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex: 1.05; /* 略大于删除按钮 */\n  width: 52%;\n}\n\n:deep(.custom-btn-primary):hover {\n  background-color: rgba(99, 90, 249, 0.9) !important;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.3);\n}\n\n/* 样式化卡片体 */\n:deep(.el-card) {\n  background-color: rgba(32, 41, 64, 0.9);\n  border: none;\n  border-radius: 0.25rem;\n  overflow: hidden;\n  margin-bottom: 15px;\n}\n\n:deep(.el-card__header) {\n  background-color: rgba(42, 52, 82, 0.9);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.08);\n  padding: 10px 15px;\n}\n\n:deep(.el-card__body) {\n  background-color: rgba(32, 41, 64, 0.9);\n  color: rgba(255, 255, 255, 0.9);\n  padding: 15px;\n}\n\n/* 标签样式 */\n:deep(.el-tag) {\n  border: none;\n  padding: 0.25rem 0.75rem;\n  font-weight: 500;\n  border-radius: 1rem;\n}\n\n:deep(.el-tag--info) {\n  background-color: rgba(51, 65, 85, 0.7) !important;\n  color: #cbd5e1 !important;\n  border: none !important;\n}\n\n:deep(.el-tag--success) {\n  background-color: rgba(16, 185, 129, 0.2) !important;\n  color: #34d399 !important;\n  border: 1px solid rgba(16, 185, 129, 0.3) !important;\n}\n\n:deep(.el-tag--danger) {\n  background-color: rgba(239, 68, 68, 0.2) !important;\n  color: #f87171 !important;\n  border: 1px solid rgba(239, 68, 68, 0.3) !important;\n}\n\n:deep(.el-tag--warning) {\n  background-color: rgba(245, 158, 11, 0.2) !important;\n  color: #fbbf24 !important;\n  border: 1px solid rgba(245, 158, 11, 0.3) !important;\n}\n\n/* 保持item-actions调整一致 */\n.item-actions {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 10px;\n  margin-top: 10px;\n  position: relative;\n  z-index: 0;\n}\n\n.details-link {\n  display: block;\n  position: relative;\n  text-decoration: none;\n}\n\n.delete-btn-wrapper {\n  position: relative;\n  z-index: 10;\n  pointer-events: auto;\n}\n\n.delete-btn {\n  width: 100%;\n  position: relative;\n  z-index: 10;\n  cursor: pointer !important;\n  pointer-events: auto !important;\n}\n\n/* 移除可能影响点击的样式 */\n.item-actions a,\n.item-actions button {\n  flex: unset;\n  width: 100%;\n}\n\n/* 下拉框和选择器样式优化 */\n:deep(.el-select .el-input__wrapper) {\n  background-color: rgba(25, 32, 50, 0.8) !important;\n  background: rgba(25, 32, 50, 0.8) !important;\n  box-shadow: none !important;\n  border: 1px solid rgba(255, 255, 255, 0.15) !important;\n  border-radius: 0.25rem !important;\n  transition: all 0.3s ease !important;\n}\n\n:deep(.el-select .el-input__wrapper:hover) {\n  background-color: rgba(25, 32, 50, 0.9) !important;\n  background: rgba(25, 32, 50, 0.9) !important;\n  border-color: rgba(59, 130, 246, 0.5) !important;\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;\n}\n\n:deep(.el-select .el-input__wrapper.is-focus),\n:deep(.el-select .el-input__wrapper:focus-within) {\n  background-color: rgba(25, 32, 50, 0.95) !important;\n  background: rgba(25, 32, 50, 0.95) !important;\n  border-color: #3b82f6 !important;\n  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;\n}\n\n:deep(.el-select .el-input__inner) {\n  color: #ffffff !important;\n  background-color: transparent !important;\n  background: transparent !important;\n  font-weight: 500 !important;\n}\n\n:deep(.el-select .el-select__input) {\n  color: rgba(255, 255, 255, 0.9) !important;\n}\n\n:deep(.el-select .el-select__placeholder) {\n  color: rgba(255, 255, 255, 0.4) !important;\n}\n\n:deep(.el-select-dropdown) {\n  background-color: rgba(25, 32, 50, 0.95) !important;\n  border: 1px solid rgba(255, 255, 255, 0.1) !important;\n}\n\n:deep(.el-select-dropdown__item) {\n  color: rgba(255, 255, 255, 0.9) !important;\n}\n\n:deep(.el-select-dropdown__item.hover),\n:deep(.el-select-dropdown__item:hover) {\n  background-color: rgba(99, 102, 241, 0.2) !important;\n}\n\n:deep(.el-select-dropdown__item.selected) {\n  background-color: rgba(79, 70, 229, 0.3) !important;\n  color: #ffffff !important;\n  font-weight: 600;\n}\n\n:deep(.el-select__selected-item) {\n  color: rgba(255, 255, 255, 0.9) !important;\n  background-color: transparent !important;\n}\n\n/* 自定义对话框样式 */\n:deep(.dark-theme-dialog) {\n  background: rgba(13, 18, 38, 0.98) !important;\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5) !important;\n  border-radius: 0.75rem !important;\n  border: 1px solid rgba(99, 102, 241, 0.2) !important;\n  overflow: hidden !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__header) {\n  background: rgba(22, 30, 55, 0.98) !important;\n  color: #e5e7eb !important;\n  border-bottom: 1px solid rgba(99, 102, 241, 0.15) !important;\n  padding: 15px 20px !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__title) {\n  color: #e5e7eb !important;\n  font-weight: 600 !important;\n  font-size: 1.1rem !important;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__body) {\n  background: rgba(13, 18, 38, 0.98) !important;\n  color: #e5e7eb !important;\n  padding: 24px 20px !important;\n  font-size: 1rem !important;\n  line-height: 1.5 !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__footer) {\n  background: rgba(22, 30, 55, 0.98) !important;\n  border-top: 1px solid rgba(99, 102, 241, 0.15) !important;\n  padding: 15px 20px !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__headerbtn:hover .el-dialog__close) {\n  color: #818cf8 !important;\n}\n\n:deep(.dark-theme-dialog .el-dialog__headerbtn .el-dialog__close) {\n  color: #e5e7eb !important;\n}\n\n/* 修复按钮样式和比例问题 */\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  width: 100%;\n}\n\n:deep(.cancel-btn) {\n  background: rgba(31, 41, 65, 0.8) !important;\n  border: 1px solid rgba(255, 255, 255, 0.15) !important;\n  color: #e5e7eb !important;\n  min-width: 80px !important;\n  flex: 0 0 auto !important;\n}\n\n:deep(.cancel-btn:hover) {\n  background: rgba(55, 65, 95, 0.8) !important;\n  border-color: rgba(255, 255, 255, 0.25) !important;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) !important;\n}\n\n:deep(.confirm-delete-btn) {\n  background: rgba(220, 38, 38, 0.15) !important;\n  border: 1px solid rgba(239, 68, 68, 0.5) !important;\n  color: #f87171 !important;\n  min-width: 100px !important;\n  flex: 0 0 auto !important;\n  font-weight: 600 !important;\n}\n\n:deep(.confirm-delete-btn:hover) {\n  background: rgba(220, 38, 38, 0.25) !important;\n  color: #fca5a5 !important;\n  border-color: rgba(239, 68, 68, 0.7) !important;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3) !important;\n}\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAGnBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAgB;;EANrCC,GAAA;EAOiBD,KAAK,EAAC;;;EAPvBC,GAAA;EAYiBD,KAAK,EAAC;;;EAZvBC,GAAA;EAkD2HD,KAAK,EAAC;;;EAyB9GA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAsB;;EA5EjDC,GAAA;EA0FiBD,KAAK,EAAC;;;EAOAA,KAAK,EAAC;AAAmB;;EAjGhDC,GAAA;EAuGwBD,KAAK,EAAC;;;EAIbA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAErBA,KAAK,EAAC;AAAU;;EAEbA,KAAK,EAAC;AAAY;;EAIvBA,KAAK,EAAC;AAAc;;EAMlBA,KAAK,EAAC;AAAoB;;EAyBhCA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;;uBA5JhCE,mBAAA,CAoKM,OApKNC,UAoKM,GAnKJC,YAAA,CA+IUC,kBAAA;IA/IDC,MAAM,EAAC;EAAO;IACVC,MAAM,EAAAC,QAAA,CACf,MA+BM,CA/BNC,mBAAA,CA+BM,OA/BNC,UA+BM,G,4BA9BJD,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CA4BM,OA5BNE,UA4BM,GA3ByBC,MAAA,CAAAC,WAAW,I,cAAxCX,mBAAA,CAIM,OAJNY,UAIM,G,0BAHJL,mBAAA,CAA8C;MAAxCM,KAA2B,EAA3B;QAAA;MAAA;IAA2B,GAAC,OAAK,sBACvCX,YAAA,CAA2CY,iBAAA;MATzDC,OAAA,EAAAT,QAAA,CASsB,MAA0B,CAThDU,gBAAA,CAAAC,gBAAA,CASyBP,MAAA,CAAAC,WAAW,CAACO,QAAQ,iB;MAT7CC,CAAA;QAU4BT,MAAA,CAAAU,OAAO,I,cAArBC,YAAA,CAAkDP,iBAAA;MAVhEf,GAAA;MAUqCuB,IAAI,EAAC;;MAV1CP,OAAA,EAAAT,QAAA,CAUoD,MAAGiB,MAAA,QAAAA,MAAA,OAVvDP,gBAAA,CAUoD,KAAG,E;MAVvDG,CAAA;UAAAK,mBAAA,e,KAAAA,mBAAA,gBAY2Cd,MAAA,CAAAU,OAAO,IAAIV,MAAA,CAAAC,WAAW,I,cAArDX,mBAAA,CAkBM,OAlBNyB,UAkBM,G,0BAjBJlB,mBAAA,CAA8C;MAAxCM,KAA2B,EAA3B;QAAA;MAAA;IAA2B,GAAC,OAAK,sBACvCX,YAAA,CAeYwB,oBAAA;MA7B1BC,UAAA,EAeyBjB,MAAA,CAAAkB,kBAAkB;MAf3C,uBAAAL,MAAA,QAAAA,MAAA,MAAAM,MAAA,IAeyBnB,MAAA,CAAAkB,kBAAkB,GAAAC,MAAA;MAC3BC,WAAW,EAAC,MAAM;MAClBC,SAAS,EAAT,EAAS;MACRC,QAAM,EAAEtB,MAAA,CAAAuB,kBAAkB;MAC3BpB,KAA6C,EAA7C;QAAA;QAAA;MAAA,CAA6C;MAC7CqB,IAAI,EAAC;;MApBrBnB,OAAA,EAAAT,QAAA,CAsBgB,MAA6C,CAA7CJ,YAAA,CAA6CiC,oBAAA;QAAlCC,KAAK,EAAC,MAAM;QAACC,KAAK,EAAC;6BAC9BrC,mBAAA,CAKYsC,SAAA,QA5B5BC,WAAA,CAwBiC7B,MAAA,CAAA8B,cAAc,EAAtBC,IAAI;6BADbpB,YAAA,CAKYc,oBAAA;UAHTpC,GAAG,EAAE0C,IAAI,CAACC,EAAE,IAAID,IAAI,CAACE,GAAG;UACxBP,KAAK,EAAEK,IAAI,CAACvB,QAAQ;UACpBmB,KAAK,EAAEI,IAAI,CAACvB;;;MA3B/BC,CAAA;uDAAAK,mBAAA,gBA+BYtB,YAAA,CAEY0C,oBAAA;MAFD9C,KAAK,EAAC,oBAAoB;MAAE+C,OAAK,EAAEnC,MAAA,CAAAoC,WAAW;MAAGC,OAAO,EAAErC,MAAA,CAAAqC;;MA/BjFhC,OAAA,EAAAT,QAAA,CAgCc,MAA8B,CAA9BJ,YAAA,CAA8B8C,kBAAA;QAhC5CjC,OAAA,EAAAT,QAAA,CAgCuB,MAAW,CAAXJ,YAAA,CAAW+C,kBAAA,E;QAhClC9B,CAAA;sCAAAH,gBAAA,CAgC4C,MAChC,G;MAjCZG,CAAA;;IAAAJ,OAAA,EAAAT,QAAA,CAgDI,MAKH,CAdaI,MAAA,CAAAwC,KAAK,I,cADb7B,YAAA,CASW8B,mBAAA;MA/CjBpD,GAAA;MAwCQuB,IAAI,EAAC,SAAS;MACb8B,QAAQ,EAAE,IAAI;MACf,WAAS,EAAT,EAAS;MACRC,KAAK,EAAE;;MA3ChBtC,OAAA,EAAAT,QAAA,CA6CQ,MAAkB,CAAlBC,mBAAA,CAAkB,WAAAU,gBAAA,CAAZP,MAAA,CAAAwC,KAAK,kB,4BACX3C,mBAAA,CAAsB,WAAnB,iBAAe,qB;MA9C1BY,CAAA;UAAAK,mBAAA,gBAkDiBd,MAAA,CAAA4C,cAAc,CAACC,SAAS,IAAI7C,MAAA,CAAA4C,cAAc,CAACE,OAAO,IAAI9C,MAAA,CAAA4C,cAAc,CAACG,SAAS,IAAI/C,MAAA,CAAA4C,cAAc,CAACI,OAAO,I,cAAnH1D,mBAAA,CAeM,OAfN2D,UAeM,GAdUjD,MAAA,CAAA4C,cAAc,CAACC,SAAS,I,cAAtClC,YAAA,CAESP,iBAAA;MArDjBf,GAAA;MAmDgDD,KAAK,EAAC,YAAY;MAACsD,QAAQ,EAAR,EAAQ;MAAEQ,OAAK,EAAArC,MAAA,QAAAA,MAAA,MAAAM,MAAA,IAAEnB,MAAA,CAAAmD,eAAe;;MAnDnG9C,OAAA,EAAAT,QAAA,CAmDkH,MAClG,CApDhBU,gBAAA,CAmDkH,SAClG,GAAAC,gBAAA,CAAGP,MAAA,CAAA4C,cAAc,CAACC,SAAS,iB;MApD3CpC,CAAA;UAAAK,mBAAA,gBAsDsBd,MAAA,CAAA4C,cAAc,CAACE,OAAO,I,cAApCnC,YAAA,CAESP,iBAAA;MAxDjBf,GAAA;MAsD8CD,KAAK,EAAC,YAAY;MAACsD,QAAQ,EAAR,EAAQ;MAAEQ,OAAK,EAAArC,MAAA,QAAAA,MAAA,MAAAM,MAAA,IAAEnB,MAAA,CAAAmD,eAAe;;MAtDjG9C,OAAA,EAAAT,QAAA,CAsD8G,MAC9F,CAvDhBU,gBAAA,CAsD8G,SAC9F,GAAAC,gBAAA,CAAGP,MAAA,CAAA4C,cAAc,CAACE,OAAO,iB;MAvDzCrC,CAAA;UAAAK,mBAAA,gBAyDsBd,MAAA,CAAA4C,cAAc,CAACG,SAAS,I,cAAtCpC,YAAA,CAESP,iBAAA;MA3DjBf,GAAA;MAyDgDD,KAAK,EAAC,YAAY;MAACsD,QAAQ,EAAR,EAAQ;MAAEQ,OAAK,EAAArC,MAAA,QAAAA,MAAA,MAAAM,MAAA,IAAEnB,MAAA,CAAAmD,eAAe;;MAzDnG9C,OAAA,EAAAT,QAAA,CAyDkH,MAClG,CA1DhBU,gBAAA,CAyDkH,SAClG,GAAAC,gBAAA,CAAGP,MAAA,CAAA4C,cAAc,CAACG,SAAS,iB;MA1D3CtC,CAAA;UAAAK,mBAAA,gBA4DsBd,MAAA,CAAA4C,cAAc,CAACI,OAAO,I,cAApCrC,YAAA,CAESP,iBAAA;MA9DjBf,GAAA;MA4D8CD,KAAK,EAAC,YAAY;MAACsD,QAAQ,EAAR,EAAQ;MAAEQ,OAAK,EAAArC,MAAA,QAAAA,MAAA,MAAAM,MAAA,IAAEnB,MAAA,CAAAmD,eAAe;;MA5DjG9C,OAAA,EAAAT,QAAA,CA4D8G,MAC9F,CA7DhBU,gBAAA,CA4D8G,SAC9F,GAAAC,gBAAA,CAAGP,MAAA,CAAA4C,cAAc,CAACI,OAAO,iB;MA7DzCvC,CAAA;UAAAK,mBAAA,gBAgEQtB,YAAA,CAA2F0C,oBAAA;MAAhF9C,KAAK,EAAC,oBAAoB;MAACoC,IAAI,EAAC,OAAO;MAAEW,OAAK,EAAEnC,MAAA,CAAAoD;;MAhEnE/C,OAAA,EAAAT,QAAA,CAgEiF,MAAMiB,MAAA,SAAAA,MAAA,QAhEvFP,gBAAA,CAgEiF,QAAM,E;MAhEvFG,CAAA;wCAAAK,mBAAA,gBAmEsBd,MAAA,CAAAqD,eAAe,CAACC,MAAM,WAAWtD,MAAA,CAAAqC,OAAO,I,cAAxD1B,YAAA,CAAiG4C,mBAAA;MAnEvGlE,GAAA;MAmEiEmE,WAAW,EAAExD,MAAA,CAAAyD,mBAAmB;gDAnEjG3C,mBAAA,gBAqE4Cd,MAAA,CAAAqC,OAAO,IAAIrC,MAAA,CAAA0D,gBAAgB,CAACJ,MAAM,U,cAAxE3C,YAAA,CAAkFgD,sBAAA;MArExFtE,GAAA;MAqEoBuE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAR;yBAEvBlD,YAAA,CAyESmD,iBAAA;MAhJfzE,GAAA;MAuEsB0E,MAAM,EAAE;;MAvE9B1D,OAAA,EAAAT,QAAA,CAwEgB,MAAgC,E,kBAAxCN,mBAAA,CAuESsC,SAAA,QA/IjBC,WAAA,CAwE+B7B,MAAA,CAAA0D,gBAAgB,EAAxBM,IAAI;6BAAnBrD,YAAA,CAuESsD,iBAAA;UAvEiC5E,GAAG,EAAE2E,IAAI,CAAC/B,GAAG,IAAI+B,IAAI,CAAChC,EAAE;UAAGkC,EAAE,EAAE,EAAE;UAAGC,EAAE,EAAE,EAAE;UAAGC,EAAE,EAAE,CAAC;UAAGC,EAAE,EAAE,CAAC;UAAGC,EAAE,EAAE;;UAxEnHjE,OAAA,EAAAT,QAAA,CAyEU,MAqEU,CArEVJ,YAAA,CAqEUC,kBAAA;YArEDC,MAAM,EAAC,OAAO;YAACN,KAAK,EAzEvCmF,eAAA,EAyEwC,mBAAmB;cAAA,eAA0BvE,MAAA,CAAAwE,UAAU,CAACR,IAAI;YAAA;;YAC7ErE,MAAM,EAAAC,QAAA,CACf,MAYM,CAZNC,mBAAA,CAYM,OAZN4E,UAYM,GAXJ5E,mBAAA,CAOM,OAPN6E,UAOM,GALIC,MAAA,CAAAC,aAAa,I,cADrBjE,YAAA,CAIEkE,sBAAA;cAjFpBxF,GAAA;cAAA4B,UAAA,EA+E6B+C,IAAI,CAACc,QAAQ;cA/E1C,uBAAA3D,MAAA,IA+E6B6C,IAAI,CAACc,QAAQ,GAAA3D,MAAA;cACrBG,QAAM,EAAGyD,GAAG,IAAK/E,MAAA,CAAAgF,gBAAgB,CAAChB,IAAI,EAAEe,GAAG;0FAhFhEjE,mBAAA,gBAkFgBjB,mBAAA,CAAwD,cAAlD,MAAI,GAAAU,gBAAA,CAAGP,MAAA,CAAAiF,WAAW,CAACjB,IAAI,EAAEkB,SAAS,uB,GAExC1F,YAAA,CAESY,iBAAA;cAFAQ,IAAI,EAAEZ,MAAA,CAAAmF,aAAa,CAACnB,IAAI,CAACoB,MAAM;;cApFxD/E,OAAA,EAAAT,QAAA,CAqFkB,MAAgC,CArFlDU,gBAAA,CAAAC,gBAAA,CAqFqBP,MAAA,CAAAqF,aAAa,CAACrB,IAAI,CAACoB,MAAM,kB;cArF9C3E,CAAA;;YAAAJ,OAAA,EAAAT,QAAA,CAqHI,MAcH,CAzC2CoE,IAAI,CAACsB,QAAQ,I,cAA7ChG,mBAAA,CAYM,OAZNiG,UAYM,GAXJ/F,YAAA,CAUWgG,mBAAA;cATRC,GAAG,EAAEzF,MAAA,CAAA0F,WAAW,CAAC1B,IAAI,CAACsB,QAAQ;cAC/BK,GAAG,EAAC,OAAO;cACV,kBAAgB,GAAG3F,MAAA,CAAA0F,WAAW,CAAC1B,IAAI,CAACsB,QAAQ;;cAElC9C,KAAK,EAAA5C,QAAA,CACd,MAEM,CAFNC,mBAAA,CAEM,OAFN+F,WAEM,GADJpG,YAAA,CAAqC8C,kBAAA;gBAlGzDjC,OAAA,EAAAT,QAAA,CAkG6B,MAAkB,CAAlBJ,YAAA,CAAkBqG,yBAAA,E;gBAlG/CpF,CAAA;;cAAAA,CAAA;iGAuGYnB,mBAAA,CAEM,OAFNwG,WAEM,GADJtG,YAAA,CAAmC8C,kBAAA;cAxGjDjC,OAAA,EAAAT,QAAA,CAwGuB,MAAgB,CAAhBJ,YAAA,CAAgBuG,uBAAA,E;cAxGvCtF,CAAA;mBA2GYZ,mBAAA,CAiBM,OAjBNmG,WAiBM,GAhBJnG,mBAAA,CAGM,OAHNoG,WAGM,G,4BAFJpG,mBAAA,CAAqC;cAA/BT,KAAK,EAAC;YAAY,GAAC,OAAK,sBAC9BS,mBAAA,CAAoF,QAApFqG,WAAoF,EAAA3F,gBAAA,CAAxDyD,IAAI,CAACmC,YAAY,IAAInC,IAAI,CAACoC,aAAa,SAAQ,IAAE,gB,GAE/EvG,mBAAA,CAGM,OAHNwG,WAGM,G,4BAFJxG,mBAAA,CAAqC;cAA/BT,KAAK,EAAC;YAAY,GAAC,OAAK,sBAC9BS,mBAAA,CAAuG,QAAvGyG,WAAuG,EAAA/F,gBAAA,CAA3EyD,IAAI,CAACuC,iBAAiB,IAAIvG,MAAA,CAAAwG,mBAAmB,CAACxC,IAAI,CAACyC,aAAa,kB,GAE9F5G,mBAAA,CAGM,OAHN6G,WAGM,G,4BAFJ7G,mBAAA,CAAqC;cAA/BT,KAAK,EAAC;YAAY,GAAC,OAAK,sBAC9BS,mBAAA,CAAgK,QAAhK8G,WAAgK,EAAApG,gBAAA,CAApIyD,IAAI,CAAC4C,aAAa,IAAI5G,MAAA,CAAA6G,UAAU,CAAC7C,IAAI,CAAC8C,mBAAmB,IAAI9C,IAAI,CAAC+C,iBAAiB,IAAI/C,IAAI,CAACgD,WAAW,IAAIhD,IAAI,CAACiD,SAAS,kB,GAEvJpH,mBAAA,CAGM,OAHNqH,WAGM,G,4BAFJrH,mBAAA,CAAqC;cAA/BT,KAAK,EAAC;YAAY,GAAC,OAAK,sBAC9BS,mBAAA,CAAsD,QAAtDsH,WAAsD,EAAA5G,gBAAA,CAA1BP,MAAA,CAAAoH,UAAU,CAACpD,IAAI,kB,KAI/CnE,mBAAA,CAeM,OAfNwH,WAeM,GAdJ7H,YAAA,CAIc8H,sBAAA;cAJAC,EAAE,aAAavD,IAAI,CAACwD,SAAS,IAAIxD,IAAI,CAACyD,eAAe,EAAEzF,EAAE,IAAIgC,IAAI,CAAC0D,cAAc,EAAE1F,EAAE;cAAU5C,KAAK,EAAC;;cA/HhIiB,OAAA,EAAAT,QAAA,CAgIgB,MAEY,CAFZJ,YAAA,CAEY0C,oBAAA;gBAFD9C,KAAK,EAAC;cAAoB;gBAhIrDiB,OAAA,EAAAT,QAAA,CAiIkB,MAA+B,CAA/BJ,YAAA,CAA+B8C,kBAAA;kBAjIjDjC,OAAA,EAAAT,QAAA,CAiI2B,MAAY,CAAZJ,YAAA,CAAYmI,mBAAA,E;kBAjIvClH,CAAA;gDAAAH,gBAAA,CAiIiD,QACjC,G;gBAlIhBG,CAAA;;cAAAA,CAAA;yDAoIcZ,mBAAA,CAQM,OARN+H,WAQM,GAPJpI,YAAA,CAMY0C,oBAAA;cALV9C,KAAK,EAAC,+BAA+B;cACpC+C,OAAK,EAvIxB0F,cAAA,CAAA1G,MAAA,IAuIuCnB,MAAA,CAAA8H,aAAa,CAAC9D,IAAI;cACvCpD,IAAI,EAAC;;cAxIvBP,OAAA,EAAAT,QAAA,CA0IkB,MAA6B,CAA7BJ,YAAA,CAA6B8C,kBAAA;gBA1I/CjC,OAAA,EAAAT,QAAA,CA0I2B,MAAU,CAAVJ,YAAA,CAAUuI,iBAAA,E;gBA1IrCtH,CAAA;8CAAAH,gBAAA,CA0I+C,MAC/B,G;cA3IhBG,CAAA;;YAAAA,CAAA;;UAAAA,CAAA;;;MAAAA,CAAA;;IAAAA,CAAA;MAmJIK,mBAAA,aAAgB,EAChBtB,YAAA,CAgBYwI,oBAAA;IApKhB/G,UAAA,EAqJejB,MAAA,CAAAiI,mBAAmB;IArJlC,uBAAApH,MAAA,QAAAA,MAAA,MAAAM,MAAA,IAqJenB,MAAA,CAAAiI,mBAAmB,GAAA9G,MAAA;IAC5BwB,KAAK,EAAC,MAAM;IACZuF,KAAK,EAAC,KAAK;IACX9I,KAAK,EAAC,mBAAmB;IACxB,sBAAoB,EAAE;;IAGZ+I,MAAM,EAAAvI,QAAA,CACf,MAKM,CALNC,mBAAA,CAKM,OALNuI,WAKM,GAJJ5I,YAAA,CAEY0C,oBAAA;MAFD9C,KAAK,EAAC,oBAAoB;MAAE+C,OAAK,EAAEnC,MAAA,CAAAqI;;MA9JxDhI,OAAA,EAAAT,QAAA,CA8JsE,MAE5DiB,MAAA,SAAAA,MAAA,QAhKVP,gBAAA,CA8JsE,QAE5D,E;MAhKVG,CAAA;oCAiKUjB,YAAA,CAAiF0C,oBAAA;MAAtE9C,KAAK,EAAC,YAAY;MAAE+C,OAAK,EAAAtB,MAAA,QAAAA,MAAA,MAAAM,MAAA,IAAEnB,MAAA,CAAAiI,mBAAmB;;MAjKnE5H,OAAA,EAAAT,QAAA,CAiK6E,MAAEiB,MAAA,SAAAA,MAAA,QAjK/EP,gBAAA,CAiK6E,IAAE,E;MAjK/EG,CAAA;;IAAAJ,OAAA,EAAAT,QAAA,CA2JM,MAAkC,C,4BAAlCC,mBAAA,CAAkC,cAA5B,uBAAqB,qB;IA3JjCY,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}