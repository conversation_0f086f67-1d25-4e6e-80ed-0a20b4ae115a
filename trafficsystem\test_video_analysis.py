#!/usr/bin/env python3
"""
测试视频分析过程中的实时帧推送
"""

import requests
import json
import time
import base64
from datetime import datetime

# 配置
JAVA_BACKEND_URL = "http://localhost:8080"
PYTHON_API_URL = "http://localhost:5000"
TEST_TASK_ID = "7640209d-a9bf-499b-8f1f-bc7fe875abea"

def check_task_status():
    """检查任务状态"""
    try:
        url = f"{JAVA_BACKEND_URL}/api/video/task/{TEST_TASK_ID}"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 任务状态: {data.get('status', 'unknown')}")
            print(f"📈 进度: {data.get('progress', 0)}%")
            print(f"💬 消息: {data.get('message', 'N/A')}")
            return data
        else:
            print(f"❌ 获取任务状态失败: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 检查任务状态时出错: {e}")
        return None

def simulate_frame_push():
    """模拟发送多个帧数据"""
    print(f"🎬 开始模拟帧推送...")
    
    for frame_num in range(1, 6):  # 发送5帧测试数据
        frame_data = {
            "type": "frame_update",
            "taskId": TEST_TASK_ID,
            "frameNumber": frame_num,
            "totalFrames": 100,
            "imageData": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "detectionCount": frame_num % 5,
            "timestamp": datetime.now().isoformat(),
            "quality": 75
        }
        
        try:
            url = f"{JAVA_BACKEND_URL}/api/video-progress/frame-update"
            response = requests.post(url, json=frame_data, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ 帧 {frame_num} 推送成功")
            else:
                print(f"❌ 帧 {frame_num} 推送失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 帧 {frame_num} 推送异常: {e}")
        
        time.sleep(2)  # 间隔2秒

def test_websocket_endpoint():
    """测试WebSocket相关端点"""
    print(f"🔌 测试WebSocket相关功能...")
    
    try:
        # 测试获取缓存帧数据
        url = f"{JAVA_BACKEND_URL}/api/video-progress/frames/{TEST_TASK_ID}"
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            frame_count = data.get('count', 0)
            print(f"📦 缓存帧数量: {frame_count}")
            
            if frame_count > 0:
                print(f"✅ WebSocket缓存功能正常")
            else:
                print(f"⚠️  没有缓存的帧数据")
        else:
            print(f"❌ 获取缓存帧失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试WebSocket端点时出错: {e}")

def check_python_api_logs():
    """检查Python API是否有相关日志"""
    print(f"🔍 检查Python API状态...")
    
    try:
        # 检查Python API健康状态
        response = requests.get(f"{PYTHON_API_URL}/health", timeout=5)
        if response.status_code == 200:
            print(f"✅ Python API运行正常")
        else:
            print(f"⚠️  Python API状态异常: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"❌ 无法连接到Python API: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 视频分析实时帧推送完整测试")
    print("=" * 60)
    print()
    
    # 1. 检查服务状态
    print("1️⃣ 检查服务状态")
    print("-" * 30)
    check_python_api_logs()
    print()
    
    # 2. 检查任务状态
    print("2️⃣ 检查任务状态")
    print("-" * 30)
    task_status = check_task_status()
    print()
    
    # 3. 测试WebSocket端点
    print("3️⃣ 测试WebSocket功能")
    print("-" * 30)
    test_websocket_endpoint()
    print()
    
    # 4. 模拟帧推送
    print("4️⃣ 模拟帧推送")
    print("-" * 30)
    simulate_frame_push()
    print()
    
    # 5. 再次检查缓存
    print("5️⃣ 检查推送后的缓存")
    print("-" * 30)
    test_websocket_endpoint()
    print()
    
    print("=" * 60)
    print("🏁 测试完成")
    print("=" * 60)
    print()
    print("💡 如果前端仍然看不到实时视频，请检查:")
    print("   1. 浏览器控制台是否有WebSocket连接错误")
    print("   2. 前端是否正确订阅了帧数据主题")
    print("   3. RealTimeFrameViewer组件是否正确渲染")
    print("   4. 网络防火墙是否阻止了WebSocket连接")

if __name__ == "__main__":
    main()
