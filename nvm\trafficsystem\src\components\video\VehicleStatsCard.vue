<template>
  <div class="vehicle-stats-card">
    <el-card class="stats-card">
      <template #header>
        <div class="card-header">
          <span>车辆类型统计</span>
          <el-button link @click="exportStats">
            <el-icon><download /></el-icon>
          </el-button>
        </div>
      </template>
      
      <div class="stats-content">
        <div class="vehicle-icons">
          <div class="vehicle-type" v-for="(count, type) in vehicleTypeStats" :key="type">
            <div class="vehicle-icon-container">
              <div class="vehicle-icon">{{ getVehicleIcon(type) }}</div>
              <div class="vehicle-count">{{ count }}</div>
            </div>
            <div class="vehicle-label">{{ getVehicleTypeName(type) }}</div>
            <div class="vehicle-percentage">
              {{ getPercentage(count) }}%
            </div>
            <el-progress 
              :percentage="getPercentage(count)" 
              :color="getVehicleColor(type)"
              :show-text="false"
              :stroke-width="8"
            />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Download } from '@element-plus/icons-vue';

export default {
  name: 'VehicleStatsCard',
  components: {
    Download
  },
  props: {
    // 车辆类型统计数据
    vehicleTypeStats: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    // 计算总车辆数
    const totalVehicles = computed(() => {
      let total = 0;
      Object.values(props.vehicleTypeStats).forEach(count => {
        total += count;
      });
      return total;
    });
    
    // 获取车辆类型名称
    const getVehicleTypeName = (type) => {
      if (!type || typeof type !== 'string') {
        return '未知';
      }

      const lowerType = type.toLowerCase().trim();

      // 英文到中文的映射
      const englishToChineseMap = {
        'car': '汽车',
        'auto': '汽车',
        'vehicle': '汽车',
        'truck': '卡车',
        'bus': '公交车',
        'motorcycle': '摩托车',
        'motorbike': '摩托车',
        'bicycle': '自行车',
        'bike': '自行车',
        'person': '行人',
        'pedestrian': '行人',
        'human': '行人',
        'unknown': '未知'
      };

      // 中文类型标准化
      const chineseNormalizationMap = {
        '小汽车': '汽车',
        '轿车': '汽车',
        '货车': '卡车',
        '客车': '公交车',
        '大巴': '公交车',
        '单车': '自行车',
        '人': '行人'
      };

      // 先检查是否是英文类型
      if (englishToChineseMap[lowerType]) {
        return englishToChineseMap[lowerType];
      }

      // 检查是否是中文类型需要标准化
      if (chineseNormalizationMap[type]) {
        return chineseNormalizationMap[type];
      }

      // 如果已经是标准中文名称，直接返回
      const standardChineseTypes = ['汽车', '卡车', '公交车', '摩托车', '自行车', '行人', '未知'];
      if (standardChineseTypes.includes(type)) {
        return type;
      }

      // 默认返回原始类型
      return type;
    };
    
    // 获取车辆图标
    const getVehicleIcon = (type) => {
      if (!type || typeof type !== 'string') {
        return '🚗';
      }

      const lowerType = type.toLowerCase().trim();

      const icons = {
        'car': '🚗',
        'auto': '🚗',
        'vehicle': '🚗',
        'truck': '🚚',
        'bus': '🚌',
        'motorcycle': '🏍️',
        'motorbike': '🏍️',
        'bicycle': '🚲',
        'bike': '🚲',
        'person': '🚶',
        'pedestrian': '🚶',
        'human': '🚶',
        'unknown': '❓'
      };

      // 中文类型映射
      const chineseIcons = {
        '汽车': '🚗',
        '小汽车': '🚗',
        '轿车': '🚗',
        '卡车': '🚚',
        '货车': '🚚',
        '公交车': '🚌',
        '客车': '🚌',
        '大巴': '🚌',
        '摩托车': '🏍️',
        '自行车': '🚲',
        '单车': '🚲',
        '行人': '🚶',
        '人': '🚶',
        '未知': '❓'
      };

      return icons[lowerType] || chineseIcons[type] || '🚗';
    };

    // 获取车辆颜色
    const getVehicleColor = (type) => {
      if (!type || typeof type !== 'string') {
        return '#4f46e5';
      }

      const lowerType = type.toLowerCase().trim();

      const colors = {
        'car': '#5470C6',
        'auto': '#5470C6',
        'vehicle': '#5470C6',
        'truck': '#91CC75',
        'bus': '#FAC858',
        'motorcycle': '#EE6666',
        'motorbike': '#EE6666',
        'bicycle': '#73C0DE',
        'bike': '#73C0DE',
        'person': '#3BA272',
        'pedestrian': '#3BA272',
        'human': '#3BA272',
        'unknown': '#9A60B4'
      };

      // 中文类型颜色映射
      const chineseColors = {
        '汽车': '#5470C6',
        '小汽车': '#5470C6',
        '轿车': '#5470C6',
        '卡车': '#91CC75',
        '货车': '#91CC75',
        '公交车': '#FAC858',
        '客车': '#FAC858',
        '大巴': '#FAC858',
        '摩托车': '#EE6666',
        '自行车': '#73C0DE',
        '单车': '#73C0DE',
        '行人': '#3BA272',
        '人': '#3BA272',
        '未知': '#9A60B4'
      };

      return colors[lowerType] || chineseColors[type] || '#5470C6';
    };
    
    // 计算百分比
    const getPercentage = (count) => {
      if (totalVehicles.value === 0) return 0;
      return Math.round((count / totalVehicles.value) * 100);
    };
    
    // 导出统计数据
    const exportStats = () => {
      try {
        const data = [];
        
        // 添加表头
        data.push(['车辆类型', '数量', '百分比']);
        
        // 添加数据行
        Object.entries(props.vehicleTypeStats).forEach(([type, count]) => {
          data.push([
            getVehicleTypeName(type),
            count,
            `${getPercentage(count)}%`
          ]);
        });
        
        // 添加总计行
        data.push(['总计', totalVehicles.value, '100%']);
        
        // 生成CSV内容
        const csvContent = data.map(row => row.join(',')).join('\n');
        
        // 创建Blob对象
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        
        // 创建下载链接
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        link.setAttribute('href', url);
        link.setAttribute('download', '车辆类型统计.csv');
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        ElMessage.success('统计数据已导出');
      } catch (err) {
        console.error('导出统计数据失败:', err);
        ElMessage.error('导出统计数据失败');
      }
    };
    
    return {
      totalVehicles,
      getVehicleTypeName,
      getVehicleIcon,
      getVehicleColor,
      getPercentage,
      exportStats
    };
  }
};
</script>

<style scoped>
.vehicle-stats-card {
  margin: 20px 0;
}

.stats-card {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.06) !important;
}

:deep(.stats-card .el-card__header) {
  background-color: rgba(26, 32, 50, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.card-header span {
  font-weight: bold;
  color: #ffffff;
}

.stats-content {
  padding: 15px 0;
}

.vehicle-icons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 20px;
}

.vehicle-type {
  margin-bottom: 15px;
}

.vehicle-icon-container {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.vehicle-icon {
  font-size: 24px;
  margin-right: 10px;
}

.vehicle-count {
  font-size: 18px;
  font-weight: 700;
  color: #ffffff;
}

.vehicle-label {
  font-size: 14px;
  color: #d1d5db;
  margin-bottom: 5px;
}

.vehicle-percentage {
  font-size: 12px;
  color: #9ca3af;
  margin-bottom: 5px;
  text-align: right;
}
</style> 