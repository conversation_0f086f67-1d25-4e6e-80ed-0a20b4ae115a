/**
 * 增强的双视频预览样式
 * 确保样式能够正确应用到动态生成的元素
 */

/* 重要：使用!important确保样式优先级 */
.realtime-preview-section {
  margin: 24px -20px !important; /* 负边距让容器突破父容器限制 */
  padding: 32px !important;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95)) !important;
  border-radius: 16px !important;
  border: 1px solid rgba(148, 163, 184, 0.2) !important;
  box-shadow:
    0 10px 25px rgba(0, 0, 0, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  position: relative !important;
  overflow: hidden !important;
  width: calc(100vw - 40px) !important; /* 使用视口宽度减去边距 */
  max-width: 1400px !important; /* 设置最大宽度 */
  margin-left: auto !important;
  margin-right: auto !important;
}

.realtime-preview-section::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%) !important;
  pointer-events: none !important;
  z-index: 0 !important;
}

.preview-title {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 12px !important;
  color: #f1f5f9 !important;
  margin-bottom: 24px !important;
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  text-align: center !important;
  position: relative !important;
  z-index: 1 !important;
}

.preview-title::before {
  content: '' !important;
  position: absolute !important;
  bottom: -8px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 60px !important;
  height: 3px !important;
  background: linear-gradient(90deg, #3b82f6, #10b981) !important;
  border-radius: 2px !important;
}

.video-preview-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 32px !important; /* 增加间距 */
  position: relative !important;
  z-index: 1 !important;
  max-width: none !important; /* 移除最大宽度限制 */
  width: 100% !important;
}

.video-preview-container {
  background: linear-gradient(145deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9)) !important;
  border-radius: 12px !important;
  padding: 24px !important; /* 增加内边距 */
  border: 1px solid rgba(148, 163, 184, 0.15) !important;
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  min-height: 400px !important; /* 设置最小高度 */
  flex: 1 !important; /* 让容器平均分配空间 */
}

.video-preview-container:hover {
  transform: translateY(-2px) !important;
  box-shadow: 
    0 12px 30px rgba(0, 0, 0, 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(148, 163, 184, 0.3) !important;
}

.video-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 16px !important;
  padding-bottom: 12px !important;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1) !important;
}

.video-header h4 {
  color: #f1f5f9 !important;
  margin: 0 !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.video-header h4::before {
  content: '📹' !important;
  font-size: 1.2rem !important;
}

.video-preview-area {
  min-height: 320px !important; /* 增加最小高度 */
  height: 320px !important; /* 设置固定高度 */
  background: linear-gradient(135deg, #0f172a, #1e293b) !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #94a3b8 !important;
  font-size: 0.9rem !important;
  border: 2px dashed rgba(148, 163, 184, 0.2) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
  flex-direction: column !important;
}

.video-preview-area:hover {
  border-color: rgba(148, 163, 184, 0.4) !important;
  background: linear-gradient(135deg, #1e293b, #334155) !important;
}

.video-preview-area.waiting {
  animation: pulse 2s infinite !important;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.detection-frame-container {
  position: relative !important;
  width: 100% !important;
  height: 320px !important; /* 与预览区域高度一致 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1)) !important;
  border: 2px solid rgba(16, 185, 129, 0.4) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  animation: detectGlow 2s ease-in-out infinite alternate !important;
}

@keyframes detectGlow {
  from {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.5);
  }
}

.detection-frame-image {
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: contain !important;
  border-radius: 6px !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4) !important;
}

.detection-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  pointer-events: none !important;
}

.detection-header {
  position: absolute !important;
  top: 12px !important;
  right: 12px !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
  gap: 8px !important;
}

.detection-time {
  background: rgba(0, 0, 0, 0.8) !important;
  color: #f1f5f9 !important;
  padding: 6px 12px !important;
  border-radius: 20px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.detection-info {
  position: absolute !important;
  bottom: 12px !important;
  left: 12px !important;
  background: rgba(0, 0, 0, 0.85) !important;
  color: #f1f5f9 !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  font-size: 0.8rem !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
  border-left: 4px solid #10b981 !important;
  backdrop-filter: blur(10px) !important;
  min-width: 140px !important;
}

.detection-status {
  color: #10b981 !important;
  font-weight: 600 !important;
  font-size: 0.75rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.video-stats {
  margin-top: 12px !important;
  padding: 12px !important;
  background: rgba(15, 23, 42, 0.6) !important;
  border-radius: 6px !important;
  font-size: 0.8rem !important;
  color: #94a3b8 !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  border: 1px solid rgba(148, 163, 184, 0.1) !important;
}

.video-stats .stat-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 2px !important;
}

.video-stats .stat-value {
  color: #f1f5f9 !important;
  font-weight: 600 !important;
  font-size: 0.9rem !important;
}

.video-stats .stat-label {
  color: #64748b !important;
  font-size: 0.7rem !important;
}

.loading-spinner {
  width: 40px !important;
  height: 40px !important;
  border: 3px solid rgba(148, 163, 184, 0.3) !important;
  border-top: 3px solid #3b82f6 !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
  margin-bottom: 12px !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.success-indicator {
  position: absolute !important;
  top: 8px !important;
  left: 8px !important;
  width: 12px !important;
  height: 12px !important;
  background: #10b981 !important;
  border-radius: 50% !important;
  animation: successPulse 2s infinite !important;
}

@keyframes successPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .realtime-preview-section {
    margin: 24px -10px !important; /* 减少负边距 */
    padding: 24px !important;
    width: calc(100vw - 20px) !important;
  }

  .video-preview-grid {
    gap: 24px !important;
  }
}

@media (max-width: 768px) {
  .video-preview-grid {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }

  .realtime-preview-section {
    margin: 16px 0 !important; /* 移动端不使用负边距 */
    padding: 16px !important;
    width: 100% !important;
  }

  .preview-title {
    font-size: 1.3rem !important;
    margin-bottom: 20px !important;
  }

  .video-preview-container {
    padding: 16px !important;
    min-height: 300px !important;
  }

  .video-preview-area,
  .detection-frame-container {
    min-height: 240px !important;
    height: 240px !important;
  }
}

@media (min-width: 1400px) {
  .realtime-preview-section {
    margin: 32px -40px !important; /* 大屏幕增加负边距 */
    padding: 40px !important;
    width: calc(100vw - 80px) !important;
    max-width: 1600px !important;
  }

  .video-preview-grid {
    gap: 40px !important;
  }

  .video-preview-container {
    padding: 32px !important;
    min-height: 450px !important;
  }

  .video-preview-area,
  .detection-frame-container {
    height: 380px !important;
  }
}
