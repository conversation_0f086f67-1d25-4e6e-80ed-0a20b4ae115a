# 交通分析系统 - Python模型服务

本项目是交通分析系统的Python模型服务，提供基于YOLOv11的交通图像分析功能。该服务由两个主要API组成：模型API和数据API。

## 目录结构

```
python_model/
├── api_controller.py         # 数据API控制器(端口5000)
├── model_api.py              # 模型推理API(端口5001)
├── car_detection.py          # 车辆检测核心实现
├── config.py                 # 模型配置文件
├── analysis_service.py       # 分析结果服务
├── user_service.py           # 用户服务
├── db_config.py              # 数据库配置
├── requirements.txt          # Python依赖
├── start_api_server.bat      # 启动服务脚本
├── yolox.pt                  # YOLOv11x预训练模型
├── core/                     # 核心功能模块
├── services/                 # 服务层模块
├── utils/                    # 工具类
└── static/                   # 静态资源目录
```

## 功能说明

### 1. 模型API (端口5001)

模型API负责图像处理和车辆检测：

- `GET /health` - 健康检查
- `GET /status` - 获取模型状态和加载信息
- `POST /analyze` - 分析图片并返回检测结果

### 2. 数据API (端口5000)

数据API负责用户认证和结果存储：

- `POST /api/auth/register` - 注册新用户
- `POST /api/auth/login` - 用户登录
- `POST /api/analyze` - 分析图片(代理到模型API)
- `GET /api/history` - 获取分析历史
- `GET /api/result/<id>` - 获取分析结果详情

## 依赖安装

```bash
pip install -r requirements.txt
```

## 启动服务

```bash
# 使用批处理脚本
start_api_server.bat

# 或手动启动
python api_controller.py
```

默认端口：
- 数据API: 5000
- 模型API: 5001

## 数据格式

### 分析请求

```json
{
  "image_base64": "base64编码的图片",
  "confidence": 0.5,  // 可选，置信度阈值
  "user_id": "用户ID"  // 可选
}
```

### 分析响应

```json
{
  "success": true,
  "vehicle_count": 5,
  "inference_time": 0.45,
  "result_image_base64": "base64编码的结果图片",
  "detections": [
    {
      "class_name": "car",
      "confidence": 0.92,
      "bbox": [x1, y1, x2, y2]
    },
    // ...
  ]
}
```

## 数据库配置

模型服务使用MongoDB存储分析结果和用户信息：

- 数据库名: `traffic_analysis`
- 主要集合: 
  - `users` - 用户信息
  - `analysis_results` - 分析结果

## 车辆类别配置

在`config.py`中可以配置检测的车辆类别：

```python
MODEL_CONFIG = {
    "vehicle_classes": [2, 3, 5, 7],  # 对应"car", "motorcycle", "bus", "truck"
    "class_names": ["person", "bicycle", "car", "motorcycle", "airplane", 
                   "bus", "train", "truck", "boat", ...]
}
```

## 开发指南

### 添加新的检测类别

1. 修改`config.py`中的`vehicle_classes`列表
2. 更新相应的`class_names`映射

### 优化检测性能

1. 调整`MODEL_CONFIG`中的`conf_threshold`和`iou_threshold`参数
2. 修改`car_detection.py`中的预处理参数 