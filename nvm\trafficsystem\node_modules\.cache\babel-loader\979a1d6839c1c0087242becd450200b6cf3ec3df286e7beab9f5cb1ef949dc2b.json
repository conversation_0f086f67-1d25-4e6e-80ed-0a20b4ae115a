{"ast": null, "code": "import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Grid, Upload, VideoCamera, DataAnalysis, Document, Plus, MoreFilled, Refresh, InfoFilled } from '@element-plus/icons-vue';\n\n// 导入组件\nimport FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue';\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue';\nimport TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue';\nimport IntelligentTrafficReport from '@/components/analysis/IntelligentTrafficReport.vue';\nexport default {\n  name: 'FourWayAnalysisConsole',\n  components: {\n    Grid,\n    Upload,\n    VideoCamera,\n    DataAnalysis,\n    Document,\n    Plus,\n    MoreFilled,\n    Refresh,\n    InfoFilled,\n    FourWayVideoUpload,\n    FourWayRealtimeViewer,\n    TrafficAnalysisDashboard,\n    IntelligentTrafficReport\n  },\n  setup() {\n    const router = useRouter();\n\n    // 响应式数据\n    const currentStep = ref(0);\n    const currentTaskId = ref('');\n    const currentTask = ref({\n      id: '',\n      name: '四方向交通分析任务',\n      status: 'waiting',\n      progress: 0,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    });\n    const reportData = ref(null);\n    const activeConnections = ref(0);\n    const lastUpdateTime = ref('');\n\n    // 系统状态\n    const systemStatus = reactive({\n      type: 'success',\n      text: '正常运行'\n    });\n\n    // 计算属性\n    const totalTasks = computed(() => 1);\n    const activeTasks = computed(() => currentStep.value > 0 && currentStep.value < 4 ? 1 : 0);\n    const completedTasks = computed(() => currentStep.value === 4 ? 1 : 0);\n    const canUpload = computed(() => true);\n    const canDetect = computed(() => currentStep.value >= 1);\n    const canAnalyze = computed(() => currentStep.value >= 2);\n    const canExport = computed(() => currentStep.value >= 3);\n\n    // 方法\n\n    // 事件处理\n    const handleUploadSuccess = response => {\n      const taskId = response.data?.taskId || `task_${Date.now()}`;\n      currentTaskId.value = taskId;\n\n      // 更新当前任务信息\n      currentTask.value = {\n        id: taskId,\n        name: '四方向交通分析任务',\n        status: 'processing',\n        progress: 10,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n      currentStep.value = 1;\n      ElMessage.success('视频上传成功，开始实时检测');\n    };\n    const handleUploadError = error => {\n      ElMessage.error('视频上传失败: ' + error.message);\n    };\n    const handleUploadProgress = progress => {\n      console.log('上传进度:', progress);\n    };\n    const handleUploadStatusChange = status => {\n      console.log('上传状态变化:', status);\n    };\n    const handleDetectionUpdate = data => {\n      console.log('检测更新:', data);\n\n      // 更新任务进度\n      if (currentTask.value && data.progress) {\n        currentTask.value.progress = Math.min(data.progress, 90); // 检测阶段最多90%\n        currentTask.value.updatedAt = new Date();\n      }\n    };\n    const handleDetectionStatusChange = status => {\n      if (status === 'completed') {\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed';\n          currentTask.value.progress = 100;\n          currentTask.value.updatedAt = new Date();\n        }\n        currentStep.value = 2;\n        ElMessage.success('实时检测完成，开始智能分析');\n      }\n    };\n\n    // 处理四方向分析完成事件\n    const handleAnalysisComplete = completeData => {\n      try {\n        console.log('🎉 收到四方向分析完成事件:', completeData);\n\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed';\n          currentTask.value.progress = 100;\n          currentTask.value.updatedAt = new Date();\n        }\n\n        // 显示完成提示并自动跳转\n        ElMessage({\n          message: `四方向分析完成！检测到 ${completeData.summary?.totalVehicles || 0} 辆车辆，正在跳转到智能分析模块...`,\n          type: 'success',\n          duration: 4000\n        });\n\n        // 延迟跳转到智能分析模块，给用户时间看到完成消息\n        setTimeout(() => {\n          currentStep.value = 2;\n          ElMessage.info('已自动跳转到智能分析模块');\n        }, 2000);\n      } catch (error) {\n        console.error('处理分析完成事件失败:', error);\n        ElMessage.error('处理完成事件失败，请手动跳转到智能分析');\n      }\n    };\n    const handleAnalysisDataUpdate = data => {\n      reportData.value = data;\n      currentStep.value = 3;\n      ElMessage.success('智能分析完成，可以生成报告');\n    };\n    const handleExportReport = taskId => {\n      ElMessage.success('报告导出成功');\n    };\n    const handleRefreshReportData = taskId => {\n      ElMessage.success('报告数据刷新成功');\n    };\n\n    // 快速操作\n    const goToUpload = () => {\n      currentStep.value = 0;\n    };\n    const startDetection = () => {\n      if (canDetect.value) {\n        currentStep.value = 1;\n      } else {\n        ElMessage.warning('请先上传视频文件');\n      }\n    };\n    const generateAnalysis = () => {\n      if (canAnalyze.value) {\n        currentStep.value = 2;\n      } else {\n        ElMessage.warning('请先完成视频检测');\n      }\n    };\n    const exportReport = () => {\n      if (canExport.value) {\n        currentStep.value = 3;\n      } else {\n        ElMessage.warning('请先完成智能分析');\n      }\n    };\n    const refreshSystem = () => {\n      lastUpdateTime.value = new Date().toLocaleTimeString();\n      ElMessage.success('系统状态已刷新');\n    };\n    const showSystemInfo = () => {\n      ElMessageBox.alert('四方向智能交通分析系统 v1.0.0', '系统信息', {\n        confirmButtonText: '确定'\n      });\n    };\n\n    // 任务状态辅助方法\n    const getTaskStatusType = status => {\n      const statusMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'failed': 'danger',\n        'paused': 'info'\n      };\n      return statusMap[status] || 'info';\n    };\n    const getTaskStatusText = status => {\n      const statusMap = {\n        'waiting': '等待中',\n        'processing': '处理中',\n        'completed': '已完成',\n        'failed': '失败',\n        'paused': '已暂停'\n      };\n      return statusMap[status] || '未知';\n    };\n    const getProgressStatus = status => {\n      if (status === 'completed') return 'success';\n      if (status === 'failed') return 'exception';\n      return null;\n    };\n    const formatTime = time => {\n      if (!time) return '-';\n      return new Date(time).toLocaleString();\n    };\n    const getProcessingDuration = task => {\n      if (!task || !task.createdAt) return '-';\n      const start = new Date(task.createdAt);\n      const end = task.updatedAt ? new Date(task.updatedAt) : new Date();\n      const duration = Math.floor((end - start) / 1000);\n      if (duration < 60) return `${duration}秒`;\n      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`;\n      return `${Math.floor(duration / 3600)}小时`;\n    };\n\n    // 生命周期\n    onMounted(() => {\n      // 初始化系统状态\n      lastUpdateTime.value = new Date().toLocaleTimeString();\n      activeConnections.value = 1;\n    });\n    return {\n      // 图标组件\n      Upload,\n      VideoCamera,\n      DataAnalysis,\n      Document,\n      Plus,\n      MoreFilled,\n      Refresh,\n      InfoFilled,\n      // 响应式数据\n      currentStep,\n      currentTaskId,\n      currentTask,\n      reportData,\n      activeConnections,\n      lastUpdateTime,\n      systemStatus,\n      // 计算属性\n      totalTasks,\n      activeTasks,\n      completedTasks,\n      canUpload,\n      canDetect,\n      canAnalyze,\n      canExport,\n      // 方法\n      handleUploadSuccess,\n      handleUploadError,\n      handleUploadProgress,\n      handleUploadStatusChange,\n      handleDetectionUpdate,\n      handleDetectionStatusChange,\n      handleAnalysisDataUpdate,\n      handleExportReport,\n      handleRefreshReportData,\n      goToUpload,\n      startDetection,\n      generateAnalysis,\n      exportReport,\n      refreshSystem,\n      showSystemInfo,\n      // 任务状态辅助方法\n      getTaskStatusType,\n      getTaskStatusText,\n      getProgressStatus,\n      formatTime,\n      getProcessingDuration\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "onUnmounted", "useRouter", "ElMessage", "ElMessageBox", "Grid", "Upload", "VideoCamera", "DataAnalysis", "Document", "Plus", "MoreFilled", "Refresh", "InfoFilled", "FourWayVideoUpload", "FourWayRealtimeViewer", "TrafficAnalysisDashboard", "IntelligentTrafficReport", "name", "components", "setup", "router", "currentStep", "currentTaskId", "currentTask", "id", "status", "progress", "createdAt", "Date", "updatedAt", "reportData", "activeConnections", "lastUpdateTime", "systemStatus", "type", "text", "totalTasks", "activeTasks", "value", "completedTasks", "canUpload", "canDetect", "canAnalyze", "canExport", "handleUploadSuccess", "response", "taskId", "data", "now", "success", "handleUploadError", "error", "message", "handleUploadProgress", "console", "log", "handleUploadStatusChange", "handleDetectionUpdate", "Math", "min", "handleDetectionStatusChange", "handleAnalysisComplete", "completeData", "summary", "totalVehicles", "duration", "setTimeout", "info", "handleAnalysisDataUpdate", "handleExportReport", "handleRefreshReportData", "goToUpload", "startDetection", "warning", "generateAnalysis", "exportReport", "refreshSystem", "toLocaleTimeString", "showSystemInfo", "alert", "confirmButtonText", "getTaskStatusType", "statusMap", "getTaskStatusText", "getProgressStatus", "formatTime", "time", "toLocaleString", "getProcessingDuration", "task", "start", "end", "floor"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\FourWayAnalysisConsole.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-analysis-console\">\n    <!-- 页面头部 -->\n    <div class=\"console-header\">\n      <div class=\"header-content\">\n        <h1 class=\"console-title\">\n          <el-icon><Grid /></el-icon>\n          四方向智能交通分析控制台\n        </h1>\n        <p class=\"console-description\">\n          集成视频上传、实时检测、智能分析和报告生成的一站式交通分析平台\n        </p>\n      </div>\n      \n      <div class=\"header-stats\">\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ totalTasks }}</div>\n          <div class=\"stat-label\">总任务数</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ activeTasks }}</div>\n          <div class=\"stat-label\">活跃任务</div>\n        </div>\n        <div class=\"stat-item\">\n          <div class=\"stat-value\">{{ completedTasks }}</div>\n          <div class=\"stat-label\">已完成</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 工作流程导航 -->\n    <div class=\"workflow-navigation\">\n      <el-steps :active=\"currentStep\" align-center>\n        <el-step \n          title=\"视频上传\" \n          description=\"上传四方向视频文件\"\n          icon=\"Upload\"\n        />\n        <el-step \n          title=\"实时检测\" \n          description=\"AI模型实时分析\"\n          icon=\"VideoCamera\"\n        />\n        <el-step \n          title=\"智能分析\" \n          description=\"生成分析结果\"\n          icon=\"DataAnalysis\"\n        />\n        <el-step \n          title=\"报告生成\" \n          description=\"导出分析报告\"\n          icon=\"Document\"\n        />\n      </el-steps>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"console-content\">\n      <!-- 左侧面板 -->\n      <div class=\"left-panel\">\n\n\n        <!-- 快速操作 -->\n        <el-card class=\"quick-actions-card\">\n          <template #header>\n            <span>快速操作</span>\n          </template>\n          \n          <div class=\"quick-actions\">\n            <el-button \n              type=\"primary\" \n              :icon=\"Upload\" \n              @click=\"goToUpload\"\n              :disabled=\"!canUpload\"\n            >\n              上传视频\n            </el-button>\n            <el-button \n              type=\"success\" \n              :icon=\"VideoCamera\" \n              @click=\"startDetection\"\n              :disabled=\"!canDetect\"\n            >\n              开始检测\n            </el-button>\n            <el-button \n              type=\"warning\" \n              :icon=\"DataAnalysis\" \n              @click=\"generateAnalysis\"\n              :disabled=\"!canAnalyze\"\n            >\n              智能分析\n            </el-button>\n            <el-button \n              type=\"info\" \n              :icon=\"Document\" \n              @click=\"exportReport\"\n              :disabled=\"!canExport\"\n            >\n              导出报告\n            </el-button>\n          </div>\n        </el-card>\n      </div>\n\n      <!-- 右侧主内容 -->\n      <div class=\"main-content\">\n        <!-- 当前任务信息 -->\n        <div v-if=\"currentTask\" class=\"current-task-info\">\n          <el-card>\n            <template #header>\n              <div class=\"task-header\">\n                <div class=\"task-title-section\">\n                  <h3>{{ currentTask.name }}</h3>\n                  <el-tag :type=\"getTaskStatusType(currentTask.status)\">\n                    {{ getTaskStatusText(currentTask.status) }}\n                  </el-tag>\n                </div>\n                <div class=\"task-progress-section\">\n                  <el-progress \n                    :percentage=\"currentTask.progress\" \n                    :status=\"getProgressStatus(currentTask.status)\"\n                    :stroke-width=\"8\"\n                  />\n                  <span class=\"progress-text\">{{ currentTask.progress }}%</span>\n                </div>\n              </div>\n            </template>\n            \n            <div class=\"task-details\">\n              <el-row :gutter=\"20\">\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">任务ID:</span>\n                    <span class=\"detail-value\">{{ currentTask.id }}</span>\n                  </div>\n                </el-col>\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">创建时间:</span>\n                    <span class=\"detail-value\">{{ formatTime(currentTask.createdAt) }}</span>\n                  </div>\n                </el-col>\n                <el-col :span=\"8\">\n                  <div class=\"detail-item\">\n                    <span class=\"detail-label\">处理时长:</span>\n                    <span class=\"detail-value\">{{ getProcessingDuration(currentTask) }}</span>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </el-card>\n        </div>\n\n        <!-- 动态内容区域 -->\n        <div class=\"dynamic-content\">\n          <!-- 步骤1: 视频上传 -->\n          <div v-if=\"currentStep === 0\" class=\"step-content\">\n            <FourWayVideoUpload\n              @upload-success=\"handleUploadSuccess\"\n              @upload-error=\"handleUploadError\"\n              @upload-progress=\"handleUploadProgress\"\n              @status-change=\"handleUploadStatusChange\"\n            />\n          </div>\n\n          <!-- 步骤2: 实时检测 -->\n          <div v-if=\"currentStep === 1\" class=\"step-content\">\n            <FourWayRealtimeViewer\n              v-if=\"currentTaskId\"\n              :task-id=\"currentTaskId\"\n              :auto-start=\"true\"\n              @detection-update=\"handleDetectionUpdate\"\n              @status-change=\"handleDetectionStatusChange\"\n              @analysis-complete=\"handleAnalysisComplete\"\n            />\n            <el-empty v-else description=\"请先上传视频文件\">\n              <el-button type=\"primary\" @click=\"currentStep = 0\">\n                返回上传\n              </el-button>\n            </el-empty>\n          </div>\n\n          <!-- 步骤3: 智能分析 -->\n          <div v-if=\"currentStep === 2\" class=\"step-content\">\n            <TrafficAnalysisDashboard\n              v-if=\"currentTaskId\"\n              :task-id=\"currentTaskId\"\n              @data-updated=\"handleAnalysisDataUpdate\"\n            />\n            <el-empty v-else description=\"请先完成视频检测\">\n              <el-button type=\"primary\" @click=\"currentStep = 1\">\n                返回检测\n              </el-button>\n            </el-empty>\n          </div>\n\n          <!-- 步骤4: 报告生成 -->\n          <div v-if=\"currentStep === 3\" class=\"step-content\">\n            <IntelligentTrafficReport\n              v-if=\"currentTaskId && reportData\"\n              :task-id=\"currentTaskId\"\n              :report-data=\"reportData\"\n              @export-report=\"handleExportReport\"\n              @refresh-data=\"handleRefreshReportData\"\n            />\n            <el-empty v-else description=\"请先完成智能分析\">\n              <el-button type=\"primary\" @click=\"currentStep = 2\">\n                返回分析\n              </el-button>\n            </el-empty>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 底部状态栏 -->\n    <div class=\"console-footer\">\n      <div class=\"footer-info\">\n        <span>系统状态: </span>\n        <el-tag :type=\"systemStatus.type\" size=\"small\">{{ systemStatus.text }}</el-tag>\n        <span class=\"separator\">|</span>\n        <span>活跃连接: {{ activeConnections }}</span>\n        <span class=\"separator\">|</span>\n        <span>最后更新: {{ lastUpdateTime }}</span>\n      </div>\n      \n      <div class=\"footer-actions\">\n        <el-button size=\"small\" @click=\"refreshSystem\">\n          <el-icon><Refresh /></el-icon>\n          刷新\n        </el-button>\n        <el-button size=\"small\" @click=\"showSystemInfo\">\n          <el-icon><InfoFilled /></el-icon>\n          系统信息\n        </el-button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted, onUnmounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport {\n  Grid, Upload, VideoCamera, DataAnalysis, Document, Plus, \n  MoreFilled, Refresh, InfoFilled\n} from '@element-plus/icons-vue'\n\n// 导入组件\nimport FourWayVideoUpload from '@/components/analysis/FourWayVideoUpload.vue'\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue'\nimport TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue'\nimport IntelligentTrafficReport from '@/components/analysis/IntelligentTrafficReport.vue'\n\nexport default {\n  name: 'FourWayAnalysisConsole',\n  components: {\n    Grid, Upload, VideoCamera, DataAnalysis, Document, Plus, \n    MoreFilled, Refresh, InfoFilled,\n    FourWayVideoUpload,\n    FourWayRealtimeViewer,\n    TrafficAnalysisDashboard,\n    IntelligentTrafficReport\n  },\n  setup() {\n    const router = useRouter()\n    \n    // 响应式数据\n    const currentStep = ref(0)\n    const currentTaskId = ref('')\n    const currentTask = ref({\n      id: '',\n      name: '四方向交通分析任务',\n      status: 'waiting',\n      progress: 0,\n      createdAt: new Date(),\n      updatedAt: new Date()\n    })\n    const reportData = ref(null)\n    const activeConnections = ref(0)\n    const lastUpdateTime = ref('')\n\n    // 系统状态\n    const systemStatus = reactive({\n      type: 'success',\n      text: '正常运行'\n    })\n\n    // 计算属性\n    const totalTasks = computed(() => 1)\n    const activeTasks = computed(() => currentStep.value > 0 && currentStep.value < 4 ? 1 : 0)\n    const completedTasks = computed(() => currentStep.value === 4 ? 1 : 0)\n\n    const canUpload = computed(() => true)\n    const canDetect = computed(() => currentStep.value >= 1)\n    const canAnalyze = computed(() => currentStep.value >= 2)\n    const canExport = computed(() => currentStep.value >= 3)\n    \n    // 方法\n    \n    // 事件处理\n    const handleUploadSuccess = (response) => {\n      const taskId = response.data?.taskId || `task_${Date.now()}`\n      currentTaskId.value = taskId\n\n      // 更新当前任务信息\n      currentTask.value = {\n        id: taskId,\n        name: '四方向交通分析任务',\n        status: 'processing',\n        progress: 10,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      }\n\n      currentStep.value = 1\n      ElMessage.success('视频上传成功，开始实时检测')\n    }\n\n    const handleUploadError = (error) => {\n      ElMessage.error('视频上传失败: ' + error.message)\n    }\n\n    const handleUploadProgress = (progress) => {\n      console.log('上传进度:', progress)\n    }\n\n    const handleUploadStatusChange = (status) => {\n      console.log('上传状态变化:', status)\n    }\n\n    const handleDetectionUpdate = (data) => {\n      console.log('检测更新:', data)\n\n      // 更新任务进度\n      if (currentTask.value && data.progress) {\n        currentTask.value.progress = Math.min(data.progress, 90) // 检测阶段最多90%\n        currentTask.value.updatedAt = new Date()\n      }\n    }\n\n    const handleDetectionStatusChange = (status) => {\n      if (status === 'completed') {\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed'\n          currentTask.value.progress = 100\n          currentTask.value.updatedAt = new Date()\n        }\n\n        currentStep.value = 2\n        ElMessage.success('实时检测完成，开始智能分析')\n      }\n    }\n\n    // 处理四方向分析完成事件\n    const handleAnalysisComplete = (completeData) => {\n      try {\n        console.log('🎉 收到四方向分析完成事件:', completeData)\n\n        // 更新任务状态\n        if (currentTask.value) {\n          currentTask.value.status = 'completed'\n          currentTask.value.progress = 100\n          currentTask.value.updatedAt = new Date()\n        }\n\n        // 显示完成提示并自动跳转\n        ElMessage({\n          message: `四方向分析完成！检测到 ${completeData.summary?.totalVehicles || 0} 辆车辆，正在跳转到智能分析模块...`,\n          type: 'success',\n          duration: 4000\n        })\n\n        // 延迟跳转到智能分析模块，给用户时间看到完成消息\n        setTimeout(() => {\n          currentStep.value = 2\n          ElMessage.info('已自动跳转到智能分析模块')\n        }, 2000)\n\n      } catch (error) {\n        console.error('处理分析完成事件失败:', error)\n        ElMessage.error('处理完成事件失败，请手动跳转到智能分析')\n      }\n    }\n\n    const handleAnalysisDataUpdate = (data) => {\n      reportData.value = data\n      currentStep.value = 3\n      ElMessage.success('智能分析完成，可以生成报告')\n    }\n\n    const handleExportReport = (taskId) => {\n      ElMessage.success('报告导出成功')\n    }\n\n    const handleRefreshReportData = (taskId) => {\n      ElMessage.success('报告数据刷新成功')\n    }\n    \n    // 快速操作\n    const goToUpload = () => {\n      currentStep.value = 0\n    }\n    \n    const startDetection = () => {\n      if (canDetect.value) {\n        currentStep.value = 1\n      } else {\n        ElMessage.warning('请先上传视频文件')\n      }\n    }\n    \n    const generateAnalysis = () => {\n      if (canAnalyze.value) {\n        currentStep.value = 2\n      } else {\n        ElMessage.warning('请先完成视频检测')\n      }\n    }\n    \n    const exportReport = () => {\n      if (canExport.value) {\n        currentStep.value = 3\n      } else {\n        ElMessage.warning('请先完成智能分析')\n      }\n    }\n\n    const refreshSystem = () => {\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      ElMessage.success('系统状态已刷新')\n    }\n\n    const showSystemInfo = () => {\n      ElMessageBox.alert('四方向智能交通分析系统 v1.0.0', '系统信息', {\n        confirmButtonText: '确定'\n      })\n    }\n\n    // 任务状态辅助方法\n    const getTaskStatusType = (status) => {\n      const statusMap = {\n        'waiting': 'info',\n        'processing': 'warning',\n        'completed': 'success',\n        'failed': 'danger',\n        'paused': 'info'\n      }\n      return statusMap[status] || 'info'\n    }\n\n    const getTaskStatusText = (status) => {\n      const statusMap = {\n        'waiting': '等待中',\n        'processing': '处理中',\n        'completed': '已完成',\n        'failed': '失败',\n        'paused': '已暂停'\n      }\n      return statusMap[status] || '未知'\n    }\n\n    const getProgressStatus = (status) => {\n      if (status === 'completed') return 'success'\n      if (status === 'failed') return 'exception'\n      return null\n    }\n\n    const formatTime = (time) => {\n      if (!time) return '-'\n      return new Date(time).toLocaleString()\n    }\n\n    const getProcessingDuration = (task) => {\n      if (!task || !task.createdAt) return '-'\n      const start = new Date(task.createdAt)\n      const end = task.updatedAt ? new Date(task.updatedAt) : new Date()\n      const duration = Math.floor((end - start) / 1000)\n\n      if (duration < 60) return `${duration}秒`\n      if (duration < 3600) return `${Math.floor(duration / 60)}分钟`\n      return `${Math.floor(duration / 3600)}小时`\n    }\n\n    // 生命周期\n    onMounted(() => {\n      // 初始化系统状态\n      lastUpdateTime.value = new Date().toLocaleTimeString()\n      activeConnections.value = 1\n    })\n    \n    return {\n      // 图标组件\n      Upload,\n      VideoCamera,\n      DataAnalysis,\n      Document,\n      Plus,\n      MoreFilled,\n      Refresh,\n      InfoFilled,\n\n      // 响应式数据\n      currentStep,\n      currentTaskId,\n      currentTask,\n      reportData,\n      activeConnections,\n      lastUpdateTime,\n      systemStatus,\n\n      // 计算属性\n      totalTasks,\n      activeTasks,\n      completedTasks,\n      canUpload,\n      canDetect,\n      canAnalyze,\n      canExport,\n\n      // 方法\n      handleUploadSuccess,\n      handleUploadError,\n      handleUploadProgress,\n      handleUploadStatusChange,\n      handleDetectionUpdate,\n      handleDetectionStatusChange,\n      handleAnalysisDataUpdate,\n      handleExportReport,\n      handleRefreshReportData,\n      goToUpload,\n      startDetection,\n      generateAnalysis,\n      exportReport,\n      refreshSystem,\n      showSystemInfo,\n\n      // 任务状态辅助方法\n      getTaskStatusType,\n      getTaskStatusText,\n      getProgressStatus,\n      formatTime,\n      getProcessingDuration\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-analysis-console {\n  min-height: 100vh;\n  background: #f5f7fa;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 控制台头部 */\n.console-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 24px 32px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-content {\n  flex: 1;\n}\n\n.console-title {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 28px;\n  font-weight: 700;\n  margin: 0 0 8px 0;\n}\n\n.console-description {\n  font-size: 16px;\n  opacity: 0.9;\n  margin: 0;\n  line-height: 1.5;\n}\n\n.header-stats {\n  display: flex;\n  gap: 32px;\n}\n\n.stat-item {\n  text-align: center;\n}\n\n.stat-value {\n  font-size: 24px;\n  font-weight: 700;\n  line-height: 1.2;\n}\n\n.stat-label {\n  font-size: 14px;\n  opacity: 0.8;\n  margin-top: 4px;\n}\n\n/* 工作流程导航 */\n.workflow-navigation {\n  background: white;\n  padding: 24px 32px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n/* 主要内容区域 */\n.console-content {\n  flex: 1;\n  display: grid;\n  grid-template-columns: 280px 1fr;\n  gap: 24px;\n  padding: 24px 32px;\n  min-height: 0;\n}\n\n/* 左侧面板 */\n.left-panel {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.quick-actions-card {\n  height: fit-content;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.quick-actions .el-button {\n  justify-content: flex-start;\n}\n\n/* 主内容区域 */\n.main-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  min-height: 0;\n}\n\n.current-task-info {\n  flex-shrink: 0;\n}\n\n.task-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.task-title-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.task-title-section h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2937;\n}\n\n.task-progress-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  min-width: 200px;\n}\n\n.progress-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: #6b7280;\n  min-width: 40px;\n}\n\n.task-details {\n  margin-top: 16px;\n}\n\n.detail-item {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.detail-label {\n  font-size: 12px;\n  color: #6b7280;\n  font-weight: 500;\n}\n\n.detail-value {\n  font-size: 14px;\n  color: #1f2937;\n  font-weight: 500;\n}\n\n.dynamic-content {\n  flex: 1;\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  overflow: auto;\n}\n\n.step-content {\n  height: 100%;\n}\n\n/* 底部状态栏 */\n.console-footer {\n  background: white;\n  border-top: 1px solid #e5e7eb;\n  padding: 12px 32px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 14px;\n}\n\n.footer-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #6b7280;\n}\n\n.separator {\n  color: #d1d5db;\n}\n\n.footer-actions {\n  display: flex;\n  gap: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .console-content {\n    grid-template-columns: 300px 1fr;\n  }\n\n  .header-stats {\n    gap: 16px;\n  }\n}\n\n@media (max-width: 768px) {\n  .console-header {\n    flex-direction: column;\n    gap: 16px;\n    text-align: center;\n  }\n\n  .header-stats {\n    justify-content: center;\n  }\n\n  .console-content {\n    grid-template-columns: 1fr;\n    padding: 16px;\n  }\n\n  .workflow-navigation {\n    padding: 16px;\n  }\n\n  .console-footer {\n    flex-direction: column;\n    gap: 12px;\n    padding: 16px;\n  }\n\n  .task-header {\n    flex-direction: column;\n    gap: 12px;\n    align-items: flex-start;\n  }\n\n  .task-progress-section {\n    width: 100%;\n    min-width: auto;\n  }\n}\n\n/* 滚动条样式 */\n.task-list::-webkit-scrollbar {\n  width: 6px;\n}\n\n.task-list::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.task-list::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.task-list::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 动画效果 */\n.task-item {\n  animation: slideIn 0.3s ease-out;\n}\n\n@keyframes slideIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.step-content {\n  animation: fadeIn 0.5s ease-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n</style>\n"], "mappings": "AAkPA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AACpE,SAASC,SAAQ,QAAS,YAAW;AACrC,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SACEC,IAAI,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,IAAI,EACvDC,UAAU,EAAEC,OAAO,EAAEC,UAAS,QACzB,yBAAwB;;AAE/B;AACA,OAAOC,kBAAiB,MAAO,8CAA6C;AAC5E,OAAOC,qBAAoB,MAAO,iDAAgD;AAClF,OAAOC,wBAAuB,MAAO,oDAAmD;AACxF,OAAOC,wBAAuB,MAAO,oDAAmD;AAExF,eAAe;EACbC,IAAI,EAAE,wBAAwB;EAC9BC,UAAU,EAAE;IACVd,IAAI;IAAEC,MAAM;IAAEC,WAAW;IAAEC,YAAY;IAAEC,QAAQ;IAAEC,IAAI;IACvDC,UAAU;IAAEC,OAAO;IAAEC,UAAU;IAC/BC,kBAAkB;IAClBC,qBAAqB;IACrBC,wBAAwB;IACxBC;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAInB,SAAS,CAAC;;IAEzB;IACA,MAAMoB,WAAU,GAAIzB,GAAG,CAAC,CAAC;IACzB,MAAM0B,aAAY,GAAI1B,GAAG,CAAC,EAAE;IAC5B,MAAM2B,WAAU,GAAI3B,GAAG,CAAC;MACtB4B,EAAE,EAAE,EAAE;MACNP,IAAI,EAAE,WAAW;MACjBQ,MAAM,EAAE,SAAS;MACjBC,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;MACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;IACtB,CAAC;IACD,MAAME,UAAS,GAAIlC,GAAG,CAAC,IAAI;IAC3B,MAAMmC,iBAAgB,GAAInC,GAAG,CAAC,CAAC;IAC/B,MAAMoC,cAAa,GAAIpC,GAAG,CAAC,EAAE;;IAE7B;IACA,MAAMqC,YAAW,GAAIpC,QAAQ,CAAC;MAC5BqC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE;IACR,CAAC;;IAED;IACA,MAAMC,UAAS,GAAItC,QAAQ,CAAC,MAAM,CAAC;IACnC,MAAMuC,WAAU,GAAIvC,QAAQ,CAAC,MAAMuB,WAAW,CAACiB,KAAI,GAAI,KAAKjB,WAAW,CAACiB,KAAI,GAAI,IAAI,IAAI,CAAC;IACzF,MAAMC,cAAa,GAAIzC,QAAQ,CAAC,MAAMuB,WAAW,CAACiB,KAAI,KAAM,IAAI,IAAI,CAAC;IAErE,MAAME,SAAQ,GAAI1C,QAAQ,CAAC,MAAM,IAAI;IACrC,MAAM2C,SAAQ,GAAI3C,QAAQ,CAAC,MAAMuB,WAAW,CAACiB,KAAI,IAAK,CAAC;IACvD,MAAMI,UAAS,GAAI5C,QAAQ,CAAC,MAAMuB,WAAW,CAACiB,KAAI,IAAK,CAAC;IACxD,MAAMK,SAAQ,GAAI7C,QAAQ,CAAC,MAAMuB,WAAW,CAACiB,KAAI,IAAK,CAAC;;IAEvD;;IAEA;IACA,MAAMM,mBAAkB,GAAKC,QAAQ,IAAK;MACxC,MAAMC,MAAK,GAAID,QAAQ,CAACE,IAAI,EAAED,MAAK,IAAK,QAAQlB,IAAI,CAACoB,GAAG,CAAC,CAAC,EAAC;MAC3D1B,aAAa,CAACgB,KAAI,GAAIQ,MAAK;;MAE3B;MACAvB,WAAW,CAACe,KAAI,GAAI;QAClBd,EAAE,EAAEsB,MAAM;QACV7B,IAAI,EAAE,WAAW;QACjBQ,MAAM,EAAE,YAAY;QACpBC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;MACtB;MAEAP,WAAW,CAACiB,KAAI,GAAI;MACpBpC,SAAS,CAAC+C,OAAO,CAAC,eAAe;IACnC;IAEA,MAAMC,iBAAgB,GAAKC,KAAK,IAAK;MACnCjD,SAAS,CAACiD,KAAK,CAAC,UAAS,GAAIA,KAAK,CAACC,OAAO;IAC5C;IAEA,MAAMC,oBAAmB,GAAK3B,QAAQ,IAAK;MACzC4B,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE7B,QAAQ;IAC/B;IAEA,MAAM8B,wBAAuB,GAAK/B,MAAM,IAAK;MAC3C6B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE9B,MAAM;IAC/B;IAEA,MAAMgC,qBAAoB,GAAKV,IAAI,IAAK;MACtCO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAER,IAAI;;MAEzB;MACA,IAAIxB,WAAW,CAACe,KAAI,IAAKS,IAAI,CAACrB,QAAQ,EAAE;QACtCH,WAAW,CAACe,KAAK,CAACZ,QAAO,GAAIgC,IAAI,CAACC,GAAG,CAACZ,IAAI,CAACrB,QAAQ,EAAE,EAAE,GAAE;QACzDH,WAAW,CAACe,KAAK,CAACT,SAAQ,GAAI,IAAID,IAAI,CAAC;MACzC;IACF;IAEA,MAAMgC,2BAA0B,GAAKnC,MAAM,IAAK;MAC9C,IAAIA,MAAK,KAAM,WAAW,EAAE;QAC1B;QACA,IAAIF,WAAW,CAACe,KAAK,EAAE;UACrBf,WAAW,CAACe,KAAK,CAACb,MAAK,GAAI,WAAU;UACrCF,WAAW,CAACe,KAAK,CAACZ,QAAO,GAAI,GAAE;UAC/BH,WAAW,CAACe,KAAK,CAACT,SAAQ,GAAI,IAAID,IAAI,CAAC;QACzC;QAEAP,WAAW,CAACiB,KAAI,GAAI;QACpBpC,SAAS,CAAC+C,OAAO,CAAC,eAAe;MACnC;IACF;;IAEA;IACA,MAAMY,sBAAqB,GAAKC,YAAY,IAAK;MAC/C,IAAI;QACFR,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEO,YAAY;;QAE3C;QACA,IAAIvC,WAAW,CAACe,KAAK,EAAE;UACrBf,WAAW,CAACe,KAAK,CAACb,MAAK,GAAI,WAAU;UACrCF,WAAW,CAACe,KAAK,CAACZ,QAAO,GAAI,GAAE;UAC/BH,WAAW,CAACe,KAAK,CAACT,SAAQ,GAAI,IAAID,IAAI,CAAC;QACzC;;QAEA;QACA1B,SAAS,CAAC;UACRkD,OAAO,EAAE,eAAeU,YAAY,CAACC,OAAO,EAAEC,aAAY,IAAK,CAAC,qBAAqB;UACrF9B,IAAI,EAAE,SAAS;UACf+B,QAAQ,EAAE;QACZ,CAAC;;QAED;QACAC,UAAU,CAAC,MAAM;UACf7C,WAAW,CAACiB,KAAI,GAAI;UACpBpC,SAAS,CAACiE,IAAI,CAAC,cAAc;QAC/B,CAAC,EAAE,IAAI;MAET,EAAE,OAAOhB,KAAK,EAAE;QACdG,OAAO,CAACH,KAAK,CAAC,aAAa,EAAEA,KAAK;QAClCjD,SAAS,CAACiD,KAAK,CAAC,qBAAqB;MACvC;IACF;IAEA,MAAMiB,wBAAuB,GAAKrB,IAAI,IAAK;MACzCjB,UAAU,CAACQ,KAAI,GAAIS,IAAG;MACtB1B,WAAW,CAACiB,KAAI,GAAI;MACpBpC,SAAS,CAAC+C,OAAO,CAAC,eAAe;IACnC;IAEA,MAAMoB,kBAAiB,GAAKvB,MAAM,IAAK;MACrC5C,SAAS,CAAC+C,OAAO,CAAC,QAAQ;IAC5B;IAEA,MAAMqB,uBAAsB,GAAKxB,MAAM,IAAK;MAC1C5C,SAAS,CAAC+C,OAAO,CAAC,UAAU;IAC9B;;IAEA;IACA,MAAMsB,UAAS,GAAIA,CAAA,KAAM;MACvBlD,WAAW,CAACiB,KAAI,GAAI;IACtB;IAEA,MAAMkC,cAAa,GAAIA,CAAA,KAAM;MAC3B,IAAI/B,SAAS,CAACH,KAAK,EAAE;QACnBjB,WAAW,CAACiB,KAAI,GAAI;MACtB,OAAO;QACLpC,SAAS,CAACuE,OAAO,CAAC,UAAU;MAC9B;IACF;IAEA,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7B,IAAIhC,UAAU,CAACJ,KAAK,EAAE;QACpBjB,WAAW,CAACiB,KAAI,GAAI;MACtB,OAAO;QACLpC,SAAS,CAACuE,OAAO,CAAC,UAAU;MAC9B;IACF;IAEA,MAAME,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIhC,SAAS,CAACL,KAAK,EAAE;QACnBjB,WAAW,CAACiB,KAAI,GAAI;MACtB,OAAO;QACLpC,SAAS,CAACuE,OAAO,CAAC,UAAU;MAC9B;IACF;IAEA,MAAMG,aAAY,GAAIA,CAAA,KAAM;MAC1B5C,cAAc,CAACM,KAAI,GAAI,IAAIV,IAAI,CAAC,CAAC,CAACiD,kBAAkB,CAAC;MACrD3E,SAAS,CAAC+C,OAAO,CAAC,SAAS;IAC7B;IAEA,MAAM6B,cAAa,GAAIA,CAAA,KAAM;MAC3B3E,YAAY,CAAC4E,KAAK,CAAC,oBAAoB,EAAE,MAAM,EAAE;QAC/CC,iBAAiB,EAAE;MACrB,CAAC;IACH;;IAEA;IACA,MAAMC,iBAAgB,GAAKxD,MAAM,IAAK;MACpC,MAAMyD,SAAQ,GAAI;QAChB,SAAS,EAAE,MAAM;QACjB,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE,SAAS;QACtB,QAAQ,EAAE,QAAQ;QAClB,QAAQ,EAAE;MACZ;MACA,OAAOA,SAAS,CAACzD,MAAM,KAAK,MAAK;IACnC;IAEA,MAAM0D,iBAAgB,GAAK1D,MAAM,IAAK;MACpC,MAAMyD,SAAQ,GAAI;QAChB,SAAS,EAAE,KAAK;QAChB,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE;MACZ;MACA,OAAOA,SAAS,CAACzD,MAAM,KAAK,IAAG;IACjC;IAEA,MAAM2D,iBAAgB,GAAK3D,MAAM,IAAK;MACpC,IAAIA,MAAK,KAAM,WAAW,EAAE,OAAO,SAAQ;MAC3C,IAAIA,MAAK,KAAM,QAAQ,EAAE,OAAO,WAAU;MAC1C,OAAO,IAAG;IACZ;IAEA,MAAM4D,UAAS,GAAKC,IAAI,IAAK;MAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,GAAE;MACpB,OAAO,IAAI1D,IAAI,CAAC0D,IAAI,CAAC,CAACC,cAAc,CAAC;IACvC;IAEA,MAAMC,qBAAoB,GAAKC,IAAI,IAAK;MACtC,IAAI,CAACA,IAAG,IAAK,CAACA,IAAI,CAAC9D,SAAS,EAAE,OAAO,GAAE;MACvC,MAAM+D,KAAI,GAAI,IAAI9D,IAAI,CAAC6D,IAAI,CAAC9D,SAAS;MACrC,MAAMgE,GAAE,GAAIF,IAAI,CAAC5D,SAAQ,GAAI,IAAID,IAAI,CAAC6D,IAAI,CAAC5D,SAAS,IAAI,IAAID,IAAI,CAAC;MACjE,MAAMqC,QAAO,GAAIP,IAAI,CAACkC,KAAK,CAAC,CAACD,GAAE,GAAID,KAAK,IAAI,IAAI;MAEhD,IAAIzB,QAAO,GAAI,EAAE,EAAE,OAAO,GAAGA,QAAQ,GAAE;MACvC,IAAIA,QAAO,GAAI,IAAI,EAAE,OAAO,GAAGP,IAAI,CAACkC,KAAK,CAAC3B,QAAO,GAAI,EAAE,CAAC,IAAG;MAC3D,OAAO,GAAGP,IAAI,CAACkC,KAAK,CAAC3B,QAAO,GAAI,IAAI,CAAC,IAAG;IAC1C;;IAEA;IACAlE,SAAS,CAAC,MAAM;MACd;MACAiC,cAAc,CAACM,KAAI,GAAI,IAAIV,IAAI,CAAC,CAAC,CAACiD,kBAAkB,CAAC;MACrD9C,iBAAiB,CAACO,KAAI,GAAI;IAC5B,CAAC;IAED,OAAO;MACL;MACAjC,MAAM;MACNC,WAAW;MACXC,YAAY;MACZC,QAAQ;MACRC,IAAI;MACJC,UAAU;MACVC,OAAO;MACPC,UAAU;MAEV;MACAS,WAAW;MACXC,aAAa;MACbC,WAAW;MACXO,UAAU;MACVC,iBAAiB;MACjBC,cAAc;MACdC,YAAY;MAEZ;MACAG,UAAU;MACVC,WAAW;MACXE,cAAc;MACdC,SAAS;MACTC,SAAS;MACTC,UAAU;MACVC,SAAS;MAET;MACAC,mBAAmB;MACnBM,iBAAiB;MACjBG,oBAAoB;MACpBG,wBAAwB;MACxBC,qBAAqB;MACrBG,2BAA2B;MAC3BQ,wBAAwB;MACxBC,kBAAkB;MAClBC,uBAAuB;MACvBC,UAAU;MACVC,cAAc;MACdE,gBAAgB;MAChBC,YAAY;MACZC,aAAa;MACbE,cAAc;MAEd;MACAG,iBAAiB;MACjBE,iBAAiB;MACjBC,iBAAiB;MACjBC,UAAU;MACVG;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}