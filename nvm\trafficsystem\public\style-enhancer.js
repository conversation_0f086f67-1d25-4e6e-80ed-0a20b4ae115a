/**
 * 样式增强器
 * 确保双视频预览模块的样式正确应用
 */

// 日志防抖控制
const logDebounce = {
  lastMessages: new Map(),
  debounceTime: 5000 // 5秒内相同消息只显示一次
};

// 智能日志函数
function smartLog(level, message, data = null) {
  // 使用全局配置，如果不存在则使用默认值
  const config = window.GLOBAL_LOG_CONFIG || {
    enabled: false,
    level: 'ERROR',
    showStyleLogs: false,
    debounceTime: 5000
  };

  if (!config.enabled || !config.showStyleLogs) return;

  const levels = ['DEBUG', 'INFO', 'WARN', 'ERROR'];
  const currentLevelIndex = levels.indexOf(config.level);
  const messageLevelIndex = levels.indexOf(level);

  if (messageLevelIndex < currentLevelIndex) return;

  // 防抖检查
  const now = Date.now();
  const debounceTime = config.debounceTime || 5000;
  const lastTime = logDebounce.lastMessages.get(message);
  if (lastTime && (now - lastTime) < debounceTime) {
    return;
  }
  logDebounce.lastMessages.set(message, now);

  // 输出日志
  const logMethod = level === 'ERROR' ? console.error :
                   level === 'WARN' ? console.warn : console.log;
  logMethod(`[样式增强] ${message}`, data || '');
}

document.addEventListener('DOMContentLoaded', function() {
  smartLog('INFO', '🎨 样式增强器已加载');

  // 等待Vue应用初始化
  setTimeout(() => {
    enhancePreviewStyles();
    startStyleMonitoring();
  }, 2000);
});

function enhancePreviewStyles() {
  smartLog('DEBUG', '🎨 开始增强预览样式...');

  // 查找预览容器
  const previewSection = document.querySelector('.realtime-preview-section');
  if (previewSection) {
    smartLog('INFO', '✅ 找到预览容器，应用增强样式');
    applyEnhancedStyles(previewSection);
  } else {
    smartLog('DEBUG', '⚠️ 未找到预览容器，等待创建...');
    setTimeout(enhancePreviewStyles, 1000);
  }
}

function applyEnhancedStyles(container) {
  // 确保容器有正确的类名
  container.classList.add('realtime-preview-section');
  
  // 应用容器样式
  Object.assign(container.style, {
    margin: '24px -20px',
    padding: '32px',
    background: 'linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95))',
    borderRadius: '16px',
    border: '1px solid rgba(148, 163, 184, 0.2)',
    boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3), 0 4px 10px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
    position: 'relative',
    overflow: 'hidden',
    width: 'calc(100vw - 40px)',
    maxWidth: '1400px',
    marginLeft: 'auto',
    marginRight: 'auto'
  });
  
  // 应用标题样式
  const title = container.querySelector('.preview-title');
  if (title) {
    Object.assign(title.style, {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: '12px',
      color: '#f1f5f9',
      marginBottom: '24px',
      fontSize: '1.5rem',
      fontWeight: '700',
      textAlign: 'center',
      position: 'relative',
      zIndex: '1'
    });
  }
  
  // 应用网格样式
  const grid = container.querySelector('.video-preview-grid');
  if (grid) {
    Object.assign(grid.style, {
      display: 'grid',
      gridTemplateColumns: '1fr 1fr',
      gap: '32px',
      position: 'relative',
      zIndex: '1',
      maxWidth: 'none',
      width: '100%'
    });
  }
  
  // 应用视频容器样式
  const videoContainers = container.querySelectorAll('.video-preview-container');
  videoContainers.forEach(videoContainer => {
    Object.assign(videoContainer.style, {
      background: 'linear-gradient(145deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9))',
      borderRadius: '12px',
      padding: '24px',
      border: '1px solid rgba(148, 163, 184, 0.15)',
      boxShadow: '0 8px 20px rgba(0, 0, 0, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.05)',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      position: 'relative',
      overflow: 'hidden',
      minHeight: '400px',
      flex: '1'
    });
    
    // 添加悬停效果
    videoContainer.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-2px)';
      this.style.boxShadow = '0 12px 30px rgba(0, 0, 0, 0.35), inset 0 1px 0 rgba(255, 255, 255, 0.1)';
      this.style.borderColor = 'rgba(148, 163, 184, 0.3)';
    });
    
    videoContainer.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.05)';
      this.style.borderColor = 'rgba(148, 163, 184, 0.15)';
    });
  });
  
  // 应用视频头部样式
  const videoHeaders = container.querySelectorAll('.video-header');
  videoHeaders.forEach(header => {
    Object.assign(header.style, {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '16px',
      paddingBottom: '12px',
      borderBottom: '1px solid rgba(148, 163, 184, 0.1)'
    });
    
    const h4 = header.querySelector('h4');
    if (h4) {
      Object.assign(h4.style, {
        color: '#f1f5f9',
        margin: '0',
        fontSize: '1.1rem',
        fontWeight: '600',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      });
    }
  });
  
  // 应用预览区域样式
  const previewAreas = container.querySelectorAll('.video-preview-area');
  previewAreas.forEach(area => {
    Object.assign(area.style, {
      minHeight: '320px',
      height: '320px',
      background: 'linear-gradient(135deg, #0f172a, #1e293b)',
      borderRadius: '8px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: '#94a3b8',
      fontSize: '0.9rem',
      border: '2px dashed rgba(148, 163, 184, 0.2)',
      transition: 'all 0.3s ease',
      position: 'relative',
      overflow: 'hidden',
      flexDirection: 'column'
    });
    
    // 添加悬停效果
    area.addEventListener('mouseenter', function() {
      this.style.borderColor = 'rgba(148, 163, 184, 0.4)';
      this.style.background = 'linear-gradient(135deg, #1e293b, #334155)';
    });
    
    area.addEventListener('mouseleave', function() {
      this.style.borderColor = 'rgba(148, 163, 184, 0.2)';
      this.style.background = 'linear-gradient(135deg, #0f172a, #1e293b)';
    });
  });
  
  // 应用统计信息样式
  const statsElements = container.querySelectorAll('.video-stats');
  statsElements.forEach(stats => {
    Object.assign(stats.style, {
      marginTop: '12px',
      padding: '12px',
      background: 'rgba(15, 23, 42, 0.6)',
      borderRadius: '6px',
      fontSize: '0.8rem',
      color: '#94a3b8',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      border: '1px solid rgba(148, 163, 184, 0.1)'
    });
    
    const statItems = stats.querySelectorAll('.stat-item');
    statItems.forEach(item => {
      Object.assign(item.style, {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '2px'
      });
      
      const value = item.querySelector('.stat-value');
      const label = item.querySelector('.stat-label');
      
      if (value) {
        Object.assign(value.style, {
          color: '#f1f5f9',
          fontWeight: '600',
          fontSize: '0.9rem'
        });
      }
      
      if (label) {
        Object.assign(label.style, {
          color: '#64748b',
          fontSize: '0.7rem'
        });
      }
    });
  });
  
  smartLog('INFO', '✅ 增强样式应用完成');
}

function startStyleMonitoring() {
  smartLog('INFO', '👀 开始样式监控...');

  // 状态跟踪
  let lastStyleCheck = 0;
  let styleCheckCount = 0;

  // 监控DOM变化，确保新添加的元素也能应用样式
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // 检查是否是预览相关元素
            if (node.classList && (
              node.classList.contains('realtime-preview-section') ||
              node.classList.contains('video-preview-container') ||
              node.classList.contains('detection-frame-container')
            )) {
              smartLog('INFO', '🔄 检测到新的预览元素，应用样式');
              setTimeout(() => applyEnhancedStyles(node.closest('.realtime-preview-section') || node), 100);
            }
          }
        });
      }
    });
  });

  // 开始观察
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // 定期检查和修复样式（减少频率和日志）
  setInterval(() => {
    const previewSection = document.querySelector('.realtime-preview-section');
    if (previewSection) {
      // 检查关键样式是否丢失
      const computedStyle = window.getComputedStyle(previewSection);
      if (!computedStyle.background.includes('linear-gradient')) {
        smartLog('WARN', '🔧 检测到样式丢失，重新应用');
        applyEnhancedStyles(previewSection);
      } else {
        // 只在前几次检查时显示正常状态
        styleCheckCount++;
        if (styleCheckCount <= 3) {
          smartLog('DEBUG', '✅ 样式状态正常');
        }
      }
    }
    lastStyleCheck = Date.now();
  }, 10000); // 增加检查间隔到10秒
}

// 导出函数供其他脚本使用
window.enhancePreviewStyles = enhancePreviewStyles;
window.applyEnhancedStyles = applyEnhancedStyles;
