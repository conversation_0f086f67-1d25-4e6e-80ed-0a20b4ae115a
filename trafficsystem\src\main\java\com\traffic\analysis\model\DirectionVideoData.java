package com.traffic.analysis.model;

import lombok.Data;
import java.util.Map;
import java.util.List;
import java.util.HashMap;
import java.util.ArrayList;
import java.time.LocalDateTime;

/**
 * 单个方向的视频数据和分析结果
 */
@Data
public class DirectionVideoData {
    
    /**
     * 视频文件路径（GridFS文件ID）
     */
    private String videoPath;
    
    /**
     * 原始文件名
     */
    private String filename;
    
    /**
     * 视频时长（秒）
     */
    private double duration;
    
    /**
     * 检测到的车辆总数
     */
    private int vehicleCount;
    
    /**
     * 车辆类型统计
     */
    private Map<String, Integer> vehicleTypes = new HashMap<>();
    
    /**
     * 拥挤等级
     */
    private String crowdLevel;
    
    /**
     * 检测结果详情
     */
    private List<DetectionFrame> detectionResults = new ArrayList<>();
    
    /**
     * 处理状态
     */
    private String status; // processing, completed, failed
    
    /**
     * 处理进度 (0-100)
     */
    private int progress;
    
    /**
     * 状态消息
     */
    private String message;

    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 处理开始时间
     */
    private LocalDateTime processingStartTime;
    
    /**
     * 处理完成时间
     */
    private LocalDateTime processingEndTime;

    /**
     * 最后一帧时间
     */
    private LocalDateTime lastFrameTime;
    
    /**
     * 平均车流密度（车辆/分钟）
     */
    private double averageFlowDensity;
    
    /**
     * 峰值车流时间点
     */
    private LocalDateTime peakFlowTime;
    
    /**
     * 峰值车流量
     */
    private int peakFlowCount;
    
    /**
     * 检测帧数据内部类
     */
    @Data
    public static class DetectionFrame {
        /**
         * 帧时间戳（相对于视频开始的秒数）
         */
        private double timestamp;
        
        /**
         * 该帧检测到的车辆数量
         */
        private int vehicleCount;
        
        /**
         * 检测到的对象列表
         */
        private List<DetectedObject> detectedObjects = new ArrayList<>();
        
        /**
         * 帧图像的Base64编码（可选，用于实时预览）
         */
        private String frameImageBase64;
        
        /**
         * 推理时间（毫秒）
         */
        private double inferenceTime;
    }
    
    /**
     * 检测对象内部类
     */
    @Data
    public static class DetectedObject {
        /**
         * 对象类别ID
         */
        private int classId;
        
        /**
         * 对象类别名称
         */
        private String className;
        
        /**
         * 置信度
         */
        private double confidence;
        
        /**
         * 边界框坐标 [x1, y1, x2, y2]
         */
        private List<Double> bbox = new ArrayList<>();
        
        /**
         * 对象中心点坐标
         */
        private Point centerPoint;
        
        /**
         * 对象面积
         */
        private double area;
    }
    
    /**
     * 点坐标内部类
     */
    @Data
    public static class Point {
        private double x;
        private double y;
        
        public Point(double x, double y) {
            this.x = x;
            this.y = y;
        }
    }
    
    /**
     * 计算平均车流密度
     */
    public void calculateAverageFlowDensity() {
        if (duration <= 0 || detectionResults.isEmpty()) {
            this.averageFlowDensity = 0.0;
            return;
        }
        
        double totalVehicles = detectionResults.stream()
                .mapToInt(DetectionFrame::getVehicleCount)
                .sum();
        
        // 车辆/分钟
        this.averageFlowDensity = (totalVehicles / duration) * 60;
    }
    
    /**
     * 找出峰值车流时间点
     */
    public void findPeakFlowTime() {
        if (detectionResults.isEmpty()) {
            return;
        }
        
        DetectionFrame peakFrame = detectionResults.stream()
                .max((f1, f2) -> Integer.compare(f1.getVehicleCount(), f2.getVehicleCount()))
                .orElse(null);
        
        if (peakFrame != null) {
            this.peakFlowCount = peakFrame.getVehicleCount();
            // 这里简化处理，实际应该根据视频开始时间计算
            this.peakFlowTime = processingStartTime != null ? 
                processingStartTime.plusSeconds((long) peakFrame.getTimestamp()) : 
                LocalDateTime.now();
        }
    }
    
    /**
     * 更新车辆类型统计
     */
    public void updateVehicleTypeStats() {
        vehicleTypes.clear();
        
        for (DetectionFrame frame : detectionResults) {
            for (DetectedObject obj : frame.getDetectedObjects()) {
                String className = obj.getClassName();
                vehicleTypes.put(className, vehicleTypes.getOrDefault(className, 0) + 1);
            }
        }
    }
}
