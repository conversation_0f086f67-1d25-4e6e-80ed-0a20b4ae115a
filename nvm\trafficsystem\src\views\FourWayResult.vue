<template>
  <div class="four-way-result-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/four-way-analysis' }">四方向分析</el-breadcrumb-item>
        <el-breadcrumb-item>分析结果</el-breadcrumb-item>
      </el-breadcrumb>
      
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><DataAnalysis /></el-icon>
          四方向分析结果
        </h1>
        <div class="header-actions">
          <el-button-group>
            <el-button 
              :type="currentMode === 'preview' ? 'primary' : ''"
              @click="switchMode('preview')"
            >
              <el-icon><Monitor /></el-icon>
              实时预览
            </el-button>
            <el-button 
              :type="currentMode === 'dashboard' ? 'primary' : ''"
              @click="switchMode('dashboard')"
            >
              <el-icon><DataBoard /></el-icon>
              分析仪表板
            </el-button>
            <el-button 
              :type="currentMode === 'report' ? 'primary' : ''"
              @click="switchMode('report')"
            >
              <el-icon><Document /></el-icon>
              分析报告
            </el-button>
          </el-button-group>
          
          <el-button @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 任务状态信息 -->
    <div class="task-status-section" v-if="taskInfo">
      <el-card>
        <div class="status-content">
          <div class="status-item">
            <span class="status-label">任务ID:</span>
            <span class="status-value">{{ taskId }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">状态:</span>
            <el-tag :type="getStatusTagType(taskInfo.status)" size="small">
              {{ getStatusText(taskInfo.status) }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="status-label">进度:</span>
            <el-progress 
              :percentage="taskInfo.progress || 0" 
              :status="taskInfo.progress === 100 ? 'success' : ''"
              :stroke-width="6"
              class="status-progress"
            />
          </div>
          <div class="status-item" v-if="taskInfo.processingDuration">
            <span class="status-label">处理时长:</span>
            <span class="status-value">{{ taskInfo.processingDuration }} 秒</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 内容区域 -->
    <div class="content-section">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="8" animated />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <el-result
          icon="error"
          title="加载失败"
          :sub-title="error"
        >
          <template #extra>
            <el-button type="primary" @click="refreshData">
              重新加载
            </el-button>
          </template>
        </el-result>
      </div>

      <!-- 任务未完成状态 -->
      <div v-else-if="taskInfo && taskInfo.status !== 'completed'" class="processing-container">
        <el-result
          icon="info"
          title="分析进行中"
          sub-title="四方向交通分析正在进行中，请稍候..."
        >
          <template #extra>
            <div class="processing-actions">
              <el-button type="primary" @click="viewRealTimePreview">
                <el-icon><Monitor /></el-icon>
                查看实时预览
              </el-button>
              <el-button @click="refreshData">
                <el-icon><Refresh /></el-icon>
                刷新状态
              </el-button>
            </div>
          </template>
        </el-result>
        
        <!-- 进度详情 -->
        <el-card class="progress-details">
          <template #header>
            <span>处理进度</span>
          </template>
          <div class="progress-info">
            <div class="overall-progress">
              <span>整体进度:</span>
              <el-progress 
                :percentage="taskInfo.progress || 0"
                :stroke-width="12"
                text-inside
              />
            </div>
            <div class="directions-progress" v-if="taskInfo.directions">
              <div 
                v-for="(directionData, direction) in taskInfo.directions" 
                :key="direction"
                class="direction-progress"
              >
                <span class="direction-name">{{ getDirectionName(direction) }}:</span>
                <el-progress 
                  :percentage="directionData.progress || 0"
                  :status="getProgressStatus(directionData.status)"
                  :stroke-width="8"
                />
                <span class="direction-status">{{ getStatusText(directionData.status) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 分析完成 - 显示对应模式的内容 -->
      <div v-else class="result-content">
        <!-- 实时预览模式 -->
        <FourWayRealTimePreview
          v-if="currentMode === 'preview'"
          :task-id="taskId"
          :auto-start="false"
          @status-changed="onPreviewStatusChanged"
          @error="onPreviewError"
        />

        <!-- 分析仪表板模式 -->
        <TrafficAnalysisDashboard
          v-else-if="currentMode === 'dashboard'"
          :task-id="taskId"
          :auto-refresh="true"
          :refresh-interval="30000"
          @data-updated="onDashboardDataUpdated"
          @optimization-applied="onOptimizationApplied"
          @error="onDashboardError"
        />

        <!-- 分析报告模式 -->
        <div v-else-if="currentMode === 'report'" class="report-container">
          <div class="report-actions">
            <el-button type="primary" @click="generateReport" :loading="generatingReport">
              <el-icon><Document /></el-icon>
              生成报告
            </el-button>
            <el-button @click="exportReport" :disabled="!reportGenerated">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
          
          <div v-if="reportGenerated" class="report-iframe-container">
            <iframe 
              :src="reportUrl" 
              class="report-iframe"
              frameborder="0"
            ></iframe>
          </div>
          
          <div v-else class="report-placeholder">
            <el-empty description="点击生成报告按钮查看详细分析报告" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  DataAnalysis, Monitor, DataBoard, Document, 
  Refresh, Download 
} from '@element-plus/icons-vue'
import FourWayRealTimePreview from '@/components/analysis/FourWayRealTimePreview.vue'
import TrafficAnalysisDashboard from '@/components/analysis/TrafficAnalysisDashboard.vue'
import { getFourWayTaskStatus, getFourWayAnalysisResult, generateFourWayTrafficReport } from '@/api/video'

export default {
  name: 'FourWayResult',
  components: {
    DataAnalysis, Monitor, DataBoard, Document, Refresh, Download,
    FourWayRealTimePreview,
    TrafficAnalysisDashboard
  },
  props: {
    taskId: {
      type: String,
      required: true
    },
    mode: {
      type: String,
      default: 'dashboard',
      validator: (value) => ['preview', 'dashboard', 'report'].includes(value)
    }
  },
  setup(props) {
    const route = useRoute()
    const router = useRouter()
    
    // 响应式数据
    const loading = ref(false)
    const error = ref('')
    const taskInfo = ref(null)
    const currentMode = ref(props.mode)
    const generatingReport = ref(false)
    const reportGenerated = ref(false)
    const reportUrl = ref('')
    const statusCheckTimer = ref(null)
    
    // 计算属性
    const isTaskCompleted = computed(() => {
      return taskInfo.value && taskInfo.value.status === 'completed'
    })
    
    // 方法
    const loadTaskInfo = async () => {
      if (!props.taskId) return
      
      loading.value = true
      error.value = ''
      
      try {
        const response = await getFourWayTaskStatus(props.taskId)
        taskInfo.value = response.data
        
        // 如果任务未完成，开始轮询
        if (taskInfo.value.status === 'processing' || taskInfo.value.status === 'queued') {
          startStatusPolling()
        }
        
      } catch (err) {
        console.error('加载任务信息失败:', err)
        error.value = err.message || '加载任务信息失败'
      } finally {
        loading.value = false
      }
    }
    
    const startStatusPolling = () => {
      if (statusCheckTimer.value) {
        clearInterval(statusCheckTimer.value)
      }
      
      statusCheckTimer.value = setInterval(async () => {
        try {
          const response = await getFourWayTaskStatus(props.taskId)
          taskInfo.value = response.data
          
          // 如果任务完成，停止轮询
          if (response.data.status === 'completed' || response.data.status === 'failed') {
            clearInterval(statusCheckTimer.value)
            statusCheckTimer.value = null
            
            if (response.data.status === 'completed') {
              ElMessage.success('四方向交通分析完成')
            } else {
              ElMessage.error('四方向交通分析失败')
            }
          }
        } catch (err) {
          console.error('轮询任务状态失败:', err)
        }
      }, 5000) // 每5秒检查一次
    }
    
    const stopStatusPolling = () => {
      if (statusCheckTimer.value) {
        clearInterval(statusCheckTimer.value)
        statusCheckTimer.value = null
      }
    }
    
    const refreshData = () => {
      loadTaskInfo()
    }
    
    const switchMode = (mode) => {
      currentMode.value = mode
      
      // 更新URL但不刷新页面
      const newPath = `/four-way-${mode}/${props.taskId}`
      router.replace(newPath)
    }
    
    const viewRealTimePreview = () => {
      switchMode('preview')
    }
    
    const generateReport = async () => {
      if (!props.taskId) return
      
      generatingReport.value = true
      
      try {
        const response = await generateFourWayTrafficReport(props.taskId)
        
        if (response.data) {
          // 创建报告URL
          reportUrl.value = `/api/video-report/four-way/${props.taskId}/report`
          reportGenerated.value = true
          ElMessage.success('报告生成成功')
        }
        
      } catch (err) {
        console.error('生成报告失败:', err)
        ElMessage.error('生成报告失败: ' + (err.message || '未知错误'))
      } finally {
        generatingReport.value = false
      }
    }
    
    const exportReport = async () => {
      if (!props.taskId) return
      
      try {
        const response = await generateFourWayTrafficReport(props.taskId)
        const reportData = response.data
        
        if (reportData) {
          // 创建并下载JSON报告文件
          const reportContent = JSON.stringify(reportData, null, 2)
          const blob = new Blob([reportContent], { type: 'application/json' })
          const url = URL.createObjectURL(blob)
          
          const link = document.createElement('a')
          link.href = url
          link.download = `四方向交通分析报告_${props.taskId}_${new Date().toISOString().slice(0, 10)}.json`
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          URL.revokeObjectURL(url)
          
          ElMessage.success('报告导出成功')
        }
        
      } catch (err) {
        console.error('导出报告失败:', err)
        ElMessage.error('导出报告失败: ' + (err.message || '未知错误'))
      }
    }
    
    const getDirectionName = (direction) => {
      const names = {
        east: '东向',
        south: '南向',
        west: '西向',
        north: '北向'
      }
      return names[direction] || direction
    }
    
    const getStatusTagType = (status) => {
      const typeMap = {
        completed: 'success',
        processing: 'warning',
        failed: 'danger',
        queued: 'info'
      }
      return typeMap[status] || 'info'
    }
    
    const getStatusText = (status) => {
      const textMap = {
        completed: '已完成',
        processing: '处理中',
        failed: '失败',
        queued: '排队中'
      }
      return textMap[status] || '未知'
    }
    
    const getProgressStatus = (status) => {
      if (status === 'completed') return 'success'
      if (status === 'failed') return 'exception'
      return ''
    }
    
    // 事件处理
    const onPreviewStatusChanged = (data) => {
      console.log('预览状态变化:', data)
    }
    
    const onPreviewError = (error) => {
      console.error('预览错误:', error)
      ElMessage.error('预览出错: ' + (error.message || '未知错误'))
    }
    
    const onDashboardDataUpdated = (data) => {
      console.log('仪表板数据更新:', data)
    }
    
    const onOptimizationApplied = (data) => {
      console.log('优化方案已应用:', data)
      ElMessage.success('优化方案已应用')
    }
    
    const onDashboardError = (error) => {
      console.error('仪表板错误:', error)
      ElMessage.error('仪表板出错: ' + (error.message || '未知错误'))
    }
    
    // 生命周期
    onMounted(() => {
      loadTaskInfo()
    })
    
    onUnmounted(() => {
      stopStatusPolling()
    })
    
    // 监听器
    watch(() => props.taskId, (newTaskId) => {
      if (newTaskId) {
        stopStatusPolling()
        loadTaskInfo()
      }
    })
    
    return {
      loading,
      error,
      taskInfo,
      currentMode,
      generatingReport,
      reportGenerated,
      reportUrl,
      isTaskCompleted,
      loadTaskInfo,
      refreshData,
      switchMode,
      viewRealTimePreview,
      generateReport,
      exportReport,
      getDirectionName,
      getStatusTagType,
      getStatusText,
      getProgressStatus,
      onPreviewStatusChanged,
      onPreviewError,
      onDashboardDataUpdated,
      onOptimizationApplied,
      onDashboardError
    }
  }
}
</script>

<style scoped>
.four-way-result-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 24px;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.task-status-section {
  margin-bottom: 20px;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-weight: 500;
  color: #606266;
}

.status-value {
  color: #2c3e50;
}

.status-progress {
  min-width: 120px;
}

.content-section {
  min-height: 600px;
}

.loading-container,
.error-container,
.processing-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

.processing-actions {
  display: flex;
  gap: 12px;
}

.progress-details {
  margin-top: 20px;
  max-width: 600px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.overall-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.directions-progress {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.direction-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.direction-name {
  min-width: 60px;
  font-weight: 500;
  color: #606266;
}

.direction-status {
  min-width: 60px;
  font-size: 12px;
  color: #909399;
}

.result-content {
  min-height: 600px;
}

.report-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.report-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.report-iframe-container {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  height: 800px;
}

.report-iframe {
  width: 100%;
  height: 100%;
}

.report-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .four-way-result-page {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .status-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .status-item {
    width: 100%;
    justify-content: space-between;
  }
  
  .processing-actions {
    flex-direction: column;
    width: 100%;
    max-width: 300px;
  }
  
  .direction-progress {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .report-actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
