# 四方向智能交通分析系统

## 🚀 项目概述

四方向智能交通分析系统是一个基于AI视觉识别技术的交通流量分析平台，专门用于分析十字路口四个方向的交通状况。系统能够智能识别车辆、分析交通流量、提供信号优化建议，并生成专业的交通分析报告。

## ✨ 主要功能

### 🎯 核心功能
- 🚗 **四方向视频上传**: 支持东、南、西、北四个方向视频同时上传，最大支持500MB单文件
- 📊 **智能车辆识别**: 基于YOLOv12x模型，精确识别各类车辆（轿车、卡车、公交车、摩托车等）
- 🎥 **实时四画面预览**: WebSocket实时推送检测结果，四画面同步显示，支持帧冻结模式
- 📈 **智能交通分析**: AI算法计算流量平衡度、拥堵等级、信号优化建议和流向分析
- 📋 **可视化仪表板**: 丰富的图表展示，包括热力图、对比图、优化面板和实时统计
- 📄 **专业报告生成**: 自动生成PDF、Excel、HTML格式的智能交通分析报告

### 🔧 系统功能
- 🔐 **用户管理**: 完整的用户认证、权限管理和角色控制
- 📊 **任务管理**: 分析任务的创建、监控、重试、删除和批量操作
- 🔄 **实时监控**: 系统状态监控、性能指标和错误追踪
- 💾 **数据管理**: 历史记录查询、数据导出和备份恢复
- 🛡️ **安全保障**: 数据验证、文件格式检查和访问控制
- 📱 **响应式设计**: 支持桌面端、平板和移动端的完美适配

## 🏗️ 技术架构

### 后端技术栈
- **Spring Boot 3.x**: 主框架，提供RESTful API和WebSocket支持
- **MongoDB**: 数据存储，支持分片和副本集
- **WebSocket**: 实时通信，支持STOMP协议
- **GridFS**: 大文件存储，支持视频文件的分块存储
- **Maven**: 项目管理和依赖管理
- **Spring Security**: 安全认证和授权
- **Spring Cache**: 缓存管理，提升系统性能
- **Spring Validation**: 数据验证和参数校验

### 前端技术栈
- **Vue 3**: 前端框架，采用Composition API
- **Element Plus**: UI组件库，提供丰富的组件
- **ECharts**: 数据可视化，支持多种图表类型
- **STOMP**: WebSocket通信协议
- **Axios**: HTTP客户端，支持请求拦截和响应处理
- **Vue Router**: 路由管理
- **Pinia**: 状态管理
- **Vite**: 构建工具，快速开发和热更新

### AI服务技术栈
- **Python 3.8+**: 主语言
- **YOLOv12x**: 目标检测模型，高精度车辆识别
- **OpenCV**: 图像处理和视频分析
- **Flask**: Web服务框架
- **NumPy**: 数值计算
- **Pillow**: 图像处理库
- **Ultralytics**: YOLO模型框架

### 数据库设计
- **用户管理**: users集合，支持角色和权限
- **任务管理**: intersection_analysis_four_way集合
- **分析结果**: traffic_analysis_results集合
- **报告数据**: report_data集合
- **信号优化**: signal_optimizations集合
- **系统配置**: 支持动态配置和缓存

## 📋 环境要求

### 最低配置
- **Java**: OpenJDK 11 或更高版本
- **Node.js**: v16.x 或更高版本
- **Python**: 3.8 或更高版本
- **MongoDB**: 4.4 或更高版本
- **内存**: 8GB RAM
- **存储**: 100GB 可用空间

### 推荐配置
- **Java**: OpenJDK 17
- **内存**: 16GB RAM 或更多
- **存储**: 500GB SSD
- **GPU**: NVIDIA GTX 1060 或更高（可选，用于AI加速）

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd traffic-analysis-system
```

### 2. 后端启动

#### 方式一：使用启动脚本（Windows）

```bash
cd trafficsystem
start-test.bat
```

#### 方式二：手动启动

```bash
cd trafficsystem

# 编译项目
mvn clean compile

# 运行测试（可选）
mvn test

# 打包项目
mvn package -DskipTests

# 启动应用
java -jar target/traffic-system-1.0.0.jar
```

#### 方式三：开发模式

```bash
cd trafficsystem
mvn spring-boot:run
```

### 3. 前端启动

```bash
cd nvm/trafficsystem

# 安装依赖
npm install

# 开发模式启动
npm run serve

# 或构建生产版本
npm run build
```

### 4. Python AI服务启动

```bash
cd python-service

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 启动AI服务
python model_api.py
```

### 5. MongoDB启动

```bash
# 启动MongoDB服务
mongod --dbpath /path/to/data/directory

# 或使用系统服务
sudo systemctl start mongod  # Linux
net start MongoDB           # Windows
```

## 🌐 访问地址

- **前端应用**: http://localhost:8080 (生产模式) 或 http://localhost:8081 (开发模式)
- **后端API**: http://localhost:8080/api
- **AI服务**: http://localhost:5000
- **MongoDB**: mongodb://localhost:27017

## 📖 使用指南

### 1. 上传四方向视频

1. 访问系统主页
2. 点击"四方向智能交通分析"
3. 选择"开始新分析"
4. 按照界面提示上传东、南、西、北四个方向的视频文件
5. 点击"开始分析"

### 2. 查看实时预览

- 上传完成后，系统会自动开始处理视频
- 可以选择查看四画面实时预览
- 实时显示每个方向的车辆检测结果

### 3. 查看分析结果

- 处理完成后，查看智能分析仪表板
- 包含车流量统计、可视化图表、优化建议等

### 4. 生成分析报告

- 点击"生成报告"按钮
- 查看或导出专业的交通分析报告

## 📁 项目结构

```
traffic-analysis-system/
├── trafficsystem/              # 后端Spring Boot项目
│   ├── src/main/java/         # Java源代码
│   ├── src/main/resources/    # 配置文件
│   ├── src/test/java/         # 测试代码
│   └── pom.xml               # Maven配置
├── nvm/trafficsystem/         # 前端Vue项目
│   ├── src/                  # Vue源代码
│   ├── public/               # 静态资源
│   └── package.json          # npm配置
├── python-service/           # Python AI服务
│   ├── model_api.py          # AI服务主文件
│   ├── requirements.txt      # Python依赖
│   └── models/               # AI模型文件
└── docs/                     # 项目文档
    ├── api/                  # API文档
    ├── user-guide/           # 用户指南
    └── deployment/           # 部署文档
```

## 🔧 配置说明

### 后端配置

主要配置文件：`trafficsystem/src/main/resources/application.yml`

```yaml
server:
  port: 8080

spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/traffic_analysis

# AI服务配置
ai:
  service:
    url: http://localhost:5000
```

### 前端配置

环境配置文件：`nvm/trafficsystem/.env`

```bash
VUE_APP_API_BASE_URL=http://localhost:8080
VUE_APP_WS_URL=ws://localhost:8080/ws
```

## 🐛 故障排除

### 常见问题

1. **编译错误**: 确保Java版本为11或更高
2. **端口冲突**: 检查8080、5000、27017端口是否被占用
3. **MongoDB连接失败**: 确认MongoDB服务已启动
4. **前端页面空白**: 检查API服务是否正常运行

### 日志查看

- **后端日志**: `trafficsystem/logs/`
- **前端控制台**: 浏览器开发者工具
- **AI服务日志**: `python-service/logs/`

## 📚 文档

### 📖 用户文档
- [四方向实时检测使用说明](docs/四方向实时检测使用说明.md) - 详细的功能使用指南
- [用户指南](docs/user-guide/four-way-analysis-guide.md) - 完整的用户操作手册

### 🔧 技术文档
- [API文档](docs/API文档.md) - 完整的RESTful API接口文档
- [系统部署指南](docs/系统部署指南.md) - 详细的部署和配置说明
- [数据库设计](docs/database-design.md) - 数据库结构和索引设计

### 🚀 开发文档
- [开发环境搭建](docs/development-setup.md) - 开发环境配置指南
- [代码规范](docs/coding-standards.md) - 项目代码规范和最佳实践
- [测试指南](docs/testing-guide.md) - 单元测试和集成测试说明

### 📋 项目文档
- [项目总结](docs/PROJECT_SUMMARY.md) - 项目概述和技术总结
- [更新日志](docs/CHANGELOG.md) - 版本更新记录
- [常见问题](docs/FAQ.md) - 常见问题解答

## 📈 版本信息

**当前版本**: v2.0.0

### 🆕 最新更新 (v2.0.0)
- ✅ 完善的四方向交通分析功能
- ✅ 新增智能分析建议和信号优化
- ✅ 完整的API接口和文档
- ✅ 统一的异常处理机制
- ✅ 前后端数据验证
- ✅ 性能优化和缓存机制
- ✅ 端到端测试覆盖
- ✅ 完整的部署指南

### 🔄 版本历史
- **v1.0.0**: 基础四方向视频上传和分析功能
- **v1.5.0**: 添加实时预览和WebSocket支持
- **v2.0.0**: 完整的智能交通分析系统

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

### 贡献指南
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用MIT许可证。详见 [LICENSE](LICENSE) 文件。

## 📞 技术支持

如有问题，请通过以下方式联系：

- 📧 邮箱: <EMAIL>
- 📱 技术支持: +86-xxx-xxxx-xxxx
- 💬 在线客服: 工作日 9:00-18:00
- 📋 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)

### 🔗 相关链接
- [官方网站](https://traffic-analysis.com)
- [在线演示](https://demo.traffic-analysis.com)
- [技术博客](https://blog.traffic-analysis.com)
- [用户社区](https://community.traffic-analysis.com)

---

*四方向智能交通分析系统 - 让交通管理更智能* 🚦✨
