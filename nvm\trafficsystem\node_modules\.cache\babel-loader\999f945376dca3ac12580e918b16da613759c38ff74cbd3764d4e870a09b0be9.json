{"ast": null, "code": "import { ref, computed, watch } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport { VideoCamera, VideoPlay, VideoPause, Download } from '@element-plus/icons-vue';\nexport default {\n  name: 'RealTimeFrameViewer',\n  components: {\n    VideoCamera,\n    VideoPlay,\n    VideoPause,\n    Download\n  },\n  props: {\n    direction: {\n      type: String,\n      required: true,\n      validator: value => ['east', 'south', 'west', 'north'].includes(value)\n    },\n    frameData: {\n      type: Object,\n      default: null\n    },\n    status: {\n      type: String,\n      default: 'waiting',\n      validator: value => ['waiting', 'processing', 'completed', 'error'].includes(value)\n    },\n    progress: {\n      type: Number,\n      default: 0\n    },\n    showControls: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['pause-toggled', 'frame-saved'],\n  setup(props, {\n    emit\n  }) {\n    // 响应式数据\n    const currentFrame = ref(null);\n    const isPaused = ref(false);\n    const imageLoading = ref(false);\n\n    // 计算属性\n    const directionName = computed(() => {\n      const names = {\n        east: '东向',\n        south: '南向',\n        west: '西向',\n        north: '北向'\n      };\n      return names[props.direction] || props.direction;\n    });\n    const hasCurrentFrame = computed(() => {\n      return currentFrame.value && currentFrame.value.imageData;\n    });\n    const statusClass = computed(() => {\n      return {\n        'status-waiting': props.status === 'waiting',\n        'status-processing': props.status === 'processing',\n        'status-completed': props.status === 'completed',\n        'status-error': props.status === 'error'\n      };\n    });\n    const statusText = computed(() => {\n      const statusMap = {\n        waiting: '等待处理',\n        processing: '正在处理',\n        completed: '处理完成',\n        error: '处理失败'\n      };\n      return statusMap[props.status] || '未知状态';\n    });\n\n    // 监听帧数据变化\n    watch(() => props.frameData, newFrameData => {\n      if (newFrameData && !isPaused.value) {\n        updateFrame(newFrameData);\n      }\n    }, {\n      immediate: true\n    });\n\n    // 方法\n    const updateFrame = frameData => {\n      if (!frameData) return;\n      console.log(`🎬 ${props.direction}方向RealTimeFrameViewer更新帧数据:`, {\n        frameNumber: frameData.frameNumber,\n        detectionCount: frameData.detectionCount,\n        hasImageData: !!frameData.imageData,\n        imageDataLength: frameData.imageData ? frameData.imageData.length : 0,\n        imageDataPrefix: frameData.imageData ? frameData.imageData.substring(0, 50) + '...' : 'null'\n      });\n\n      // 验证帧数据\n      if (!frameData.imageData) {\n        console.warn(`❌ ${props.direction}方向无效的帧数据: 缺少imageData`, frameData);\n        return;\n      }\n\n      // 处理图像数据格式\n      let processedImageData = frameData.imageData;\n      if (!frameData.imageData.startsWith('data:image/')) {\n        processedImageData = `data:image/jpeg;base64,${frameData.imageData}`;\n        console.log(`🔧 ${props.direction}方向转换图像数据格式: Base64 -> Data URL`);\n      }\n      currentFrame.value = {\n        ...frameData,\n        imageData: processedImageData,\n        timestamp: new Date().toISOString()\n      };\n      console.log(`✅ ${props.direction}方向帧数据更新完成: 帧${frameData.frameNumber}`);\n    };\n    const getVehicleTypeName = type => {\n      const typeNames = {\n        car: '小汽车',\n        truck: '卡车',\n        bus: '公交车',\n        motorcycle: '摩托车'\n      };\n      return typeNames[type] || type;\n    };\n    const togglePause = () => {\n      isPaused.value = !isPaused.value;\n      emit('pause-toggled', {\n        direction: props.direction,\n        isPaused: isPaused.value\n      });\n    };\n    const saveFrame = () => {\n      if (!hasCurrentFrame.value) {\n        ElMessage.warning('没有可保存的帧数据');\n        return;\n      }\n      try {\n        // 创建下载链接\n        const link = document.createElement('a');\n        link.href = currentFrame.value.imageData;\n        link.download = `${props.direction}_frame_${currentFrame.value.frameNumber || 'unknown'}_${Date.now()}.jpg`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        ElMessage.success('帧图像已保存');\n        emit('frame-saved', {\n          direction: props.direction,\n          frameData: currentFrame.value\n        });\n      } catch (error) {\n        console.error('保存帧失败:', error);\n        ElMessage.error('保存帧失败');\n      }\n    };\n    const onImageLoad = () => {\n      imageLoading.value = false;\n    };\n    const onImageError = () => {\n      imageLoading.value = false;\n      console.error('帧图像加载失败');\n    };\n    return {\n      currentFrame,\n      isPaused,\n      imageLoading,\n      directionName,\n      hasCurrentFrame,\n      statusClass,\n      statusText,\n      getVehicleTypeName,\n      togglePause,\n      saveFrame,\n      onImageLoad,\n      onImageError\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "watch", "ElMessage", "VideoCamera", "VideoPlay", "VideoPause", "Download", "name", "components", "props", "direction", "type", "String", "required", "validator", "value", "includes", "frameData", "Object", "default", "status", "progress", "Number", "showControls", "Boolean", "emits", "setup", "emit", "currentFrame", "isPaused", "imageLoading", "directionName", "names", "east", "south", "west", "north", "hasCurrentFrame", "imageData", "statusClass", "statusText", "statusMap", "waiting", "processing", "completed", "error", "newFrameData", "updateFrame", "immediate", "console", "log", "frameNumber", "detectionCount", "hasImageData", "imageDataLength", "length", "imageDataPrefix", "substring", "warn", "processedImageData", "startsWith", "timestamp", "Date", "toISOString", "getVehicleTypeName", "typeNames", "car", "truck", "bus", "motorcycle", "toggle<PERSON><PERSON>e", "saveFrame", "warning", "link", "document", "createElement", "href", "download", "now", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "success", "onImageLoad", "onImageError"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\analysis\\RealTimeFrameViewer.vue"], "sourcesContent": ["<template>\n  <div class=\"real-time-frame-viewer\">\n    <!-- 视频显示区域 -->\n    <div class=\"video-container\" :class=\"{ 'has-frame': hasCurrentFrame }\">\n      <div v-if=\"!hasCurrentFrame\" class=\"no-frame-placeholder\">\n        <el-icon size=\"48\"><VideoCamera /></el-icon>\n        <p>等待视频帧数据...</p>\n        <el-progress \n          v-if=\"progress > 0\"\n          :percentage=\"progress\" \n          :stroke-width=\"6\"\n          :show-text=\"false\"\n        />\n      </div>\n      \n      <div v-else class=\"frame-display\">\n        <img \n          :src=\"currentFrame.imageData\" \n          :alt=\"`${directionName}方向检测结果`\"\n          class=\"frame-image\"\n          @load=\"onImageLoad\"\n          @error=\"onImageError\"\n        />\n        \n        <!-- 检测信息覆盖层 -->\n        <div class=\"detection-overlay\">\n          <div class=\"detection-info\">\n            <span class=\"vehicle-count\">{{ currentFrame.detectionCount || 0 }}</span>\n            <span class=\"vehicle-label\">辆车</span>\n          </div>\n          \n          <div class=\"frame-info\">\n            <span class=\"frame-number\">\n              {{ currentFrame.frameNumber || 0 }} / {{ currentFrame.totalFrames || 0 }}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 方向标识 -->\n    <div class=\"direction-header\">\n      <div class=\"direction-icon\" :class=\"direction\">\n        {{ directionName.substring(0, 1) }}\n      </div>\n      <div class=\"direction-info\">\n        <h4 class=\"direction-name\">{{ directionName }}</h4>\n        <p class=\"direction-status\" :class=\"statusClass\">{{ statusText }}</p>\n      </div>\n    </div>\n    \n    <!-- 统计信息 -->\n    <div class=\"stats-panel\" v-if=\"hasCurrentFrame && currentFrame.vehicleTypes\">\n      <h5>车辆类型统计</h5>\n      <div class=\"vehicle-types\">\n        <div \n          v-for=\"(count, type) in currentFrame.vehicleTypes\" \n          :key=\"type\"\n          class=\"vehicle-type-item\"\n        >\n          <span class=\"type-name\">{{ getVehicleTypeName(type) }}</span>\n          <span class=\"type-count\">{{ count }}</span>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 控制按钮 -->\n    <div class=\"controls\" v-if=\"showControls\">\n      <el-button \n        size=\"small\" \n        @click=\"togglePause\"\n        :icon=\"isPaused ? VideoPlay : VideoPause\"\n      >\n        {{ isPaused ? '播放' : '暂停' }}\n      </el-button>\n      \n      <el-button \n        size=\"small\" \n        @click=\"saveFrame\"\n        :disabled=\"!hasCurrentFrame\"\n        icon=\"Download\"\n      >\n        保存帧\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, watch } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport { \n  VideoCamera, VideoPlay, VideoPause, Download \n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'RealTimeFrameViewer',\n  components: {\n    VideoCamera, VideoPlay, VideoPause, Download\n  },\n  props: {\n    direction: {\n      type: String,\n      required: true,\n      validator: (value) => ['east', 'south', 'west', 'north'].includes(value)\n    },\n    frameData: {\n      type: Object,\n      default: null\n    },\n    status: {\n      type: String,\n      default: 'waiting',\n      validator: (value) => ['waiting', 'processing', 'completed', 'error'].includes(value)\n    },\n    progress: {\n      type: Number,\n      default: 0\n    },\n    showControls: {\n      type: Boolean,\n      default: true\n    }\n  },\n  emits: ['pause-toggled', 'frame-saved'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const currentFrame = ref(null)\n    const isPaused = ref(false)\n    const imageLoading = ref(false)\n    \n    // 计算属性\n    const directionName = computed(() => {\n      const names = {\n        east: '东向',\n        south: '南向',\n        west: '西向',\n        north: '北向'\n      }\n      return names[props.direction] || props.direction\n    })\n    \n    const hasCurrentFrame = computed(() => {\n      return currentFrame.value && currentFrame.value.imageData\n    })\n    \n    const statusClass = computed(() => {\n      return {\n        'status-waiting': props.status === 'waiting',\n        'status-processing': props.status === 'processing',\n        'status-completed': props.status === 'completed',\n        'status-error': props.status === 'error'\n      }\n    })\n    \n    const statusText = computed(() => {\n      const statusMap = {\n        waiting: '等待处理',\n        processing: '正在处理',\n        completed: '处理完成',\n        error: '处理失败'\n      }\n      return statusMap[props.status] || '未知状态'\n    })\n    \n    // 监听帧数据变化\n    watch(() => props.frameData, (newFrameData) => {\n      if (newFrameData && !isPaused.value) {\n        updateFrame(newFrameData)\n      }\n    }, { immediate: true })\n    \n    // 方法\n    const updateFrame = (frameData) => {\n      if (!frameData) return\n\n      console.log(`🎬 ${props.direction}方向RealTimeFrameViewer更新帧数据:`, {\n        frameNumber: frameData.frameNumber,\n        detectionCount: frameData.detectionCount,\n        hasImageData: !!frameData.imageData,\n        imageDataLength: frameData.imageData ? frameData.imageData.length : 0,\n        imageDataPrefix: frameData.imageData ? frameData.imageData.substring(0, 50) + '...' : 'null'\n      })\n\n      // 验证帧数据\n      if (!frameData.imageData) {\n        console.warn(`❌ ${props.direction}方向无效的帧数据: 缺少imageData`, frameData)\n        return\n      }\n\n      // 处理图像数据格式\n      let processedImageData = frameData.imageData\n      if (!frameData.imageData.startsWith('data:image/')) {\n        processedImageData = `data:image/jpeg;base64,${frameData.imageData}`\n        console.log(`🔧 ${props.direction}方向转换图像数据格式: Base64 -> Data URL`)\n      }\n\n      currentFrame.value = {\n        ...frameData,\n        imageData: processedImageData,\n        timestamp: new Date().toISOString()\n      }\n\n      console.log(`✅ ${props.direction}方向帧数据更新完成: 帧${frameData.frameNumber}`)\n    }\n    \n    const getVehicleTypeName = (type) => {\n      const typeNames = {\n        car: '小汽车',\n        truck: '卡车',\n        bus: '公交车',\n        motorcycle: '摩托车'\n      }\n      return typeNames[type] || type\n    }\n    \n    const togglePause = () => {\n      isPaused.value = !isPaused.value\n      emit('pause-toggled', {\n        direction: props.direction,\n        isPaused: isPaused.value\n      })\n    }\n    \n    const saveFrame = () => {\n      if (!hasCurrentFrame.value) {\n        ElMessage.warning('没有可保存的帧数据')\n        return\n      }\n      \n      try {\n        // 创建下载链接\n        const link = document.createElement('a')\n        link.href = currentFrame.value.imageData\n        link.download = `${props.direction}_frame_${currentFrame.value.frameNumber || 'unknown'}_${Date.now()}.jpg`\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        \n        ElMessage.success('帧图像已保存')\n        emit('frame-saved', {\n          direction: props.direction,\n          frameData: currentFrame.value\n        })\n        \n      } catch (error) {\n        console.error('保存帧失败:', error)\n        ElMessage.error('保存帧失败')\n      }\n    }\n    \n    const onImageLoad = () => {\n      imageLoading.value = false\n    }\n    \n    const onImageError = () => {\n      imageLoading.value = false\n      console.error('帧图像加载失败')\n    }\n    \n    return {\n      currentFrame,\n      isPaused,\n      imageLoading,\n      directionName,\n      hasCurrentFrame,\n      statusClass,\n      statusText,\n      getVehicleTypeName,\n      togglePause,\n      saveFrame,\n      onImageLoad,\n      onImageError\n    }\n  }\n}\n</script>\n\n<style scoped>\n.real-time-frame-viewer {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  overflow: hidden;\n  background: #fff;\n}\n\n.video-container {\n  flex: 1;\n  position: relative;\n  background: #f5f7fa;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 200px;\n}\n\n.no-frame-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #c0c4cc;\n  text-align: center;\n  padding: 20px;\n}\n\n.no-frame-placeholder p {\n  margin: 12px 0 16px 0;\n  font-size: 14px;\n}\n\n.frame-display {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.frame-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 4px;\n}\n\n.detection-overlay {\n  position: absolute;\n  top: 8px;\n  left: 8px;\n  right: 8px;\n  display: flex;\n  justify-content: space-between;\n  pointer-events: none;\n}\n\n.detection-info {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 6px 12px;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.vehicle-count {\n  font-size: 18px;\n  font-weight: bold;\n}\n\n.vehicle-label {\n  font-size: 12px;\n}\n\n.frame-info {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 6px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.direction-header {\n  display: flex;\n  align-items: center;\n  padding: 12px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #fafafa;\n}\n\n.direction-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n  font-size: 14px;\n  margin-right: 12px;\n}\n\n.direction-icon.east {\n  background: #409eff;\n}\n\n.direction-icon.south {\n  background: #67c23a;\n}\n\n.direction-icon.west {\n  background: #e6a23c;\n}\n\n.direction-icon.north {\n  background: #f56c6c;\n}\n\n.direction-info {\n  flex: 1;\n}\n\n.direction-name {\n  margin: 0 0 4px 0;\n  font-size: 16px;\n  color: #2c3e50;\n}\n\n.direction-status {\n  margin: 0;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.status-waiting {\n  color: #909399;\n}\n\n.status-processing {\n  color: #e6a23c;\n}\n\n.status-completed {\n  color: #67c23a;\n}\n\n.status-error {\n  color: #f56c6c;\n}\n\n.stats-panel {\n  padding: 12px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #fafafa;\n}\n\n.stats-panel h5 {\n  margin: 0 0 8px 0;\n  font-size: 14px;\n  color: #606266;\n}\n\n.vehicle-types {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.vehicle-type-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 12px;\n}\n\n.type-name {\n  color: #606266;\n}\n\n.type-count {\n  color: #2c3e50;\n  font-weight: 500;\n}\n\n.controls {\n  display: flex;\n  gap: 8px;\n  padding: 12px;\n  background: #fafafa;\n}\n\n.controls .el-button {\n  flex: 1;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .detection-overlay {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .direction-header {\n    padding: 8px;\n  }\n  \n  .direction-icon {\n    width: 28px;\n    height: 28px;\n    font-size: 12px;\n  }\n  \n  .direction-name {\n    font-size: 14px;\n  }\n}\n</style>\n"], "mappings": "AAyFA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,KAAI,QAAS,KAAI;AACzC,SAASC,SAAQ,QAAS,cAAa;AACvC,SACEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAO,QACtC,yBAAwB;AAE/B,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAC3BC,UAAU,EAAE;IACVL,WAAW;IAAEC,SAAS;IAAEC,UAAU;IAAEC;EACtC,CAAC;EACDG,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAGC,KAAK,IAAK,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACzE,CAAC;IACDE,SAAS,EAAE;MACTN,IAAI,EAAEO,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,MAAM,EAAE;MACNT,IAAI,EAAEC,MAAM;MACZO,OAAO,EAAE,SAAS;MAClBL,SAAS,EAAGC,KAAK,IAAK,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACD,KAAK;IACtF,CAAC;IACDM,QAAQ,EAAE;MACRV,IAAI,EAAEW,MAAM;MACZH,OAAO,EAAE;IACX,CAAC;IACDI,YAAY,EAAE;MACZZ,IAAI,EAAEa,OAAO;MACbL,OAAO,EAAE;IACX;EACF,CAAC;EACDM,KAAK,EAAE,CAAC,eAAe,EAAE,aAAa,CAAC;EACvCC,KAAKA,CAACjB,KAAK,EAAE;IAAEkB;EAAK,CAAC,EAAE;IACrB;IACA,MAAMC,YAAW,GAAI7B,GAAG,CAAC,IAAI;IAC7B,MAAM8B,QAAO,GAAI9B,GAAG,CAAC,KAAK;IAC1B,MAAM+B,YAAW,GAAI/B,GAAG,CAAC,KAAK;;IAE9B;IACA,MAAMgC,aAAY,GAAI/B,QAAQ,CAAC,MAAM;MACnC,MAAMgC,KAAI,GAAI;QACZC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE;MACT;MACA,OAAOJ,KAAK,CAACvB,KAAK,CAACC,SAAS,KAAKD,KAAK,CAACC,SAAQ;IACjD,CAAC;IAED,MAAM2B,eAAc,GAAIrC,QAAQ,CAAC,MAAM;MACrC,OAAO4B,YAAY,CAACb,KAAI,IAAKa,YAAY,CAACb,KAAK,CAACuB,SAAQ;IAC1D,CAAC;IAED,MAAMC,WAAU,GAAIvC,QAAQ,CAAC,MAAM;MACjC,OAAO;QACL,gBAAgB,EAAES,KAAK,CAACW,MAAK,KAAM,SAAS;QAC5C,mBAAmB,EAAEX,KAAK,CAACW,MAAK,KAAM,YAAY;QAClD,kBAAkB,EAAEX,KAAK,CAACW,MAAK,KAAM,WAAW;QAChD,cAAc,EAAEX,KAAK,CAACW,MAAK,KAAM;MACnC;IACF,CAAC;IAED,MAAMoB,UAAS,GAAIxC,QAAQ,CAAC,MAAM;MAChC,MAAMyC,SAAQ,GAAI;QAChBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,MAAM;QACjBC,KAAK,EAAE;MACT;MACA,OAAOJ,SAAS,CAAChC,KAAK,CAACW,MAAM,KAAK,MAAK;IACzC,CAAC;;IAED;IACAnB,KAAK,CAAC,MAAMQ,KAAK,CAACQ,SAAS,EAAG6B,YAAY,IAAK;MAC7C,IAAIA,YAAW,IAAK,CAACjB,QAAQ,CAACd,KAAK,EAAE;QACnCgC,WAAW,CAACD,YAAY;MAC1B;IACF,CAAC,EAAE;MAAEE,SAAS,EAAE;IAAK,CAAC;;IAEtB;IACA,MAAMD,WAAU,GAAK9B,SAAS,IAAK;MACjC,IAAI,CAACA,SAAS,EAAE;MAEhBgC,OAAO,CAACC,GAAG,CAAC,MAAMzC,KAAK,CAACC,SAAS,6BAA6B,EAAE;QAC9DyC,WAAW,EAAElC,SAAS,CAACkC,WAAW;QAClCC,cAAc,EAAEnC,SAAS,CAACmC,cAAc;QACxCC,YAAY,EAAE,CAAC,CAACpC,SAAS,CAACqB,SAAS;QACnCgB,eAAe,EAAErC,SAAS,CAACqB,SAAQ,GAAIrB,SAAS,CAACqB,SAAS,CAACiB,MAAK,GAAI,CAAC;QACrEC,eAAe,EAAEvC,SAAS,CAACqB,SAAQ,GAAIrB,SAAS,CAACqB,SAAS,CAACmB,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,KAAI,GAAI;MACxF,CAAC;;MAED;MACA,IAAI,CAACxC,SAAS,CAACqB,SAAS,EAAE;QACxBW,OAAO,CAACS,IAAI,CAAC,KAAKjD,KAAK,CAACC,SAAS,uBAAuB,EAAEO,SAAS;QACnE;MACF;;MAEA;MACA,IAAI0C,kBAAiB,GAAI1C,SAAS,CAACqB,SAAQ;MAC3C,IAAI,CAACrB,SAAS,CAACqB,SAAS,CAACsB,UAAU,CAAC,aAAa,CAAC,EAAE;QAClDD,kBAAiB,GAAI,0BAA0B1C,SAAS,CAACqB,SAAS,EAAC;QACnEW,OAAO,CAACC,GAAG,CAAC,MAAMzC,KAAK,CAACC,SAAS,gCAAgC;MACnE;MAEAkB,YAAY,CAACb,KAAI,GAAI;QACnB,GAAGE,SAAS;QACZqB,SAAS,EAAEqB,kBAAkB;QAC7BE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC;MAEAd,OAAO,CAACC,GAAG,CAAC,KAAKzC,KAAK,CAACC,SAAS,eAAeO,SAAS,CAACkC,WAAW,EAAE;IACxE;IAEA,MAAMa,kBAAiB,GAAKrD,IAAI,IAAK;MACnC,MAAMsD,SAAQ,GAAI;QAChBC,GAAG,EAAE,KAAK;QACVC,KAAK,EAAE,IAAI;QACXC,GAAG,EAAE,KAAK;QACVC,UAAU,EAAE;MACd;MACA,OAAOJ,SAAS,CAACtD,IAAI,KAAKA,IAAG;IAC/B;IAEA,MAAM2D,WAAU,GAAIA,CAAA,KAAM;MACxBzC,QAAQ,CAACd,KAAI,GAAI,CAACc,QAAQ,CAACd,KAAI;MAC/BY,IAAI,CAAC,eAAe,EAAE;QACpBjB,SAAS,EAAED,KAAK,CAACC,SAAS;QAC1BmB,QAAQ,EAAEA,QAAQ,CAACd;MACrB,CAAC;IACH;IAEA,MAAMwD,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAI,CAAClC,eAAe,CAACtB,KAAK,EAAE;QAC1Bb,SAAS,CAACsE,OAAO,CAAC,WAAW;QAC7B;MACF;MAEA,IAAI;QACF;QACA,MAAMC,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG;QACvCF,IAAI,CAACG,IAAG,GAAIhD,YAAY,CAACb,KAAK,CAACuB,SAAQ;QACvCmC,IAAI,CAACI,QAAO,GAAI,GAAGpE,KAAK,CAACC,SAAS,UAAUkB,YAAY,CAACb,KAAK,CAACoC,WAAU,IAAK,SAAS,IAAIW,IAAI,CAACgB,GAAG,CAAC,CAAC,MAAK;QAC1GJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI;QAC9BA,IAAI,CAACQ,KAAK,CAAC;QACXP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI;QAE9BvE,SAAS,CAACiF,OAAO,CAAC,QAAQ;QAC1BxD,IAAI,CAAC,aAAa,EAAE;UAClBjB,SAAS,EAAED,KAAK,CAACC,SAAS;UAC1BO,SAAS,EAAEW,YAAY,CAACb;QAC1B,CAAC;MAEH,EAAE,OAAO8B,KAAK,EAAE;QACdI,OAAO,CAACJ,KAAK,CAAC,QAAQ,EAAEA,KAAK;QAC7B3C,SAAS,CAAC2C,KAAK,CAAC,OAAO;MACzB;IACF;IAEA,MAAMuC,WAAU,GAAIA,CAAA,KAAM;MACxBtD,YAAY,CAACf,KAAI,GAAI,KAAI;IAC3B;IAEA,MAAMsE,YAAW,GAAIA,CAAA,KAAM;MACzBvD,YAAY,CAACf,KAAI,GAAI,KAAI;MACzBkC,OAAO,CAACJ,KAAK,CAAC,SAAS;IACzB;IAEA,OAAO;MACLjB,YAAY;MACZC,QAAQ;MACRC,YAAY;MACZC,aAAa;MACbM,eAAe;MACfE,WAAW;MACXC,UAAU;MACVwB,kBAAkB;MAClBM,WAAW;MACXC,SAAS;MACTa,WAAW;MACXC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}