/**
 * WebSocket连接修复脚本
 * 用于解决STOMP连接断开的问题
 */

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
  console.log('🔧 WebSocket修复脚本已加载');
  
  // 等待Vue应用初始化
  setTimeout(() => {
    fixWebSocketConnection();
  }, 2000);
});

function fixWebSocketConnection() {
  console.log('🔍 检查WebSocket连接状态...');
  
  // 检查stompService是否可用
  if (typeof window.stompService === 'undefined') {
    console.log('⚠️ stompService未找到，等待加载...');
    setTimeout(fixWebSocketConnection, 1000);
    return;
  }
  
  const stompService = window.stompService;
  
  // 检查连接状态
  if (!stompService.connected) {
    console.log('🔄 WebSocket未连接，尝试重新连接...');
    
    stompService.init().then(() => {
      console.log('✅ WebSocket重连成功');
      
      // 如果有正在进行的任务，重新订阅
      const currentTaskId = getCurrentTaskId();
      if (currentTaskId) {
        console.log(`🔄 重新订阅任务 ${currentTaskId} 的数据...`);
        resubscribeTaskData(currentTaskId);
      }
      
    }).catch(error => {
      console.error('❌ WebSocket重连失败:', error);
      
      // 5秒后重试
      setTimeout(fixWebSocketConnection, 5000);
    });
  } else {
    console.log('✅ WebSocket连接正常');
  }
}

function getCurrentTaskId() {
  // 从URL中获取任务ID
  const urlParams = new URLSearchParams(window.location.search);
  let taskId = urlParams.get('taskId');
  
  if (!taskId) {
    // 从路径中获取任务ID
    const pathParts = window.location.pathname.split('/');
    const taskIndex = pathParts.indexOf('video-status');
    if (taskIndex !== -1 && pathParts[taskIndex + 1]) {
      taskId = pathParts[taskIndex + 1];
    }
  }
  
  if (!taskId) {
    // 从sessionStorage获取
    try {
      const uploadState = sessionStorage.getItem('uploadState');
      if (uploadState) {
        const state = JSON.parse(uploadState);
        taskId = state.taskId;
      }
    } catch (e) {
      console.warn('无法从sessionStorage获取任务ID:', e);
    }
  }
  
  return taskId;
}

function resubscribeTaskData(taskId) {
  const stompService = window.stompService;
  
  try {
    // 重新订阅视频进度
    stompService.subscribe(`video-progress/${taskId}`, (data) => {
      console.log('📊 收到视频进度更新:', data);
      
      // 触发自定义事件，让Vue组件能够接收到数据
      window.dispatchEvent(new CustomEvent('videoProgressUpdate', {
        detail: data
      }));
    });
    
    // 重新订阅帧数据
    stompService.subscribeFrameUpdates(taskId, (frameData) => {
      console.log('🎬 收到帧数据:', frameData);
      
      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('frameDataReceived', {
        detail: frameData
      }));
    });
    
    console.log('✅ 任务数据订阅已恢复');
    
  } catch (error) {
    console.error('❌ 重新订阅失败:', error);
  }
}

// 导出函数供其他脚本使用
window.fixWebSocketConnection = fixWebSocketConnection;
window.resubscribeTaskData = resubscribeTaskData;
