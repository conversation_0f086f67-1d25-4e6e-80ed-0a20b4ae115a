{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementBlock as _createElementBlock, Fragment as _Fragment, normalizeClass as _normalizeClass, renderList as _renderList } from \"vue\";\nconst _hoisted_1 = {\n  class: \"realtime-frame-viewer\"\n};\nconst _hoisted_2 = {\n  class: \"viewer-header\"\n};\nconst _hoisted_3 = {\n  class: \"viewer-controls\"\n};\nconst _hoisted_4 = {\n  class: \"viewer-content\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"no-frames-message\"\n};\nconst _hoisted_6 = {\n  key: 1,\n  class: \"detection-frame-container\"\n};\nconst _hoisted_7 = [\"src\", \"alt\"];\nconst _hoisted_8 = {\n  class: \"detection-overlay\"\n};\nconst _hoisted_9 = {\n  class: \"detection-header\"\n};\nconst _hoisted_10 = {\n  class: \"detection-time\"\n};\nconst _hoisted_11 = {\n  class: \"detection-info\"\n};\nconst _hoisted_12 = {\n  class: \"frame-number\"\n};\nconst _hoisted_13 = {\n  class: \"frame-container\"\n};\nconst _hoisted_14 = [\"src\", \"alt\"];\nconst _hoisted_15 = {\n  class: \"frame-overlay\"\n};\nconst _hoisted_16 = {\n  class: \"frame-info\"\n};\nconst _hoisted_17 = {\n  class: \"frame-number\"\n};\nconst _hoisted_18 = {\n  class: \"detection-count\"\n};\nconst _hoisted_19 = {\n  class: \"timestamp\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"playback-controls\"\n};\nconst _hoisted_21 = {\n  class: \"playback-info\"\n};\nconst _hoisted_22 = {\n  class: \"buffer-info\"\n};\nconst _hoisted_23 = {\n  key: 0,\n  class: \"thumbnail-strip\"\n};\nconst _hoisted_24 = {\n  class: \"thumbnail-container\"\n};\nconst _hoisted_25 = [\"onClick\"];\nconst _hoisted_26 = [\"src\", \"alt\"];\nconst _hoisted_27 = {\n  class: \"thumbnail-label\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_video_play = _resolveComponent(\"video-play\");\n  const _component_video_pause = _resolveComponent(\"video-pause\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_delete = _resolveComponent(\"delete\");\n  const _component_el_switch = _resolveComponent(\"el-switch\");\n  const _component_loading = _resolveComponent(\"loading\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_slider = _resolveComponent(\"el-slider\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_connection = _resolveComponent(\"connection\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[9] || (_cache[9] = _createElementVNode(\"h4\", null, \"实时视频预览\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n    size: \"small\",\n    type: $setup.isPlaying ? 'danger' : 'primary',\n    onClick: $setup.togglePlayback,\n    disabled: !$setup.hasFrames\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [!$setup.isPlaying ? (_openBlock(), _createBlock(_component_video_play, {\n        key: 0\n      })) : (_openBlock(), _createBlock(_component_video_pause, {\n        key: 1\n      }))]),\n      _: 1 /* STABLE */\n    }), _createTextVNode(\" \" + _toDisplayString($setup.isPlaying ? '暂停' : '播放'), 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\", \"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n    size: \"small\",\n    type: \"info\",\n    onClick: $setup.clearFrames,\n    disabled: !$setup.hasFrames\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_delete)]),\n      _: 1 /* STABLE */\n    }), _cache[7] || (_cache[7] = _createTextVNode(\" 清空 \"))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"disabled\"]), _createVNode(_component_el_button, {\n    size: \"small\",\n    type: \"warning\",\n    onClick: $setup.clearLatestDetection,\n    disabled: !$setup.latestDetectionFrame,\n    title: \"清除定格显示的检测结果\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_delete)]),\n      _: 1 /* STABLE */\n    }), _cache[8] || (_cache[8] = _createTextVNode(\" 清除检测 \"))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"disabled\"]), _createVNode(_component_el_switch, {\n    modelValue: $setup.autoPlay,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.autoPlay = $event),\n    \"active-text\": \"自动播放\",\n    \"inactive-text\": \"手动控制\",\n    size: \"small\"\n  }, null, 8 /* PROPS */, [\"modelValue\"])])]), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 主显示区域 \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"frame-display\", {\n      'no-frames': !$setup.hasFrames\n    }])\n  }, [!$setup.hasFrames ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_icon, {\n    class: \"waiting-icon\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_loading)]),\n    _: 1 /* STABLE */\n  }), _cache[10] || (_cache[10] = _createElementVNode(\"p\", null, \"等待实时帧数据...\", -1 /* HOISTED */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 最新检测帧显示（定格显示） \"), $setup.latestDetectionFrame ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createElementVNode(\"img\", {\n    src: $setup.getFrameImageUrl($setup.latestDetectionFrame),\n    alt: `检测帧 ${$setup.latestDetectionFrame.frameNumber}`,\n    class: \"detection-frame-image\",\n    onLoad: _cache[1] || (_cache[1] = (...args) => $setup.handleImageLoad && $setup.handleImageLoad(...args)),\n    onError: _cache[2] || (_cache[2] = (...args) => $setup.handleImageError && $setup.handleImageError(...args))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_7), _createCommentVNode(\" 检测结果覆盖层 \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_tag, {\n    type: $setup.getDetectionTagType($setup.latestDetectionFrame.detectionCount),\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [_createTextVNode(\" 🚗 检测到 \" + _toDisplayString($setup.latestDetectionFrame.detectionCount) + \" 辆车 \", 1 /* TEXT */)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"type\"]), _createElementVNode(\"span\", _hoisted_10, _toDisplayString($setup.formatTimestamp($setup.latestDetectionFrame.timestamp)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"span\", _hoisted_12, \"帧: \" + _toDisplayString($setup.latestDetectionFrame.frameNumber) + \"/\" + _toDisplayString($setup.totalFrames || 0), 1 /* TEXT */), _cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n    class: \"detection-status\"\n  }, \"最新检测结果 (定格显示)\", -1 /* HOISTED */))])])])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" 常规帧显示 \"), _createElementVNode(\"div\", _hoisted_13, [$setup.currentFrame ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $setup.getFrameImageUrl($setup.currentFrame),\n    alt: `帧 ${$setup.currentFrame.frameNumber}`,\n    class: \"frame-image\",\n    onLoad: _cache[3] || (_cache[3] = (...args) => $setup.handleImageLoad && $setup.handleImageLoad(...args)),\n    onError: _cache[4] || (_cache[4] = (...args) => $setup.handleImageError && $setup.handleImageError(...args))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_14)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 帧信息覆盖层 \"), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"span\", _hoisted_17, \"帧: \" + _toDisplayString($setup.currentFrame?.frameNumber || 0) + \"/\" + _toDisplayString($setup.totalFrames || 0), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_18, \"车辆: \" + _toDisplayString($setup.currentFrame?.detectionCount || 0), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_19, _toDisplayString($setup.formatTimestamp($setup.currentFrame?.timestamp)), 1 /* TEXT */)])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))], 2 /* CLASS */), _createCommentVNode(\" 进度条和控制 \"), $setup.hasFrames ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createVNode(_component_el_slider, {\n    modelValue: $setup.currentFrameIndex,\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.currentFrameIndex = $event),\n    min: 0,\n    max: $setup.frames.length - 1,\n    step: 1,\n    \"show-tooltip\": false,\n    onChange: $setup.onFrameIndexChange,\n    class: \"frame-slider\"\n  }, null, 8 /* PROPS */, [\"modelValue\", \"max\", \"onChange\"]), _createElementVNode(\"div\", _hoisted_21, [_cache[12] || (_cache[12] = _createElementVNode(\"span\", null, \"播放速度:\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n    modelValue: $setup.playbackSpeed,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.playbackSpeed = $event),\n    size: \"small\",\n    style: {\n      \"width\": \"80px\"\n    }\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_option, {\n      label: \"0.5x\",\n      value: 0.5\n    }), _createVNode(_component_el_option, {\n      label: \"1x\",\n      value: 1\n    }), _createVNode(_component_el_option, {\n      label: \"2x\",\n      value: 2\n    }), _createVNode(_component_el_option, {\n      label: \"4x\",\n      value: 4\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"span\", _hoisted_22, \"缓冲: \" + _toDisplayString($setup.frames.length) + \" 帧\", 1 /* TEXT */), _createElementVNode(\"span\", {\n    class: _normalizeClass([\"network-info\", $setup.networkQualityClass])\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode(_component_connection)]),\n    _: 1 /* STABLE */\n  }), _createTextVNode(\" \" + _toDisplayString($setup.networkQualityText), 1 /* TEXT */)], 2 /* CLASS */)])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 缩略图条 \"), $setup.hasFrames && $setup.frames.length > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.visibleThumbnails, (frame, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: frame.frameNumber,\n      class: _normalizeClass([\"thumbnail-item\", {\n        'active': index === $setup.currentFrameIndex\n      }]),\n      onClick: $event => $setup.selectFrame(index)\n    }, [_createElementVNode(\"img\", {\n      src: $setup.getFrameImageUrl(frame),\n      alt: `缩略图 ${frame.frameNumber}`,\n      class: \"thumbnail-image\"\n    }, null, 8 /* PROPS */, _hoisted_26), _createElementVNode(\"div\", _hoisted_27, _toDisplayString(frame.frameNumber), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_25);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_button", "size", "type", "$setup", "isPlaying", "onClick", "togglePlayback", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "default", "_withCtx", "_component_el_icon", "_createBlock", "_component_video_play", "_component_video_pause", "_", "_createTextVNode", "_toDisplayString", "clearFrames", "_component_delete", "clearLatestDetection", "latestDetectionFrame", "title", "_component_el_switch", "modelValue", "autoPlay", "_cache", "$event", "_hoisted_4", "_createCommentVNode", "_normalizeClass", "_hoisted_5", "_component_loading", "_hoisted_6", "src", "getFrameImageUrl", "alt", "frameNumber", "onLoad", "args", "handleImageLoad", "onError", "handleImageError", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_component_el_tag", "getDetectionTagType", "detectionCount", "_hoisted_10", "formatTimestamp", "timestamp", "_hoisted_11", "_hoisted_12", "totalFrames", "_Fragment", "_hoisted_13", "currentFrame", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_component_el_slider", "currentFrameIndex", "min", "max", "frames", "length", "step", "onChange", "onFrameIndexChange", "_hoisted_21", "_component_el_select", "playbackSpeed", "style", "_component_el_option", "label", "value", "_hoisted_22", "networkQualityClass", "_component_connection", "networkQualityText", "_hoisted_23", "_hoisted_24", "_renderList", "visibleThumbnails", "frame", "index", "selectFrame", "_hoisted_26", "_hoisted_27", "_hoisted_25"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\components\\video\\RealTimeFrameViewer.vue"], "sourcesContent": ["<template>\n  <div class=\"realtime-frame-viewer\">\n    <div class=\"viewer-header\">\n      <h4>实时视频预览</h4>\n      <div class=\"viewer-controls\">\n        <el-button \n          size=\"small\" \n          :type=\"isPlaying ? 'danger' : 'primary'\"\n          @click=\"togglePlayback\"\n          :disabled=\"!hasFrames\"\n        >\n          <el-icon><video-play v-if=\"!isPlaying\" /><video-pause v-else /></el-icon>\n          {{ isPlaying ? '暂停' : '播放' }}\n        </el-button>\n        \n        <el-button\n          size=\"small\"\n          type=\"info\"\n          @click=\"clearFrames\"\n          :disabled=\"!hasFrames\"\n        >\n          <el-icon><delete /></el-icon>\n          清空\n        </el-button>\n\n        <el-button\n          size=\"small\"\n          type=\"warning\"\n          @click=\"clearLatestDetection\"\n          :disabled=\"!latestDetectionFrame\"\n          title=\"清除定格显示的检测结果\"\n        >\n          <el-icon><delete /></el-icon>\n          清除检测\n        </el-button>\n        \n        <el-switch\n          v-model=\"autoPlay\"\n          active-text=\"自动播放\"\n          inactive-text=\"手动控制\"\n          size=\"small\"\n        />\n      </div>\n    </div>\n\n    <div class=\"viewer-content\">\n      <!-- 主显示区域 -->\n      <div class=\"frame-display\" :class=\"{ 'no-frames': !hasFrames }\">\n        <div v-if=\"!hasFrames\" class=\"no-frames-message\">\n          <el-icon class=\"waiting-icon\"><loading /></el-icon>\n          <p>等待实时帧数据...</p>\n        </div>\n        \n        <!-- 最新检测帧显示（定格显示） -->\n        <div v-if=\"latestDetectionFrame\" class=\"detection-frame-container\">\n          <img\n            :src=\"getFrameImageUrl(latestDetectionFrame)\"\n            :alt=\"`检测帧 ${latestDetectionFrame.frameNumber}`\"\n            class=\"detection-frame-image\"\n            @load=\"handleImageLoad\"\n            @error=\"handleImageError\"\n          />\n\n          <!-- 检测结果覆盖层 -->\n          <div class=\"detection-overlay\">\n            <div class=\"detection-header\">\n              <el-tag :type=\"getDetectionTagType(latestDetectionFrame.detectionCount)\" size=\"small\">\n                🚗 检测到 {{ latestDetectionFrame.detectionCount }} 辆车\n              </el-tag>\n              <span class=\"detection-time\">{{ formatTimestamp(latestDetectionFrame.timestamp) }}</span>\n            </div>\n\n            <div class=\"detection-info\">\n              <span class=\"frame-number\">帧: {{ latestDetectionFrame.frameNumber }}/{{ totalFrames || 0 }}</span>\n              <span class=\"detection-status\">最新检测结果 (定格显示)</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 常规帧显示 -->\n        <div v-else class=\"frame-container\">\n          <img\n            v-if=\"currentFrame\"\n            :src=\"getFrameImageUrl(currentFrame)\"\n            :alt=\"`帧 ${currentFrame.frameNumber}`\"\n            class=\"frame-image\"\n            @load=\"handleImageLoad\"\n            @error=\"handleImageError\"\n          />\n\n          <!-- 帧信息覆盖层 -->\n          <div class=\"frame-overlay\">\n            <div class=\"frame-info\">\n              <span class=\"frame-number\">帧: {{ currentFrame?.frameNumber || 0 }}/{{ totalFrames || 0 }}</span>\n              <span class=\"detection-count\">车辆: {{ currentFrame?.detectionCount || 0 }}</span>\n              <span class=\"timestamp\">{{ formatTimestamp(currentFrame?.timestamp) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 进度条和控制 -->\n      <div class=\"playback-controls\" v-if=\"hasFrames\">\n        <el-slider\n          v-model=\"currentFrameIndex\"\n          :min=\"0\"\n          :max=\"frames.length - 1\"\n          :step=\"1\"\n          :show-tooltip=\"false\"\n          @change=\"onFrameIndexChange\"\n          class=\"frame-slider\"\n        />\n        \n        <div class=\"playback-info\">\n          <span>播放速度:</span>\n          <el-select v-model=\"playbackSpeed\" size=\"small\" style=\"width: 80px;\">\n            <el-option label=\"0.5x\" :value=\"0.5\" />\n            <el-option label=\"1x\" :value=\"1\" />\n            <el-option label=\"2x\" :value=\"2\" />\n            <el-option label=\"4x\" :value=\"4\" />\n          </el-select>\n          \n          <span class=\"buffer-info\">缓冲: {{ frames.length }} 帧</span>\n          <span class=\"network-info\" :class=\"networkQualityClass\">\n            <el-icon><connection /></el-icon>\n            {{ networkQualityText }}\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 缩略图条 -->\n    <div class=\"thumbnail-strip\" v-if=\"hasFrames && frames.length > 1\">\n      <div class=\"thumbnail-container\">\n        <div \n          v-for=\"(frame, index) in visibleThumbnails\" \n          :key=\"frame.frameNumber\"\n          class=\"thumbnail-item\"\n          :class=\"{ 'active': index === currentFrameIndex }\"\n          @click=\"selectFrame(index)\"\n        >\n          <img \n            :src=\"getFrameImageUrl(frame)\"\n            :alt=\"`缩略图 ${frame.frameNumber}`\"\n            class=\"thumbnail-image\"\n          />\n          <div class=\"thumbnail-label\">{{ frame.frameNumber }}</div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport {\n  VideoPlay,\n  VideoPause,\n  Delete,\n  Loading,\n  Connection\n} from '@element-plus/icons-vue'\n\nexport default {\n  name: 'RealTimeFrameViewer',\n  components: {\n    VideoPlay,\n    VideoPause,\n    Delete,\n    Loading,\n    Connection\n  },\n  props: {\n    // 任务ID，用于接收对应的帧数据\n    taskId: {\n      type: String,\n      required: true\n    },\n    // 是否自动开始播放\n    autoStart: {\n      type: Boolean,\n      default: true\n    },\n    // 最大缓冲帧数\n    maxBufferFrames: {\n      type: Number,\n      default: 30\n    }\n  },\n  emits: ['frame-received', 'playback-state-change'],\n  setup(props, { emit }) {\n    // 响应式数据\n    const frames = ref([])\n    const currentFrameIndex = ref(0)\n    const isPlaying = ref(false)\n    const autoPlay = ref(props.autoStart)\n    const playbackSpeed = ref(1)\n    const totalFrames = ref(0)\n\n    // 最新检测帧（定格显示）\n    const latestDetectionFrame = ref(null)\n\n    // 网络状况监控（优化帧率计算）\n    const networkStats = ref({\n      quality: 'good',\n      frameRate: 0,\n      averageSize: 0,\n      lastFrameTime: 0,\n      frameIntervals: [],\n      realTimeFrameRate: 0\n    })\n\n    // 播放控制\n    let playbackTimer = null\n    let networkStatsTimer = null\n    \n    // 计算属性\n    const hasFrames = computed(() => frames.value.length > 0)\n    const currentFrame = computed(() => frames.value[currentFrameIndex.value] || null)\n    \n    // 可见缩略图（最多显示10个）\n    const visibleThumbnails = computed(() => {\n      const maxThumbnails = 10\n      if (frames.value.length <= maxThumbnails) {\n        return frames.value\n      }\n\n      const step = Math.floor(frames.value.length / maxThumbnails)\n      return frames.value.filter((_, index) => index % step === 0).slice(0, maxThumbnails)\n    })\n\n    // 网络质量相关计算属性\n    const networkQualityClass = computed(() => {\n      return `network-${networkStats.value.quality}`\n    })\n\n    const networkQualityText = computed(() => {\n      const quality = networkStats.value.quality\n      const rate = networkStats.value.frameRate.toFixed(1)\n\n      switch (quality) {\n        case 'good':\n          return `良好 ${rate}fps`\n        case 'fair':\n          return `一般 ${rate}fps`\n        case 'poor':\n          return `较差 ${rate}fps`\n        default:\n          return `未知 ${rate}fps`\n      }\n    })\n    \n    // 方法\n    const getFrameImageUrl = (frame) => {\n      if (!frame || !frame.imageData) {\n        console.log('🖼️ getFrameImageUrl: 无效的帧数据', { frame: !!frame, hasImageData: frame ? !!frame.imageData : false })\n        return ''\n      }\n      const imageUrl = `data:image/jpeg;base64,${frame.imageData}`\n      console.log('🖼️ getFrameImageUrl: 生成图像URL', {\n        frameNumber: frame.frameNumber,\n        imageDataLength: frame.imageData.length,\n        urlLength: imageUrl.length,\n        urlPrefix: imageUrl.substring(0, 50) + '...'\n      })\n      return imageUrl\n    }\n    \n    const formatTimestamp = (timestamp) => {\n      if (!timestamp) return ''\n      try {\n        const date = new Date(timestamp)\n        return date.toLocaleTimeString()\n      } catch (e) {\n        return timestamp\n      }\n    }\n\n    // 获取检测标签类型\n    const getDetectionTagType = (detectionCount) => {\n      if (detectionCount === 0) return 'info'\n      if (detectionCount <= 2) return 'success'\n      if (detectionCount <= 5) return 'warning'\n      return 'danger'\n    }\n    \n    const addFrame = (frameData) => {\n      try {\n        // 调试日志：检查接收到的帧数据\n        console.log('🎬 RealTimeFrameViewer接收到帧数据:', {\n          frameNumber: frameData.frameNumber,\n          detectionCount: frameData.detectionCount,\n          hasImageData: !!frameData.imageData,\n          imageDataLength: frameData.imageData ? frameData.imageData.length : 0,\n          imageDataPrefix: frameData.imageData ? frameData.imageData.substring(0, 50) + '...' : 'null'\n        })\n\n        // 添加新帧到缓冲区\n        frames.value.push(frameData)\n\n        // 更新总帧数\n        if (frameData.totalFrames) {\n          totalFrames.value = frameData.totalFrames\n        }\n\n        // 检查是否有车辆检测，如果有则更新最新检测帧（定格显示）\n        if (frameData.detectionCount && frameData.detectionCount > 0) {\n          console.log(`🎯 更新最新检测帧: 帧${frameData.frameNumber}, 车辆${frameData.detectionCount}`)\n          latestDetectionFrame.value = { ...frameData }\n        }\n\n        // 限制缓冲区大小\n        while (frames.value.length > props.maxBufferFrames) {\n          frames.value.shift()\n          if (currentFrameIndex.value > 0) {\n            currentFrameIndex.value--\n          }\n        }\n\n        // 如果启用自动播放且当前在最后一帧，自动跳到新帧\n        if (autoPlay.value && currentFrameIndex.value === frames.value.length - 2) {\n          currentFrameIndex.value = frames.value.length - 1\n        }\n\n        // 发出帧接收事件\n        emit('frame-received', frameData)\n\n      } catch (error) {\n        console.error('添加帧数据失败:', error)\n        ElMessage.error('处理帧数据失败')\n      }\n    }\n    \n    const togglePlayback = () => {\n      if (isPlaying.value) {\n        stopPlayback()\n      } else {\n        startPlayback()\n      }\n    }\n    \n    const startPlayback = () => {\n      if (!hasFrames.value) return\n      \n      isPlaying.value = true\n      emit('playback-state-change', { playing: true, speed: playbackSpeed.value })\n      \n      const interval = 1000 / playbackSpeed.value // 基础间隔1秒，根据速度调整\n      \n      playbackTimer = setInterval(() => {\n        if (currentFrameIndex.value < frames.value.length - 1) {\n          currentFrameIndex.value++\n        } else {\n          // 播放完毕，停止播放\n          stopPlayback()\n        }\n      }, interval)\n    }\n    \n    const stopPlayback = () => {\n      isPlaying.value = false\n      emit('playback-state-change', { playing: false, speed: playbackSpeed.value })\n      \n      if (playbackTimer) {\n        clearInterval(playbackTimer)\n        playbackTimer = null\n      }\n    }\n    \n    const clearFrames = () => {\n      stopPlayback()\n      frames.value = []\n      currentFrameIndex.value = 0\n      totalFrames.value = 0\n      latestDetectionFrame.value = null  // 清除定格显示的检测帧\n      ElMessage.success('已清空帧缓冲区')\n    }\n\n    // 清除最新检测帧\n    const clearLatestDetection = () => {\n      latestDetectionFrame.value = null\n      console.log('🧹 已清除最新检测帧')\n      ElMessage.success('已清除最新检测结果')\n    }\n    \n    const selectFrame = (index) => {\n      if (index >= 0 && index < frames.value.length) {\n        currentFrameIndex.value = index\n      }\n    }\n    \n    const onFrameIndexChange = (value) => {\n      currentFrameIndex.value = value\n    }\n    \n    const handleImageLoad = () => {\n      // 图像加载成功\n    }\n    \n    const handleImageError = () => {\n      console.error('帧图像加载失败')\n    }\n\n    // 更新网络状况统计\n    const updateNetworkStats = () => {\n      try {\n        // 从STOMP服务获取网络统计\n        const stats = window.stompService?.getNetworkStats?.() || {}\n\n        networkStats.value = {\n          quality: stats.connectionQuality || 'good',\n          frameRate: stats.frameReceiveRate || 0,\n          averageSize: stats.averageFrameSize || 0\n        }\n\n      } catch (error) {\n        console.error('更新网络统计失败:', error)\n      }\n    }\n\n    // 启动网络状况监控\n    const startNetworkMonitoring = () => {\n      // 每2秒更新一次网络统计\n      networkStatsTimer = setInterval(updateNetworkStats, 2000)\n    }\n\n    // 停止网络状况监控\n    const stopNetworkMonitoring = () => {\n      if (networkStatsTimer) {\n        clearInterval(networkStatsTimer)\n        networkStatsTimer = null\n      }\n    }\n    \n    // 监听播放速度变化\n    watch(playbackSpeed, (newSpeed) => {\n      if (isPlaying.value) {\n        stopPlayback()\n        nextTick(() => {\n          startPlayback()\n        })\n      }\n    })\n    \n    // 监听自动播放设置变化\n    watch(autoPlay, (newValue) => {\n      if (!newValue && isPlaying.value) {\n        stopPlayback()\n      }\n    })\n    \n    // 组件挂载时启动网络监控\n    onMounted(() => {\n      startNetworkMonitoring()\n    })\n\n    // 组件卸载时清理\n    onUnmounted(() => {\n      stopPlayback()\n      stopNetworkMonitoring()\n    })\n    \n    // 暴露方法给父组件\n    const addFrameData = addFrame\n    const clearFrameData = clearFrames\n    const getFrameCount = () => frames.value.length\n    const getCurrentFrame = () => currentFrame.value\n    \n    return {\n      // 响应式数据\n      frames,\n      currentFrameIndex,\n      isPlaying,\n      autoPlay,\n      playbackSpeed,\n      totalFrames,\n      latestDetectionFrame,\n      \n      // 计算属性\n      hasFrames,\n      currentFrame,\n      visibleThumbnails,\n      networkQualityClass,\n      networkQualityText,\n      \n      // 方法\n      getFrameImageUrl,\n      formatTimestamp,\n      getDetectionTagType,\n      togglePlayback,\n      clearFrames,\n      clearLatestDetection,\n      selectFrame,\n      onFrameIndexChange,\n      handleImageLoad,\n      handleImageError,\n      \n      // 暴露给父组件的方法\n      addFrameData,\n      clearFrameData,\n      getFrameCount,\n      getCurrentFrame\n    }\n  }\n}\n</script>\n\n<style scoped>\n.realtime-frame-viewer {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.viewer-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.viewer-header h4 {\n  margin: 0;\n  color: #333;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.viewer-controls {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.viewer-content {\n  padding: 20px;\n}\n\n.frame-display {\n  position: relative;\n  width: 100%;\n  height: 400px;\n  background: #000;\n  border-radius: 6px;\n  overflow: hidden;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.frame-display.no-frames {\n  background: #f5f5f5;\n  border: 2px dashed #d9d9d9;\n}\n\n.no-frames-message {\n  text-align: center;\n  color: #999;\n}\n\n.no-frames-message .waiting-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n  animation: spin 2s linear infinite;\n}\n\n@keyframes spin {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n.frame-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* 检测帧容器样式 */\n.detection-frame-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1));\n  border: 2px solid rgba(16, 185, 129, 0.3);\n  border-radius: 8px;\n}\n\n.detection-frame-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n}\n\n.detection-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n}\n\n.detection-header {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-end;\n  gap: 8px;\n}\n\n.detection-time {\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 11px;\n}\n\n.detection-info {\n  position: absolute;\n  bottom: 12px;\n  left: 12px;\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n  border-left: 3px solid #10b981;\n}\n\n.detection-status {\n  color: #10b981;\n  font-weight: 600;\n  font-size: 11px;\n}\n\n.frame-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 4px;\n}\n\n.frame-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n}\n\n.frame-info {\n  position: absolute;\n  top: 12px;\n  left: 12px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  padding: 8px 12px;\n  border-radius: 4px;\n  font-size: 12px;\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.frame-info span {\n  white-space: nowrap;\n}\n\n.playback-controls {\n  margin-top: 20px;\n  padding: 16px;\n  background: #f8f9fa;\n  border-radius: 6px;\n}\n\n.frame-slider {\n  margin-bottom: 12px;\n}\n\n.playback-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  color: #666;\n}\n\n.playback-info > span {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.buffer-info {\n  color: #999;\n  font-size: 12px;\n}\n\n.network-info {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n  padding: 2px 6px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.network-good {\n  color: #10b981;\n  background: rgba(16, 185, 129, 0.1);\n}\n\n.network-fair {\n  color: #f59e0b;\n  background: rgba(245, 158, 11, 0.1);\n}\n\n.network-poor {\n  color: #ef4444;\n  background: rgba(239, 68, 68, 0.1);\n}\n\n.network-info .el-icon {\n  font-size: 14px;\n}\n\n.thumbnail-strip {\n  padding: 16px 20px;\n  background: #f8f9fa;\n  border-top: 1px solid #e9ecef;\n  overflow-x: auto;\n}\n\n.thumbnail-container {\n  display: flex;\n  gap: 8px;\n  min-height: 60px;\n}\n\n.thumbnail-item {\n  flex-shrink: 0;\n  width: 80px;\n  cursor: pointer;\n  border-radius: 4px;\n  overflow: hidden;\n  border: 2px solid transparent;\n  transition: all 0.2s ease;\n}\n\n.thumbnail-item:hover {\n  border-color: #409eff;\n  transform: translateY(-2px);\n}\n\n.thumbnail-item.active {\n  border-color: #409eff;\n  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);\n}\n\n.thumbnail-image {\n  width: 100%;\n  height: 45px;\n  object-fit: cover;\n  display: block;\n}\n\n.thumbnail-label {\n  padding: 2px 4px;\n  background: #fff;\n  text-align: center;\n  font-size: 10px;\n  color: #666;\n  border-top: 1px solid #e9ecef;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .viewer-header {\n    flex-direction: column;\n    gap: 12px;\n    align-items: stretch;\n  }\n\n  .viewer-controls {\n    justify-content: center;\n  }\n\n  .frame-display {\n    height: 300px;\n  }\n\n  .playback-info {\n    flex-direction: column;\n    gap: 8px;\n    align-items: stretch;\n  }\n\n  .thumbnail-item {\n    width: 60px;\n  }\n\n  .thumbnail-image {\n    height: 35px;\n  }\n}\n\n/* 深色模式支持 */\n@media (prefers-color-scheme: dark) {\n  .realtime-frame-viewer {\n    background: #1f1f1f;\n    color: #fff;\n  }\n\n  .viewer-header {\n    background: #2d2d2d;\n    border-bottom-color: #404040;\n  }\n\n  .viewer-header h4 {\n    color: #fff;\n  }\n\n  .frame-display.no-frames {\n    background: #2d2d2d;\n    border-color: #404040;\n  }\n\n  .no-frames-message {\n    color: #ccc;\n  }\n\n  .playback-controls {\n    background: #2d2d2d;\n  }\n\n  .thumbnail-strip {\n    background: #2d2d2d;\n    border-top-color: #404040;\n  }\n\n  .thumbnail-label {\n    background: #2d2d2d;\n    color: #ccc;\n    border-top-color: #404040;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAiB;;EAyCzBA,KAAK,EAAC;AAAgB;;EA7C/BC,GAAA;EAgD+BD,KAAK,EAAC;;;EAhDrCC,GAAA;EAsDyCD,KAAK,EAAC;;mBAtD/C;;EAgEeA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAkB;;EAIrBA,KAAK,EAAC;AAAgB;;EAGzBA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAc;;EAOpBA,KAAK,EAAC;AAAiB;oBAhF3C;;EA2FeA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAY;;EACfA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAW;;EA/FrCC,GAAA;EAsGWD,KAAK,EAAC;;;EAWJA,KAAK,EAAC;AAAe;;EASlBA,KAAK,EAAC;AAAa;;EA1HnCC,GAAA;EAoISD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAqB;oBArItC;oBAAA;;EAkJeA,KAAK,EAAC;AAAiB;;;;;;;;;;;;;;uBAjJpCE,mBAAA,CAqJM,OArJNC,UAqJM,GApJJC,mBAAA,CAyCM,OAzCNC,UAyCM,G,0BAxCJD,mBAAA,CAAe,YAAX,QAAM,sBACVA,mBAAA,CAsCM,OAtCNE,UAsCM,GArCJC,YAAA,CAQYC,oBAAA;IAPVC,IAAI,EAAC,OAAO;IACXC,IAAI,EAAEC,MAAA,CAAAC,SAAS;IACfC,OAAK,EAAEF,MAAA,CAAAG,cAAc;IACrBC,QAAQ,GAAGJ,MAAA,CAAAK;;IATtBC,OAAA,EAAAC,QAAA,CAWU,MAAyE,CAAzEX,YAAA,CAAyEY,kBAAA;MAXnFF,OAAA,EAAAC,QAAA,CAWmB,MAAgC,C,CAAbP,MAAA,CAAAC,SAAS,I,cAA5BQ,YAAA,CAAgCC,qBAAA;QAXnDpB,GAAA;MAAA,O,cAWmDmB,YAAA,CAAsBE,sBAAA;QAXzErB,GAAA;MAAA,I;MAAAsB,CAAA;QAAAC,gBAAA,CAWmF,GACzE,GAAAC,gBAAA,CAAGd,MAAA,CAAAC,SAAS,+B;IAZtBW,CAAA;sDAeQhB,YAAA,CAQYC,oBAAA;IAPVC,IAAI,EAAC,OAAO;IACZC,IAAI,EAAC,MAAM;IACVG,OAAK,EAAEF,MAAA,CAAAe,WAAW;IAClBX,QAAQ,GAAGJ,MAAA,CAAAK;;IAnBtBC,OAAA,EAAAC,QAAA,CAqBU,MAA6B,CAA7BX,YAAA,CAA6BY,kBAAA;MArBvCF,OAAA,EAAAC,QAAA,CAqBmB,MAAU,CAAVX,YAAA,CAAUoB,iBAAA,E;MArB7BJ,CAAA;kCAAAC,gBAAA,CAqBuC,MAE/B,G;IAvBRD,CAAA;8CAyBQhB,YAAA,CASYC,oBAAA;IARVC,IAAI,EAAC,OAAO;IACZC,IAAI,EAAC,SAAS;IACbG,OAAK,EAAEF,MAAA,CAAAiB,oBAAoB;IAC3Bb,QAAQ,GAAGJ,MAAA,CAAAkB,oBAAoB;IAChCC,KAAK,EAAC;;IA9BhBb,OAAA,EAAAC,QAAA,CAgCU,MAA6B,CAA7BX,YAAA,CAA6BY,kBAAA;MAhCvCF,OAAA,EAAAC,QAAA,CAgCmB,MAAU,CAAVX,YAAA,CAAUoB,iBAAA,E;MAhC7BJ,CAAA;kCAAAC,gBAAA,CAgCuC,QAE/B,G;IAlCRD,CAAA;8CAoCQhB,YAAA,CAKEwB,oBAAA;IAzCVC,UAAA,EAqCmBrB,MAAA,CAAAsB,QAAQ;IArC3B,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAqCmBxB,MAAA,CAAAsB,QAAQ,GAAAE,MAAA;IACjB,aAAW,EAAC,MAAM;IAClB,eAAa,EAAC,MAAM;IACpB1B,IAAI,EAAC;+CAKXL,mBAAA,CAoFM,OApFNgC,UAoFM,GAnFJC,mBAAA,WAAc,EACdjC,mBAAA,CAoDM;IApDDJ,KAAK,EA/ChBsC,eAAA,EA+CiB,eAAe;MAAA,cAAyB3B,MAAA,CAAAK;IAAS;OAC9CL,MAAA,CAAAK,SAAS,I,cAArBd,mBAAA,CAGM,OAHNqC,UAGM,GAFJhC,YAAA,CAAmDY,kBAAA;IAA1CnB,KAAK,EAAC;EAAc;IAjDvCiB,OAAA,EAAAC,QAAA,CAiDwC,MAAW,CAAXX,YAAA,CAAWiC,kBAAA,E;IAjDnDjB,CAAA;kCAkDUnB,mBAAA,CAAiB,WAAd,YAAU,qB,KAlDvBiC,mBAAA,gBAqDQA,mBAAA,mBAAsB,EACX1B,MAAA,CAAAkB,oBAAoB,I,cAA/B3B,mBAAA,CAuBM,OAvBNuC,UAuBM,GAtBJrC,mBAAA,CAME;IALCsC,GAAG,EAAE/B,MAAA,CAAAgC,gBAAgB,CAAChC,MAAA,CAAAkB,oBAAoB;IAC1Ce,GAAG,SAASjC,MAAA,CAAAkB,oBAAoB,CAACgB,WAAW;IAC7C7C,KAAK,EAAC,uBAAuB;IAC5B8C,MAAI,EAAAZ,MAAA,QAAAA,MAAA,UAAAa,IAAA,KAAEpC,MAAA,CAAAqC,eAAA,IAAArC,MAAA,CAAAqC,eAAA,IAAAD,IAAA,CAAe;IACrBE,OAAK,EAAAf,MAAA,QAAAA,MAAA,UAAAa,IAAA,KAAEpC,MAAA,CAAAuC,gBAAA,IAAAvC,MAAA,CAAAuC,gBAAA,IAAAH,IAAA,CAAgB;2CA5DpCI,UAAA,GA+DUd,mBAAA,aAAgB,EAChBjC,mBAAA,CAYM,OAZNgD,UAYM,GAXJhD,mBAAA,CAKM,OALNiD,UAKM,GAJJ9C,YAAA,CAES+C,iBAAA;IAFA5C,IAAI,EAAEC,MAAA,CAAA4C,mBAAmB,CAAC5C,MAAA,CAAAkB,oBAAoB,CAAC2B,cAAc;IAAG/C,IAAI,EAAC;;IAlE5FQ,OAAA,EAAAC,QAAA,CAkEoG,MAC7E,CAnEvBM,gBAAA,CAkEoG,UAC7E,GAAAC,gBAAA,CAAGd,MAAA,CAAAkB,oBAAoB,CAAC2B,cAAc,IAAG,MAClD,gB;IApEdjC,CAAA;+BAqEcnB,mBAAA,CAAyF,QAAzFqD,WAAyF,EAAAhC,gBAAA,CAAzDd,MAAA,CAAA+C,eAAe,CAAC/C,MAAA,CAAAkB,oBAAoB,CAAC8B,SAAS,kB,GAGhFvD,mBAAA,CAGM,OAHNwD,WAGM,GAFJxD,mBAAA,CAAkG,QAAlGyD,WAAkG,EAAvE,KAAG,GAAApC,gBAAA,CAAGd,MAAA,CAAAkB,oBAAoB,CAACgB,WAAW,IAAG,GAAC,GAAApB,gBAAA,CAAGd,MAAA,CAAAmD,WAAW,uB,4BACnF1D,mBAAA,CAAmD;IAA7CJ,KAAK,EAAC;EAAkB,GAAC,eAAa,qB,wBAMlDE,mBAAA,CAkBM6D,SAAA;IAlGd9D,GAAA;EAAA,IA+EQoC,mBAAA,WAAc,EACdjC,mBAAA,CAkBM,OAlBN4D,WAkBM,GAhBIrD,MAAA,CAAAsD,YAAY,I,cADpB/D,mBAAA,CAOE;IAxFZD,GAAA;IAmFayC,GAAG,EAAE/B,MAAA,CAAAgC,gBAAgB,CAAChC,MAAA,CAAAsD,YAAY;IAClCrB,GAAG,OAAOjC,MAAA,CAAAsD,YAAY,CAACpB,WAAW;IACnC7C,KAAK,EAAC,aAAa;IAClB8C,MAAI,EAAAZ,MAAA,QAAAA,MAAA,UAAAa,IAAA,KAAEpC,MAAA,CAAAqC,eAAA,IAAArC,MAAA,CAAAqC,eAAA,IAAAD,IAAA,CAAe;IACrBE,OAAK,EAAAf,MAAA,QAAAA,MAAA,UAAAa,IAAA,KAAEpC,MAAA,CAAAuC,gBAAA,IAAAvC,MAAA,CAAAuC,gBAAA,IAAAH,IAAA,CAAgB;2CAvFpCmB,WAAA,KAAA7B,mBAAA,gBA0FUA,mBAAA,YAAe,EACfjC,mBAAA,CAMM,OANN+D,WAMM,GALJ/D,mBAAA,CAIM,OAJNgE,WAIM,GAHJhE,mBAAA,CAAgG,QAAhGiE,WAAgG,EAArE,KAAG,GAAA5C,gBAAA,CAAGd,MAAA,CAAAsD,YAAY,EAAEpB,WAAW,SAAQ,GAAC,GAAApB,gBAAA,CAAGd,MAAA,CAAAmD,WAAW,uBACjF1D,mBAAA,CAAgF,QAAhFkE,WAAgF,EAAlD,MAAI,GAAA7C,gBAAA,CAAGd,MAAA,CAAAsD,YAAY,EAAET,cAAc,uBACjEpD,mBAAA,CAA6E,QAA7EmE,WAA6E,EAAA9C,gBAAA,CAAlDd,MAAA,CAAA+C,eAAe,CAAC/C,MAAA,CAAAsD,YAAY,EAAEN,SAAS,kB,0EAM1EtB,mBAAA,YAAe,EACsB1B,MAAA,CAAAK,SAAS,I,cAA9Cd,mBAAA,CA0BM,OA1BNsE,WA0BM,GAzBJjE,YAAA,CAQEkE,oBAAA;IA/GVzC,UAAA,EAwGmBrB,MAAA,CAAA+D,iBAAiB;IAxGpC,uBAAAxC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAwGmBxB,MAAA,CAAA+D,iBAAiB,GAAAvC,MAAA;IACzBwC,GAAG,EAAE,CAAC;IACNC,GAAG,EAAEjE,MAAA,CAAAkE,MAAM,CAACC,MAAM;IAClBC,IAAI,EAAE,CAAC;IACP,cAAY,EAAE,KAAK;IACnBC,QAAM,EAAErE,MAAA,CAAAsE,kBAAkB;IAC3BjF,KAAK,EAAC;8DAGRI,mBAAA,CAcM,OAdN8E,WAcM,G,4BAbJ9E,mBAAA,CAAkB,cAAZ,OAAK,sBACXG,YAAA,CAKY4E,oBAAA;IAxHtBnD,UAAA,EAmH8BrB,MAAA,CAAAyE,aAAa;IAnH3C,uBAAAlD,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAmH8BxB,MAAA,CAAAyE,aAAa,GAAAjD,MAAA;IAAE1B,IAAI,EAAC,OAAO;IAAC4E,KAAoB,EAApB;MAAA;IAAA;;IAnH1DpE,OAAA,EAAAC,QAAA,CAoHY,MAAuC,CAAvCX,YAAA,CAAuC+E,oBAAA;MAA5BC,KAAK,EAAC,MAAM;MAAEC,KAAK,EAAE;QAChCjF,YAAA,CAAmC+E,oBAAA;MAAxBC,KAAK,EAAC,IAAI;MAAEC,KAAK,EAAE;QAC9BjF,YAAA,CAAmC+E,oBAAA;MAAxBC,KAAK,EAAC,IAAI;MAAEC,KAAK,EAAE;QAC9BjF,YAAA,CAAmC+E,oBAAA;MAAxBC,KAAK,EAAC,IAAI;MAAEC,KAAK,EAAE;;IAvH1CjE,CAAA;qCA0HUnB,mBAAA,CAA0D,QAA1DqF,WAA0D,EAAhC,MAAI,GAAAhE,gBAAA,CAAGd,MAAA,CAAAkE,MAAM,CAACC,MAAM,IAAG,IAAE,iBACnD1E,mBAAA,CAGO;IAHDJ,KAAK,EA3HrBsC,eAAA,EA2HsB,cAAc,EAAS3B,MAAA,CAAA+E,mBAAmB;MACpDnF,YAAA,CAAiCY,kBAAA;IA5H7CF,OAAA,EAAAC,QAAA,CA4HqB,MAAc,CAAdX,YAAA,CAAcoF,qBAAA,E;IA5HnCpE,CAAA;MAAAC,gBAAA,CA4H6C,GACjC,GAAAC,gBAAA,CAAGd,MAAA,CAAAiF,kBAAkB,iB,wBA7HjCvD,mBAAA,e,GAmIIA,mBAAA,UAAa,EACsB1B,MAAA,CAAAK,SAAS,IAAIL,MAAA,CAAAkE,MAAM,CAACC,MAAM,Q,cAA7D5E,mBAAA,CAiBM,OAjBN2F,WAiBM,GAhBJzF,mBAAA,CAeM,OAfN0F,WAeM,I,kBAdJ5F,mBAAA,CAaM6D,SAAA,QAnJdgC,WAAA,CAuImCpF,MAAA,CAAAqF,iBAAiB,EAvIpD,CAuIkBC,KAAK,EAAEC,KAAK;yBADtBhG,mBAAA,CAaM;MAXHD,GAAG,EAAEgG,KAAK,CAACpD,WAAW;MACvB7C,KAAK,EAzIfsC,eAAA,EAyIgB,gBAAgB;QAAA,UACF4D,KAAK,KAAKvF,MAAA,CAAA+D;MAAiB;MAC9C7D,OAAK,EAAAsB,MAAA,IAAExB,MAAA,CAAAwF,WAAW,CAACD,KAAK;QAEzB9F,mBAAA,CAIE;MAHCsC,GAAG,EAAE/B,MAAA,CAAAgC,gBAAgB,CAACsD,KAAK;MAC3BrD,GAAG,SAASqD,KAAK,CAACpD,WAAW;MAC9B7C,KAAK,EAAC;4BAhJlBoG,WAAA,GAkJUhG,mBAAA,CAA0D,OAA1DiG,WAA0D,EAAA5E,gBAAA,CAA1BwE,KAAK,CAACpD,WAAW,iB,yBAlJ3DyD,WAAA;wCAAAjE,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}