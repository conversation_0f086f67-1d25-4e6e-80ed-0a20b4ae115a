@echo off
echo ========================================
echo 四方向智能交通分析系统启动测试
echo ========================================

echo.
echo 1. 检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo 错误: Java未安装或未配置到PATH
    pause
    exit /b 1
)

echo.
echo 2. 检查Maven版本...
mvn -version
if %errorlevel% neq 0 (
    echo 错误: Maven未安装或未配置到PATH
    pause
    exit /b 1
)

echo.
echo 3. 编译项目...
mvn clean compile -q
if %errorlevel% neq 0 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo 4. 运行测试...
mvn test -q
if %errorlevel% neq 0 (
    echo 警告: 测试执行失败，但继续启动
)

echo.
echo 5. 打包项目...
mvn package -DskipTests -q
if %errorlevel% neq 0 (
    echo 错误: 项目打包失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 编译和打包成功完成！
echo ========================================
echo.
echo 生成的JAR文件位置:
echo target\traffic-system-1.0.0.jar
echo.
echo 要启动应用程序，请运行:
echo java -jar target\traffic-system-1.0.0.jar
echo.
echo 或者运行:
echo mvn spring-boot:run
echo.
echo 应用程序将在以下地址启动:
echo http://localhost:8080
echo.
pause
