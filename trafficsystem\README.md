# 交通分析系统

本项目是一个基于Spring Boot和Python的交通视频分析系统，可以对交通视频进行智能分析，识别车辆并生成报告。

## 项目结构

```
trafficsystem/
├── src/                    # Java源代码
│   └── main/
│       ├── java/com/traffic/analysis/  # 核心代码
│       │   ├── controller/     # 控制器
│       │   ├── service/        # 服务层
│       │   ├── model/          # 数据模型
│       │   ├── entity/         # 实体类
│       │   ├── repository/     # 数据访问
│       │   ├── config/         # 配置类
│       │   ├── security/       # 安全相关
│       │   └── utils/          # 工具类
│       └── resources/
│           ├── static/         # 静态资源
│           ├── templates/      # Thymeleaf模板
│           └── application.properties # 应用配置
├── python_model/          # Python模型服务
├── uploads/               # 上传文件目录
├── logs/                  # 日志目录
├── pom.xml                # Maven配置
└── start_application.bat  # 启动脚本
```

## 最近的改动

本项目最近进行了目录结构整合，将原本分散在`src/`和`traffic-web/`两个目录下的代码合并到一个统一的结构中。主要更改包括：

1. 将`traffic-web/src/main/java`中的代码迁移到`src/main/java`
2. 合并配置文件，保证所有设置正确
3. 更新pom.xml以使用单一项目结构
4. 创建统一的启动脚本

这些更改解决了由于目录结构混乱导致的组件扫描问题和API路径映射失败问题，特别是`/api/video-analysis/upload/direct`端点现在可以正常工作。

## 如何启动

1. 确保已安装Maven和Python环境
2. 配置MongoDB数据库（见配置文件）
3. 运行`start_application.bat`脚本启动所有服务
4. 在浏览器访问`http://localhost:8080`使用系统

## 主要功能

- 视频上传和分析
- 车辆检测和分类
- 分析结果展示
- 历史记录管理
- 用户认证与授权

## 系统架构

交通分析系统由三个主要组件构成：

1. **Java后端服务**：Spring Boot应用，提供Web界面和用户请求处理
2. **Python模型服务**：基于Flask的API服务，负责图像处理和车辆检测
3. **MongoDB数据库**：存储用户信息和分析结果

## 功能特性

- **图像分析**：上传交通图片，自动检测和标记车辆
- **结果展示**：显示分析结果，包括车辆数量、类型和位置
- **历史记录**：保存分析历史，支持查看和管理历史记录
- **用户管理**：支持用户登录和权限控制

## 数据库配置

系统使用MongoDB存储数据，配置如下：

- **数据库名称**：`traffic_analysis`
- **数据库地址**：`127.0.0.1:27017`（本地MongoDB）
- **主要集合**：
  - `users`：存储用户信息
  - `analysis_results`：存储分析结果
  - `analysis_history`：存储分析历史记录

## 安装与启动

### 1. 安装依赖

#### Java依赖
```bash
mvn install
```

#### Python依赖
```bash
cd traffic-web/python_model
pip install -r requirements.txt
```

### 2. 启动MongoDB
```bash
# Windows
mongod --dbpath=<数据存储路径>

# 如果配置了配置文件
mongod -f <配置文件路径>
```

### 3. 启动服务

#### 使用启动脚本（推荐）
```bash
start_services.bat
```

#### 手动启动各服务
启动Python API服务：
```bash
cd traffic-web/python_model
python api_controller.py
```

启动Java后端服务：
```bash
cd traffic-web
mvn spring-boot:run
```

### 4. 访问系统

浏览器访问：http://localhost:8080

## API接口

### 模型API (端口5001)
- `GET /health` - 健康检查
- `GET /status` - 获取模型状态
- `POST /analyze` - 分析图片

### 数据API (端口5000)
- `POST /api/auth/register` - 注册用户
- `POST /api/auth/login` - 用户登录
- `POST /api/analyze` - 分析图片
- `GET /api/history` - 获取分析历史
- `GET /api/result/<id>` - 获取分析结果详情

## 故障排除

### MongoDB连接问题
- 确保MongoDB服务正在运行
- 使用`127.0.0.1`而非`localhost`作为连接地址
- 确认端口`27017`未被占用

### API服务无法启动
- 检查Python版本（推荐3.8+）
- 确保所有依赖已正确安装
- 检查端口`5000`和`5001`是否被占用

### Java服务问题
- 检查Maven依赖是否正确安装
- 确保MongoDB连接配置正确
- 检查日志文件了解详细错误

## 技术栈

- **前端**：Thymeleaf, Bootstrap, jQuery
- **后端**：Spring Boot, Flask
- **数据库**：MongoDB
- **AI模型**：YOLOv11x 目标检测模型

## 开发与扩展

1. **添加新的检测类别**：
   - 修改`config.py`中的类别配置
   - 更新`car_detection.py`中的模型参数

2. **优化分析算法**：
   - 修改`model_api.py`中的推理逻辑
   - 调整置信度阈值以提高准确性

3. **添加新功能**：
   - 在Java后端添加新的控制器和服务
   - 在Python API中添加新的端点
   - 更新前端模板 