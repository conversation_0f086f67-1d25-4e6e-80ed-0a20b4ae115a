<template>
  <div class="intelligent-traffic-report">
    <!-- 报告头部 -->
    <div class="report-header">
      <div class="header-content">
        <h1 class="report-title">
          <el-icon><DataAnalysis /></el-icon>
          四方向智能交通分析报告
        </h1>
        <div class="report-meta">
          <el-tag type="info">任务ID: {{ taskId }}</el-tag>
          <el-tag type="success">{{ formatDate(reportData.generatedAt) }}</el-tag>
          <el-tag>{{ reportData.analysisType }}</el-tag>
        </div>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="exportReport" :loading="exporting">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
        <el-button @click="refreshData" :loading="refreshing">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 执行摘要 -->
    <div class="executive-summary">
      <h2>执行摘要</h2>
      <div class="summary-grid">
        <div class="summary-card highlight">
          <div class="card-icon">🚗</div>
          <div class="card-content">
            <div class="card-value">{{ reportData.summary.totalVehicles }}</div>
            <div class="card-label">总检测车辆</div>
            <div class="card-change positive">+{{ reportData.summary.vehicleIncrease }}%</div>
          </div>
        </div>
        
        <div class="summary-card">
          <div class="card-icon">⏱️</div>
          <div class="card-content">
            <div class="card-value">{{ formatDuration(reportData.summary.processingDuration) }}</div>
            <div class="card-label">分析时长</div>
            <div class="card-change">{{ reportData.summary.efficiency }}% 效率</div>
          </div>
        </div>
        
        <div class="summary-card">
          <div class="card-icon">📍</div>
          <div class="card-content">
            <div class="card-value">{{ reportData.summary.peakDirection }}</div>
            <div class="card-label">最繁忙方向</div>
            <div class="card-change">{{ reportData.summary.peakPercentage }}% 占比</div>
          </div>
        </div>
        
        <div class="summary-card">
          <div class="card-icon">🚨</div>
          <div class="card-content">
            <div class="card-value">{{ reportData.summary.congestionLevel }}</div>
            <div class="card-label">拥堵等级</div>
            <div class="card-change" :class="getCongestionClass()">
              {{ reportData.summary.congestionTrend }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 方向详细分析 -->
    <div class="direction-analysis">
      <h2>方向详细分析</h2>
      <div class="direction-grid">
        <div 
          v-for="(data, direction) in reportData.directions" 
          :key="direction"
          class="direction-card"
        >
          <div class="direction-header">
            <el-icon>
              <component :is="getDirectionIcon(direction)" />
            </el-icon>
            <h3>{{ getDirectionName(direction) }}</h3>
            <el-tag :type="getDirectionStatusType(data.status)">
              {{ data.status }}
            </el-tag>
          </div>
          
          <div class="direction-stats">
            <div class="stat-row">
              <span class="stat-label">检测车辆:</span>
              <span class="stat-value">{{ data.vehicleCount }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">平均速度:</span>
              <span class="stat-value">{{ data.averageSpeed }} km/h</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">流量密度:</span>
              <span class="stat-value">{{ data.density }} 辆/km</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">拥堵指数:</span>
              <span class="stat-value" :class="getCongestionIndexClass(data.congestionIndex)">
                {{ data.congestionIndex }}
              </span>
            </div>
          </div>
          
          <div class="direction-chart">
            <div :ref="`chart_${direction}`" class="mini-chart"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能分析结果 -->
    <div class="intelligent-analysis">
      <h2>智能分析结果</h2>
      
      <!-- 交通流量分析 -->
      <div class="analysis-section">
        <h3>交通流量分析</h3>
        <div class="flow-analysis">
          <div class="flow-chart-container">
            <div ref="flowChart" class="flow-chart"></div>
          </div>
          <div class="flow-insights">
            <div class="insight-item">
              <strong>流量平衡度:</strong> {{ reportData.intelligentAnalysis.flowBalance }}%
              <el-progress :percentage="reportData.intelligentAnalysis.flowBalance" :show-text="false" />
            </div>
            <div class="insight-item">
              <strong>峰值时段:</strong> {{ reportData.intelligentAnalysis.peakHours }}
            </div>
            <div class="insight-item">
              <strong>流量趋势:</strong> {{ reportData.intelligentAnalysis.flowTrend }}
            </div>
          </div>
        </div>
      </div>

      <!-- 拥堵分析 -->
      <div class="analysis-section">
        <h3>拥堵分析</h3>
        <div class="congestion-analysis">
          <div class="congestion-heatmap">
            <div ref="heatmapChart" class="heatmap-chart"></div>
          </div>
          <div class="congestion-insights">
            <el-alert
              :title="`当前拥堵等级: ${reportData.intelligentAnalysis.congestionLevel}`"
              :type="getCongestionAlertType()"
              :description="reportData.intelligentAnalysis.congestionDescription"
              show-icon
              :closable="false"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 优化建议 -->
    <div class="optimization-recommendations">
      <h2>优化建议</h2>
      <div class="recommendations-list">
        <div 
          v-for="(recommendation, index) in reportData.recommendations" 
          :key="index"
          class="recommendation-item"
          :class="recommendation.priority"
        >
          <div class="recommendation-header">
            <el-icon>
              <component :is="getRecommendationIcon(recommendation.type)" />
            </el-icon>
            <h4>{{ recommendation.title }}</h4>
            <el-tag :type="getPriorityType(recommendation.priority)" size="small">
              {{ getPriorityText(recommendation.priority) }}
            </el-tag>
          </div>
          <p class="recommendation-description">{{ recommendation.description }}</p>
          <div class="recommendation-impact">
            <span class="impact-label">预期效果:</span>
            <span class="impact-value">{{ recommendation.expectedImprovement }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 技术指标 -->
    <div class="technical-metrics">
      <h2>技术指标</h2>
      <div class="metrics-grid">
        <div class="metric-card">
          <h4>检测精度</h4>
          <div class="metric-value">{{ reportData.technicalMetrics.accuracy }}%</div>
          <el-progress :percentage="reportData.technicalMetrics.accuracy" :show-text="false" />
        </div>
        <div class="metric-card">
          <h4>处理速度</h4>
          <div class="metric-value">{{ reportData.technicalMetrics.processingSpeed }} FPS</div>
          <el-progress :percentage="getSpeedPercentage()" :show-text="false" />
        </div>
        <div class="metric-card">
          <h4>系统稳定性</h4>
          <div class="metric-value">{{ reportData.technicalMetrics.stability }}%</div>
          <el-progress :percentage="reportData.technicalMetrics.stability" :show-text="false" />
        </div>
        <div class="metric-card">
          <h4>数据完整性</h4>
          <div class="metric-value">{{ reportData.technicalMetrics.dataIntegrity }}%</div>
          <el-progress :percentage="reportData.technicalMetrics.dataIntegrity" :show-text="false" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DataAnalysis, Download, Refresh, Top, Bottom, 
  ArrowLeft as Back, Right, Setting, Warning, 
  CircleCheck, InfoFilled
} from '@element-plus/icons-vue'

export default {
  name: 'IntelligentTrafficReport',
  components: {
    DataAnalysis, Download, Refresh, Top, Bottom, 
    Back, Right, Setting, Warning, CircleCheck, InfoFilled
  },
  props: {
    taskId: {
      type: String,
      required: true
    },
    reportData: {
      type: Object,
      default: () => ({
        generatedAt: new Date(),
        analysisType: '四方向智能分析',
        summary: {
          totalVehicles: 0,
          vehicleIncrease: 0,
          processingDuration: 0,
          efficiency: 0,
          peakDirection: '未知',
          peakPercentage: 0,
          congestionLevel: '畅通',
          congestionTrend: '稳定'
        },
        directions: {},
        intelligentAnalysis: {},
        recommendations: [],
        technicalMetrics: {}
      })
    }
  },
  emits: ['export-report', 'refresh-data'],
  setup(props, { emit }) {
    // 响应式数据
    const exporting = ref(false)
    const refreshing = ref(false)
    
    // 图表引用
    const flowChart = ref(null)
    const heatmapChart = ref(null)
    
    // 方法
    const formatDate = (date) => {
      return new Date(date).toLocaleString('zh-CN')
    }
    
    const formatDuration = (seconds) => {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}分${remainingSeconds}秒`
    }
    
    const getDirectionIcon = (direction) => {
      const icons = {
        east: Right,
        south: Bottom,
        west: Back,
        north: Top
      }
      return icons[direction] || InfoFilled
    }
    
    const getDirectionName = (direction) => {
      const names = {
        east: '东向',
        south: '南向',
        west: '西向',
        north: '北向'
      }
      return names[direction] || direction
    }
    
    const getDirectionStatusType = (status) => {
      const types = {
        'completed': 'success',
        'processing': 'warning',
        'error': 'danger',
        'waiting': 'info'
      }
      return types[status] || 'info'
    }
    
    const getCongestionClass = () => {
      const level = props.reportData.summary.congestionLevel
      if (level.includes('严重')) return 'negative'
      if (level.includes('中度')) return 'warning'
      if (level.includes('轻度')) return 'caution'
      return 'positive'
    }
    
    const getCongestionIndexClass = (index) => {
      if (index >= 0.8) return 'high-congestion'
      if (index >= 0.5) return 'medium-congestion'
      if (index >= 0.3) return 'low-congestion'
      return 'no-congestion'
    }
    
    const getCongestionAlertType = () => {
      const level = props.reportData.intelligentAnalysis.congestionLevel
      if (level?.includes('严重')) return 'error'
      if (level?.includes('中度')) return 'warning'
      if (level?.includes('轻度')) return 'info'
      return 'success'
    }
    
    const getRecommendationIcon = (type) => {
      const icons = {
        'signal': Setting,
        'infrastructure': Warning,
        'management': CircleCheck,
        'technology': InfoFilled
      }
      return icons[type] || InfoFilled
    }
    
    const getPriorityType = (priority) => {
      const types = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'info'
      }
      return types[priority] || 'info'
    }
    
    const getPriorityText = (priority) => {
      const texts = {
        'high': '高优先级',
        'medium': '中优先级',
        'low': '低优先级'
      }
      return texts[priority] || '未知'
    }
    
    const getSpeedPercentage = () => {
      const speed = props.reportData.technicalMetrics.processingSpeed || 0
      return Math.min(100, (speed / 30) * 100) // 假设30 FPS为满分
    }
    
    const exportReport = async () => {
      exporting.value = true
      try {
        emit('export-report', props.taskId)
        ElMessage.success('报告导出成功')
      } catch (error) {
        ElMessage.error('报告导出失败: ' + error.message)
      } finally {
        exporting.value = false
      }
    }
    
    const refreshData = async () => {
      refreshing.value = true
      try {
        emit('refresh-data', props.taskId)
        ElMessage.success('数据刷新成功')
      } catch (error) {
        ElMessage.error('数据刷新失败: ' + error.message)
      } finally {
        refreshing.value = false
      }
    }
    
    // 初始化图表
    const initializeCharts = () => {
      nextTick(() => {
        // 这里可以集成 ECharts 或其他图表库
        console.log('初始化图表...')
      })
    }
    
    onMounted(() => {
      initializeCharts()
    })
    
    return {
      // 响应式数据
      exporting,
      refreshing,
      flowChart,
      heatmapChart,
      
      // 方法
      formatDate,
      formatDuration,
      getDirectionIcon,
      getDirectionName,
      getDirectionStatusType,
      getCongestionClass,
      getCongestionIndexClass,
      getCongestionAlertType,
      getRecommendationIcon,
      getPriorityType,
      getPriorityText,
      getSpeedPercentage,
      exportReport,
      refreshData
    }
  }
}
</script>

<style scoped>
.intelligent-traffic-report {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: #ffffff;
}

/* 报告头部 */
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 2px solid #e5e7eb;
}

.header-content {
  flex: 1;
}

.report-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 12px 0;
}

.report-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 执行摘要 */
.executive-summary {
  margin-bottom: 32px;
}

.executive-summary h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.summary-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.summary-card.highlight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.card-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
}

.summary-card:not(.highlight) .card-icon {
  background: #f3f4f6;
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 4px;
}

.card-change {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
}

.summary-card:not(.highlight) .card-change {
  background: #f3f4f6;
  color: #6b7280;
}

.card-change.positive {
  background: #dcfce7;
  color: #16a34a;
}

.card-change.negative {
  background: #fee2e2;
  color: #dc2626;
}

.card-change.warning {
  background: #fef3c7;
  color: #d97706;
}

/* 方向分析 */
.direction-analysis {
  margin-bottom: 32px;
}

.direction-analysis h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.direction-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.direction-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.direction-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.direction-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f3f4f6;
}

.direction-header h3 {
  flex: 1;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.direction-stats {
  margin-bottom: 16px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f9fafb;
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.stat-value {
  font-weight: 600;
  color: #1f2937;
}

.stat-value.high-congestion {
  color: #dc2626;
}

.stat-value.medium-congestion {
  color: #d97706;
}

.stat-value.low-congestion {
  color: #059669;
}

.stat-value.no-congestion {
  color: #10b981;
}

.direction-chart {
  height: 120px;
  background: #f9fafb;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.mini-chart {
  width: 100%;
  height: 100%;
}

/* 智能分析 */
.intelligent-analysis {
  margin-bottom: 32px;
}

.intelligent-analysis h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.analysis-section {
  margin-bottom: 24px;
  background: #f9fafb;
  border-radius: 12px;
  padding: 20px;
}

.analysis-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.flow-analysis {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  align-items: start;
}

.flow-chart-container {
  background: white;
  border-radius: 8px;
  padding: 16px;
  height: 300px;
}

.flow-chart {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.flow-insights {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-item {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.insight-item strong {
  display: block;
  margin-bottom: 8px;
  color: #1f2937;
}

.congestion-analysis {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  align-items: start;
}

.congestion-heatmap {
  background: white;
  border-radius: 8px;
  padding: 16px;
  height: 250px;
}

.heatmap-chart {
  width: 100%;
  height: 100%;
  background: #f3f4f6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
}

.congestion-insights {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 优化建议 */
.optimization-recommendations {
  margin-bottom: 32px;
}

.optimization-recommendations h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-item {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.recommendation-item.high {
  border-left: 4px solid #dc2626;
}

.recommendation-item.medium {
  border-left: 4px solid #d97706;
}

.recommendation-item.low {
  border-left: 4px solid #059669;
}

.recommendation-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.recommendation-header h4 {
  flex: 1;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.recommendation-description {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12px;
}

.recommendation-impact {
  display: flex;
  align-items: center;
  gap: 8px;
}

.impact-label {
  font-size: 14px;
  color: #6b7280;
}

.impact-value {
  font-weight: 600;
  color: #059669;
}

/* 技术指标 */
.technical-metrics h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metric-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-card h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .intelligent-traffic-report {
    padding: 16px;
  }

  .report-header {
    flex-direction: column;
    gap: 16px;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .direction-grid {
    grid-template-columns: 1fr;
  }

  .flow-analysis {
    grid-template-columns: 1fr;
  }

  .congestion-analysis {
    grid-template-columns: 1fr;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
