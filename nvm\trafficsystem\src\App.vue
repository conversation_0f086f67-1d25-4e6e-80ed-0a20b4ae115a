<template>
  <div id="app">
    <navbar v-if="$route.meta.requiresAuth" />
    
    <notification 
      v-if="notification"
      :type="notification.type"
      :message="notification.message"
      @close="clearNotification"
    />
    
    <main>
      <router-view />
    </main>
    
    <app-footer />
  </div>
</template>

<script>
import { computed, onMounted } from 'vue';
import { useStore } from 'vuex';
import Navbar from '@/components/common/Navbar.vue';
import AppFooter from '@/components/common/Footer.vue';
import Notification from '@/components/common/Notification.vue';
import apiClient from '@/utils/http-common';

export default {
  name: 'App',
  components: {
    Navbar,
    AppFooter,
    Notification
  },
  setup() {
    const store = useStore();
    
    const notification = computed(() => store.state.notification);
    
    const clearNotification = () => {
      store.commit('CLEAR_NOTIFICATION');
    };
    
    onMounted(() => {
      const token = localStorage.getItem('auth_token');
      if (token) {
        store.commit('setAuthToken', token);
        apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        try {
          store.dispatch('fetchUserProfile');
        } catch (error) {

        }
      }
    });
    
    return {
      notification,
      clearNotification
    };
  }
};
</script>

<style>
body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}
</style>
