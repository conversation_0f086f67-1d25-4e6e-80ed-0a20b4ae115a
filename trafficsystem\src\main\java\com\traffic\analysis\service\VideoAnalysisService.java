package com.traffic.analysis.service;

import com.traffic.analysis.model.VideoAnalysis;
import com.traffic.analysis.model.FourWayIntersectionAnalysis;
import com.traffic.analysis.model.Direction;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 视频分析服务接口
 * 提供视频分析相关的服务
 */
public interface VideoAnalysisService {

    /**
     * 保存视频分析任务
     * @param videoAnalysis 视频分析任务
     * @return 保存后的视频分析任务
     */
    VideoAnalysis saveVideoAnalysis(VideoAnalysis videoAnalysis);

    /**
     * 通过ID查找视频分析任务
     * @param id 视频分析任务ID
     * @return 视频分析任务
     */
    Optional<VideoAnalysis> findById(String id);

    /**
     * 通过任务ID查找视频分析任务
     * @param taskId 任务ID
     * @return 视频分析任务
     */
    Optional<VideoAnalysis> findByTaskId(String taskId);

    /**
     * 获取用户的视频分析任务列表
     * 根据用户角色决定返回全部任务或仅自己的任务
     * @param userId 用户ID
     * @param role 用户角色
     * @param pageable 分页参数
     * @return 用户视频分析任务列表
     */
    Page<VideoAnalysis> findByUserIdAndRole(String userId, String role, Pageable pageable);
    
    /**
     * 根据方向和角色查询视频分析记录
     * @param direction 方向
     * @param role 角色
     * @param pageable 分页参数
     * @return 视频分析记录
     */
    Page<VideoAnalysis> findByDirectionAndRole(String direction, String role, Pageable pageable);
    
    /**
     * 根据用户ID、方向和角色查询视频分析记录
     * @param userId 用户ID
     * @param direction 方向
     * @param role 角色
     * @param pageable 分页参数
     * @return 视频分析记录
     */
    Page<VideoAnalysis> findByUserIdDirectionAndRole(String userId, String direction, String role, Pageable pageable);
    
    /**
     * 上传并分析视频
     * @param videoFile 视频文件
     * @param userId 用户ID
     * @param username 用户名
     * @param role 用户角色
     * @param direction 方向(horizontal/vertical/intersection)
     * @return 视频分析任务
     * @throws IOException 文件操作异常
     */
    VideoAnalysis uploadAndAnalyzeVideo(MultipartFile videoFile, String userId, String username, String role, String direction) throws IOException;
    
    /**
     * 处理十字路口视频分析
     * @param horizontalFile 横向视频文件
     * @param verticalFile 纵向视频文件
     * @param userId 用户ID
     * @param username 用户名
     * @param role 用户角色
     * @return 视频分析任务
     * @throws IOException 文件操作异常
     */
    VideoAnalysis processIntersectionVideos(MultipartFile horizontalFile, MultipartFile verticalFile, String userId, String username, String role) throws IOException;
    
    /**
     * 更新视频分析任务进度
     * @param taskId 任务ID
     * @param progress 进度(0-100)
     * @return 更新后的视频分析任务
     */
    VideoAnalysis updateProgress(String taskId, int progress);
    
    /**
     * 更新视频分析任务状态
     * @param taskId 任务ID
     * @param status 状态
     * @param message 状态消息
     * @return 更新后的视频分析任务
     */
    VideoAnalysis updateStatus(String taskId, String status, String message);
    
    /**
     * 完成视频分析任务并保存结果
     * @param taskId 任务ID
     * @param resultData 结果数据
     * @return 更新后的视频分析任务
     */
    VideoAnalysis completeAnalysis(String taskId, Map<String, Object> resultData);
    
    /**
     * 获取视频分析结果
     * @param resultId 结果ID
     * @return 视频分析结果数据
     */
    Map<String, Object> getVideoAnalysisResult(String resultId);
    
    /**
     * 重新分析视频
     * @param taskId 任务ID
     * @return 重新分析的视频分析任务
     */
    VideoAnalysis retryAnalysis(String taskId);
    
    /**
     * 删除视频分析任务
     * @param taskId 任务ID
     * @param userId 当前用户ID
     * @param role 当前用户角色
     * @return 是否删除成功
     */
    boolean deleteAnalysis(String taskId, String userId, String role);
    
    /**
     * 重命名视频分析任务
     * @param taskId 任务ID
     * @param videoName 新的视频名称
     * @return 是否重命名成功
     */
    boolean renameVideo(String taskId, String videoName);

    // ==================== 四方向交通分析相关方法 ====================

    /**
     * 处理四方向十字路口视频分析
     * @param eastFile 东向视频文件
     * @param southFile 南向视频文件
     * @param westFile 西向视频文件
     * @param northFile 北向视频文件
     * @param userId 用户ID
     * @param username 用户名
     * @param role 用户角色
     * @return 四方向分析任务
     * @throws IOException 文件操作异常
     */
    FourWayIntersectionAnalysis processFourWayIntersectionVideos(
            MultipartFile eastFile,
            MultipartFile southFile,
            MultipartFile westFile,
            MultipartFile northFile,
            String userId,
            String username,
            String role) throws IOException;

    /**
     * 根据任务ID查找四方向分析任务
     * @param taskId 任务ID
     * @return 四方向分析任务
     */
    Optional<FourWayIntersectionAnalysis> findFourWayAnalysisByTaskId(String taskId);

    /**
     * 更新四方向分析任务状态
     * @param taskId 任务ID
     * @param status 状态
     * @param message 状态消息
     * @return 更新后的四方向分析任务
     */
    FourWayIntersectionAnalysis updateFourWayAnalysisStatus(String taskId, String status, String message);

    /**
     * 更新指定方向的处理进度
     * @param taskId 任务ID
     * @param direction 方向
     * @param progress 进度(0-100)
     * @param status 状态
     * @return 更新后的四方向分析任务
     */
    FourWayIntersectionAnalysis updateDirectionProgress(
            String taskId,
            Direction direction,
            int progress,
            String status);

    /**
     * 完成四方向分析并生成智能交通分析结果
     * @param taskId 任务ID
     * @param directionsResults 各方向的检测结果
     * @return 完成的四方向分析任务
     */
    FourWayIntersectionAnalysis completeFourWayAnalysis(
            String taskId,
            Map<Direction, Map<String, Object>> directionsResults);

    /**
     * 获取四方向分析结果
     * @param taskId 任务ID
     * @return 四方向分析结果数据
     */
    Map<String, Object> getFourWayAnalysisResult(String taskId);

    /**
     * 生成四方向智能交通分析报告
     * @param taskId 任务ID
     * @return 报告数据
     */
    Map<String, Object> generateFourWayTrafficReport(String taskId);

    // ==================== 扩展的四方向分析方法 ====================

    /**
     * 获取四方向任务列表
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 四方向任务列表
     */
    Page<FourWayIntersectionAnalysis> getFourWayTaskList(String userId, Pageable pageable);

    /**
     * 获取四方向任务列表（支持筛选）
     * @param queryParams 查询参数
     * @param pageable 分页参数
     * @return 四方向任务列表
     */
    Page<FourWayIntersectionAnalysis> getFourWayTaskListWithFilters(Map<String, Object> queryParams, Pageable pageable);

    /**
     * 删除四方向分析任务
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteFourWayTask(String taskId, String userId);

    /**
     * 批量删除四方向分析任务
     * @param taskIds 任务ID列表
     * @param userId 用户ID
     * @param userRole 用户角色
     * @return 删除的任务数量
     */
    int batchDeleteFourWayTasks(List<String> taskIds, String userId, String userRole);

    /**
     * 重新分析四方向视频
     * @param taskId 任务ID
     * @return 重新分析的任务
     */
    FourWayIntersectionAnalysis retryFourWayAnalysis(String taskId);

    /**
     * 获取四方向实时检测数据
     * @param taskId 任务ID
     * @return 实时检测数据
     */
    Map<String, Object> getFourWayRealtimeData(String taskId);

    /**
     * 获取四方向智能分析建议
     * @param taskId 任务ID
     * @return 智能分析建议
     */
    Map<String, Object> getFourWayIntelligentRecommendations(String taskId);

    /**
     * 更新四方向任务配置
     * @param taskId 任务ID
     * @param config 配置参数
     * @return 更新后的任务
     */
    FourWayIntersectionAnalysis updateFourWayTaskConfig(String taskId, Map<String, Object> config);

    /**
     * 获取四方向系统状态
     * @return 系统状态信息
     */
    Map<String, Object> getFourWaySystemStatus();

    /**
     * 获取四方向统计数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计数据
     */
    Map<String, Object> getFourWayStatistics(String startTime, String endTime);

    /**
     * 导出四方向分析数据
     * @param taskId 任务ID
     * @param format 导出格式 (pdf, excel, json)
     * @return 导出文件路径
     */
    String exportFourWayAnalysisData(String taskId, String format);

    /**
     * 获取四方向分析历史记录
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 历史记录
     */
    Page<FourWayIntersectionAnalysis> getFourWayAnalysisHistory(String userId, Pageable pageable);



    /**
     * 获取四方向分析任务详情
     * @param taskId 任务ID
     * @return 任务详情
     */
    Map<String, Object> getFourWayTaskDetails(String taskId);

    /**
     * 暂停四方向分析任务
     * @param taskId 任务ID
     * @return 是否暂停成功
     */
    boolean pauseFourWayTask(String taskId);

    /**
     * 恢复四方向分析任务
     * @param taskId 任务ID
     * @return 是否恢复成功
     */
    boolean resumeFourWayTask(String taskId);

    /**
     * 取消四方向分析任务
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    boolean cancelFourWayTask(String taskId);

    /**
     * 重新启动特定方向的分析
     * @param taskId 任务ID
     * @param direction 方向名称
     * @return 是否重新启动成功
     */
    boolean restartDirectionAnalysis(String taskId, String direction);
}