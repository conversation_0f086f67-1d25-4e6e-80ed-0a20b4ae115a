# 四方向智能交通分析系统用户指南

## 目录

1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [功能详解](#功能详解)
4. [操作指南](#操作指南)
5. [常见问题](#常见问题)
6. [技术支持](#技术支持)

## 系统概述

四方向智能交通分析系统是一个基于AI视觉识别技术的交通流量分析平台，专门用于分析十字路口四个方向的交通状况。系统能够：

- 🚗 **智能车辆识别**: 自动识别和统计各类车辆（小汽车、卡车、公交车、摩托车）
- 📊 **流量分析**: 分析各方向的车流量分布和密度
- 🚦 **信号优化**: 提供智能信号灯配时优化建议
- 📈 **可视化报告**: 生成专业的交通分析报告和图表
- ⚡ **实时监控**: 支持实时视频处理和四画面同步显示

### 系统特点

- **高精度识别**: 基于YOLOv12x深度学习模型，识别准确率达95%以上
- **并行处理**: 支持四个方向视频同时处理，提高分析效率
- **智能分析**: 自动计算交通流量平衡度、拥堵等级等关键指标
- **用户友好**: 直观的Web界面，操作简单易懂

## 快速开始

### 第一步：登录系统

1. 打开浏览器，访问系统地址
2. 输入用户名和密码登录
3. 首次使用建议先阅读系统帮助信息

### 第二步：准备视频文件

在开始分析前，请准备好四个方向的视频文件：

**视频要求**：
- 格式：MP4、AVI、MOV
- 分辨率：建议720p或更高
- 大小：单个文件不超过500MB
- 时长：建议30秒到5分钟
- 拍摄角度：确保能清晰看到车辆行驶

**方向说明**：
- **东向**：从西向东行驶的车辆
- **南向**：从北向南行驶的车辆
- **西向**：从东向西行驶的车辆
- **北向**：从南向北行驶的车辆

### 第三步：开始分析

1. 点击"四方向智能交通分析"菜单
2. 选择"开始新分析"
3. 按照界面提示上传四个方向的视频文件
4. 点击"开始分析"按钮

## 功能详解

### 1. 视频上传功能

#### 上传界面说明

系统提供直观的四方向上传界面：

```
        北向
         ↑
西向 ← 十字路口 → 东向
         ↓
        南向
```

- 每个方向都有独立的上传区域
- 支持拖拽上传和点击选择文件
- 实时显示上传进度和文件信息

#### 上传注意事项

- 确保四个方向的视频是同一时间段拍摄
- 检查视频质量，避免模糊或光线不足
- 建议在交通高峰期拍摄，以获得更准确的分析结果

### 2. 实时预览功能

#### 四画面同步显示

上传完成后，系统会自动开始处理视频并提供实时预览：

- **四画面布局**：同时显示四个方向的检测结果
- **实时统计**：显示每个方向的车辆数量和类型
- **检测标注**：在视频中标出识别到的车辆
- **进度显示**：显示每个方向的处理进度

#### 预览控制

- **暂停/播放**：控制预览的播放状态
- **速度调节**：调整预览播放速度
- **全屏查看**：单独查看某个方向的详细检测结果

### 3. 智能分析仪表板

#### 数据概览

- **总车辆数**：四个方向的车辆总数
- **车流量分布**：各方向车辆数量对比
- **车辆类型统计**：不同类型车辆的数量分布
- **拥堵等级评估**：基于车流密度的拥堵程度

#### 可视化图表

- **方向对比图**：柱状图显示各方向车流量
- **车型分布图**：饼图显示车辆类型比例
- **流量热力图**：直观显示交通流量分布
- **时间趋势图**：显示车流量随时间的变化

#### 智能优化建议

- **信号配时优化**：推荐的信号灯周期和绿灯时间分配
- **交通改善建议**：基于分析结果的交通优化建议
- **预期效果评估**：优化方案的预期改善效果

### 4. 分析报告功能

#### 报告内容

系统自动生成专业的交通分析报告，包含：

- **执行摘要**：分析结果的关键指标总结
- **详细数据**：各方向的具体统计数据
- **可视化图表**：多种图表展示分析结果
- **优化建议**：专业的交通改善建议
- **技术说明**：分析方法和技术参数

#### 报告导出

- **在线查看**：在浏览器中直接查看HTML报告
- **PDF导出**：生成PDF格式的报告文件
- **数据导出**：导出JSON格式的原始数据

## 操作指南

### 新建分析任务

1. **进入分析页面**
   - 点击主菜单"四方向智能交通分析"
   - 选择"开始新分析"

2. **上传视频文件**
   - 按照方向标识上传对应的视频文件
   - 确认文件信息无误后点击"开始分析"

3. **等待处理完成**
   - 系统会显示处理进度
   - 可以选择查看实时预览或等待完成

### 查看分析结果

1. **实时预览**
   - 在处理过程中可以查看实时检测结果
   - 四画面同步显示，直观了解各方向情况

2. **分析仪表板**
   - 处理完成后查看详细的分析结果
   - 包含各种统计图表和智能分析

3. **生成报告**
   - 点击"生成报告"按钮
   - 查看或导出专业分析报告

### 历史记录管理

1. **查看历史任务**
   - 在主页面可以看到最近的分析记录
   - 点击"历史记录"查看所有任务

2. **重新查看结果**
   - 点击任务ID可以重新查看分析结果
   - 历史数据保留30天

## 常见问题

### Q1: 支持哪些视频格式？
**A**: 系统支持MP4、AVI、MOV格式的视频文件。推荐使用MP4格式以获得最佳兼容性。

### Q2: 视频文件大小有限制吗？
**A**: 单个视频文件最大500MB，四个文件总大小不超过2GB。如果文件过大，建议先进行压缩。

### Q3: 分析需要多长时间？
**A**: 处理时间取决于视频长度和复杂度，通常为5-30分钟。系统会显示实时进度。

### Q4: 可以同时处理多个任务吗？
**A**: 每个用户最多可以同时处理4个四方向分析任务。

### Q5: 分析结果的准确性如何？
**A**: 系统基于YOLOv12x深度学习模型，在良好的视频条件下识别准确率可达95%以上。

### Q6: 如何获得最佳的分析效果？
**A**: 
- 确保视频清晰，光线充足
- 拍摄角度能清楚看到车辆
- 选择交通流量较大的时间段
- 四个方向的视频应为同一时间段

### Q7: 系统支持哪些浏览器？
**A**: 支持Chrome、Firefox、Safari、Edge等现代浏览器。推荐使用Chrome以获得最佳体验。

### Q8: 数据会保存多久？
**A**: 分析结果保留30天，原始视频文件保留7天。建议及时导出重要数据。

## 技术支持

### 联系方式

- **技术支持邮箱**: <EMAIL>
- **用户手册**: 系统内置帮助文档
- **在线客服**: 工作日9:00-18:00

### 系统要求

**服务器端**:
- Java 11+
- MongoDB 4.4+
- Python 3.8+
- 推荐内存: 8GB+

**客户端**:
- 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+, Edge 80+）
- 网络带宽: 建议10Mbps以上
- 屏幕分辨率: 1280x720或更高

### 更新日志

**v1.0.0** (2024-01-19)
- 首次发布四方向智能交通分析功能
- 支持四方向视频同时上传和处理
- 实现实时四画面预览
- 提供智能分析仪表板和报告生成

---

*本用户指南会根据系统更新持续完善，如有疑问请联系技术支持团队。*
