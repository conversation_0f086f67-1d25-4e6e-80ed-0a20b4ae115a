import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

/**
 * 四方向交通分析系统错误处理工具
 * 统一处理前端错误和异常情况
 */

// 错误类型常量
export const ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  UPLOAD_ERROR: 'UPLOAD_ERROR',
  PROCESSING_ERROR: 'PROCESSING_ERROR',
  WEBSOCKET_ERROR: 'WEBSOCKET_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

// 错误消息映射
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_TYPES.UPLOAD_ERROR]: '视频上传失败，请重试',
  [ERROR_TYPES.PROCESSING_ERROR]: '视频处理失败，请稍后重试',
  [ERROR_TYPES.WEBSOCKET_ERROR]: '实时连接断开，正在尝试重连...',
  [ERROR_TYPES.VALIDATION_ERROR]: '输入数据验证失败',
  [ERROR_TYPES.PERMISSION_ERROR]: '权限不足，请联系管理员',
  [ERROR_TYPES.TIMEOUT_ERROR]: '请求超时，请重试',
  [ERROR_TYPES.UNKNOWN_ERROR]: '发生未知错误，请联系技术支持'
}

/**
 * 错误处理器类
 */
class ErrorHandler {
  constructor() {
    this.errorLog = []
    this.maxLogSize = 100
    this.retryAttempts = new Map()
    this.maxRetryAttempts = 3
  }

  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {Object} 处理结果
   */
  handleApiError(error, context = {}) {
    const errorInfo = this.parseError(error)
    this.logError(errorInfo, context)

    // 根据错误类型进行不同处理
    switch (errorInfo.type) {
      case ERROR_TYPES.NETWORK_ERROR:
        return this.handleNetworkError(errorInfo, context)
      
      case ERROR_TYPES.UPLOAD_ERROR:
        return this.handleUploadError(errorInfo, context)
      
      case ERROR_TYPES.PROCESSING_ERROR:
        return this.handleProcessingError(errorInfo, context)
      
      case ERROR_TYPES.PERMISSION_ERROR:
        return this.handlePermissionError(errorInfo, context)
      
      case ERROR_TYPES.TIMEOUT_ERROR:
        return this.handleTimeoutError(errorInfo, context)
      
      default:
        return this.handleGenericError(errorInfo, context)
    }
  }

  /**
   * 解析错误对象
   * @param {Error} error - 错误对象
   * @returns {Object} 解析后的错误信息
   */
  parseError(error) {
    let errorType = ERROR_TYPES.UNKNOWN_ERROR
    let message = error.message || '未知错误'
    let code = null
    let details = null

    if (error.response) {
      // HTTP错误响应
      const { status, data } = error.response
      code = status

      if (data && data.errorCode) {
        // 后端返回的结构化错误
        errorType = this.mapBackendErrorCode(data.errorCode)
        message = data.message || message
        details = data.details
      } else {
        // 根据HTTP状态码判断错误类型
        errorType = this.mapHttpStatusToErrorType(status)
      }
    } else if (error.code) {
      // 网络错误
      if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {
        errorType = ERROR_TYPES.NETWORK_ERROR
      } else if (error.code === 'TIMEOUT') {
        errorType = ERROR_TYPES.TIMEOUT_ERROR
      }
    }

    return {
      type: errorType,
      message,
      code,
      details,
      originalError: error,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 映射后端错误代码到前端错误类型
   * @param {string} errorCode - 后端错误代码
   * @returns {string} 前端错误类型
   */
  mapBackendErrorCode(errorCode) {
    const mapping = {
      'FOUR_WAY_ANALYSIS_ERROR': ERROR_TYPES.PROCESSING_ERROR,
      'VIDEO_UPLOAD_ERROR': ERROR_TYPES.UPLOAD_ERROR,
      'VIDEO_PROCESSING_ERROR': ERROR_TYPES.PROCESSING_ERROR,
      'TASK_NOT_FOUND': ERROR_TYPES.VALIDATION_ERROR,
      'FILE_SIZE_EXCEEDED': ERROR_TYPES.UPLOAD_ERROR,
      'MULTIPART_UPLOAD_ERROR': ERROR_TYPES.UPLOAD_ERROR,
      'IO_ERROR': ERROR_TYPES.PROCESSING_ERROR,
      'DATABASE_CONNECTION_ERROR': ERROR_TYPES.NETWORK_ERROR,
      'WEBSOCKET_CONNECTION_ERROR': ERROR_TYPES.WEBSOCKET_ERROR,
      'MODEL_SERVICE_ERROR': ERROR_TYPES.PROCESSING_ERROR
    }
    
    return mapping[errorCode] || ERROR_TYPES.UNKNOWN_ERROR
  }

  /**
   * 映射HTTP状态码到错误类型
   * @param {number} status - HTTP状态码
   * @returns {string} 错误类型
   */
  mapHttpStatusToErrorType(status) {
    if (status >= 400 && status < 500) {
      if (status === 401 || status === 403) {
        return ERROR_TYPES.PERMISSION_ERROR
      } else if (status === 413) {
        return ERROR_TYPES.UPLOAD_ERROR
      } else {
        return ERROR_TYPES.VALIDATION_ERROR
      }
    } else if (status >= 500) {
      return ERROR_TYPES.PROCESSING_ERROR
    } else {
      return ERROR_TYPES.NETWORK_ERROR
    }
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(errorInfo, context) {
    ElMessage.error({
      message: '网络连接失败，请检查网络设置后重试',
      duration: 5000
    })

    return {
      handled: true,
      shouldRetry: true,
      retryDelay: 3000
    }
  }

  /**
   * 处理上传错误
   */
  handleUploadError(errorInfo, context) {
    let message = '视频上传失败'
    
    if (errorInfo.details && errorInfo.details.direction) {
      const directionNames = {
        east: '东向',
        south: '南向', 
        west: '西向',
        north: '北向'
      }
      const directionName = directionNames[errorInfo.details.direction] || errorInfo.details.direction
      message = `${directionName}视频上传失败`
    }

    if (errorInfo.message.includes('大小超过限制')) {
      message += '，文件大小超过限制，请压缩后重试'
    }

    ElMessage.error({
      message,
      duration: 5000
    })

    return {
      handled: true,
      shouldRetry: false
    }
  }

  /**
   * 处理处理错误
   */
  handleProcessingError(errorInfo, context) {
    ElNotification.error({
      title: '处理失败',
      message: errorInfo.message || '视频处理失败，请稍后重试',
      duration: 8000
    })

    return {
      handled: true,
      shouldRetry: true,
      retryDelay: 5000
    }
  }

  /**
   * 处理权限错误
   */
  handlePermissionError(errorInfo, context) {
    ElMessageBox.alert(
      '您没有执行此操作的权限，请联系管理员',
      '权限不足',
      {
        confirmButtonText: '确定',
        type: 'warning'
      }
    )

    return {
      handled: true,
      shouldRetry: false,
      shouldRedirect: '/login'
    }
  }

  /**
   * 处理超时错误
   */
  handleTimeoutError(errorInfo, context) {
    ElMessage.warning({
      message: '请求超时，正在重试...',
      duration: 3000
    })

    return {
      handled: true,
      shouldRetry: true,
      retryDelay: 2000
    }
  }

  /**
   * 处理通用错误
   */
  handleGenericError(errorInfo, context) {
    ElMessage.error({
      message: errorInfo.message || ERROR_MESSAGES[ERROR_TYPES.UNKNOWN_ERROR],
      duration: 5000
    })

    return {
      handled: true,
      shouldRetry: false
    }
  }

  /**
   * 记录错误日志
   * @param {Object} errorInfo - 错误信息
   * @param {Object} context - 错误上下文
   */
  logError(errorInfo, context) {
    const logEntry = {
      ...errorInfo,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId()
    }

    this.errorLog.push(logEntry)

    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift()
    }

    // 在开发环境下打印详细错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error('Error logged:', logEntry)
    }

    // 发送错误日志到服务器（可选）
    this.sendErrorToServer(logEntry)
  }

  /**
   * 获取当前用户ID
   */
  getCurrentUserId() {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}')
      return user.id || 'anonymous'
    } catch {
      return 'anonymous'
    }
  }

  /**
   * 发送错误日志到服务器
   * @param {Object} logEntry - 日志条目
   */
  async sendErrorToServer(logEntry) {
    try {
      // 这里可以实现发送错误日志到服务器的逻辑
      // await api.post('/api/error-log', logEntry)
    } catch (error) {
      // 忽略日志发送失败
      console.warn('Failed to send error log to server:', error)
    }
  }

  /**
   * 获取错误日志
   * @returns {Array} 错误日志数组
   */
  getErrorLog() {
    return [...this.errorLog]
  }

  /**
   * 清空错误日志
   */
  clearErrorLog() {
    this.errorLog = []
  }

  /**
   * 检查是否应该重试
   * @param {string} key - 重试键
   * @returns {boolean} 是否应该重试
   */
  shouldRetry(key) {
    const attempts = this.retryAttempts.get(key) || 0
    return attempts < this.maxRetryAttempts
  }

  /**
   * 增加重试次数
   * @param {string} key - 重试键
   */
  incrementRetryAttempts(key) {
    const attempts = this.retryAttempts.get(key) || 0
    this.retryAttempts.set(key, attempts + 1)
  }

  /**
   * 重置重试次数
   * @param {string} key - 重试键
   */
  resetRetryAttempts(key) {
    this.retryAttempts.delete(key)
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler()

// 导出错误处理函数
export const handleError = (error, context) => {
  return errorHandler.handleApiError(error, context)
}

export const logError = (error, context) => {
  errorHandler.logError(error, context)
}

export const getErrorLog = () => {
  return errorHandler.getErrorLog()
}

export const clearErrorLog = () => {
  errorHandler.clearErrorLog()
}

export default errorHandler
