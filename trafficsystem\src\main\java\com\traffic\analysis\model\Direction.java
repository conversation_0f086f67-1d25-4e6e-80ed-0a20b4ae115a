package com.traffic.analysis.model;

/**
 * 交通方向枚举
 * 用于四方向交通分析
 */
public enum Direction {
    EAST("east", "东", "东向"),
    SOUTH("south", "南", "南向"), 
    WEST("west", "西", "西向"),
    NORTH("north", "北", "北向");
    
    private final String code;
    private final String chineseName;
    private final String displayName;
    
    Direction(String code, String chineseName, String displayName) {
        this.code = code;
        this.chineseName = chineseName;
        this.displayName = displayName;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getChineseName() {
        return chineseName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 根据代码获取方向
     */
    public static Direction fromCode(String code) {
        for (Direction direction : values()) {
            if (direction.code.equals(code)) {
                return direction;
            }
        }
        throw new IllegalArgumentException("未知的方向代码: " + code);
    }
    
    /**
     * 获取相对方向
     */
    public Direction getOpposite() {
        switch (this) {
            case EAST: return WEST;
            case WEST: return EAST;
            case SOUTH: return NORTH;
            case NORTH: return SOUTH;
            default: throw new IllegalStateException("未知方向: " + this);
        }
    }
    
    /**
     * 获取垂直方向
     */
    public Direction[] getPerpendicular() {
        switch (this) {
            case EAST:
            case WEST:
                return new Direction[]{NORTH, SOUTH};
            case NORTH:
            case SOUTH:
                return new Direction[]{EAST, WEST};
            default:
                throw new IllegalStateException("未知方向: " + this);
        }
    }
}
