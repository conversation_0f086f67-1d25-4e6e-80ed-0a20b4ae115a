<template>
  <div class="video-upload-area">
    <div 
      class="drop-area" 
      :class="{ 
        'drag-over': isDragOver, 
        'has-file': videoFile,
        'upload-error': uploadError 
      }"
      @click="triggerFileInput" 
      @drop.prevent="handleFileDrop" 
      @dragover.prevent
      @dragenter.prevent="onDragEnter"
      @dragleave.prevent="onDragLeave"
    >
      <!-- 无文件状态 -->
      <div v-if="!videoFile" class="empty-state">
        <div class="upload-icon-container">
          <el-icon size="48" class="upload-icon">
            <VideoCamera />
          </el-icon>
          <div class="upload-arrow">
            <el-icon size="24">
              <Upload />
            </el-icon>
          </div>
        </div>
        <p class="upload-title">
          拖拽{{ directionName }}视频到此处
        </p>
        <p class="upload-subtitle">或 <span class="click-upload">点击选择文件</span></p>
        <p class="upload-hint">支持 MP4, AVI, MOV 格式，最大 500MB</p>
      </div>

      <!-- 有文件状态 -->
      <div v-else class="file-preview">
        <div class="file-info">
          <el-icon size="32" class="file-icon">
            <VideoPlay />
          </el-icon>
          <div class="file-details">
            <p class="file-name">{{ videoFile.name }}</p>
            <p class="file-size">{{ formatFileSize(videoFile.size) }}</p>
            <p class="file-type">{{ getFileType(videoFile.name) }}</p>
          </div>
        </div>
        
        <!-- 视频预览 -->
        <div v-if="previewUrl" class="video-preview">
          <video 
            :src="previewUrl" 
            controls 
            preload="metadata"
            class="preview-video"
            @loadedmetadata="onVideoLoaded"
          ></video>
          <div class="video-info">
            <span v-if="videoDuration">时长: {{ formatDuration(videoDuration) }}</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="file-actions">
          <el-button 
            type="primary" 
            size="small" 
            @click.stop="triggerFileInput"
            class="replace-btn"
          >
            <el-icon><RefreshLeft /></el-icon>
            更换文件
          </el-button>
          <el-button 
            type="danger" 
            size="small" 
            @click.stop="removeFile"
            class="remove-btn"
          >
            <el-icon><Delete /></el-icon>
            移除
          </el-button>
        </div>
      </div>

      <!-- 上传错误状态 -->
      <div v-if="uploadError" class="error-message">
        <el-icon><WarningFilled /></el-icon>
        <span>{{ uploadError }}</span>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      type="file"
      ref="fileInput"
      style="display: none;"
      @change="handleFileChange"
      accept="video/mp4,video/avi,video/mov,video/quicktime"
    />
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  VideoCamera, Upload, VideoPlay, RefreshLeft, 
  Delete, WarningFilled 
} from '@element-plus/icons-vue'

export default {
  name: 'VideoUploadArea',
  components: {
    VideoCamera, Upload, VideoPlay, RefreshLeft,
    Delete, WarningFilled
  },
  props: {
    direction: {
      type: String,
      required: true
    },
    videoFile: {
      type: File,
      default: null
    },
    maxSize: {
      type: Number,
      default: 500 * 1024 * 1024 // 500MB
    }
  },
  emits: ['file-selected', 'file-removed'],
  setup(props, { emit }) {
    // 响应式数据
    const fileInput = ref(null)
    const isDragOver = ref(false)
    const uploadError = ref('')
    const previewUrl = ref('')
    const videoDuration = ref(0)
    
    // 计算属性
    const directionName = computed(() => {
      const names = {
        east: '东向',
        south: '南向',
        west: '西向', 
        north: '北向'
      }
      return names[props.direction] || props.direction
    })
    
    // 监听文件变化，生成预览URL
    watch(() => props.videoFile, (newFile) => {
      if (previewUrl.value) {
        URL.revokeObjectURL(previewUrl.value)
        previewUrl.value = ''
      }
      
      if (newFile) {
        previewUrl.value = URL.createObjectURL(newFile)
        uploadError.value = ''
      }
      
      videoDuration.value = 0
    })
    
    // 方法
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
    
    const getFileType = (filename) => {
      const ext = filename.split('.').pop().toUpperCase()
      return `${ext} 视频`
    }
    
    const formatDuration = (seconds) => {
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins}:${secs.toString().padStart(2, '0')}`
    }
    
    const validateFile = (file) => {
      // 检查文件类型
      const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/quicktime']
      if (!allowedTypes.includes(file.type)) {
        return '不支持的文件格式，请选择 MP4、AVI 或 MOV 格式的视频文件'
      }
      
      // 检查文件大小
      if (file.size > props.maxSize) {
        return `文件大小超过限制，最大支持 ${formatFileSize(props.maxSize)}`
      }
      
      return null
    }
    
    const triggerFileInput = () => {
      fileInput.value?.click()
    }
    
    const handleFileChange = (event) => {
      const file = event.target.files[0]
      if (file) {
        handleFileSelection(file)
      }
      // 清空input值，允许重复选择同一文件
      event.target.value = ''
    }
    
    const handleFileDrop = (event) => {
      isDragOver.value = false
      const files = event.dataTransfer.files
      if (files.length > 0) {
        handleFileSelection(files[0])
      }
    }
    
    const handleFileSelection = (file) => {
      const error = validateFile(file)
      if (error) {
        uploadError.value = error
        ElMessage.error(error)
        return
      }
      
      uploadError.value = ''
      emit('file-selected', props.direction, file)
      
      console.log(`${props.direction}方向视频文件已选择:`, {
        name: file.name,
        size: formatFileSize(file.size),
        type: file.type
      })
    }
    
    const removeFile = () => {
      if (previewUrl.value) {
        URL.revokeObjectURL(previewUrl.value)
        previewUrl.value = ''
      }
      
      uploadError.value = ''
      videoDuration.value = 0
      emit('file-removed', props.direction)
    }
    
    const onDragEnter = () => {
      isDragOver.value = true
    }
    
    const onDragLeave = (event) => {
      // 只有当离开整个拖拽区域时才设置为false
      if (!event.currentTarget.contains(event.relatedTarget)) {
        isDragOver.value = false
      }
    }
    
    const onVideoLoaded = (event) => {
      videoDuration.value = event.target.duration
    }
    
    return {
      // 响应式数据
      fileInput,
      isDragOver,
      uploadError,
      previewUrl,
      videoDuration,
      
      // 计算属性
      directionName,
      
      // 方法
      formatFileSize,
      getFileType,
      formatDuration,
      triggerFileInput,
      handleFileChange,
      handleFileDrop,
      removeFile,
      onDragEnter,
      onDragLeave,
      onVideoLoaded
    }
  }
}
</script>

<style scoped>
.video-upload-area {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

.drop-area {
  width: 100%;
  height: 100%;
  min-height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  position: relative;
}

.drop-area:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.drop-area.drag-over {
  border-color: #409eff;
  background: #e6f7ff;
  transform: scale(1.02);
}

.drop-area.has-file {
  border-color: #67c23a;
  background: #f0f9ff;
}

.drop-area.upload-error {
  border-color: #f56c6c;
  background: #fef0f0;
}

.empty-state {
  text-align: center;
}

.upload-icon-container {
  position: relative;
  margin-bottom: 16px;
}

.upload-icon {
  color: #c0c4cc;
}

.upload-arrow {
  position: absolute;
  bottom: -8px;
  right: -8px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-title {
  font-size: 16px;
  color: #606266;
  margin: 0 0 8px 0;
}

.upload-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0 0 8px 0;
}

.click-upload {
  color: #409eff;
  text-decoration: underline;
}

.upload-hint {
  font-size: 12px;
  color: #c0c4cc;
  margin: 0;
}

.file-preview {
  width: 100%;
  text-align: center;
}

.file-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.file-icon {
  color: #67c23a;
}

.file-details {
  text-align: left;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 4px 0;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #909399;
  margin: 0 0 2px 0;
}

.file-type {
  font-size: 12px;
  color: #409eff;
  margin: 0;
}

.video-preview {
  margin-bottom: 16px;
}

.preview-video {
  width: 100%;
  max-width: 200px;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-info {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.replace-btn, .remove-btn {
  min-width: 80px;
}

.error-message {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f56c6c;
  font-size: 12px;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .drop-area {
    min-height: 150px;
    padding: 15px;
  }
  
  .upload-icon-container {
    margin-bottom: 12px;
  }
  
  .upload-title {
    font-size: 14px;
  }
  
  .file-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .file-details {
    text-align: center;
  }
}
</style>
