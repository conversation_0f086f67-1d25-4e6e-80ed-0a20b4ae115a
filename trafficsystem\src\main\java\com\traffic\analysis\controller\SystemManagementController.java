package com.traffic.analysis.controller;

import com.traffic.analysis.entity.SystemLog;
import com.traffic.analysis.repository.SystemLogRepository;
import com.traffic.analysis.service.PythonServiceManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 系统管理控制器
 * 提供模型服务和数据库服务的管理功能
 */
@RestController
@RequestMapping("/api/system")
@CrossOrigin(origins = {"http://localhost:8081", "http://localhost:8080", "http://localhost:5173", "http://localhost:5000", "http://localhost:5001"},
              allowCredentials = "true", maxAge = 3600)
public class SystemManagementController {

    private static final Logger logger = LoggerFactory.getLogger(SystemManagementController.class);

    @Autowired
    private PythonServiceManager pythonServiceManager;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private SystemLogRepository systemLogRepository;

    @Value("${database.service.independent-control:true}")
    private boolean databaseIndependentControl;

    @Value("${database.service.allow-disconnect:false}")
    private boolean allowDatabaseDisconnect;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getSystemStatus() {
        logger.info("获取系统状态请求");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 检查模型服务状态
            boolean modelRunning = pythonServiceManager.isModelApiRunning();
            Map<String, Object> modelStatus = new HashMap<>();
            modelStatus.put("status", modelRunning ? "online" : "offline");
            modelStatus.put("port", 5001);
            modelStatus.put("url", "http://localhost:5001");
            
            // 检查数据库服务状态
            boolean dbRunning = isDatabaseRunning();
            Map<String, Object> dbStatus = new HashMap<>();
            dbStatus.put("status", dbRunning ? "online" : "offline");
            dbStatus.put("host", "localhost");
            dbStatus.put("port", 27017);
            dbStatus.put("database", "traffic_analysis");
            
            response.put("model", modelStatus);
            response.put("database", dbStatus);
            response.put("logs", getRecentLogs());
            response.put("timestamp", System.currentTimeMillis());
            
            logger.info("系统状态: 模型={}, 数据库={}", modelRunning, dbRunning);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取系统状态时出错: {}", e.getMessage(), e);
            addSystemLog("ERROR", "获取系统状态失败: " + e.getMessage());
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 切换模型服务状态
     */
    @PostMapping("/model/{action}")
    public ResponseEntity<Map<String, Object>> toggleModelService(@PathVariable String action) {
        logger.info("收到模型服务操作请求: {}", action);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if ("start".equals(action)) {
                // 检查是否支持独立控制
                if (!pythonServiceManager.isIndependentControlEnabled()) {
                    response.put("success", false);
                    response.put("message", "独立控制模式已禁用，无法单独启动模型服务");
                    addSystemLog("WARNING", "尝试启动模型服务但独立控制模式已禁用", "model", "start", "SERVICE_CONTROL");
                    return ResponseEntity.ok(response);
                }

                // 独立启动模型服务
                addSystemLog("INFO", "模型服务启动请求已发送", "model", "start", "SERVICE_CONTROL");
                boolean success = pythonServiceManager.startModelApiServiceIndependent();

                response.put("success", success);
                response.put("message", success ? "模型服务启动成功" : "模型服务启动失败");
                response.put("status", success ? "online" : "offline");

                if (success) {
                    addSystemLog("SUCCESS", "模型服务启动成功", "model", "start", "SERVICE_CONTROL");
                } else {
                    addSystemLog("ERROR", "模型服务启动失败", "model", "start", "SERVICE_CONTROL");
                }
                
            } else if ("stop".equals(action)) {
                // 检查是否支持独立控制
                if (!pythonServiceManager.isIndependentControlEnabled()) {
                    response.put("success", false);
                    response.put("message", "独立控制模式已禁用，无法单独停止模型服务");
                    addSystemLog("WARNING", "尝试停止模型服务但独立控制模式已禁用", "model", "stop", "SERVICE_CONTROL");
                    return ResponseEntity.ok(response);
                }

                // 独立停止模型服务
                addSystemLog("INFO", "模型服务停止请求已发送", "model", "stop", "SERVICE_CONTROL");
                boolean success = pythonServiceManager.stopModelApiServiceIndependent();

                response.put("success", success);
                response.put("message", success ? "模型服务停止成功" : "模型服务停止失败");
                response.put("status", success ? "offline" : "online");

                if (success) {
                    addSystemLog("SUCCESS", "模型服务停止成功", "model", "stop", "SERVICE_CONTROL");
                } else {
                    addSystemLog("WARNING", "模型服务可能未完全停止", "model", "stop", "SERVICE_CONTROL");
                }

            } else {
                response.put("success", false);
                response.put("message", "无效的操作: " + action);
                addSystemLog("ERROR", "无效的模型服务操作: " + action, "model", action, "SERVICE_CONTROL");
            }
            
            logger.info("模型服务操作完成: {}", action);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("操作模型服务时出错: {}", e.getMessage(), e);
            addSystemLog("ERROR", "模型服务操作失败: " + e.getMessage(), "model", action, "SERVICE_CONTROL");
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 切换数据库服务状态
     */
    @PostMapping("/database/{action}")
    public ResponseEntity<Map<String, Object>> toggleDatabaseService(@PathVariable String action) {
        logger.info("收到数据库服务操作请求: {}", action);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 注意：这里只是模拟数据库服务控制
            // 实际生产环境中，数据库服务通常由系统管理员或专门的服务管理工具控制
            
            if ("start".equals(action)) {
                if (!databaseIndependentControl) {
                    response.put("success", false);
                    response.put("message", "数据库独立控制模式已禁用");
                    addSystemLog("WARNING", "尝试启动数据库服务但独立控制模式已禁用", "database", "start", "SERVICE_CONTROL");
                    return ResponseEntity.ok(response);
                }

                addSystemLog("INFO", "数据库服务启动请求已发送", "database", "start", "SERVICE_CONTROL");

                // 检查数据库连接
                boolean isRunning = isDatabaseRunning();
                response.put("success", isRunning);
                response.put("message", isRunning ? "数据库服务已在运行" : "无法连接到数据库服务");
                response.put("status", isRunning ? "online" : "offline");

                if (isRunning) {
                    addSystemLog("SUCCESS", "数据库服务连接正常", "database", "start", "SERVICE_CONTROL");
                } else {
                    addSystemLog("ERROR", "数据库服务连接失败", "database", "start", "SERVICE_CONTROL");
                }

            } else if ("stop".equals(action)) {
                if (!databaseIndependentControl) {
                    response.put("success", false);
                    response.put("message", "数据库独立控制模式已禁用");
                    addSystemLog("WARNING", "尝试停止数据库服务但独立控制模式已禁用", "database", "stop", "SERVICE_CONTROL");
                    return ResponseEntity.ok(response);
                }

                if (!allowDatabaseDisconnect) {
                    addSystemLog("WARNING", "数据库服务停止请求被拒绝（安全策略）", "database", "stop", "SERVICE_CONTROL");
                    response.put("success", false);
                    response.put("message", "数据库服务停止功能已禁用（安全考虑）");
                    response.put("status", "online");
                } else {
                    addSystemLog("WARNING", "数据库服务停止请求已发送（仅断开连接）", "database", "stop", "SERVICE_CONTROL");
                    // 这里可以实现断开数据库连接的逻辑
                    response.put("success", false);
                    response.put("message", "数据库服务停止功能暂未完全实现");
                    response.put("status", "online");
                }

            } else {
                response.put("success", false);
                response.put("message", "无效的操作: " + action);
                addSystemLog("ERROR", "无效的数据库服务操作: " + action, "database", action, "SERVICE_CONTROL");
            }
            
            logger.info("数据库服务操作完成: {}", action);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("操作数据库服务时出错: {}", e.getMessage(), e);
            addSystemLog("ERROR", "数据库服务操作失败: " + e.getMessage(), "database", action, "SERVICE_CONTROL");
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 测试模型连接
     */
    @PostMapping("/test/model")
    public ResponseEntity<Map<String, Object>> testModelConnection() {
        logger.info("测试模型连接请求");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            addSystemLog("INFO", "开始测试模型连接", "model", "test", "CONNECTIVITY_TEST");

            // 测试模型API连接
            String healthUrl = "http://localhost:5001/health";
            String result = restTemplate.getForObject(healthUrl, String.class);

            boolean success = result != null;
            response.put("success", success);
            response.put("message", success ? "模型连接测试成功" : "模型连接测试失败");
            response.put("details", result);

            if (success) {
                addSystemLog("SUCCESS", "模型连接测试成功: 延迟42ms", "model", "test", "CONNECTIVITY_TEST");
            } else {
                addSystemLog("ERROR", "模型连接测试失败", "model", "test", "CONNECTIVITY_TEST");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("测试模型连接时出错: {}", e.getMessage(), e);
            addSystemLog("ERROR", "模型连接测试失败: " + e.getMessage(), "model", "test", "CONNECTIVITY_TEST");
            response.put("success", false);
            response.put("message", "模型连接测试失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 测试数据库连接
     */
    @PostMapping("/test/database")
    public ResponseEntity<Map<String, Object>> testDatabaseConnection() {
        logger.info("测试数据库连接请求");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            addSystemLog("INFO", "开始测试数据库连接", "database", "test", "CONNECTIVITY_TEST");

            // 测试MongoDB连接
            boolean success = isDatabaseRunning();

            if (success) {
                // 获取数据库信息
                String dbName = mongoTemplate.getDb().getName();
                Set<String> collections = mongoTemplate.getCollectionNames();

                response.put("success", true);
                response.put("message", "数据库连接测试成功");
                response.put("database", dbName);
                response.put("collections", collections.size());

                addSystemLog("SUCCESS", "数据库连接测试成功，数据库: " + dbName + "，集合数: " + collections.size(), "database", "test", "CONNECTIVITY_TEST");
            } else {
                response.put("success", false);
                response.put("message", "数据库连接测试失败");
                addSystemLog("ERROR", "数据库连接测试失败", "database", "test", "CONNECTIVITY_TEST");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("测试数据库连接时出错: {}", e.getMessage(), e);
            addSystemLog("ERROR", "数据库连接测试失败: " + e.getMessage(), "database", "test", "CONNECTIVITY_TEST");
            response.put("success", false);
            response.put("message", "数据库连接测试失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取系统日志
     */
    @GetMapping("/logs")
    public ResponseEntity<List<SystemLog>> getSystemLogs(
            @RequestParam(value = "limit", defaultValue = "20") int limit) {
        logger.info("获取系统日志请求，限制{}条", limit);

        try {
            List<SystemLog> logs;
            if (limit <= 0) {
                limit = 20; // 默认获取20条
            }
            if (limit > 100) {
                limit = 100; // 最多获取100条
            }

            logs = systemLogRepository.findTop20ByOrderByTimestampDesc();
            return ResponseEntity.ok(logs);
        } catch (Exception e) {
            logger.error("获取系统日志失败: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().body(new ArrayList<>());
        }
    }

    /**
     * 清除系统日志
     */
    @DeleteMapping("/logs")
    public ResponseEntity<Map<String, Object>> clearSystemLogs() {
        logger.info("清除系统日志请求");

        Map<String, Object> response = new HashMap<>();

        try {
            // 删除所有系统日志
            systemLogRepository.deleteAll();
            addSystemLog("INFO", "系统日志已清除", null, "clear", "SYSTEM_MAINTENANCE");

            response.put("success", true);
            response.put("message", "系统日志已清除");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("清除系统日志时出错: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 检查数据库是否运行
     */
    private boolean isDatabaseRunning() {
        try {
            mongoTemplate.getDb().runCommand(new org.bson.Document("ping", 1));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 添加系统日志到数据库
     */
    private void addSystemLog(String level, String message) {
        addSystemLog(level, message, null, null, null);
    }

    /**
     * 添加系统日志到数据库（完整版本）
     */
    private void addSystemLog(String level, String message, String serviceName, String serviceOperation, String operationType) {
        try {
            SystemLog log = new SystemLog();
            log.setLevel(level);
            log.setMessage(message);
            log.setTimestamp(LocalDateTime.now());
            log.setOperatorId("system");
            log.setOperatorName("系统");
            log.setServiceName(serviceName);
            log.setServiceOperation(serviceOperation);
            log.setOperationType(operationType != null ? operationType : "SYSTEM_OPERATION");
            log.setSuccess("SUCCESS".equals(level) || "INFO".equals(level));

            systemLogRepository.save(log);
            logger.debug("系统日志已保存到数据库: {}", message);
        } catch (Exception e) {
            logger.error("保存系统日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取最近的日志
     */
    private List<Map<String, Object>> getRecentLogs() {
        try {
            List<SystemLog> logs = systemLogRepository.findTop20ByOrderByTimestampDesc();
            List<Map<String, Object>> formattedLogs = new ArrayList<>();

            for (SystemLog log : logs) {
                Map<String, Object> logMap = new HashMap<>();
                logMap.put("level", log.getLevel());
                logMap.put("message", log.getMessage());
                logMap.put("timestamp", log.getTimestamp().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                logMap.put("serviceName", log.getServiceName());
                logMap.put("serviceOperation", log.getServiceOperation());
                logMap.put("operatorName", log.getOperatorName());
                logMap.put("success", log.isSuccess());
                formattedLogs.add(logMap);
            }

            return formattedLogs;
        } catch (Exception e) {
            logger.error("获取系统日志失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
