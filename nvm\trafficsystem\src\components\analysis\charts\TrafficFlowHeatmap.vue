<template>
  <div class="traffic-flow-heatmap" :style="{ height: height + 'px' }">
    <div ref="chartContainer" class="chart-container"></div>
    <div v-if="!hasData" class="no-data-message">
      <el-icon size="48"><DataLine /></el-icon>
      <p>暂无交通流向数据</p>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { DataLine } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

export default {
  name: 'TrafficFlowHeatmap',
  components: {
    DataLine
  },
  props: {
    flowData: {
      type: Array,
      default: () => []
    },
    height: {
      type: Number,
      default: 300
    }
  },
  setup(props) {
    const chartContainer = ref(null)
    let chartInstance = null
    
    const hasData = computed(() => {
      return props.flowData && props.flowData.length > 0
    })
    
    const getDirectionName = (direction) => {
      const names = {
        east: '东',
        south: '南',
        west: '西',
        north: '北'
      }
      return names[direction] || direction
    }
    
    const getDirectionIndex = (direction) => {
      const indices = {
        north: 0,
        east: 1,
        south: 2,
        west: 3
      }
      return indices[direction] || 0
    }
    
    const createHeatmapChart = () => {
      if (!props.flowData || props.flowData.length === 0) {
        // 创建空的热力图
        createEmptyHeatmap()
        return
      }
      
      const directions = ['north', 'east', 'south', 'west']
      const directionNames = directions.map(d => getDirectionName(d))
      
      // 创建4x4的矩阵数据
      const matrixData = []
      
      // 初始化矩阵
      for (let i = 0; i < 4; i++) {
        for (let j = 0; j < 4; j++) {
          matrixData.push([i, j, 0]) // [x, y, value]
        }
      }
      
      // 填充实际流量数据
      props.flowData.forEach(flow => {
        const fromIndex = getDirectionIndex(flow.fromDirection)
        const toIndex = getDirectionIndex(flow.toDirection)
        const intensity = flow.intensity || 0
        
        // 找到对应的矩阵位置并更新值
        const dataIndex = fromIndex * 4 + toIndex
        if (dataIndex < matrixData.length) {
          matrixData[dataIndex][2] = intensity
        }
      })
      
      // 计算最大值用于颜色映射
      const maxValue = Math.max(...matrixData.map(item => item[2]), 1)
      
      const option = {
        title: {
          text: '交通流向强度热力图',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#2c3e50'
          }
        },
        tooltip: {
          position: 'top',
          formatter: function(params) {
            const fromDir = directionNames[params.data[0]]
            const toDir = directionNames[params.data[1]]
            const intensity = (params.data[2] * 100).toFixed(1)
            
            if (params.data[0] === params.data[1]) {
              return `${fromDir}向<br/>内部流量: ${intensity}%`
            } else {
              return `${fromDir} → ${toDir}<br/>流量强度: ${intensity}%`
            }
          }
        },
        grid: {
          height: '60%',
          top: '15%',
          left: '15%',
          right: '10%'
        },
        xAxis: {
          type: 'category',
          data: directionNames,
          splitArea: {
            show: true
          },
          axisLabel: {
            color: '#606266'
          }
        },
        yAxis: {
          type: 'category',
          data: directionNames,
          splitArea: {
            show: true
          },
          axisLabel: {
            color: '#606266'
          }
        },
        visualMap: {
          min: 0,
          max: maxValue,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          bottom: '5%',
          inRange: {
            color: ['#f7f7f7', '#d9f7be', '#95de64', '#52c41a', '#389e0d', '#237804']
          },
          textStyle: {
            color: '#606266'
          }
        },
        series: [{
          name: '交通流向',
          type: 'heatmap',
          data: matrixData,
          label: {
            show: true,
            formatter: function(params) {
              const value = (params.data[2] * 100).toFixed(0)
              return value + '%'
            },
            color: '#2c3e50',
            fontSize: 11
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      
      chartInstance.setOption(option)
    }
    
    const createEmptyHeatmap = () => {
      const directions = ['north', 'east', 'south', 'west']
      const directionNames = directions.map(d => getDirectionName(d))
      
      // 创建空的4x4矩阵
      const matrixData = []
      for (let i = 0; i < 4; i++) {
        for (let j = 0; j < 4; j++) {
          matrixData.push([i, j, 0])
        }
      }
      
      const option = {
        title: {
          text: '交通流向强度热力图',
          subtext: '暂无数据',
          left: 'center',
          textStyle: {
            fontSize: 14,
            color: '#2c3e50'
          },
          subtextStyle: {
            color: '#909399'
          }
        },
        tooltip: {
          position: 'top',
          formatter: function(params) {
            const fromDir = directionNames[params.data[0]]
            const toDir = directionNames[params.data[1]]
            return `${fromDir} → ${toDir}<br/>暂无数据`
          }
        },
        grid: {
          height: '60%',
          top: '15%',
          left: '15%',
          right: '10%'
        },
        xAxis: {
          type: 'category',
          data: directionNames,
          splitArea: {
            show: true
          },
          axisLabel: {
            color: '#606266'
          }
        },
        yAxis: {
          type: 'category',
          data: directionNames,
          splitArea: {
            show: true
          },
          axisLabel: {
            color: '#606266'
          }
        },
        visualMap: {
          min: 0,
          max: 1,
          calculable: false,
          orient: 'horizontal',
          left: 'center',
          bottom: '5%',
          inRange: {
            color: ['#f5f5f5', '#f5f5f5']
          },
          textStyle: {
            color: '#c0c4cc'
          }
        },
        series: [{
          name: '交通流向',
          type: 'heatmap',
          data: matrixData,
          label: {
            show: true,
            formatter: '0%',
            color: '#c0c4cc',
            fontSize: 11
          }
        }]
      }
      
      chartInstance.setOption(option)
    }
    
    const initChart = () => {
      if (!chartContainer.value) return
      
      // 销毁现有图表实例
      if (chartInstance) {
        chartInstance.dispose()
      }
      
      // 创建新的图表实例
      chartInstance = echarts.init(chartContainer.value)
      
      // 渲染图表
      createHeatmapChart()
      
      // 响应式调整
      const resizeHandler = () => {
        if (chartInstance) {
          chartInstance.resize()
        }
      }
      
      window.addEventListener('resize', resizeHandler)
      
      // 保存清理函数
      chartInstance._resizeHandler = resizeHandler
    }
    
    const updateChart = () => {
      if (!chartInstance) {
        initChart()
        return
      }
      
      createHeatmapChart()
    }
    
    // 生命周期
    onMounted(() => {
      nextTick(() => {
        initChart()
      })
    })
    
    // 监听器
    watch(() => props.flowData, () => {
      updateChart()
    }, { deep: true })
    
    watch(() => props.height, () => {
      if (chartInstance) {
        nextTick(() => {
          chartInstance.resize()
        })
      }
    })
    
    // 清理
    const cleanup = () => {
      if (chartInstance) {
        if (chartInstance._resizeHandler) {
          window.removeEventListener('resize', chartInstance._resizeHandler)
        }
        chartInstance.dispose()
        chartInstance = null
      }
    }
    
    return {
      chartContainer,
      hasData,
      cleanup
    }
  },
  beforeUnmount() {
    this.cleanup()
  }
}
</script>

<style scoped>
.traffic-flow-heatmap {
  width: 100%;
  position: relative;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.no-data-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #c0c4cc;
}

.no-data-message p {
  margin: 8px 0 0 0;
  font-size: 14px;
}
</style>
