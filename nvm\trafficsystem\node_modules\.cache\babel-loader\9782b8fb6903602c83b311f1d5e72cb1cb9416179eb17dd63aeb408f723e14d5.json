{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive } from 'vue';\nimport { ElMessage } from 'element-plus';\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue';\nexport default {\n  name: 'FourWayDetectionTest',\n  components: {\n    FourWayRealtimeViewer\n  },\n  setup() {\n    // 响应式数据\n    const testTaskId = ref('8689ed66-1063-4d52-a83e-6c0cd54ea37d');\n    const currentTaskId = ref('');\n    const isTestRunning = ref(false);\n    const logs = ref([]);\n\n    // 方法\n    const addLog = (message, type = 'info') => {\n      logs.value.push({\n        time: new Date().toLocaleTimeString(),\n        message,\n        type\n      });\n    };\n    const startTest = () => {\n      if (!testTaskId.value) {\n        ElMessage.warning('请输入测试任务ID');\n        return;\n      }\n      currentTaskId.value = testTaskId.value;\n      isTestRunning.value = true;\n      addLog(`开始测试，任务ID: ${testTaskId.value}`, 'success');\n      ElMessage.success('测试已开始');\n    };\n    const stopTest = () => {\n      isTestRunning.value = false;\n      currentTaskId.value = '';\n      addLog('测试已停止', 'warning');\n      ElMessage.info('测试已停止');\n    };\n    const clearLogs = () => {\n      logs.value = [];\n    };\n    const handleDetectionUpdate = data => {\n      addLog(`检测更新: ${data.direction}方向, 车辆${data.directionStats.vehicleCount}辆`, 'success');\n    };\n    const handleStatusChange = data => {\n      addLog(`状态变化: ${data.direction}方向, 状态${data.status}`, 'info');\n    };\n    return {\n      testTaskId,\n      currentTaskId,\n      isTestRunning,\n      logs,\n      startTest,\n      stopTest,\n      clearLogs,\n      handleDetectionUpdate,\n      handleStatusChange\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "ElMessage", "FourWayRealtimeViewer", "name", "components", "setup", "testTaskId", "currentTaskId", "isTestRunning", "logs", "addLog", "message", "type", "value", "push", "time", "Date", "toLocaleTimeString", "startTest", "warning", "success", "stopTest", "info", "clearLogs", "handleDetectionUpdate", "data", "direction", "directionStats", "vehicleCount", "handleStatusChange", "status"], "sources": ["D:\\code\\nvm\\trafficsystem\\src\\views\\FourWayDetectionTest.vue"], "sourcesContent": ["<template>\n  <div class=\"four-way-detection-test\">\n    <div class=\"test-header\">\n      <h1>四方向检测测试页面</h1>\n      <p>直接测试四方向实时检测功能</p>\n    </div>\n\n    <div class=\"test-controls\">\n      <el-card>\n        <template #header>\n          <span>测试控制</span>\n        </template>\n        \n        <div class=\"control-row\">\n          <el-input \n            v-model=\"testTaskId\" \n            placeholder=\"输入测试任务ID\"\n            style=\"width: 300px; margin-right: 10px;\"\n          />\n          <el-button \n            type=\"primary\" \n            @click=\"startTest\"\n            :disabled=\"!testTaskId\"\n          >\n            开始测试\n          </el-button>\n          <el-button \n            @click=\"stopTest\"\n            :disabled=\"!isTestRunning\"\n          >\n            停止测试\n          </el-button>\n        </div>\n\n        <div class=\"status-info\" style=\"margin-top: 16px;\">\n          <el-tag :type=\"isTestRunning ? 'success' : 'info'\">\n            状态: {{ isTestRunning ? '测试运行中' : '测试未开始' }}\n          </el-tag>\n          <span style=\"margin-left: 16px;\">任务ID: {{ currentTaskId || '未设置' }}</span>\n        </div>\n      </el-card>\n    </div>\n\n    <div class=\"test-content\" v-if=\"isTestRunning\">\n      <FourWayRealtimeViewer\n        :task-id=\"currentTaskId\"\n        :auto-start=\"true\"\n        @detection-update=\"handleDetectionUpdate\"\n        @status-change=\"handleStatusChange\"\n      />\n    </div>\n\n    <div class=\"test-logs\" v-if=\"logs.length > 0\">\n      <el-card>\n        <template #header>\n          <span>测试日志</span>\n          <el-button \n            size=\"small\" \n            @click=\"clearLogs\"\n            style=\"float: right;\"\n          >\n            清空日志\n          </el-button>\n        </template>\n        \n        <div class=\"log-container\">\n          <div \n            v-for=\"(log, index) in logs\" \n            :key=\"index\"\n            class=\"log-item\"\n            :class=\"log.type\"\n          >\n            <span class=\"log-time\">{{ log.time }}</span>\n            <span class=\"log-message\">{{ log.message }}</span>\n          </div>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive } from 'vue'\nimport { ElMessage } from 'element-plus'\nimport FourWayRealtimeViewer from '@/components/analysis/FourWayRealtimeViewer.vue'\n\nexport default {\n  name: 'FourWayDetectionTest',\n  components: {\n    FourWayRealtimeViewer\n  },\n  setup() {\n    // 响应式数据\n    const testTaskId = ref('8689ed66-1063-4d52-a83e-6c0cd54ea37d')\n    const currentTaskId = ref('')\n    const isTestRunning = ref(false)\n    const logs = ref([])\n\n    // 方法\n    const addLog = (message, type = 'info') => {\n      logs.value.push({\n        time: new Date().toLocaleTimeString(),\n        message,\n        type\n      })\n    }\n\n    const startTest = () => {\n      if (!testTaskId.value) {\n        ElMessage.warning('请输入测试任务ID')\n        return\n      }\n\n      currentTaskId.value = testTaskId.value\n      isTestRunning.value = true\n      addLog(`开始测试，任务ID: ${testTaskId.value}`, 'success')\n      ElMessage.success('测试已开始')\n    }\n\n    const stopTest = () => {\n      isTestRunning.value = false\n      currentTaskId.value = ''\n      addLog('测试已停止', 'warning')\n      ElMessage.info('测试已停止')\n    }\n\n    const clearLogs = () => {\n      logs.value = []\n    }\n\n    const handleDetectionUpdate = (data) => {\n      addLog(`检测更新: ${data.direction}方向, 车辆${data.directionStats.vehicleCount}辆`, 'success')\n    }\n\n    const handleStatusChange = (data) => {\n      addLog(`状态变化: ${data.direction}方向, 状态${data.status}`, 'info')\n    }\n\n    return {\n      testTaskId,\n      currentTaskId,\n      isTestRunning,\n      logs,\n      startTest,\n      stopTest,\n      clearLogs,\n      handleDetectionUpdate,\n      handleStatusChange\n    }\n  }\n}\n</script>\n\n<style scoped>\n.four-way-detection-test {\n  padding: 24px;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.test-header {\n  text-align: center;\n  margin-bottom: 24px;\n}\n\n.test-header h1 {\n  color: #303133;\n  margin-bottom: 8px;\n}\n\n.test-header p {\n  color: #606266;\n  margin: 0;\n}\n\n.test-controls {\n  margin-bottom: 24px;\n}\n\n.control-row {\n  display: flex;\n  align-items: center;\n}\n\n.test-content {\n  margin-bottom: 24px;\n}\n\n.test-logs {\n  margin-top: 24px;\n}\n\n.log-container {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.log-item {\n  padding: 8px 0;\n  border-bottom: 1px solid #f0f0f0;\n  display: flex;\n  align-items: center;\n}\n\n.log-item:last-child {\n  border-bottom: none;\n}\n\n.log-time {\n  color: #909399;\n  font-size: 12px;\n  margin-right: 12px;\n  min-width: 80px;\n}\n\n.log-message {\n  flex: 1;\n}\n\n.log-item.success .log-message {\n  color: #67c23a;\n}\n\n.log-item.warning .log-message {\n  color: #e6a23c;\n}\n\n.log-item.error .log-message {\n  color: #f56c6c;\n}\n\n.log-item.info .log-message {\n  color: #409eff;\n}\n</style>\n"], "mappings": ";AAkFA,SAASA,GAAG,EAAEC,QAAO,QAAS,KAAI;AAClC,SAASC,SAAQ,QAAS,cAAa;AACvC,OAAOC,qBAAoB,MAAO,iDAAgD;AAElF,eAAe;EACbC,IAAI,EAAE,sBAAsB;EAC5BC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN;IACA,MAAMC,UAAS,GAAIP,GAAG,CAAC,sCAAsC;IAC7D,MAAMQ,aAAY,GAAIR,GAAG,CAAC,EAAE;IAC5B,MAAMS,aAAY,GAAIT,GAAG,CAAC,KAAK;IAC/B,MAAMU,IAAG,GAAIV,GAAG,CAAC,EAAE;;IAEnB;IACA,MAAMW,MAAK,GAAIA,CAACC,OAAO,EAAEC,IAAG,GAAI,MAAM,KAAK;MACzCH,IAAI,CAACI,KAAK,CAACC,IAAI,CAAC;QACdC,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;QACrCN,OAAO;QACPC;MACF,CAAC;IACH;IAEA,MAAMM,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAI,CAACZ,UAAU,CAACO,KAAK,EAAE;QACrBZ,SAAS,CAACkB,OAAO,CAAC,WAAW;QAC7B;MACF;MAEAZ,aAAa,CAACM,KAAI,GAAIP,UAAU,CAACO,KAAI;MACrCL,aAAa,CAACK,KAAI,GAAI,IAAG;MACzBH,MAAM,CAAC,cAAcJ,UAAU,CAACO,KAAK,EAAE,EAAE,SAAS;MAClDZ,SAAS,CAACmB,OAAO,CAAC,OAAO;IAC3B;IAEA,MAAMC,QAAO,GAAIA,CAAA,KAAM;MACrBb,aAAa,CAACK,KAAI,GAAI,KAAI;MAC1BN,aAAa,CAACM,KAAI,GAAI,EAAC;MACvBH,MAAM,CAAC,OAAO,EAAE,SAAS;MACzBT,SAAS,CAACqB,IAAI,CAAC,OAAO;IACxB;IAEA,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtBd,IAAI,CAACI,KAAI,GAAI,EAAC;IAChB;IAEA,MAAMW,qBAAoB,GAAKC,IAAI,IAAK;MACtCf,MAAM,CAAC,SAASe,IAAI,CAACC,SAAS,SAASD,IAAI,CAACE,cAAc,CAACC,YAAY,GAAG,EAAE,SAAS;IACvF;IAEA,MAAMC,kBAAiB,GAAKJ,IAAI,IAAK;MACnCf,MAAM,CAAC,SAASe,IAAI,CAACC,SAAS,SAASD,IAAI,CAACK,MAAM,EAAE,EAAE,MAAM;IAC9D;IAEA,OAAO;MACLxB,UAAU;MACVC,aAAa;MACbC,aAAa;MACbC,IAAI;MACJS,SAAS;MACTG,QAAQ;MACRE,SAAS;MACTC,qBAAqB;MACrBK;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}